<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="平台">
        <el-select v-model="ruleForm.platform" clearable>
          <el-option
            v-for="(value, index) in platformList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; margin: 10px"
    >
      <el-table-column
        type="index"
        width="50"
        label="排序"
        align="center"
        :index="indexMethod"
      />
      <el-table-column label="渠道" align="center">
        <template #default="{ row }">
          {{ channelList[row.channel] }}
        </template>
      </el-table-column>
      <el-table-column prop="goodsId" label="商品ID" align="center" />
      <el-table-column prop="goodsName" label="商品名称" align="center" />
      <el-table-column prop="totalScore" label="总得分" align="center" />
      <el-table-column prop="ctr" label="ctr" align="center">
        <template #default="{ row }">
          {{ (row.ctr * 100).toFixed(2) + '%' }}
        </template>
      </el-table-column>
      <el-table-column prop="cvr" label="cvr" align="center">
        <template #default="{ row }">
          {{ (row.cvr * 100).toFixed(2) + '%' }}
        </template>
      </el-table-column>
      <el-table-column prop="favRatio" label="收藏率" align="center">
        <template #default="{ row }">
          {{ (row.favRatio * 100).toFixed(2) + '%' }}
        </template>
      </el-table-column>
      <el-table-column prop="goodsSale" label="商品售价" align="center" />
      <el-table-column prop="salesVolume" label="销量" align="center" />
      <el-table-column prop="gmv" label="GMV" align="center" />
      <el-table-column prop="expouses" label="曝光" align="center" />
      <el-table-column prop="orders" label="下单pv" align="center" />
      <el-table-column prop="favUv" label="收藏uv" align="center" />
      <el-table-column
        prop="detailClicksUv"
        label="详情点击uv"
        align="center"
      />
    </el-table>
  </div>
</template>

<script>
import { getMallHost } from '@/api/algorithmSorting'
export default {
  name: 'ShoppingMallRecommendationHot',
  data() {
    return {
      loading: false,
      listData: [],
      page: 1,
      ruleForm: {
        platform: '1',
      },
      platformList: {
        Android: '1',
        iOS: '2',
      },
      channelList: {
        1: '淘宝',
        2: '京东',
        3: '自营',
      },
    }
  },
  methods: {
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      me.loading = true
      getMallHost({
        page: me.page,
        platform: me.ruleForm.platform,
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data : []
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 重置
    resetForm() {
      this.page = 1
      this.ruleForm.platform = '1'
    },
    // 索引
    indexMethod(index) {
      return index + 1
    },
  },
}
</script>
