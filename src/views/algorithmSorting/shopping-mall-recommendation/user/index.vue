<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="用户id">
        <el-input
          v-model="ruleForm.uid"
          type="number"
          maxlength="13"
          min="1"
          clearable
          placeholder="请填写用户ID"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <p>近一年商品购买历史</p>
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; margin: 10px"
    >
      <el-table-column label="渠道" align="center" width="375">
        <template #default="{ row }">
          {{ channelList[row.channel] }}
        </template>
      </el-table-column>
      <el-table-column prop="goodsId" label="商品ID" align="center" />
      <el-table-column prop="goodsName" label="商品名称" align="center" />
    </el-table>
  </div>
</template>

<script>
import { getPersonalPurchaseList } from '@/api/algorithmSorting'
export default {
  name: 'ShoppingMallRecommendationUser',
  data() {
    return {
      loading: false,
      listData: [],
      page: 1,
      ruleForm: {
        uid: '',
      },
      channelList: {
        1: '淘宝',
        2: '京东',
        3: '自营',
      },
    }
  },
  methods: {
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      if (!me.ruleForm.uid) return me.$message.error('需要请填写用户ID')
      me.loading = true
      getPersonalPurchaseList({
        page: me.page,
        uid: me.ruleForm.uid,
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data : []
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 重置
    resetForm() {
      this.page = 1
      this.ruleForm.uid = ''
    },
  },
}
</script>
