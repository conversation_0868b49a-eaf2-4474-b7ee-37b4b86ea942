<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="城市">
        <area-cascader
          ref="areaCascader"
          :isShowAll="false"
          style="width: 260px"
          @on-update="updateArea"
        >
        </area-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; margin: 10px"
    >
      <el-table-column
        type="index"
        width="50"
        label="排序"
        align="center"
        :index="indexMethod"
      />
      <el-table-column prop="shopId" label="商家ID" align="center" />
      <el-table-column prop="shopName" label="商家名称" align="center" />
      <el-table-column prop="score" label="总得分" align="center" />
      <el-table-column label="剩余基础线索量" align="center">
        <template #default="{ row }">
          {{
            (row.shopListStatVO && row.shopListStatVO.remainingBasicNum) || 0
          }}
        </template>
      </el-table-column>
      <el-table-column label="剩余付费线索量" align="center">
        <template #default="{ row }">
          {{
            (row.shopListStatVO && row.shopListStatVO.remainingPayNumber) || 0
          }}
        </template>
      </el-table-column>
      <el-table-column label="剩余赠送线索量" align="center">
        <template #default="{ row }">
          {{
            (row.shopListStatVO && row.shopListStatVO.remainingGiveNumber) || 0
          }}
        </template>
      </el-table-column>
      <el-table-column label="前七日消耗线索量" align="center">
        <template #default="{ row }">
          {{ (row.shopListStatVO && row.shopListStatVO.consumeNum7) || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="价格" align="center">
        <template #default="{ row }">
          {{ (row.shopListStatVO && row.shopListStatVO.price) || 0 }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import AreaCascader from '@/components/area/area-cascader/index.vue'
import { getSchoolList } from '@/api/algorithmSorting'
export default {
  name: 'CarRentalList',
  components: { AreaCascader },
  data() {
    return {
      loading: false,
      listData: [],
      ruleForm: {
        cityName: '',
        provinceName: ''
      }
    }
  },
  activated() {},
  methods: {
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      if (!me.ruleForm.cityName) return me.$message.error('需要选择城市查询')
      me.loading = true
      getSchoolList({
        ...me.ruleForm,
        lat: '3.2',
        lon: '3.1',
        page: 1,
        limit: 20
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data : []
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
          this.chosenTaskId = null
          this.shopAuditDetail = null
        })
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        cityName: '',
        provinceName: ''
      }
      this.$refs.areaCascader && this.$refs.areaCascader.clear()
    },
    updateArea(name) {
      this.ruleForm.provinceName = name.provinceName
      this.ruleForm.cityName = name.cityName
    },
    // 索引
    indexMethod(index) {
      return index + 1
    }
  }
}
</script>
