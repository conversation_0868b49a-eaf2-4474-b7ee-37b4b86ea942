<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="城市">
        <area-cascader
          ref="areaCascader"
          :isShowAll="false"
          style="width: 260px"
          @on-update="updateArea"
        ></area-cascader>
      </el-form-item>
      <el-form-item label="时间" label-width="100px">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          :shortcuts="timeOptionTop && timeOptionTop.shortcuts"
          :disabled-date="timeOptionTop && timeOptionTop.disabledDate"
          :cell-class-name="timeOptionTop && timeOptionTop.cellClassName"
          v-model="daterange"
          style="width: 400px"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; margin: 10px"
    >
      <el-table-column prop="sort" label="排序" align="center" />
      <el-table-column prop="rentalId" label="租车ID" align="center" />
      <el-table-column prop="title" label="车款型名称" align="center" />
      <el-table-column prop="totalScore" label="总得分" align="center" />
      <el-table-column prop="isNewEnergy" label="是否新车源" align="center">
        <template v-slot="scope">
          <div>{{ scope.row.isNewEnergy ? '是' : '否' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="acceptedOrder" label="接单量" align="center" />
      <el-table-column prop="acceptedRatio" label="接单率" align="center" />
      <el-table-column prop="finishedRatio" label="成单率" align="center" />
      <el-table-column prop="priceScore" label="价格" align="center" />
      <el-table-column
        prop="publishValue"
        label="发布距今天数"
        align="center"
      />
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import AreaCascader from '@/components/area/area-cascader/index.vue'
import { getAlgorithmList } from '@/api/algorithmSorting'
export default {
  data() {
    return {
      loading: false,
      listData: [],
      ruleForm: {
        cityName: '',
        provinceName: '',
        rentStartDate: '',
        rentEndDate: ''
      },
      timeOptionTop: {
        disabledDate: (time) => {
          return time.getTime() < Date.now()
        }
      },
      dayjs
    }
  },
  name: 'CarRentalList',
  components: { AreaCascader },
  computed: {
    daterange: {
      get() {
        if (this.ruleForm.rentStartDate && this.ruleForm.rentEndDate) {
          return [this.ruleForm.rentStartDate, this.ruleForm.rentEndDate]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.rentStartDate = value[0]
          this.ruleForm.rentEndDate = value[1]
        } else {
          this.ruleForm.rentStartDate = ''
          this.ruleForm.rentEndDate = ''
        }
      }
    }
  },
  activated() {},
  methods: {
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      if (!me.ruleForm.cityName) return me.$message.error('需要选择城市查询')
      if (!me.ruleForm.rentStartDate || !me.ruleForm.rentEndDate)
        return me.$message.error('需要选择时间')
      me.loading = true
      getAlgorithmList({
        ...me.ruleForm,
        sortType: 'asc',
        sortColumn: 'createTime'
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data : []
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
          this.chosenTaskId = null
          this.shopAuditDetail = null
        })
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        cityName: '',
        provinceName: '',
        rentStartDate: '',
        rentEndDate: ''
      }
      this.$refs.areaCascader && this.$refs.areaCascader.clear()
    },
    updateArea(name) {
      this.ruleForm.provinceName = name.provinceName
      this.ruleForm.cityName = name.cityName
    }
  }
}
</script>
