<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="设备id">
        <el-input
          v-model="ruleForm.deviceId"
          clearable
          placeholder="请填写设备ID"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col :span="12">
        <el-table
          :data="showList"
          highlight-current-row
          row-key="shopAuditList"
          border
          style="width: 100%; margin: 10px"
        >
          <el-table-column prop="name" label="纬度" align="center" />
          <el-table-column prop="type" label="值" align="center" />
        </el-table>
      </el-col>
      <el-col :span="2">&ensp;&ensp;</el-col>
      <el-col :span="10">
        <el-table
          :data="listData"
          highlight-current-row
          row-key="shopAuditList"
          border
          style="width: 100%; margin: 10px"
        >
          <el-table-column prop="essayId" label="内容ID" align="center" />
          <el-table-column label="内容类型" align="center">
            <template #default="{ row }">
              {{ typeList[row.type] }}
            </template>
          </el-table-column>
          <el-table-column label="内容标题/内容" align="center" width="375">
            <template #default="{ row }">
              <c-feedList :card="row" />
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CFeedList from '@/components/CFeedList/index.vue'
import { getAlgorithmFeed } from '@/api/algorithmSorting'
import { typeList } from '../enum'
export default {
  name: 'HomeList',
  components: {
    CFeedList,
  },
  data() {
    return {
      typeList,
      loading: false,
      listData: [],
      circleList: [],
      page: 1,
      limit: 10,
      ruleForm: {
        deviceId: '',
      },
      showList: [],
      total: 0,
    }
  },
  methods: {
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      if (!me.ruleForm.deviceId) return me.$message.error('需要填写设备ID')
      me.loading = true
      getAlgorithmFeed({
        deviceId: me.ruleForm.deviceId,
      })
        .then((response) => {
          const data = response.data.data || {}
          me.showList = [
            {
              name: '激活距今时间',
              type: `${data.activeDays || '0'}天`,
            },
            {
              name: '近一个月最近30次曝光文章中视频点击的个数',
              type: `${data.clickVideos30 || '0'}个`,
            },
            {
              name: '近一个月最近30次曝光文章中正面反馈次数',
              type: `${data.positiveNums30 || '0'}次`,
            },
            {
              name: '近一个月最近20次点击文章的次教',
              type: `${data.clickEssays20 || '0'}次`,
            },
            {
              name: '近一个月最近20次点击视频的次数',
              type: `${data.clickVideo20 || '0'}次`,
            },
            {
              name: '近一个月最近20次点击文章和视频的总次数',
              type: data.clickVideoEssay20 || '',
            },
            {
              name: '近一个月最近20次点击文章的比率',
              type: data.clickEssaysRatio20 || '',
            },
            {
              name: '近一个月最近30次曝光中最近阅读文章的类别',
              type: data.exposureEssayType30 || '',
            },
            {
              name: '近一个月最近30次曝光中最近正面反馈的类别',
              type: data.exposurePositiveType30 || '',
            },
            {
              name: '长期标签- 过去两周之前的三个月(最多五个）',
              type: data.longTags || '',
            },
            {
              name: '短期标签- 过去两周(最多五个）',
              type: data.shortTags || '',
            },
            {
              name: '用户短期(15天)点击文章类别',
              type: data.clickEssayTypes15 || '',
            },
            {
              name: '用户短期（15天) 点击视频类别',
              type: data.clickVideoTypes15 || '',
            },
            {
              name: '近一个月点击量top5车型平均价格',
              type: `${data.avgCarPrice30 || ''}元`,
            },
            {
              name: '用户对文章详情页短期(15天)的车类别',
              type: data.shortCarTypes || '',
            },
            {
              name: '用户对文章详情页短期(15天)的车价格',
              type: data.shortPriceTypes || '',
            },
            {
              name: '用户对文章详情页长期(60天)的车类别',
              type: data.longCarTypes || '',
            },
            {
              name: '用户对文章详情页长期(60天)的车价格',
              type: data.longPriceTypes || '',
            },
          ]
          me.listData = data.clickDetailVO || []
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
        })
    },
    // 重置
    resetForm() {
      this.ruleForm.deviceId = ''
    },
  },
}
</script>
