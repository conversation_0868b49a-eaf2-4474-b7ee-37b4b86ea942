<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="设备id">
        <el-input
          v-model="ruleForm.deviceId"
          clearable
          placeholder="请填写设备ID"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :span="24">
      <el-col :span="6">
        <el-table
          :data="carList"
          highlight-current-row
          row-key="carList"
          border
          style="width: 100%; margin: 10px"
        >
          <el-table-column prop="goodsId" label="车型ID" align="center" />
          <el-table-column prop="goodsName" label="车型名称" align="center" />
          <el-table-column prop="clickNum" label="浏览次数" align="center" />
        </el-table>
      </el-col>
      <el-col :span="1">&ensp;</el-col>
      <el-col :span="6">
        <el-table
          :data="brandList"
          highlight-current-row
          row-key="brandList"
          border
          style="width: 100%; margin: 10px"
        >
          <el-table-column prop="brandId" label="品牌ID" align="center" />
          <el-table-column prop="brandName" label="品牌名称" align="center" />
          <el-table-column prop="clickNum" label="浏览次数" align="center" />
        </el-table>
      </el-col>
      <el-col :span="1">&ensp;</el-col>
      <el-col :span="5">
        <el-table
          :data="priceList"
          highlight-current-row
          row-key="priceList"
          border
          style="width: 100%; margin: 10px"
        >
          <el-table-column prop="priceName" label="价格区间" align="center" />
          <el-table-column prop="clickNum" label="浏览次数" align="center" />
        </el-table>
      </el-col>
      <el-col :span="1">&ensp;</el-col>
      <el-col :span="4">
        <el-table
          :data="typeList"
          highlight-current-row
          row-key="typeList"
          border
          style="width: 100%; margin: 10px"
        >
          <el-table-column prop="tagName" label="车辆类型" align="center" />
          <el-table-column prop="clickNum" label="浏览次数" align="center" />
        </el-table>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getAlgorithmDevicePreference } from '@/api/algorithmSorting'
export default {
  name: 'SelectedCarUser',
  data() {
    return {
      loading: false,
      carList: [],
      brandList: [],
      priceList: [],
      typeList: [],
      ruleForm: {
        deviceId: '',
      },
    }
  },
  methods: {
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      if (!me.ruleForm.deviceId) return me.$message.error('需要选择城市查询')
      me.loading = true
      getAlgorithmDevicePreference({
        ...me.ruleForm,
      })
        .then((response) => {
          const data = response.data.data || {}
          me.carList = (data.carType || []).sort(sortby('clickNum'))
          me.brandList = (data.brand || []).sort(sortby('clickNum'))
          me.priceList = (data.price || []).sort(sortby('clickNum'))
          me.typeList = (data.carTag || []).sort(sortby('clickNum'))
          function sortby(prop) {
            // prop 属性名
            return function (a, b) {
              var val1 = a[prop]
              var val2 = b[prop]
              return val2 - val1
            }
          }
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
        })
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        deviceId: '',
      }
    },
    // 索引
    indexMethod(index) {
      return index + 1
    },
  },
}
</script>
