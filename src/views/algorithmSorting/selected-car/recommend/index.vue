<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="城市">
        <area-cascader
          ref="areaCascader"
          :isShowAll="false"
          style="width: 260px"
          @on-update="updateArea"
        ></area-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; margin: 10px"
    >
      <el-table-column
        type="index"
        width="50"
        label="排序"
        align="center"
        :index="indexMethod"
      />
      <el-table-column prop="goodsId" label="车型ID" align="center" />
      <el-table-column prop="goodsName" label="车型名称" align="center" />
      <el-table-column prop="totalScore" label="总得分" align="center">
        <template v-slot="scope">
          <div>{{ Number(scope.row.totalScore).toFixed(2) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="carBidScore" label="缺口得分" align="center">
        <template v-slot="scope">
          <div>{{ scope.row.carBidScore.toFixed(2) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="gap" label="缺口值" align="center">
        <template v-slot="scope">
          <div>{{ scope.row.gap.toFixed(2) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="requirement" label="需求" align="center">
        <template v-slot="scope">
          <div>{{ scope.row.requirement.toFixed(2) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="carCtrScore" label="CTR得分" align="center">
        <template v-slot="scope">
          <div>{{ Number(scope.row.carCtrScore).toFixed(2) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="carCtr" label="ctr值" align="center">
        <template #default="{ row }">
          {{ `${(row.carCtr * 100).toFixed(2)}%` || 0 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="carEnquiryScore"
        label="询价转化得分"
        align="center"
      >
        <template v-slot="scope">
          <div>{{ Number(scope.row.carEnquiryScore).toFixed(2) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="carEnquiry" label="询价转化率" align="center">
        <template #default="{ row }">
          {{ `${(row.carEnquiry * 100).toFixed(2)}%` || 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="queryUv" label="询价uv" align="center" />
      <el-table-column prop="goodsDetailUv" label="车型uv" align="center" />
      <el-table-column prop="clicks" label="点击量" align="center" />
      <el-table-column prop="exposure" label="曝光量" align="center" />
    </el-table>
  </div>
</template>

<script>
import AreaCascader from '@/components/area/area-cascader/index.vue'
import { getAlgorithmCar } from '@/api/algorithmSorting'
export default {
  name: 'SelectedCarRecommend',
  components: { AreaCascader },
  data() {
    return {
      loading: false,
      listData: [],
      ruleForm: {
        area: '',
        province: ''
      }
    }
  },
  methods: {
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      if (!me.ruleForm.area) return me.$message.error('需要选择城市查询')
      me.loading = true
      getAlgorithmCar({
        ...me.ruleForm
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data : []
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
          this.chosenTaskId = null
          this.shopAuditDetail = null
        })
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        area: '',
        province: '',
        rentStartDate: '',
        rentEndDate: ''
      }
      this.$refs.areaCascader && this.$refs.areaCascader.clear()
    },
    updateArea(name) {
      this.ruleForm.province = name.provinceName
      this.ruleForm.area = name.cityName
    },
    // 索引
    indexMethod(index) {
      return index + 1
    }
  }
}
</script>
