<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="品牌ID">
        <el-input
          v-model="ruleForm.brandId"
          type="number"
          maxlength="13"
          min="1"
          clearable
          placeholder="请填写品牌ID"
        />
      </el-form-item>
      <el-form-item label="城市">
        <area-cascader
          ref="areaCascader"
          :isShowAll="false"
          style="width: 260px"
          @on-update="updateArea"
        ></area-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; height: 85vh; overflow-y: auto; margin: 10px"
    >
      <el-table-column type="index" label="排序" align="center" />
      <el-table-column prop="shopId" label="经销商ID" align="center" />
      <el-table-column prop="shopName" label="经销商名称" align="center" />
      <el-table-column prop="totalScore" label="总得分" align="center">
        <template #default="{ row }">
          {{ row.totalScore.toFixed(4) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="insufficiency"
        label="年包线索当日是否剩余"
        align="center"
      >
        <template #default="{ row }">
          {{ row.insufficiency ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="offerPriceSocre" label="出价得分" align="center">
        <template #default="{ row }">
          {{ row.offerPriceSocre.toFixed(4) }}
        </template>
      </el-table-column>
      <el-table-column prop="lackClues" label="缺口率" align="center">
        <template #default="{ row }">
          {{ Number(row.lackClues).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="callBackScore" label="回拨得分" align="center">
        <template #default="{ row }">
          {{ row.callBackScore.toFixed(4) }}
        </template>
      </el-table-column>
      <el-table-column prop="callBackRatio" label="回拨量" align="center" />
      <el-table-column
        prop="avgProcessScore"
        label="平均处理时长得分"
        align="center"
      >
        <template #default="{ row }">
          {{ row.avgProcessScore.toFixed(4) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="avgProcessTime"
        label="平均处理时长"
        align="center"
      >
        <template #default="{ row }">
          {{ Number(row.avgProcessTime).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="brandDemandScore"
        label="品牌需求得分"
        align="center"
      >
        <template #default="{ row }">
          {{ Number(row.brandDemandScore).toFixed(4) }}
        </template>
      </el-table-column>
      <el-table-column prop="brandDemand" label="品牌需求" align="center" />
    </el-table>
  </div>
</template>

<script>
import AreaCascader from '@/components/area/area-cascader/index.vue'
import { getAlgorithmShopQuery } from '@/api/algorithmSorting'
export default {
  name: 'CarRentalList',
  components: { AreaCascader },
  data() {
    return {
      loading: false,
      listData: [],
      ruleForm: {
        brandId: '',
        cityName: '',
        provinceName: ''
      }
    }
  },
  computed: {},
  activated() {},
  methods: {
    // 查询
    search() {
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      if (!me.ruleForm.brandId) return me.$message.error('需要输入品牌id')
      if (!me.ruleForm.cityName) return me.$message.error('需要选择城市查询')
      me.loading = true
      getAlgorithmShopQuery({
        ...me.ruleForm
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data : []
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
          this.chosenTaskId = null
          this.shopAuditDetail = null
        })
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        cityName: '',
        provinceName: '',
        brandId: ''
      }
      this.$refs.areaCascader && this.$refs.areaCascader.clear()
    },
    updateArea(name) {
      this.ruleForm.provinceName = name.provinceName
      this.ruleForm.cityName = name.cityName
    }
  }
}
</script>
