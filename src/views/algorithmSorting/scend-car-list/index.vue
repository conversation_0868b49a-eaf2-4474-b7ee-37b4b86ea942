<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="城市">
        <area-cascader
          ref="areaCascader"
          :isShowAll="false"
          style="width: 260px"
          @on-update="updateArea"
        ></area-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; margin: 10px"
    >
      <el-table-column
        type="index"
        width="50"
        label="排序"
        align="center"
        :index="indexMethod"
      />
      <el-table-column prop="carId" label="二手车ID" align="center" />
      <el-table-column prop="carName" label="车型名称" align="center" />
      <el-table-column prop="score" label="总得分" align="center" />
      <el-table-column prop="newCarSource" label="是否新车源" align="center">
        <template v-slot="scope">
          <div>{{ scope.row.newCarSource ? '是' : '否' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="snCtr"
        label="归一化后的ctr（占比40%）"
        align="center"
      />
      <el-table-column prop="clickPv" label="点击PV" align="center" />
      <el-table-column prop="exposurePv" label="曝光PV" align="center" />
      <el-table-column
        prop="edSc"
        label="最后一次刷新距今分值（占比30%）"
        align="center"
      />
      <el-table-column prop="publishValue" label="最近刷新时间" align="center">
        <template v-slot="scope">
          <div>
            {{ $filter.format(scope.row.editTime, 'YYYY-MM-DD HH:mm') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="adSc"
        label="首次审核通过距今分值（占比30%）"
        align="center"
      />
      <el-table-column prop="auditpassTime" label="首次审核时间" align="center">
        <template v-slot="scope">
          <div>
            {{ $filter.format(scope.row.auditpassTime, 'YYYY-MM-DD HH:mm') }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-size="20"
      :current-page="page"
      align="center"
      layout="slot, prev, pager, next"
      class="el-pagination-center"
      @current-change="currentChange"
    >
      <span style="margin-right: 10px; font-weight: 400; color: #606266"
        >当前第 {{ page }} 页</span
      >
    </el-pagination>
  </div>
</template>

<script>
import AreaCascader from '@/components/area/area-cascader/index.vue'
import { getUsedCarControllerList } from '@/api/algorithmSorting'
export default {
  name: 'CarRentalList',
  components: { AreaCascader },
  data() {
    return {
      loading: false,
      listData: [],
      ruleForm: {
        city: '',
        province: ''
      },
      timeOptionTop: {
        disabledDate: (time) => {
          return time.getTime() < Date.now()
        }
      },
      page: 1
    }
  },
  methods: {
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      if (!me.ruleForm.city) return me.$message.error('需要选择城市查询')
      me.loading = true
      getUsedCarControllerList({
        ...me.ruleForm,
        page: me.page
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data : []
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
        })
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        city: '',
        province: '',
        rentStartDate: '',
        rentEndDate: ''
      }
      this.page = 1
      this.$refs.areaCascader && this.$refs.areaCascader.clear()
    },
    // 更新页码
    currentChange(page) {
      this.page = page
      this.getList()
    },
    updateArea(name) {
      this.ruleForm.province = name.provinceName
      this.ruleForm.city = name.cityName
    },
    // 索引
    indexMethod(index) {
      return index + 1 + (this.page - 1) * 20
    }
  }
}
</script>
