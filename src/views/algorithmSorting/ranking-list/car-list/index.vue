<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="选择榜单">
        <el-select v-model="ruleForm.type">
          <el-option
            v-for="(value, index) in typeList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="车辆类型">
        <el-select v-model="ruleForm.goodType">
          <el-option
            v-for="(value, index) in goodTypeEnum"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; height: 85vh; overflow-y: auto; margin: 10px"
    >
      <el-table-column prop="rank" label="排序" align="center" />
      <el-table-column prop="goodId" label="车型ID" align="center" />
      <el-table-column prop="goodsName" label="车型名称" align="center" />
      <el-table-column
        v-if="ruleForm.type === 1"
        prop="browseCount"
        label="前7日浏览量之和"
        align="center"
      />
      <el-table-column
        v-if="ruleForm.type === 2"
        prop="queryPriceCount"
        label="前7日询价量之和"
        align="center"
      />
      <template v-if="ruleForm.type === 3">
        <el-table-column prop="grade" label="总得分" align="center" />
        <el-table-column
          prop="gradeCount"
          label="当前车型投票人数"
          align="center"
        />
        <el-table-column
          prop="cAvgScore"
          label="当前车型的平均数"
          align="center"
        />
        <el-table-column
          prop="allAvgScore"
          label="车库所有车型算出来总共的平均数"
          align="center"
        />
      </template>
    </el-table>
  </div>
</template>

<script>
import { getAlgorithmGoodHotList } from '@/api/algorithmSorting'
import { goodTypeEnum } from '../../enum'
export default {
  name: 'CarList',
  components: {},
  data() {
    return {
      loading: false,
      typeList: {
        人气: 1,
        询价: 2,
        口碑: 3,
      },
      goodTypeEnum,
      listData: [],
      ruleForm: {
        type: 1,
        goodType: '',
      },
    }
  },
  computed: {},
  activated() {},
  methods: {
    // 查询
    search() {
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      me.loading = true
      getAlgorithmGoodHotList({
        ...me.ruleForm,
        page: 1,
        rows: 200,
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data : []
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
          this.chosenTaskId = null
          this.shopAuditDetail = null
        })
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        type: 1,
        goodType: '',
      }
    },
  },
}
</script>
