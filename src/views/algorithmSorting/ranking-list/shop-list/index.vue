<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="选择榜单">
        <el-select v-model="ruleForm.rankType">
          <el-option
            v-for="(value, index) in typeList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="城市">
        <area-cascader
          ref="areaCascader"
          :isShowAll="false"
          style="width: 260px"
          @on-update="updateArea"
        ></area-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; height: 85vh; overflow-y: auto; margin: 10px"
    >
      <el-table-column prop="sort" label="排序" align="center" />
      <el-table-column prop="shopId" label="经销商ID" align="center" />
      <el-table-column prop="shopName" label="经销商名称" align="center" />
      <el-table-column
        v-if="ruleForm.rankType === 'hot'"
        prop="viewPV"
        label="经销商详情页的近7天浏览量"
        align="center"
      />
      <el-table-column
        v-else
        prop="queryPricepv"
        label="经销商前7日实际分配的询价量"
        align="center"
      />
    </el-table>
  </div>
</template>

<script>
import AreaCascader from '@/components/area/area-cascader/index.vue'
import { getAlgorithmRankList } from '@/api/algorithmSorting'
export default {
  name: 'ShopList',
  components: {
    AreaCascader
  },
  data() {
    return {
      loading: false,
      typeList: {
        人气: 'hot',
        询价: 'query'
      },
      listData: [],
      ruleForm: {
        rankType: 'hot',
        cityName: ''
      }
    }
  },
  computed: {},
  activated() {},
  methods: {
    // 查询
    search() {
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      if (!me.ruleForm.cityName) return me.$message.error('请选择城市')
      me.loading = true
      getAlgorithmRankList({
        ...me.ruleForm,
        page: 1,
        limit: 200
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data : []
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
          this.chosenTaskId = null
          this.shopAuditDetail = null
        })
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        rankType: 'hot',
        cityName: ''
      }
      this.$refs.areaCascader && this.$refs.areaCascader.clear()
    },
    updateArea(name) {
      this.ruleForm.cityName = name.cityName
    }
  }
}
</script>
