<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="生产厂商">
        <el-select v-model="ruleForm.productType">
          <el-option
            v-for="(value, index) in productTypeList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; height: 85vh; overflow-y: auto; margin: 10px"
    >
      <el-table-column prop="rank" label="排序" align="center" />
      <el-table-column prop="brandId" label="车型ID" align="center" />
      <el-table-column prop="brandName" label="车型名称" align="center" />
      <el-table-column
        prop="browseCount"
        label="前7日浏览量之和"
        align="center"
      />
    </el-table>
  </div>
</template>

<script>
import { getAlgorithmBrandHotList } from '@/api/algorithmSorting'
export default {
  name: 'BrandList',
  data() {
    return {
      loading: false,
      listData: [],
      productTypeList: {
        全部: 'all',
        国产: '1',
        合资: '2',
        进口: '3',
      },
      ruleForm: {
        productType: 'all',
      },
    }
  },
  computed: {},
  activated() {},
  methods: {
    // 查询
    search() {
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      me.loading = true
      getAlgorithmBrandHotList({
        ...me.ruleForm,
        page: 1,
        rows: 200,
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data : []
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
          this.chosenTaskId = null
          this.shopAuditDetail = null
        })
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        productType: 'all',
      }
    },
  },
}
</script>
