<template>
  <div v-loading="loading" style="margin: 10px">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="圈子id">
        <!-- <searchFriendsCircle ref="searchFriendsCircle" :multipleLimit="1" @updateCircleData="circleData" /> -->
        <el-input
          v-model="ruleForm.hoopId"
          type="number"
          maxlength="13"
          min="1"
          clearable
          placeholder="请填写圈子ID"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; margin: 10px"
    >
      <el-table-column
        type="index"
        width="50"
        label="排序"
        align="center"
        :index="indexMethod"
      />
      <el-table-column prop="id" label="内容ID" align="center" />
      <el-table-column label="内容标题/内容" align="center" width="375">
        <template #default="{ row }">
          <c-feedList :card="row" />
        </template>
      </el-table-column>
      <el-table-column label="内容类型" align="center" width="375">
        <template #default="{ row }">
          {{ typeList[row.type] }}
        </template>
      </el-table-column>
      <el-table-column prop="praiseCnt" label="点赞数" align="center" />
      <el-table-column prop="replycnt" label="评论数" align="center" />
      <el-table-column prop="actTime" label="发布时间" align="center">
        <template #default="{ row }">{{
          $filter.format(row.actTime * 1000, 'YYYY-MM-DD HH:mm')
        }}</template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :page-sizes="[10, 20, 40, 60]"
      :total="total"
      background
      layout="total, sizes, prev, pager, next, jumper"
      style="text-align: center; margin-top: 20px"
      @size-change="sizeChange"
      @current-change="currentChange"
    />
  </div>
</template>

<script>
import CFeedList from '@/components/CFeedList/index.vue'
import { getHoopSquareHotContent } from '@/api/algorithmSorting'
import searchFriendsCircle from '@/components/label/searchFriendsCircle.vue'
import { typeList } from '../../enum'
export default {
  name: 'CircleFriendsSquare',
  components: {
    searchFriendsCircle,
    CFeedList,
  },
  data() {
    return {
      typeList,
      loading: false,
      listData: [],
      circleList: [],
      page: 1,
      limit: 10,
      ruleForm: {
        hoopId: '',
      },
      total: 0,
    }
  },
  activated() {},
  methods: {
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      if (!me.ruleForm.hoopId) return me.$message.error('需要选择圈子进行查询')
      me.loading = true
      getHoopSquareHotContent({
        page: me.page,
        hoopId: me.ruleForm.hoopId,
        limit: me.limit,
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data.listData : []
          me.total = response.data.data ? response.data.data.total : 0
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
        })
    },
    // 更新每页数量
    sizeChange(pageSize) {
      this.page = 1
      this.limit = pageSize
      this.getList()
    },
    // 更新页码
    currentChange(page) {
      this.page = page
      this.getList()
    },
    // 重置
    resetForm() {
      this.page = 1
      this.ruleForm.hoopId = ''
      this.$refs.searchFriendsCircle && this.$refs.searchFriendsCircle.clear()
    },
    // 摩友圈数据
    circleData(data) {
      // console.log(`data`, data)
      this.ruleForm.hoopId = data.id
    },
    // 索引
    indexMethod(index) {
      return index + 1 + (this.page - 1) * this.limit
    },
  },
}
</script>
