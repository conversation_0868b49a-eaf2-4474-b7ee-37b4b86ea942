<template>
  <div v-loading="loading">
    <el-table
      :data="listData"
      highlight-current-row
      row-key="shopAuditList"
      border
      style="width: 100%; margin: 10px"
    >
      <el-table-column prop="id" label="内容ID" align="center" />
      <el-table-column label="内容标题/内容" align="center" width="375">
        <template #default="{ row }">
          <c-feedList :card="row" />
        </template>
      </el-table-column>
      <el-table-column label="内容类型" align="center" width="375">
        <template #default="{ row }">
          {{ typeList[row.type] }}
        </template>
      </el-table-column>
      <el-table-column prop="praiseCnt" label="点赞数" align="center" />
      <el-table-column prop="replycnt" label="评论数" align="center" />
      <el-table-column prop="actTime" label="发布时间" align="center">
        <template #default="{ row }">{{
          $filter.format(row.actTime * 1000, 'YYYY-MM-DD HH:mm')
        }}</template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :page-sizes="[10, 20, 40, 60]"
      :total="total"
      background
      layout="total, sizes, prev, pager, next, jumper"
      style="text-align: center; margin-top: 20px"
      @size-change="sizeChange"
      @current-change="currentChange"
    />
  </div>
</template>

<script>
import CFeedList from '@/components/CFeedList/index.vue'
import { getHoopSquareHot } from '@/api/algorithmSorting'
import { typeList } from '../../enum'
export default {
  name: 'CircleFriendsSquare',
  components: {
    CFeedList,
  },
  data() {
    return {
      typeList,
      loading: false,
      listData: [],
      page: 1,
      limit: 10,
      total: 0,
    }
  },
  activated() {
    this.search()
  },
  methods: {
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 获取列表数据
    getList() {
      const me = this
      me.loading = true
      getHoopSquareHot({
        page: me.page,
        limit: me.limit,
      })
        .then((response) => {
          me.listData = response.data.data ? response.data.data.listData : []
          me.total = response.data.data ? response.data.data.total : 0
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
        })
    },
    // 更新每页数量
    sizeChange(pageSize) {
      this.page = 1
      this.limit = pageSize
      this.getList()
    },
    // 更新页码
    currentChange(page) {
      this.page = page
      this.getList()
    },
  },
}
</script>
