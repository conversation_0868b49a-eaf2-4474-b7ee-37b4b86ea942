<template>
  <div v-loading="loading" class="set-content-margin">
    <el-form :model="form" :inline="true">
      <el-form-item label="用户ID">
        <el-input v-model="form.uid" placeholder="请输入用户ID" />
      </el-form-item>
      <el-form-item label="活动地址">
        <area-cascader
          ref="areaCascader"
          :needDistinct="true"
          :isShowAll="true"
          style="width: 260px"
          @on-update="updateArea"
        ></area-cascader>
      </el-form-item>
      <el-form-item label="活动时间">
        <el-date-picker
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          :disabled-date="pickerOptions && pickerOptions.disabledDate"
          :cell-class-name="pickerOptions && pickerOptions.cellClassName"
          v-model="daterange"
          type="datetimerange"
          style="width: 400px"
          value-format="YYYY-MM-DD HH:mm:ss"
          clearable
          range-separator="至"
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select v-model="form.activityStatus">
          <el-option
            v-for="(value, index) in activityStatusList"
            :key="index"
            :label="value"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="currentChange(1)">查询</el-button>
        <el-button type="primary" @click="resetData">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="table"
      :data="listData"
      class="table"
      highlight-current-row
      row-key="listData"
      border
      style="width: 100%; height: 75vh; overflow-y: auto"
    >
      <el-table-column prop="id" label="活动ID" align="center" />
      <el-table-column prop="activityName" label="活动标题" align="center" />
      <el-table-column prop="locationAddress" label="活动地址" align="center">
      </el-table-column>
      <el-table-column prop="relationType" label="活动时间" align="center">
        <template #default="{ row }">
          {{
            $filters.timeFullS(row.beginTimeDate) +
            ' 至 ' +
            $filters.timeFullS(row.endTimeDate)
          }}
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" label="活动状态" align="center">
        <template #default="{ row }">
          {{ activityStatusList[row.status] }}
          <div>{{ row.refuseReason || '' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="发布时间" align="center">
        <template #default="{ row }">
          {{ $filters.timeFullS(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <!-- <viewer :images="row.imgList"> -->
          <el-button link type="primary" @click="showH5(row)">查看</el-button>
          <el-button
            type="primary"
            link
            v-if="row.status == 2"
            @click="updateStatus(row)"
            >无效</el-button
          >
          <el-button link type="primary" @click="groupChat(row)"
            >查看群聊</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :page-sizes="[10, 20, 40, 60]"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      style="text-align: center; margin-top: 10px"
      @size-change="handleSizeChange"
      @current-change="currentChange"
    />
  </div>
  <ChooseShowImage ref="chooseShowImage" />
  <ChooseIframe ref="chooseIframeRef" />
  <Details ref="detailsRef" />
</template>

<script setup>
import { forwardPickerOptions } from '@/utils/configData'
import { ref, reactive, computed, getCurrentInstance } from 'vue'
import AreaCascader from '@/components/area/area-cascader/index.vue'
import { getRidingActivityList, postRidingUpdateStatus } from '@/api/article'
import ChooseShowImage from '@/components/Dialog/ChooseShowImageNew.vue'
import ChooseIframe from '@/components/Dialog/ChooseIframe.vue'
import Details from './components/details.vue'

import store from '@/store'
import { ElMessageBox, ElMessage } from 'element-plus'
const activityStatusList = {
  '': '全部',
  0: '已失效',
  2: '正在进行',
  3: '已结束',
  4: '待审核',
  5: '审核失败'
}

const pickerOptions = forwardPickerOptions
const { proxy } = getCurrentInstance()
const page = ref(1)
const chooseShowImage = ref(null)
const chooseIframeRef = ref(null)

const detailsRef = ref(null)
const areaCascader = ref(null)
const total = ref(0)
const limit = ref(20)
const listData = ref([])
const initData = () => {
  return {
    uid: '',
    activityStatus: '',
    cityName: '',
    startTime: '', // 开始时间
    endTime: '' // 结束时间
  }
}

let form = reactive(initData())
const uid = computed(() => {
  return store.getters.uid
})

onMounted(() => {
  getList()
})

const loading = ref(false)
const daterange = computed({
  get() {
    if (form.startTime && form.endTime) {
      return [form.startTime, form.endTime]
    }
    return []
  },
  set(value) {
    if (value) {
      form.startTime = value[0]
      form.endTime = value[1]
    } else {
      form.startTime = ''
      form.endTime = ''
    }
  }
})

const getList = () => {
  loading.value = true
  const postData = {
    ...form,
    page: page.value,
    limit: limit.value
  }
  getRidingActivityList(postData)
    .then((response) => {
      console.log(response)
      if (response.data.code === 0) {
        const data = response.data.data || {}
        listData.value = data.listData || []
        total.value = data.total || 0
      }
    })
    .catch((error) => {
      console.log(error)
    })
    .finally(() => {
      loading.value = false
    })
}

// 更新页码
const currentChange = (cpage) => {
  page.value = cpage
  getList()
}
// 变更查询个数
const handleSizeChange = (climit) => {
  limit.value = climit
  getList()
}

const resetData = () => {
  for (const f in form) {
    form[f] = initData()[f] || ''
  }
  areaCascader.value.clear()
}

const updateArea = (name) => {
  console.log(name)
  form.cityName = name.provinceName + name.cityName + name.distinctName
}

const showH5 = (row) => {
  detailsRef.value.init(row)
}
const updateStatus = (row) => {
  ElMessageBox.prompt('请输入无效活动原因', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async ({ value }) => {
      const res = await postRidingUpdateStatus({
        id: row.id,
        auditReason: value
      })
      if (res.data.code === 0) {
        ElMessage.success('操作成功')
        getList()
      }
    })
    .catch((err) => {
      console.log(err)
    })
}

const groupChat = (row) => {
  proxy.$router.push({
    name: 'GroupMessageQuery',
    query: {
      groupId: row.groupChatId || ''
    }
  })
}
</script>

<style lang="scss" scoped></style>
