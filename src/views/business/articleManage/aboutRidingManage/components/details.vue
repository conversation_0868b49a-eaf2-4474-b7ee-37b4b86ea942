<template>
  <el-dialog
    title="活动详情"
    v-model="visible"
    :before-close="close"
    show-close
  >
    <div class="configuration">
      <div class="box-cell">
        <b>用户昵称：</b> <span>{{ details.author }}</span>
        <p
          v-for="(certify, index) in details.certifyList"
          :key="index"
          class="content-right-tag"
        >
          <el-tag class="pointer" type="info" effect="dark">{{
            certify.certifyName
          }}</el-tag>
        </p>
        <p>{{ details.publishTime }}</p>
      </div>
      <div class="mv5">
        <b>活动标题：</b> <span>{{ details.activityName }}</span>
      </div>
      <div class="mv5">
        <b>活动地址：</b> <span>{{ details.address }}</span>
      </div>
      <div class="mv5">
        <b>活动时间：</b>
        <span
          >{{
            $filters.timeFullS(details.beginTimeDate, 'YYYY-MM-DD HH:mm:ss')
          }}
          至
          {{
            $filters.timeFullS(details.endTimeDate, 'YYYY-MM-DD HH:mm:ss')
          }}</span
        >
      </div>
    </div>
    <div class="content">
      <pre v-if="details.content" class="pre-content">{{
        details.content || ''
      }}</pre>
      <template v-for="(img, index) in details.imgList" :key="index">
        <div class="content-imgbox">
          <img
            :src="img"
            class="content-img"
            @click="showImage(details.imgList, index)"
          />
          <div v-if="index === 0" class="tag-img">封面</div>
        </div>
      </template>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
const details = ref({})
const visible = ref(false)
const init = (row) => {
  visible.value = true
  details.value = row
  const list = details.value.imageInfoList?.map((_) => _.imgUrl) || []
  details.value.imgList = [details.value.cover, ...list]
}

const close = () => {
  visible.value = false
  details.value = {}
}

defineExpose({ init })
</script>

<style lang="scss" scoped>
.content {
  height: 85vh;
  padding: 20px;
  overflow-y: auto;
  border: 1px solid #ecf5ff;
  border-radius: 4px;

  .pre-content {
    margin-bottom: 20px;
    font-size: 15px;
    word-wrap: break-word;
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
  }

  .content-imgbox {
    position: relative;
    display: inline-block;
    width: 30%;
    max-height: 50%;
    min-height: 100px;
    padding: 10px;
    margin-right: 2%;
    margin-bottom: 1%;
    overflow: hidden;
    border: 1px solid #02a7f0;
    border-radius: 5px;

    .tag-img {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      width: 100px;
      height: 24px;
      font-size: 15px;
      line-height: 24px;
      text-align: center;
      background: #ffd800;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}
</style>
