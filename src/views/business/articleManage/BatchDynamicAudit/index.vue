<template>
  <div v-loading="loading" class="contentreview">
    <div>
      <span class="pendingReview takeTenSel">
        {{ basicInformation.pendingMomentNum }}
        <span class="review">待审核</span>
      </span>
      <el-button type="warning" @click="takeTenData()">取50条</el-button>
      <el-button type="primary" @click="batchApproveMoment()"
        >批量通过</el-button
      >
    </div>
    <el-row :gutter="20" style="width: 100%">
      <div style="max-height: 80vh">
        <el-table
          ref="table"
          :data="goodsList"
          class="table"
          highlight-current-row
          row-key="goodsList"
          border
          style="width: 100%; overflow-y: auto; height: 75vh"
        >
          <el-table-column
            prop="essayId"
            label="ID"
            align="center"
            width="100"
          />
          <el-table-column
            prop="essayAuther"
            label="作者"
            align="center"
            width="100"
          >
            <template v-slot="scope">
              <span>{{ scope.row.essayAuther }}</span>
              <div style="color: red">
                {{ scope.row.ifShopUser ? '认证商家' : '' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="列表标题/内容" align="center">
            <template v-slot="scope">
              <p>{{ scope.row.shortContent }}</p>
              <div class="img-box">
                <span
                  v-for="(media, index) in scope.row.mediaInfo"
                  :key="index"
                >
                  <span
                    v-for="(img, imgIndex) in media.images"
                    :key="imgIndex"
                    @click="seeBigImg(img.imgOrgUrl)"
                  >
                    <el-image :src="img.imgUrl">
                      <template v-slot:placeholder>
                        <div class="image-slot">
                          加载中<span class="dot">...</span>
                        </div>
                      </template>
                      <template v-slot:error>
                        <div class="image-slot">
                          <el-icon><IconPictureOutline /></el-icon>
                        </div>
                      </template>
                    </el-image>
                  </span>
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="机审状态" prop="" align="center" width="120">
            <template v-slot="scope">
              <div>{{ reviewStatus[scope.row.machineAuditStatus] }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="machineAuditFailReason"
            label="机审原因"
            width="200"
            align="center"
          />
          <el-table-column label="操作" align="center" width="300">
            <template v-slot="scope">
              <el-button
                type="warning"
                @click="auditNotPassed(scope.row, scope.$index)"
                >审核不通过</el-button
              >
              <el-button
                type="danger"
                @click="systemDeletion(scope.row, scope.$index)"
                >系统删除</el-button
              >
              <el-button type="danger" @click="openDetail(scope.row)"
                >查看详情</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            label="安全官"
            prop="securityGuardUsername"
            align="center"
          />
        </el-table>
      </div>
    </el-row>
    <CommonRejectNotice
      ref="rejectNotice"
      @confirmRejection="confirmRejection"
    />
    <ChooseShowImage ref="showImage" />
  </div>
</template>

<script>
import { PictureOutline as IconPictureOutline } from '@element-plus/icons-vue'
import { mapGetters } from 'vuex'
import CommonRejectNotice from '@/components/Notice/commonRejectNotice.vue'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import {
  updateContentStatus,
  postPendingReview,
  getUpdatesDate,
  batchApproveMoment,
  getAcquiredPending
} from '@/api/article'
import { pullMachineAuthContent } from '@/api/article'
export default {
  components: {
    CommonRejectNotice,
    ChooseShowImage,
    IconPictureOutline
  },
  name: 'ContentReview',
  data() {
    return {
      type: '',
      id: '',
      goodsList: [],
      reviewStatus: {
        0: '审核中',
        1: '审核通过', // 审核通过不展示
        2: '审核不通过'
      },
      basicInformation: {},
      dynamicRemain: 0,
      total: 0, // 总数
      status: 0,
      showModule: false, // 是否显示
      ruleForm: {},
      loading: false,
      isfetch: true // 是否连续取十条
    }
  },
  computed: {
    ...mapGetters(['uid', 'name'])
  },
  watch: {},
  activated() {
    const me = this
    me.getDynamicRemain()
    me.getBasicInformation()
  },
  methods: {
    seeBigImg(link) {
      console.log(link)
      this.$refs.showImage.init(link)
    },
    takeTenData(sortOrder) {
      if (this.isfetch) {
        this.isfetch = false
        this.takeTen(sortOrder)
      }
      setTimeout(() => {
        this.isfetch = true
      }, 1000)
    },
    takeTen(sortOrder) {
      // 取50条待审核
      const me = this
      // if (me.dynamicRemain > 10) {
      //   return me.$message.warning('请先处理完动态审核')
      // }
      if (me.total > 50) {
        me.$message.error('当前审核已经大于50条')
        return
      } else if (me.basicInformation.pendingNum === 0) {
        me.$message.error('当前待审条数为0,无法拉取')
        return
      } else {
        me.getTenPendingReview(sortOrder)
      }
    },
    takeMyJob() {
      // 进入我的工作
      const me = this
      me.$router.push({
        name: 'MyTask'
      })
    },
    batchApproveMoment() {
      // 获取个人待审核列表
      const me = this
      me.loading = true
      const essayArr = me.goodsList.map((_) => _.essayId)
      console.log(essayArr && essayArr.join(','))

      batchApproveMoment({
        authUid: me.uid,
        authUser: me.name,
        essayIds: essayArr && essayArr.join(','),
        source: 6
      })
        .then((response) => {
          if (response.data.code === 0) {
            console.log(response)
            me.goodsList = []
            me.getBasicInformation(true)
            me.$message.success('批量通过成功')
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    getPendingList(needBasic = true) {
      // 获取个人待审核列表
      const me = this
      me.loading = true
      getAcquiredPending({
        authUid: this.uid,
        source: 6
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('拉取成功')
            const data = response.data.data
            data.map((value) => {
              value.ctr = (value.ctr * 100).toFixed(2) + '%'
            })
            me.goodsList = data || []
            me.total = me.goodsList.length
            if (me.goodsList.length) {
              me.id = me.goodsList[0].essayId
              me.essayAuther = me.goodsList[0].essayAuther
              me.hoopName = me.goodsList[0].hoopName
              me.hoopRec = me.goodsList[0].hoopRec
              me.showModule = true
            } else {
              me.showModule = false
            }
          } else {
            me.$message.error(response.data.msg)
          }

          needBasic && me.getBasicInformation(true)
        })
        .finally(() => {
          me.loading = false
        })
    },
    getTenPendingReview(sortOrder) {
      // 获取10条待审核列表
      const me = this
      me.loading = true
      postPendingReview({
        authUid: me.uid,
        authUser: me.name,
        source: 6,
        sortOrder: sortOrder
      })
        .then((response) => {
          if (response.data.code === 0) {
            // me.$message.success('拉取成功')
            me.goodsList = [...me.goodsList, ...response.data.data]
            me.total = me.goodsList.length
            me.getPendingList()
            const element = document.querySelector('.table')
            element.scrollTop = 0
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    getDynamicRemain(immediate) {
      // 获取动态审核剩余数量
      const me = this
      getUpdatesDate({
        authUid: me.uid,
        source: 4,
        type: 'moment_detail'
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.dynamicRemain =
              (response.data.data && response.data.data.pendingMomentNum) || 0
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
          if (me.dynamicRemain > 10) {
            return me.$message.warning('请先处理完动态审核')
          }
          // me.getPendingList()
        })
    },
    getBasicInformation() {
      // 实时更新基本信息
      const me = this

      getUpdatesDate({
        authUid: me.uid,
        source: 6
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.basicInformation = response.data.data
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    systemDeletion(item, index) {
      const me = this
      item.index = index

      me.$refs.rejectNotice.init(
        [
          '发布内容严重辱骂挑衅',
          '发布内容严重涉黄',
          '发布内容严重涉政',
          '发布内容买卖水车',
          '发布网络兼职信息、彩票信息、招嫖信息等',
          '发布内容血腥暴力，炸药枪支，迷药',
          '发布无意义内容或广告刷屏'
        ],
        item
      )
    },
    openDetail(item) {
      const me = this

      me.$router.push({
        name: 'EditArticle',
        query: {
          id: item.essayId,
          propertyType: 0
        }
      })
    },
    confirmRejection(rejectData) {
      const me = this
      me.loading = true
      rejectData.extendInfo = rejectData.extendInfo || {}
      updateContentStatus({
        authUid: me.uid,
        authEssayId: rejectData.extendInfo.essayId,
        status: 0,
        essayAuther: rejectData.extendInfo.essayAuther,
        contentHoopName: rejectData.extendInfo.hoopName,
        hoopRec: rejectData.extendInfo.hoopRec,
        reason: rejectData.content,
        source: 6
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('删除成功')
            me.goodsList.splice(rejectData.extendInfo.index, 1)
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    auditNotPassed(item, index) {
      const me = this
      me.$confirm('审核不通过将发送通用站内信，确定执行？', '提示', {
        // 审核不通过
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          me.loading = true
          updateContentStatus({
            authUid: me.uid,
            authEssayId: item.essayId,
            essayAuther: item.essayAuther,
            contentHoopName: item.hoopName,
            hoopRec: item.hoopRec,
            status: 3,
            source: 6
          })
            .then((response) => {
              if (response.data.code === 0) {
                me.$message.success('执行成功')
                me.goodsList.splice(index, 1)
                me.basicInformation.pendingMomentNum--
              } else {
                me.$message.error(response.data.msg)
              }
            })
            .finally(() => {
              me.loading = false
            })
        })
        .catch()
    }
  }
}
</script>

<style lang="scss">
.contentreview {
  .el-image__inner {
    width: 150px;
    margin-bottom: 5px;
  }
}
</style>

<style lang="scss" scoped>
.contentreview {
  margin: 20px 20px 0;
  .pendingReview {
    width: 70px;
    display: inline-block;
    margin-bottom: 20px;
    font-size: 26px;
    color: #333;
    position: relative;
    margin-right: 10px;
    .review {
      position: absolute;
      left: 0;
      bottom: -15px;
      font-size: 12px;
      color: #333;
      display: inline-block;
      width: auto;
      height: auto;
      font-weight: normal;
    }
  }

  .img-box {
    img {
      width: 150px;
    }
  }

  .takeTenSel {
    color: red;
    font-weight: bold;
  }
  .takeTenRes {
    color: blue;
    font-weight: bold;
  }
  .choose {
    margin-bottom: 20px;
    margin-right: 40px;
  }
  .takeTenSelectDesc {
    background-color: red;
    color: white;
    border-radius: 5px;
  }
  .takeTenSelectAsc {
    background-color: yellowgreen;
    color: white;
    border-radius: 5px;
  }
  .takeTenResult {
    background-color: blue;
    color: white;
    border-radius: 5px;
  }
  .tackCompleted {
    color: green;
    font-weight: bold;
  }
  iframe {
    width: 100%;
    height: calc(100vh - 220px);
    width: 400px;
  }
  .el-tag--medium {
    margin: 5px;
  }
  .el-tooltip.item.el-tag.el-tag--info.el-tag--medium {
    width: auto;
    height: 30px;
    font-size: 22px;
    text-align: center;
    line-height: 30px;
  }
  .el-tag.el-tag--info.el-tag--medium {
    width: auto;
    height: 30px;
    font-size: 22px;
    text-align: center;
    line-height: 30px;
  }
  .el-tag.el-tag--danger.el-tag--medium {
    width: auto;
    height: 30px;
    font-size: 22px;
    text-align: center;
    line-height: 30px;
  }
  .el-tag.el-tag--warning.el-tag--medium {
    width: auto;
    height: 30px;
    font-size: 22px;
    text-align: center;
    line-height: 30px;
  }
  .el-tag.el-tag--success.el-tag--medium {
    width: auto;
    height: 30px;
    font-size: 22px;
    text-align: center;
    line-height: 30px;
  }
  .el-button.submit.el-button--success.el-button--medium {
    width: 100px;
    height: 50px;
    font-size: 26px;
  }
}
</style>
