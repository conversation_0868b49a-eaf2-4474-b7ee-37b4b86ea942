<template>
  <div v-loading="loading" class="set-content-margin">
    <el-form :model="form" :inline="true">
      <el-form-item label="内容ID">
        <el-input v-model="form.businessId" placeholder="请输入内容ID" />
      </el-form-item>
      <el-form-item label="">
        <SeletedUser
          ref="selectUser"
          labelWidth="100px"
          :name="'用户昵称'"
          :placeholder="'请输入用户昵称'"
          @sendData="setUid"
        />
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select v-model="form.appealStatus">
          <el-option
            v-for="(value, index) in authStatusList"
            :key="index"
            :label="value"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          :disabled-date="pickerOptions && pickerOptions.disabledDate"
          :cell-class-name="pickerOptions && pickerOptions.cellClassName"
          v-model="daterange"
          type="datetimerange"
          style="width: 400px"
          value-format="YYYY-MM-DD HH:mm:ss"
          clearable
          range-separator="至"
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="currentChange(1)">查询</el-button>
        <el-button type="primary" @click="resetData">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="table"
      :data="listData"
      class="table"
      highlight-current-row
      row-key="listData"
      border
      style="width: 100%; height: 75vh; overflow-y: auto"
    >
      <el-table-column prop="businessId" label="内容ID" align="center" />
      <el-table-column prop="appealReason" label="申诉原因" align="center">
        <template #default="{ row }">
          {{ row.appealReason }}
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" label="审核状态" align="center">
        <template #default="{ row }">
          {{ authStatusList[row.appealStatus] }}
        </template>
      </el-table-column>
      <el-table-column label="内容详情" align="center">
        <template #default="{ row }">
          <el-button type="link" @click="showH5(row)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column label="驳回原因" align="center">
        <template #default="{ row }"> {{ row.auditReason }} </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template #default="{ row }">
          {{ $filters.timeFullS(row.createTime) }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :page-sizes="[10, 20, 40, 60]"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      style="text-align: center; margin-top: 10px"
      @size-change="handleSizeChange"
      @current-change="currentChange"
    />
    <ChooseIframe ref="chooseIframe" />
  </div>
</template>

<script setup>
import { forwardPickerOptions } from '@/utils/configData'
import { ref, reactive, computed, getCurrentInstance } from 'vue'
import { getAppealList } from '@/api/article'
import ChooseIframe from './components/ChooseDetail.vue'
import SeletedUser from '@/components/SeletedUser/SeletedUser.vue'

import store from '@/store'
const authStatusList = {
  '': '全部',
  1: '待审核',
  2: '审核通过',
  3: '审核驳回'
}
const relationTypeList = {
  '': '全部',
  1: '圈主',
  2: '用户',
  3: 'oss'
}
const chooseIframe = ref(null)
const selectUser = ref(null)
const pickerOptions = forwardPickerOptions
const { proxy } = getCurrentInstance()
const page = ref(1)
const total = ref(0)
const limit = ref(20)
const listData = ref([])
const searchLoading = ref(false)

const initData = () => {
  return {
    businessId: '',
    authorId: '',
    appealStatus: '',
    startDate: '', // 开始时间
    endDate: '' // 结束时间
  }
}

let form = reactive(initData())
const uid = computed(() => {
  return store.getters.uid
})

onMounted(() => {
  getList()
})

const loading = ref(false)
const daterange = computed({
  get() {
    if (form.startDate && form.endDate) {
      return [form.startDate, form.endDate]
    }
    return []
  },
  set(value) {
    if (value) {
      form.startDate = value[0]
      form.endDate = value[1]
    } else {
      form.startDate = ''
      form.endDate = ''
    }
  }
})

const getList = () => {
  loading.value = true
  const postData = {
    ...form,
    // operator: uid.value,
    page: page.value,
    limit: limit.value
  }
  getAppealList(postData)
    .then((response) => {
      console.log(response)
      if (response.data.code === 0) {
        const data = response.data.data || {}
        listData.value = data.listData || []
        total.value = data.total || 0
      }
    })
    .catch((error) => {
      console.log(error)
    })
    .finally(() => {
      loading.value = false
    })
}

// 更新页码
const currentChange = (cpage) => {
  page.value = cpage
  getList()
}
// 变更查询个数
const handleSizeChange = (climit) => {
  limit.value = climit
  getList()
}

const resetData = () => {
  for (const f in form) {
    form[f] = initData()[f] || ''
  }
  selectUser.value.clearData()
}
// 设置返回uid
const setUid = (id) => {
  form.authorId = id
}
const showH5 = (row) => {
  const url = `https://wap.corp.mddmoto.com/details-article/${row.businessId}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
  chooseIframe.value.init(row, url)
}
</script>

<style lang="scss" scoped></style>
