<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="预览区域"
      center
      class="choose-iframe"
      append-to-body
    >
      <p class="footer-content">
        <el-button type="primary" @click="seeQrCode()">查看二维码</el-button>
      </p>
      <div class="iframe-content"  >
          <p style="font-size: 25px; font-weight: bold;">{{ detail.title }}</p>
          <p v-if="detail.spareTitle"  style="text-indent: 36px">----{{  detail.spareTitle}}</p> 
          <div v-html="detail.text" style="line-height: 26px;text-indent: 24px"></div>
          <div v-if="detail.image">
            <img v-for="img in detail.image.split(',')" :src="img" style="width: 100%;"/>
          </div>
          <div v-if="detail.video && detail.video.length">
            <video
            v-for="video in detail.video"
            :src="video.url"
            controls="controls"
            class="pgc-video"
          ></video>
          </div>
      </div>
      <choose-qr-code ref="qrCode" />
    </el-dialog>
  </div>
</template>

<script>
import CDetailsHead from '@/components/CDetail/CDetailsHead.vue'
import COuterChain from '@/components/CDetail/COuterChain.vue'
import CDetailsContent from '@/components/CDetail/CDetailsContent.vue'
import CRelatedAnswer from '@/components/CDetail/CRelatedAnswer.vue'
import CShopSwipe from '@/components/CDetail/CShopSwipe.vue'
import ChooseQrCode from '@/components/Dialog/ChooseQrCode.vue'
import { getQueryEassyDetail } from '@/api/articleModule'
export default {
  name: 'ChooseDetail',
  components: {
    CDetailsHead,
    COuterChain,
    CDetailsContent,
    CRelatedAnswer,
    CShopSwipe,
    ChooseQrCode
  },
  data() {
    return {
      dialogVisible: false,
      loadStaus: false,
      detail: {},
      type: '',
      id: '',
      url: '',
      sourceType: '',
      isSpecialType: false,
      stringType: ''
    }
  },
  methods: {
    init(data, url) {
      const me = this
      me.url = url
      me.dialogVisible = true
      me.loadStaus = false
      me.detail = data
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
      this.id = ''
    },
    // 查看二维码
    seeQrCode() {
      this.$refs.qrCode.init(this.url)
    }
  }
}
</script>

<style lang="scss">
.choose-iframe {
  .iframe-content {
    width: 400px;
    height: 600px;
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 5px;
    margin: 0 auto;
    display: block;
    overflow: hidden;
    overflow-y: scroll;
    position: relative;
  }
  .pgc-video {
    width: 100%;
  }
}
</style>
