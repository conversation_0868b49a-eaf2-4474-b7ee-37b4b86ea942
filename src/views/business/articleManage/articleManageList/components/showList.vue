<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="选择展示的列"
      center
      class="show-list"
      append-to-body
    >
      <el-checkbox-group v-model="checkList">
        <el-checkbox v-for="(list, index) in lists" :label="list" :key="index">
          {{ list }}
        </el-checkbox>
      </el-checkbox-group>
      <div class="footer-content">
        <el-button type="primary" @click="updataList">确认</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { articleDisplayList } from '@/utils/enum'
export default {
  name: 'ShowList',
  components: {},
  data() {
    return {
      dialogVisible: false,
      lists: articleDisplayList,
      checkList: [],
    }
  },
  methods: {
    init() {
      this.dialogVisible = true
      this.checkList = JSON.parse(localStorage.articleDisplayList || '[]')
      this.checkList = this.checkList.length ? this.checkList : this.lists
    },
    updataList() {
      const me = this
      localStorage.setItem('articleDisplayList', JSON.stringify(me.checkList))
      setTimeout(() => {
        $emit(me, 'backList', me.checkList)
        me.dialogVisible = false
      }, 100)
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
    },
  },
  emits: ['backList'],
}
</script>

<style lang="scss">
.show-list {
}
</style>
