<template>
  <div v-loading="loading" style="padding: 20px">
    <p class="title">{{ title }}</p>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="ruleForm.title"
          placeholder="请输入投票标题"
          style="width: 300px"
        />
        <el-checkbox
          v-model="ruleForm.displayTitle"
          style="margin-left: 20px; margin-top: 10px"
          >显示</el-checkbox
        >
      </el-form-item>
      <el-form-item label="补充描述">
        <el-input
          v-model="ruleForm.description"
          placeholder="请输入补充描述"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="投票封面">
        <p>
          <el-button
            v-if="ruleForm.cover"
            type="danger"
            style="margin-left: 20px"
            @click="ruleForm.cover = ''"
            >删除图片</el-button
          >
        </p>
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccess"
          name="upfile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <img
            v-if="ruleForm.cover"
            :src="ruleForm.cover"
            style="max-width: 400px; max-height: 400px"
            alt=""
          />
          <el-button type="primary" style="margin-left: 20px"
            >上传图片</el-button
          >
        </el-upload>
      </el-form-item>
      <div class="type-lists">
        <el-form-item
          v-for="(item, index) in itemList"
          :key="index"
          label="投票选项"
          class="type-list"
        >
          <el-input
            v-model="item.content"
            placeholder="请输入投票选项"
            style="width: 300px"
          />
          <el-button
            v-if="!item.id"
            type="danger"
            @click="deleteItem(item, index)"
            >删除</el-button
          >
        </el-form-item>
        <el-button
          type="primary"
          style="margin: 0 0 10px 120px"
          @click="addType"
          >添加选项</el-button
        >
      </div>
      <el-form-item label="活动开始时间" prop="beginTime">
        <el-date-picker
          v-model="ruleForm.beginTime"
          type="datetime"
          value-format="x"
          placeholder="选择日期时间"
        />
      </el-form-item>
      <el-form-item label="活动结束时间" prop="endTime">
        <el-date-picker
          v-model="ruleForm.endTime"
          type="datetime"
          value-format="x"
          placeholder="选择日期时间"
        />
      </el-form-item>
      <el-form-item label="最多投票数量">
        <el-input
          v-model="ruleForm.voteLimit"
          :max="itemList && itemList.length"
          placeholder="请输入投票数量"
          type="number"
          min="1"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="是否有效">
        <el-switch v-model="ruleForm.status" active-text="" inactive-text="" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { GetVoteInfo, PostUpdateVoteDetail } from '@/api/activeConfiguration'
import { timeFullS } from '@/filters'
import { deepCopy } from '@/utils'
import { mapGetters } from 'vuex'
import { batchRecordBeforeAlter } from '@/utils/enum/logData'
export default {
  name: 'EditVoting',
  data() {
    return {
      loading: false,
      title: '添加投票',
      itemList: [],
      ruleForm: {},
      rules: {
        title: [{ required: true, message: '请输入投票标题', trigger: 'blur' }],
        beginTime: [
          {
            type: 'date',
            required: true,
            message: '请选择时间',
            trigger: 'blur',
          },
        ],
        endTime: [
          {
            type: 'date',
            required: true,
            message: '请选择时间',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  computed: {
    ...mapGetters(['uid']),
  },
  mounted() {},
  activated() {
    this.$route.query && this.$route.query.id ? this.getData() : this.initData()
    this.title =
      this.$route.query && this.$route.query.id ? '编辑投票' : '添加投票'
  },
  methods: {
    // 获取数据
    getData() {
      const me = this
      GetVoteInfo({
        id: me.$route.query.id,
      }).then((response) => {
        if (response.data.code === 0) {
          me.ruleForm = response.data.data
          batchRecordBeforeAlter(me.ruleForm, me.$route.query.id)
          me.ruleForm.status = !!me.ruleForm.status
          me.ruleForm.displayTitle = !!me.ruleForm.displayTitle
          me.itemList = me.ruleForm.itemList
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    // 添加时，手动写入数据
    initData() {
      this.ruleForm = {
        title: '',
        description: '',
        beginTime: '',
        endTime: '',
        cover: '',
        voteLimit: 1,
        displayTitle: true,
        status: true,
      }
      this.itemList = []
      this.itemList = this.itemList.concat({ content: '' }, { content: '' })
    },
    // 添加类型
    addType() {
      this.itemList = this.itemList.concat({ content: '' })
    },
    // 删除类型
    deleteItem(data, index) {
      if (this.itemList.length < 3) {
        return this.$message.error('最少两条')
      }
      this.itemList.splice(index, 1)
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      this.$oss.ossUploadImage(option)
    },
    onSuccess(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        this.ruleForm['cover'] = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误',
        })
      }
    },
    // 提交
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.saveDetail()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    saveDetail() {
      const me = this
      const voteItems = this.itemList.filter((_) => {
        if (_.content || _.id) {
          return _
        }
      }) // 新增或编辑的投票名称
      const errData = voteItems.filter((_) => {
        if (_.id && !_.content) {
          return _
        }
      })
      if (errData.length) {
        return me.$message.error('选项修改数据不可为空')
      }
      if (voteItems.length < 2) {
        return me.$message.error('最少两条投票选项')
      }
      if (me.ruleForm.beginTime > me.ruleForm.endTime) {
        return me.$message.error('开始时间不得大于结束时间')
      }
      if (voteItems.length < me.ruleForm.voteLimit) {
        return me.$message.error('可投票数量不得大于选项个数')
      }
      me.loading = true
      const postData = deepCopy(me.ruleForm)
      postData.beginTime = timeFullS(postData.beginTime)
      postData.endTime = timeFullS(postData.endTime)
      postData.voteItems = JSON.stringify(voteItems)
      postData.status = postData.status ? '1' : '0'
      postData.displayTitle = postData.displayTitle ? '1' : '0'
      delete postData.itemList
      const tip = postData.id ? '编辑' : '添加'
      PostUpdateVoteDetail(postData).then((response) => {
        if (response.data.code === 0) {
          me.$message.success(`${tip}成功`)
          me.loading = false
          setTimeout(() => {
            me.$router.go(-1)
          }, 2000)
        } else {
          me.$message.error(response.data.msg)
          me.loading = false
        }
      })
    },
  },
}
</script>

<style lang="scss">
.choose-voting-content {
}
</style>
