<template>
  <div class="cartype-config">
    <div class="header" @click.stop="showDrawer">
      <div class="tabs">
        <div>组图 | <span class="copy" @click.stop="copy">复制</span></div>
        <el-icon class="delete" @click.stop="delItem"
          ><IconCircleClose
        /></el-icon>
      </div>
      <div v-if="shuffle.length" class="cartype-box">
        <div
          v-for="(item, index) in shuffle"
          :key="index"
          :class="[
            'cartype-item',
            {
              rowNum2: configData.rowNum === '2',
              rowNum3: configData.rowNum === '3'
            }
          ]"
        >
          <img :src="item.imgUrl" alt="" />
        </div>
      </div>
      <div v-else class="add-config">
        <el-icon><IconCirclePlusOutline /></el-icon>
      </div>
    </div>
    <el-drawer
      title="编辑车型"
      :before-close="handleClose"
      v-model="dialog"
      direction="rtl"
      ref="drawer"
      size="45%"
    >
      <div class="drawer-box">
        <div class="add-button">
          <el-button type="primary" size="small" @click="append"
            >新增</el-button
          >
        </div>
        <el-form :model="updatedData">
          <el-form-item label="有效时间段" label-width="120px">
            <el-date-picker
              :default-time="
                ['00:00:00', '23:59:59'].map((d) =>
                  $dayjs(d, 'hh:mm:ss').toDate()
                )
              "
              v-model="daterange"
              style="width: 400px"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="有效开始日期"
              end-placeholder="有效结束日期"
            />
          </el-form-item>
          <el-form-item label="每行数量" required label-width="80px">
            <el-radio-group v-model="updatedData.rowNum">
              <el-radio :label="'1'">1</el-radio>
              <el-radio :label="'2'">2</el-radio>
              <el-radio :label="'3'">3</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="顺序" required label-width="80px">
            <el-radio-group v-model="updatedData.sortord">
              <el-radio :label="'1'">不打乱</el-radio>
              <el-radio :label="'2'">每次随机打乱</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <!-- <el-table :data="updatedData.carTypeList" stripe style="width: 100%" @row-dblclick="showdialog">
                        <el-table-column prop="imgUrl" label="车型图片" align="center" width="100">
                          <template v-slot="scope">
                            <img :src="scope.row.imgUrl" alt="" class="row-imgUrl" />
                          </template>
                        </el-table-column>
                        <el-table-column prop="linkUrl" label="跳转链接" align="center"> </el-table-column>
                        <el-table-column label="操作" align="center">
                          <template v-slot="scope">
                            <el-button type="primary" link @click="rowCopy(scope.row, scope.$index)">复制</el-button>
                            <el-button type="primary" link @click="rowDel(scope.$index)">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table> -->
        <div class="cartype-conent">
          <div class="column">
            <span>名称</span>
            <span>车型图片</span>
            <span>跳转链接</span>
            <span>操作</span>
          </div>
          <draggable
            class="left-content"
            v-model="updatedData.carTypeList"
            @end="refreshImageList"
          >
            <template #item="{ element, index }">
              <div class="column" :key="index" @dblclick="showdialog(element)">
                <span>{{ element.name }}</span>
                <span>
                  <img :src="element.imgUrl" alt="" class="row-imgUrl" />
                </span>
                <span>{{ element.linkUrl }}</span>
                <span
                  ><el-button
                    type="primary"
                    link
                    @click="rowCopy(element, index)"
                    >复制</el-button
                  >
                  <el-button type="primary" link @click="rowDel(index)"
                    >删除</el-button
                  >
                </span>
              </div>
            </template>
          </draggable>
        </div>
        <div class="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirmation">确 定</el-button>
        </div>
      </div>
    </el-drawer>
    <el-dialog
      :title="`${addData.isNewAdd ? '新增' : '编辑'}车型`"
      v-model="dialogVisible"
      width="580px"
    >
      <el-form :model="addData">
        <el-form-item label="名称" required label-width="120px">
          <el-input
            v-model="addData.name"
            clearable
            style="width: 350px"
            placeholder="不必填"
          />
        </el-form-item>
        <el-form-item label="车型图片" required label-width="110px">
          <el-input
            v-model="addData.imgUrl"
            clearable
            style="width: 350px"
            placeholder="请选择图片"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccessUrl"
            name="titlefile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            <el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="跳转链接" label-width="110px">
          <el-input
            v-model="addData.linkUrl"
            clearable
            style="width: 350px"
            placeholder="请输入配置跳转链接"
          />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="determine">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  CircleClose as IconCircleClose,
  CirclePlus as IconCirclePlusOutline
} from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { deepCopy } from '@/utils'
import draggable from 'vuedraggable'
export default {
  components: {
    draggable,
    IconCircleClose,
    IconCirclePlusOutline
  },
  name: 'CartypeConfig',
  props: {
    configData: {
      typeof: Object,
      default: {}
    },
    index: {
      typeof: Number,
      default: 0
    }
  },
  data() {
    return {
      updatedData: {},
      dialog: false,
      dialogVisible: false,
      addData: {
        imgUrl: '', // 图片地址
        linkUrl: '', // 跳转链接
        isNewAdd: true, // 新增
        createDate: '' // 创建时间
      }
    }
  },
  computed: {
    shuffle() {
      let arr = [...this.configData.carTypeList]
      // let i = newArr.length
      // while (i) {
      //   let j = Math.floor(Math.random() * i--)
      //   ;[newArr[j], newArr[i]] = [newArr[i], newArr[j]]
      // }
      for (let i = arr.length - 1; i > -1; i--) {
        let j = Math.floor(Math.random() * i)
        ;[arr[j], arr[i]] = [arr[i], arr[j]]
      }
      return this.configData.sortord === '2' ? arr : this.configData.carTypeList
    },
    daterange: {
      get() {
        if (this.updatedData.beginTime && this.updatedData.endTime) {
          return [this.updatedData.beginTime, this.updatedData.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.updatedData.beginTime = value[0]
          this.updatedData.endTime = value[1]
        } else {
          this.updatedData.beginTime = ''
          this.updatedData.endTime = ''
        }
      }
    }
  },
  mounted() {},
  methods: {
    showDrawer() {
      this.updatedData = deepCopy(this.configData)
      this.dialog = true
    },
    copy() {
      $emit(this, 'copyData', this.configData, this.index)
    },
    delItem() {
      $emit(this, 'delItem', this.index)
    },
    handleClose() {
      this.dialog = false
    },
    confirmation() {
      if (!this.updatedData.rowNum) {
        return this.$message.error('请选择每行数量')
      }
      if (!this.updatedData.sortord) {
        return this.$message.error('请选择顺序')
      }
      $emit(this, 'updatedData', this.updatedData, this.index)
      this.dialog = false
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 图片返回
    onSuccessUrl(res) {
      if (!res) return
      if (res.name) {
        this.addData.imgUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    showdialog(data) {
      this.addData = deepCopy(data)
      this.dialogVisible = true
    },
    append() {
      this.addData = {
        imgUrl: '', // 图片地址
        linkUrl: '', // 跳转链接
        isNewAdd: true, // 新增
        createDate: '' // 创建时间
      }
      this.dialogVisible = true
    },
    determine() {
      if (!this.addData.name) {
        return this.$message.error('请输入名称')
      }
      if (!this.addData.imgUrl) {
        return this.$message.error('请选择图片')
      }
      if (this.addData.isNewAdd) {
        this.addData.isNewAdd = false
        this.addData.createDate = Math.floor(new Date())
        this.updatedData.carTypeList.push(this.addData)
      } else {
        const findIndex = this.updatedData.carTypeList.findIndex((item) => {
          return item.createDate === this.addData.createDate
        })
        this.updatedData.carTypeList.splice(findIndex, 1, this.addData)
      }
      this.dialogVisible = false
    },
    rowCopy(data, index) {
      const newData = deepCopy(data)
      newData.createDate = Math.floor(new Date())
      console.log(`index`, index)
      // this.updatedData.carTypeList.splice(index + 1, 0, newData)
      this.updatedData.carTypeList.push(newData)
    },
    rowDel(index) {
      this.updatedData.carTypeList.splice(index, 1)
      this.$message.success('删除成功')
    },
    refreshImageList() {
      // console.log(`updatedData.carTypeList`, this.updatedData.carTypeList)
    }
  },
  emits: ['copyData', 'delItem', 'updatedData']
}
</script>

<style lang="scss" scoped>
.cartype-config {
  margin-top: 10px;
  .header {
    background-color: #f8f8f8;
    border-radius: 3px;
    overflow: hidden;
    .tabs {
      height: 30px;
      background-color: #f1f1f1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      .copy {
        color: #462be2;
        cursor: pointer;
      }
      .delete {
        font-size: 20px;
        cursor: pointer;
      }
    }
    .cartype-box {
      padding: 10px 15px 0 15px;
      display: flex;
      flex-wrap: wrap;
      .cartype-item {
        margin-bottom: 10px;
        &.rowNum2 {
          width: calc(100% / 2 - 5px);
          &:nth-child(2n) {
            margin-left: 10px;
          }
        }
        &.rowNum3 {
          width: calc(100% / 3 - 10px);
          &:nth-child(2n) {
            margin-left: 10px;
          }
          &:nth-child(3n) {
            margin-left: 10px;
          }
        }
        img {
          display: block;
          width: 100%;
          border-radius: 6px;
        }
      }
    }
    .add-config {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 150px;
      i {
        font-size: 80px;
      }
    }
  }
  .drawer-box {
    position: relative;
    height: 100%;
    padding: 0 20px;
    .add-button {
      text-align: right;
    }
    .cartype-conent {
      padding-bottom: 60px;
      .column {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        padding: 10px 0;
        align-items: center;
        text-align: center;
        border-bottom: 1px solid #dedede;
        span:nth-child(1) {
          width: 20%;
        }
        span:nth-child(2) {
          width: 30%;
        }
        span:nth-child(3) {
          width: 35%;
          word-wrap: break-word;
          word-break: normal;
        }
        span:nth-child(4) {
          width: 15%;
        }
      }
    }
    .footer {
      position: fixed;
      bottom: 20px;
      right: 20px;
    }
  }
  .dialog-footer {
    text-align: center;
    padding-top: 20px;
  }
  .row-imgUrl {
    width: 100px;
  }
}
</style>
