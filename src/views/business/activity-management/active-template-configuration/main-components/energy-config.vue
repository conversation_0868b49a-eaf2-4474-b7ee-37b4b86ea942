<template>
  <div class="energy-config">
    <div class="header" @click.stop="showDrawer">
      <div class="tabs">
        <div>能量夺宝 | <span class="copy" @click.stop="copy">复制</span></div>
        <el-icon class="delete" @click.stop="delItem"
          ><IconCircleClose
        /></el-icon>
      </div>
      <div v-if="props.configData.bgImg" class="img-box">
        <img :src="props.configData.bgImg" alt="" />
        <div class="content-style">
          <div
            class="countdown"
            :style="{ color: props.configData.countdownColor }"
          >
            开奖倒计时：00:00:00
          </div>
          <div
            class="participation"
            :style="{ color: props.configData.contentColor }"
          >
            XXX人已参与
          </div>
        </div>
      </div>
      <div v-else class="add-config">
        <el-icon><IconCirclePlusOutline /></el-icon>
      </div>
    </div>
    <el-drawer
      title="能量夺宝配置"
      :before-close="handleClose"
      v-model="dialog"
      direction="rtl"
      ref="drawer"
      size="600px"
    >
      <div class="drawer-box">
        <el-form :model="updatedData" :rules="rules" ref="ruleFormRef">
          <el-form-item label="有效时间段">
            <div>
              <el-date-picker
                :default-time="
                  ['00:00:00', '23:59:59'].map((d) =>
                    $dayjs(d, 'hh:mm:ss').toDate()
                  )
                "
                v-model="daterange"
                style="width: 400px"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="有效开始日期"
                end-placeholder="有效结束日期"
              />
            </div>
          </el-form-item>
          <el-form-item label="关联活动ID" prop="relationId" required>
            <el-input v-model="updatedData.relationId" style="width: 400px" />
          </el-form-item>
          <div class="title-style">未开奖</div>
          <el-form-item label="背景图" prop="bgImg" required>
            <el-input
              v-model="updatedData.bgImg"
              clearable
              style="width: 350px"
              placeholder="请选择图片"
            />
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest"
              :on-success="(res) => onSuccessUrl(res, 'bgImg')"
              name="titlefile"
              action
            >
              <el-button type="primary" link>选择图片</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="倒计时字体颜色" prop="countdownColor" required>
            <div style="width: 50px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.countdownColor"
              />
            </div>
          </el-form-item>
          <el-form-item label="报名字体颜色" prop="contentColor" required>
            <div style="width: 50px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.contentColor"
              />
            </div>
          </el-form-item>
          <div class="title-style">已开奖</div>
          <el-form-item label="背景图" prop="bgImgLottery" required>
            <el-input
              v-model="updatedData.bgImgLottery"
              clearable
              style="width: 350px"
              placeholder="请选择图片"
            />
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest"
              :on-success="(res) => onSuccessUrl(res, 'bgImgLottery')"
              name="titlefile"
              action
            >
              <el-button type="primary" link>选择图片</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item
            label="倒计时字体颜色"
            prop="countdownColorLottery"
            required
          >
            <div style="width: 50px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.countdownColorLottery"
              />
            </div>
          </el-form-item>
          <el-form-item
            label="报名字体颜色"
            prop="contentColorLottery"
            required
          >
            <div style="width: 50px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.contentColorLottery"
              />
            </div>
          </el-form-item>
        </el-form>
        <div class="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirmation">确 定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import {
  CircleClose as IconCircleClose,
  CirclePlus as IconCirclePlusOutline
} from '@element-plus/icons-vue'
import { deepCopy } from '@/utils'
import { predefineColors } from './config'

const { proxy } = getCurrentInstance()

const props = defineProps({
  configData: {
    typeof: Object,
    default: {}
  },
  index: {
    typeof: Number,
    default: 0
  }
})
const emit = defineEmits(['updatedData', 'copyData', 'delItem'])

const updatedData = ref({})
const dialog = ref(false)
const bgEnter = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('颜色必填'))
  }
  callback()
}
const rules = {
  relationId: [
    { required: true, message: '请填写关联活动ID', trigger: 'blur' }
  ],
  bgImg: [{ required: true, message: '请选择图片', trigger: 'blur' }],
  countdownColor: [{ validator: bgEnter, trigger: 'change' }],
  contentColor: [{ validator: bgEnter, trigger: 'change' }],
  bgImgLottery: [{ required: true, message: '请选择图片', trigger: 'blur' }],
  countdownColorLottery: [{ validator: bgEnter, trigger: 'change' }],
  contentColorLottery: [{ validator: bgEnter, trigger: 'change' }]
}
const ruleFormRef = ref()

const daterange = computed({
  get() {
    if (updatedData.value.beginTime && updatedData.value.endTime) {
      return [updatedData.value.beginTime, updatedData.value.endTime]
    }
    return []
  },
  set(value) {
    if (value) {
      updatedData.value.beginTime = value[0]
      updatedData.value.endTime = value[1]
    } else {
      updatedData.value.beginTime = ''
      updatedData.value.endTime = ''
    }
  }
})

const showDrawer = () => {
  updatedData.value = deepCopy(props.configData)
  dialog.value = true
}

// 上传图片
const httpRequest = async (option) => {
  option.imageType = 'nowater' // 无水印
  option.quality = 1
  proxy.$oss.ossUploadImage(option)
}

// 图片返回
const onSuccessUrl = (res, key) => {
  if (!res) return
  if (res.name) {
    updatedData.value[key] = res.imgOrgUrl
    return
  } else {
    proxy.$notify.error({
      title: '上传错误'
    })
  }
}

const handleClose = () => {
  dialog.value = false
}

const confirmation = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      emit('updatedData', updatedData.value, props.index)
      dialog.value = false
    }
  })
}

const copy = () => {
  emit('copyData', props.configData, props.index)
}

const delItem = () => {
  emit('delItem', props.index)
}
</script>

<style lang="scss" scoped>
.energy-config {
  margin-top: 10px;
  .header {
    background-color: #f8f8f8;
    border-radius: 3px;
    overflow: hidden;
    .tabs {
      height: 30px;
      background-color: #f1f1f1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      .copy {
        color: #462be2;
        cursor: pointer;
      }
      .delete {
        font-size: 20px;
        cursor: pointer;
      }
    }
    .img-box {
      position: relative;
      img {
        width: 100%;
        display: block;
      }
      .content-style {
        position: absolute;
        top: 24px;
        right: 38px;
        text-align: right;
        .countdown {
          font-size: 13px;
          line-height: 18px;
        }
        .participation {
          font-size: 12px;
          line-height: 16px;
        }
      }
    }
    .add-config {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 150px;
      i {
        font-size: 80px;
      }
    }
  }
  .drawer-box {
    position: relative;
    height: 100%;
    .title-style {
      border-top: 1px solid #dedede;
      padding: 20px 0 10px 0;
    }
    .footer {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
