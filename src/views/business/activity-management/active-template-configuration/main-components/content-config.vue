<template>
  <div class="content-config">
    <div class="header" @click.stop="showDrawer">
      <div class="tabs">
        <div>内容 | <span class="copy" @click.stop="copy">复制</span></div>
        <el-icon class="delete" @click.stop="delItem"
          ><IconCircleClose
        /></el-icon>
      </div>
      <div
        v-if="contentList.length"
        class="content-list"
        :style="{ backgroundColor: configData.bgColor || '#000' }"
      >
        <div
          v-for="(item, index) in contentList.slice(0, configData.listNum)"
          :key="index"
          class="item"
          :style="{ borderColor: configData.lineColor || '#666' }"
        >
          <img :src="item.img" alt="" />
          <p :style="{ color: configData.titleColor || '#fff' }">
            {{ item.title }}
            <br />
            <span :style="{ color: configData.detailColor || '#ccc' }"
              >预览</span
            >
          </p>
        </div>
      </div>
      <div v-else class="add-config">
        <el-icon><IconCirclePlusOutline /></el-icon>
      </div>
      <div
        v-if="contentList.length > configData.listNum"
        class="view-more"
        :style="{
          backgroundColor: configData.buttonBgColor || '#999',
          color: configData.buttonTextColor || '#fff'
        }"
      >
        {{ configData.buttonText || '查看更多' }}
      </div>
    </div>
    <el-drawer
      title="编辑内容"
      :before-close="handleClose"
      v-model="dialog"
      direction="rtl"
      ref="drawer"
    >
      <div class="drawer-box">
        <el-form :model="updatedData">
          <el-form-item label="有效时间段" label-width="120px">
            <el-date-picker
              :default-time="
                ['00:00:00', '23:59:59'].map((d) =>
                  $dayjs(d, 'hh:mm:ss').toDate()
                )
              "
              v-model="daterange"
              style="width: 400px"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="有效开始日期"
              end-placeholder="有效结束日期"
            />
          </el-form-item>
          <el-form-item label="名称" required label-width="120px">
            <el-input
              v-model="updatedData.name"
              clearable
              style="width: 350px"
              placeholder="必填"
            />
          </el-form-item>
          <el-form-item label="关联专题" required label-width="120px">
            <el-input
              v-model="updatedData.specialId"
              clearable
              style="width: 350px"
              placeholder="请选择专题ID"
            />
          </el-form-item>
          <p>&ensp;&ensp;模块配置</p>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="模块背景颜色:" label-width="120px">
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.bgColor"
                  style="width: 250px"
                >
                </el-color-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分隔线颜色配置:" label-width="120px">
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.lineColor"
                  style="width: 250px"
                >
                </el-color-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="标题颜色配置:" label-width="120px">
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.titleColor"
                  style="width: 250px"
                >
                </el-color-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="详情颜色配置:" label-width="120px">
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.detailColor"
                  style="width: 250px"
                >
                </el-color-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="按钮文案" required label-width="120px">
            <el-input
              v-model="updatedData.buttonText"
              clearable
              style="width: 350px"
              placeholder="不设置，默认文案为查看更多"
            />
          </el-form-item>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="按钮文案颜色配置:" label-width="140px">
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.buttonTextColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="按钮颜色配置:" label-width="120px">
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.buttonBgColor"
                  style="width: 250px"
                >
                </el-color-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="列表展示数" required label-width="120px">
            <el-input
              v-model="updatedData.listNum"
              clearable
              style="width: 350px"
              placeholder="请输入列表展示数"
            />
          </el-form-item>
          <el-form-item label="列表顺序" required label-width="120px">
            <el-radio-group v-model="updatedData.listOrder">
              <el-radio label="1" size="large">同专题顺序</el-radio>
              <el-radio label="2" size="large">随机打乱</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div class="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirmation">确 定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  CircleClose as IconCircleClose,
  CirclePlus as IconCirclePlusOutline
} from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { predefineColors } from './config'
import { deepCopy } from '@/utils'
import { GetTempletDetail } from '@/api/activeConfiguration'
export default {
  components: {
    IconCircleClose,
    IconCirclePlusOutline
  },
  name: 'ContentConfig',
  props: {
    configData: {
      typeof: Object,
      default: {}
    },
    index: {
      typeof: Number,
      default: 0
    }
  },
  data() {
    return {
      predefineColors: predefineColors,
      updatedData: {},
      dialog: false,
      contentList: []
    }
  },
  computed: {
    daterange: {
      get() {
        if (this.updatedData.beginTime && this.updatedData.endTime) {
          return [this.updatedData.beginTime, this.updatedData.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.updatedData.beginTime = value[0]
          this.updatedData.endTime = value[1]
        } else {
          this.updatedData.beginTime = ''
          this.updatedData.endTime = ''
        }
      }
    }
  },
  mounted() {
    this.getContentList(this.configData)
  },
  methods: {
    getContentList(data) {
      if (data.specialId) {
        GetTempletDetail({
          activityId: data.specialId,
          templetId: 1
        }).then((res) => {
          console.log(`res`, res)
          if (res.data.code === 0) {
            const templetJson = JSON.parse(res.data.data.templetJson)
            this.contentList = templetJson.essayMoudel.essayList || []
          }
        })
      }
    },
    showDrawer() {
      this.updatedData = deepCopy(this.configData)
      this.dialog = true
    },
    handleClose() {
      this.dialog = false
    },
    confirmation() {
      if (!this.updatedData.name) {
        return this.$message.error('请输入名称')
      }
      if (!this.updatedData.specialId) {
        return this.$message.error('请选择专题ID')
      }
      $emit(this, 'updatedData', this.updatedData, this.index)
      this.dialog = false
      this.getContentList(this.updatedData)
    },
    copy() {
      $emit(this, 'copyData', this.configData, this.index)
    },
    delItem() {
      $emit(this, 'delItem', this.index)
    }
  },
  emits: ['updatedData', 'copyData', 'delItem']
}
</script>

<style lang="scss" scoped>
.content-config {
  margin-top: 10px;
  .header {
    background-color: #f8f8f8;
    border-radius: 3px;
    overflow: hidden;

    .tabs {
      height: 30px;
      background-color: #f1f1f1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;

      .copy {
        color: #462be2;
        cursor: pointer;
      }

      .delete {
        font-size: 20px;
        cursor: pointer;
      }
    }

    .content-list {
      padding: 0 15px;

      .item {
        padding: 15px 0;
        display: flex;
        align-items: flex-start;
        border-top: 1px solid #313133;

        img {
          width: 112px;
          height: 75px;
          border-radius: 6px;
          object-fit: cover;
        }

        p {
          font-size: 16px;
          line-height: 24px;
          margin: 0 0 0 10px;
        }
        span {
          font-size: 13px;
        }

        &:nth-child(1) {
          border-top: 1px solid #31313300;
        }
      }
    }

    .img-box {
      display: block;

      img {
        width: 100%;
        display: block;
      }
    }

    .add-config {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 150px;

      i {
        font-size: 80px;
      }
    }

    .view-more {
      margin: 0 15px 15px 15px;
      height: 42px;
      background-color: #313133;
      border-radius: 21px;
      color: #999999;
      font-size: 13px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .drawer-box {
    position: relative;
    height: 100%;

    .footer {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
