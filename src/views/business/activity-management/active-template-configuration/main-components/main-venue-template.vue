<template>
  <div class="main-venue-template">
    <div class="add">
      <el-button type="primary" @click="addItem('img')">+ 大图</el-button>
      <el-button type="primary" @click="addItem('banner')">+ banner</el-button>
      <el-button type="primary" @click="addItem('cartype')">+ 组图</el-button>
      <el-button type="primary" @click="addItem('carnew')">+ 车型</el-button>
      <el-button type="primary" @click="addItem('content')">+ 内容</el-button>
      <el-button type="primary" @click="addItem('circle')">+ 摩友圈</el-button>
      <el-button type="primary" @click="addItem('video')">+ 视频</el-button>
      <el-button type="primary" @click="addItem('countdown')">
        + 倒计时
      </el-button>
      <el-button type="primary" @click="addItem('from')">+ 表单</el-button>
      <el-button type="primary" @click="addItem('energy')">
        + 能量夺宝
      </el-button>
    </div>
    <div class="tumble">
      <div class="show-list">
        <draggable
          class="left-content"
          v-model="configData"
          @end="refreshImageList"
        >
          <template #item="{ element, index }">
            <div>
              <template v-if="element.type === 'img'">
                <ImgConfig
                  :configData="element"
                  :index="index"
                  @updatedData="updatedData"
                  @copyData="copyItem"
                  @delItem="delItem"
                />
              </template>
              <template v-if="element.type === 'banner'">
                <BannerConfig
                  :configData="element"
                  :index="index"
                  @updatedData="updatedData"
                  @copyData="copyItem"
                  @delItem="delItem"
                />
              </template>
              <template v-if="element.type === 'carnew'">
                <CartypeNewConfig
                  :configData="element"
                  :index="index"
                  @updatedData="updatedData"
                  @copyData="copyItem"
                  @delItem="delItem"
                />
              </template>
              <template v-if="element.type === 'cartype'">
                <CartypeConfig
                  :configData="element"
                  :index="index"
                  @updatedData="updatedData"
                  @copyData="copyItem"
                  @delItem="delItem"
                />
              </template>
              <template v-if="element.type === 'content'">
                <ContentConfig
                  :configData="element"
                  :index="index"
                  @updatedData="updatedData"
                  @copyData="copyItem"
                  @delItem="delItem"
                />
              </template>
              <template v-if="element.type === 'circle'">
                <CircleConfig
                  :configData="element"
                  :index="index"
                  @updatedData="updatedData"
                  @copyData="copyItem"
                  @delItem="delItem"
                />
              </template>
              <template v-if="element.type === 'video'">
                <VideoConfig
                  :configData="element"
                  :index="index"
                  @updatedData="updatedData"
                  @copyData="copyItem"
                  @delItem="delItem"
                />
              </template>
              <template v-if="element.type === 'countdown'">
                <CountdownConfig
                  :configData="element"
                  :index="index"
                  @updatedData="updatedData"
                  @copyData="copyItem"
                  @delItem="delItem"
                />
              </template>
              <template v-if="element.type === 'from'">
                <FromConfig
                  :configData="element"
                  :index="index"
                  @updatedData="updatedData"
                  @delItem="delItem"
                />
              </template>
              <template v-if="element.type === 'energy'">
                <EnergyConfig
                  :configData="element"
                  :index="index"
                  @updatedData="updatedData"
                  @copyData="copyItem"
                  @delItem="delItem"
                />
              </template>
            </div>
          </template>
        </draggable>
      </div>
    </div>
    <RuleTemplate ref="RuleTemplate" />
    <ShareTemplate ref="ShareTemplate" />
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import RuleTemplate from '../components/rule-template.vue'
import ShareTemplate from '../components/share-template.vue'
import {
  imgDefault,
  bannerDefault,
  cartypeDefault,
  cartypeNewDefault,
  contentDefault,
  circleDefault,
  videoDefault,
  countdownDefault,
  fromDefault,
  energyDefault
} from './config'
import { deepCopy } from '@/utils'
import ImgConfig from './img-config.vue'
import BannerConfig from './banner-config.vue'
import CartypeConfig from './cartype-config.vue'
import CartypeNewConfig from './cartype-new-config.vue'
import ContentConfig from './content-config.vue'
import CircleConfig from './circle-config.vue'
import VideoConfig from './video-config.vue'
import CountdownConfig from './countdown-config.vue'
import FromConfig from './from-config.vue'
import EnergyConfig from './energy-config.vue'
export default {
  name: 'mainVenueTemplate',
  components: {
    draggable,
    RuleTemplate,
    ShareTemplate,
    ImgConfig,
    BannerConfig,
    CartypeConfig,
    CartypeNewConfig,
    ContentConfig,
    CircleConfig,
    VideoConfig,
    CountdownConfig,
    FromConfig,
    EnergyConfig
  },
  data() {
    return {
      configData: [],
      backStatus: false, // 保存状态
      saveListData: {} // 保存的数据
    }
  },
  mounted() {},
  methods: {
    addItem(type) {
      const fromIndex = this.configData.findIndex((item) => {
        return item.type === 'from'
      })
      if (fromIndex > -1 && type === 'from')
        return this.$message.error('已存在表单模块，不可重复配置')
      const listItemName = {
        img: imgDefault,
        banner: bannerDefault,
        cartype: cartypeDefault,
        carnew: cartypeNewDefault,
        content: contentDefault,
        circle: circleDefault,
        video: videoDefault,
        countdown: countdownDefault,
        from: fromDefault,
        energy: energyDefault
      }
      const addItem = {
        ...deepCopy(listItemName[type])
      }
      this.configData.push(addItem)
    },
    updatedData(data, index) {
      this.configData.splice(index, 1, data)
    },
    copyItem(data, index) {
      this.configData.splice(index + 1, 0, data)
    },
    delItem(index) {
      this.configData.splice(index, 1)
      this.$message.success('删除成功')
    },
    // 重置数据
    resert() {
      this.$refs.RuleTemplate.init()
      this.$refs.ShareTemplate.init()
    },
    // 预报保存
    saveData() {
      const me = this
      me.$refs.RuleTemplate.validate()
      me.$refs.ShareTemplate.validate()
      setTimeout(() => {
        if (
          !me.$refs.RuleTemplate.validateStatus ||
          !me.$refs.ShareTemplate.validateStatus
        ) {
          me.backStatus = false
        } else {
          me.backStatus = true
        }
        me.saveListData = {
          ruleData: me.$refs.RuleTemplate.ruleForm,
          shareData: me.$refs.ShareTemplate.ruleForm,
          configData: me.configData
        }
      }, 100)
    },
    // 设置数据
    initData(JsonData) {
      this.$refs.RuleTemplate.init(JsonData.ruleData)
      this.$refs.ShareTemplate.init(JsonData.shareData)
      this.configData = JsonData.configData || []
    },
    refreshImageList() {
      // console.log(`this.configData`, this.configData)
    }
  }
}
</script>

<style lang="scss" scoped>
.main-venue-template {
  .tumble {
    border: 1px solid #dedede;
    border-radius: 5px;
    width: 375px;
    height: 667px;
    margin: 20px 0;
    padding: 0 1px 5px 1px;
    .show-list {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 0;
      }
      // &::-webkit-scrollbar-thumb {
      //   background-color: #ffffffc4;
      //   border-radius: 3px;
      // }
    }
  }
}
</style>
