<template>
  <div class="video-config" v-loading="loading">
    <div class="header" @click.stop="showDrawer">
      <div class="tabs">
        <div>视频 | <span class="copy" @click.stop="copy">复制</span></div>
        <el-icon class="delete" @click.stop="delItem"
          ><IconCircleClose
        /></el-icon>
      </div>
      <div v-if="configData.videoUrl">
        <video
          :src="configData.videoUrl"
          :poster="configData.imgUrl"
          controls="controls"
          class="title-video"
        />
      </div>
      <div v-else class="add-config">
        <el-icon><IconCirclePlusOutline /></el-icon>
      </div>
    </div>
    <el-drawer
      title="视频配置"
      :before-close="handleClose"
      v-model="dialog"
      direction="rtl"
      ref="drawer"
    >
      <div class="drawer-box">
        <el-form :model="updatedData" :rules="rules">
          <el-form-item label="有效时间段" label-width="120px">
            <el-date-picker
              :default-time="
                ['00:00:00', '23:59:59'].map((d) =>
                  $dayjs(d, 'hh:mm:ss').toDate()
                )
              "
              v-model="daterange"
              style="width: 400px"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="有效开始日期"
              end-placeholder="有效结束日期"
            />
          </el-form-item>
          <el-form-item label="名称" label-width="120px">
            <el-input
              v-model="updatedData.name"
              clearable
              style="width: 350px"
              placeholder="不必填"
            />
          </el-form-item>
          <el-form-item
            label="添加视频"
            prop="videoUrl"
            required
            label-width="120px"
          >
            <el-input
              v-model="updatedData.videoUrl"
              clearable
              style="width: 350px"
              placeholder="请选择视频"
            />
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest1"
              :on-success="updateFile"
              name="upfile"
              class="cover-uploader"
              action
            >
              <el-button type="primary" class="fl-left">选择视频</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="视频封面图片：" label-width="120px">
            <el-input
              v-model="updatedData.imgUrl"
              style="width: 350px"
              placeholder="请选择图片"
            />
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest"
              :on-success="onSuccessUrl"
              name="titlefile"
              style="display: inline-block"
              class="avatar-uploader"
              action
            >
              <el-button type="primary" link>选择图片</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
        <div class="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirmation">确 定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  CircleClose as IconCircleClose,
  CirclePlus as IconCirclePlusOutline
} from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { deepCopy } from '@/utils'
export default {
  components: {
    IconCircleClose,
    IconCirclePlusOutline
  },
  name: 'VideoConfig',
  props: {
    configData: {
      typeof: Object,
      default: {}
    },
    index: {
      typeof: Number,
      default: 0
    }
  },
  data() {
    return {
      updatedData: {},
      dialog: false,
      loading: false,
      rules: {
        videoUrl: [{ required: true, message: '请选择视频', trigger: 'blur' }]
      }
    }
  },
  computed: {
    daterange: {
      get() {
        if (this.updatedData.beginTime && this.updatedData.endTime) {
          return [this.updatedData.beginTime, this.updatedData.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.updatedData.beginTime = value[0]
          this.updatedData.endTime = value[1]
        } else {
          this.updatedData.beginTime = ''
          this.updatedData.endTime = ''
        }
      }
    }
  },
  methods: {
    showDrawer() {
      this.updatedData = deepCopy(this.configData)
      this.dialog = true
    },
    handleClose() {
      this.dialog = false
    },
    confirmation() {
      if (!this.updatedData.videoUrl) {
        return this.$message.error('请选择视频')
      }
      $emit(this, 'updatedData', this.updatedData, this.index)
      this.dialog = false
    },
    // 上传图片
    async httpRequest(option) {
      console.log(option, '12321')
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 图片返回
    onSuccessUrl(res) {
      if (!res) return
      if (res.name) {
        this.updatedData.imgUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 上传视频
    async httpRequest1(option) {
      const file = option.file
      const fileName = ((file && file.name) || '').toLowerCase()
      if (!file) return
      if (file.size > 2 * 1024 * 1024 * 1024) {
        return this.setToast('视频大小不能超过2GB')
      }
      if (
        fileName.indexOf('.mp4') === -1 &&
        fileName.indexOf('.mov') === -1 &&
        fileName.indexOf('.ts') === -1
      ) {
        return this.setToast('视频格式不是mp4、mov、ts')
      }
      option.isVideo = true // 是视频
      this.$oss.ossUploadFile(option)
    },
    updateFile(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        this.updatedData['videoUrl'] = res.url
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    copy() {
      $emit(this, 'copyData', this.configData, this.index)
    },
    delItem() {
      $emit(this, 'delItem', this.index)
    }
  },
  emits: ['updatedData', 'copyData', 'delItem']
}
</script>

<style lang="scss" scoped>
.video-config {
  margin-top: 10px;
  .header {
    background-color: #f8f8f8;
    border-radius: 3px;
    overflow: hidden;

    .tabs {
      height: 30px;
      background-color: #f1f1f1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;

      .copy {
        color: #462be2;
        cursor: pointer;
      }

      .delete {
        font-size: 20px;
        cursor: pointer;
      }
    }

    .title-video {
      height: 200px;
      width: 100%;
    }

    .add-config {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 150px;

      i {
        font-size: 80px;
      }
    }
  }

  .drawer-box {
    position: relative;
    height: 100%;

    .footer {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
