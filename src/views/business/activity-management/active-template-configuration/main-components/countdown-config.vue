<template>
  <div class="countdown-config">
    <div class="header" @click.stop="showDrawer">
      <div class="tabs">
        <div>倒计时 | <span class="copy" @click.stop="copy">复制</span></div>
        <el-icon class="delete" @click.stop="delItem"
          ><IconCircleClose
        /></el-icon>
      </div>
      <div
        v-if="configData.BgColor"
        class="countdown-content"
        :style="{ backgroundColor: configData.BgColor }"
      >
        <div class="countdown-title" :style="{ color: configData.titleColor }">
          报名将在
          <div
            class="time"
            :style="{
              backgroundColor: configData.inputBgColor,
              color: configData.inputColor
            }"
          >
            00
          </div>
          天
          <div
            class="time"
            :style="{
              backgroundColor: configData.inputBgColor,
              color: configData.inputColor
            }"
          >
            00
          </div>
          :
          <div
            class="time"
            :style="{
              backgroundColor: configData.inputBgColor,
              color: configData.inputColor
            }"
          >
            00
          </div>
          :
          <div
            class="time"
            :style="{
              backgroundColor: configData.inputBgColor,
              color: configData.inputColor
            }"
          >
            00
          </div>
          后结束
        </div>
        <div
          class="countdown-subtitle"
          :style="{ color: configData.subtitleColor }"
        >
          截止当前，已有0人报名
        </div>
      </div>
      <div v-else class="add-config">
        <el-icon><IconCirclePlusOutline /></el-icon>
      </div>
    </div>
    <el-drawer
      title="倒计时配置"
      :before-close="handleClose"
      v-model="dialog"
      direction="rtl"
      ref="drawer"
      size="600px"
    >
      <div class="drawer-box">
        <el-form
          :model="updatedData"
          :inline="true"
          :rules="rules"
          ref="ruleForm"
        >
          <el-form-item label="有效时间段">
            <div>
              <el-date-picker
                :default-time="
                  ['00:00:00', '23:59:59'].map((d) =>
                    $dayjs(d, 'hh:mm:ss').toDate()
                  )
                "
                v-model="daterange"
                style="width: 400px"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="有效开始日期"
                end-placeholder="有效结束日期"
              />
            </div>
          </el-form-item>
          <br />
          <el-form-item label="关联活动ID">
            <el-input v-model="updatedData.relationId" style="width: 400px" />
          </el-form-item>
          <br />
          <el-form-item label="模块背景色" prop="BgColor" required>
            <div style="width: 50px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.BgColor"
              />
            </div>
          </el-form-item>
          <br />
          <el-form-item label="报名文案颜色" prop="titleColor" required>
            <div style="width: 50px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.titleColor"
              />
            </div>
          </el-form-item>
          <el-form-item label="副标题文案颜色" prop="subtitleColor" required>
            <div style="width: 50px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.subtitleColor"
              />
            </div>
          </el-form-item>
          <br />
          <el-form-item label="输入框背景色" prop="inputBgColor" required>
            <div style="width: 50px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.inputBgColor"
              />
            </div>
          </el-form-item>
          <el-form-item label="输入文案颜色" prop="inputColor" required>
            <div style="width: 50px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.inputColor"
              />
            </div>
          </el-form-item>
        </el-form>
        <div class="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirmation">确 定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  CircleClose as IconCircleClose,
  CirclePlus as IconCirclePlusOutline
} from '@element-plus/icons-vue'
import { $emit } from '../../../../../utils/gogocodeTransfer'
import { deepCopy } from '@/utils'
import { predefineColors } from './config'
export default {
  components: {
    IconCircleClose,
    IconCirclePlusOutline
  },
  name: 'ImgConfig',
  props: {
    configData: {
      typeof: Object,
      default: {}
    },
    index: {
      typeof: Number,
      default: 0
    }
  },
  data() {
    let bgEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('颜色必填'))
      }
      callback()
    }
    return {
      updatedData: {},
      dialog: false,
      predefineColors,
      rules: {
        BgColor: [{ validator: bgEnter, trigger: 'change' }],
        titleColor: [{ validator: bgEnter, trigger: 'change' }],
        subtitleColor: [{ validator: bgEnter, trigger: 'change' }],
        inputBgColor: [{ validator: bgEnter, trigger: 'change' }],
        inputColor: [{ validator: bgEnter, trigger: 'change' }]
      }
    }
  },
  computed: {
    daterange: {
      get() {
        if (this.updatedData.beginTime && this.updatedData.endTime) {
          return [this.updatedData.beginTime, this.updatedData.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.updatedData.beginTime = value[0]
          this.updatedData.endTime = value[1]
        } else {
          this.updatedData.beginTime = ''
          this.updatedData.endTime = ''
        }
      }
    }
  },
  mounted() {},
  methods: {
    showDrawer() {
      this.updatedData = deepCopy(this.configData)
      this.dialog = true
    },
    handleClose() {
      this.dialog = false
    },
    confirmation() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          $emit(this, 'updatedData', this.updatedData, this.index)
          this.dialog = false
        }
      })
    },
    copy() {
      $emit(this, 'copyData', this.configData, this.index)
    },
    delItem() {
      $emit(this, 'delItem', this.index)
    }
  },
  emits: ['updatedData', 'copyData', 'delItem']
}
</script>

<style lang="scss" scoped>
.countdown-config {
  margin-top: 10px;
  .header {
    background-color: #f8f8f8;
    border-radius: 3px;
    overflow: hidden;
    .tabs {
      height: 30px;
      background-color: #f1f1f1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      .copy {
        color: #462be2;
        cursor: pointer;
      }
      .delete {
        font-size: 20px;
        cursor: pointer;
      }
    }
    .countdown-content {
      width: 100%;
      padding: 15px;
      .countdown-title {
        font-size: 15px;
        line-height: 21px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        .time {
          padding: 2px 4px;
          border-radius: 5px;
          margin: 0 6px;
        }
      }
      .countdown-subtitle {
        margin-top: 10px;
        font-size: 12px;
        line-height: 16px;
        text-align: center;
      }
    }
    .add-config {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 150px;
      i {
        font-size: 80px;
      }
    }
  }
  .drawer-box {
    position: relative;
    height: 100%;
    .footer {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
