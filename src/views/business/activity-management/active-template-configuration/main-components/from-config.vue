<template>
  <div class="from-config">
    <div class="header" @click.stop="showDrawer">
      <div class="tabs">
        <div>表单</div>
        <el-icon class="delete" @click.stop="delItem"
          ><IconCircleClose
        /></el-icon>
      </div>
      <div
        v-if="configData.triggerShowFrom && configData.sginUpConfigTip"
        :style="{ backgroundColor: configData.colorBgColor }"
        class="show-from-bg"
      >
        <template
          v-for="(name, index) in configData.sginUpConfigName"
          :key="index"
        >
          <div v-if="name !== '隐私协议'" class="show-from-input-content">
            <span :style="{ color: configData.colorTitleColor }">
              {{ name }}
            </span>
            <div
              class="show-from-input-content_input"
              :style="{ backgroundColor: configData.colorInputBgColor }"
            >
              <input type="text" :placeholder="`请输入${name}`" />
              <div
                class="mobile-logic"
                v-if="name === '手机号' && configData.mobileLogic === 2"
                :style="{ color: configData.changeMobileColor }"
              >
                换绑手机号
              </div>
            </div>
          </div>
        </template>
        <div
          class="show-from-input-button"
          v-if="configData.sginUpConfigTip"
          :style="{
            backgroundColor: configData.colorNoSignButtonBgColor,
            color: configData.colorNoSignButtonTitleColor
          }"
        >
          {{ configData.sginUpConfigTip }}
        </div>
      </div>
      <div v-else-if="configData.triggerShowFromImgUrl">
        <img :src="configData.triggerShowFromImgUrl" class="show-from-img" />
      </div>
      <div v-else class="add-config">
        <el-icon><IconCirclePlusOutline /></el-icon>
      </div>
    </div>
    <el-drawer
      title="表单模块配置"
      :before-close="handleClose"
      v-model="dialog"
      direction="rtl"
      ref="drawer"
      size="50%"
    >
      <div class="drawer-box">
        <el-form :model="updatedData" :rules="rules" ref="ruleForm">
          <el-form-item label="有效时间段">
            <div>
              <el-date-picker
                :default-time="
                  ['00:00:00', '23:59:59'].map((d) =>
                    $dayjs(d, 'hh:mm:ss').toDate()
                  )
                "
                v-model="daterange"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="有效开始日期"
                end-placeholder="有效结束日期"
                style="width: 400px"
              />
            </div>
          </el-form-item>
          <el-form-item label="关联活动ID">
            <el-input v-model="updatedData.relationId" />
          </el-form-item>
          <div class="title">触发配置</div>
          <el-form-item label="配置的字段" required>
            <el-radio-group v-model="updatedData.triggerShowFrom">
              <el-radio
                v-for="(show, index) in showList"
                :label="show"
                :key="show"
                >{{ index }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="!updatedData.triggerShowFrom"
            label="添加图片："
            prop="triggerShowFromImgUrl"
          >
            <el-input
              v-model="updatedData.triggerShowFromImgUrl"
              placeholder="请选择图片"
            />
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest"
              :on-success="onSuccessUrl"
              name="titlefile"
              style="display: inline-block"
              class="avatar-uploader"
              action
            >
              <el-button type="primary" link>选择图片</el-button>
            </el-upload>
          </el-form-item>
          <div class="title">报名配置</div>
          <el-form-item label="需配置字段" prop="sginUpConfigName">
            <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(name, index) in configNameList"
                :label="name"
                :key="index"
              />
            </el-checkbox-group>
          </el-form-item>
          <el-form-item
            v-if="checkList.includes('手机号')"
            label="手机号逻辑"
            prop="mobileLogic"
            required
          >
            <el-radio-group v-model="updatedData.mobileLogic">
              <el-radio :label="1">取询价手机号逻辑(验证码修改)</el-radio>
              <el-radio :label="2">换绑逻辑</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="未报名按钮文案" prop="sginUpConfigTip">
            <el-input
              v-model="updatedData.sginUpConfigTip"
              maxlength="20"
              placeholder="请输入未报名按钮文案，最多20个字"
            />
          </el-form-item>
          <div class="title">报名后配置</div>
          <el-form-item label="报名后标题文案" prop="sginUpBeforTitle">
            <el-input
              v-model="updatedData.sginUpBeforTitle"
              maxlength="20"
              placeholder="请输入报名后标题文案，最多20个字"
            />
          </el-form-item>
          <el-form-item label="报名后按钮文案" prop="sginUpBeforButtonTitle">
            <el-input
              v-model="updatedData.sginUpBeforButtonTitle"
              maxlength="20"
              placeholder="请输入报名后按钮文案，最多20个字"
            />
          </el-form-item>
          <el-form-item label="配置按钮变更">
            <el-radio-group v-model="updatedData.sginUpbeforUpdateStatus">
              <el-radio
                v-for="(show, index) in statusList"
                :label="show"
                :key="show"
                >{{ index }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="updatedData.sginUpbeforUpdateStatus"
            label="按钮变更时间"
            prop="sginUpBeforUpdateTime"
          >
            <el-date-picker
              v-model="updatedData.sginUpBeforUpdateTime"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择按钮变更时间"
              :default-time="$dayjs('00:00:00', 'hh:mm:ss').toDate()"
            />
          </el-form-item>
          <el-form-item
            v-if="updatedData.sginUpbeforUpdateStatus"
            label="跳转链接"
          >
            <el-input
              v-model="updatedData.sginUpBeforLinkUrl"
              placeholder="请输入跳转链接"
            />
          </el-form-item>
          <el-form-item
            v-if="updatedData.sginUpbeforUpdateStatus"
            label="按钮变更文案"
            prop="sginUpbeforUpdateButtonTitle"
          >
            <el-input
              v-model="updatedData.sginUpbeforUpdateButtonTitle"
              maxlength="20"
              placeholder="请输入按钮变更文案，最多20个字"
            />
          </el-form-item>
          <div class="title">成功弹框配置</div>
          <el-form-item label="报名后弹窗配置">
            <el-radio-group v-model="updatedData.successTipConfigStatus">
              <el-radio
                v-for="(show, index) in statusList"
                :label="show"
                :key="show"
                >{{ index }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="updatedData.successTipConfigStatus"
            label="弹框图片："
            prop="successTipConfigImgUrl"
          >
            <el-input
              v-model="updatedData.successTipConfigImgUrl"
              placeholder="请选择图片"
            />
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest"
              :on-success="onSuccessImgUrl"
              name="titlefile"
              style="display: inline-block"
              class="avatar-uploader"
              action
            >
              <el-button type="primary" link>选择图片</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item
            v-if="updatedData.successTipConfigStatus"
            label="跳转链接"
          >
            <el-input
              v-model="updatedData.successTipConfigLinkUrl"
              placeholder="请输入跳转链接"
            />
          </el-form-item>
          <div class="title">颜色配置</div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="背景颜色：" prop="colorBgColor" required>
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.colorBgColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="字段标题颜色："
                prop="colorTitleColor"
                required
              >
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.colorTitleColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item
                label="报名后标题颜色："
                prop="colorSignUpScuccessTitleColor"
                required
              >
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.colorSignUpScuccessTitleColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="输入框字体颜色："
                prop="colorInputColor"
                required
              >
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.colorInputColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item
            label="输入框背景色："
            prop="colorInputBgColor"
            required
          >
            <el-color-picker
              :predefine="predefineColors"
              v-model="updatedData.colorInputBgColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
          <p class="title">未报名</p>
          <el-row :gutter="24">
            <el-col
              :span="
                checkList.includes('手机号') && updatedData.mobileLogic === 2
                  ? 8
                  : 12
              "
            >
              <el-form-item
                label="按钮背景色："
                prop="colorNoSignButtonBgColor"
                required
              >
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.colorNoSignButtonBgColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
            <el-col
              :span="
                checkList.includes('手机号') && updatedData.mobileLogic === 2
                  ? 8
                  : 12
              "
            >
              <el-form-item
                label="按钮文案颜色："
                prop="colorNoSignButtonTitleColor"
                required
              >
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.colorNoSignButtonTitleColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
            <el-col
              v-if="
                checkList.includes('手机号') && updatedData.mobileLogic === 2
              "
              :span="8"
            >
              <el-form-item
                label="换绑手机号文案颜色："
                prop="changeMobileColor"
                required
              >
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.changeMobileColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <p class="title">已报名</p>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item
                label="按钮背景色："
                prop="colorSignButtonBgColor"
                required
              >
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.colorSignButtonBgColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="按钮文案颜色："
                prop="colorSignButtonTitleColor"
                required
              >
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.colorSignButtonTitleColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <p v-if="updatedData.successTipConfigStatus" class="title">
            变更状态
          </p>
          <el-row :gutter="24" v-if="updatedData.successTipConfigStatus">
            <el-col :span="12">
              <el-form-item
                label="按钮背景色："
                prop="colorUpdateButtonBgColor"
                required
              >
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.colorUpdateButtonBgColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="按钮文案颜色："
                prop="colorUpdateButtonTitleColor"
                required
              >
                <el-color-picker
                  :predefine="predefineColors"
                  v-model="updatedData.colorUpdateButtonTitleColor"
                  style="width: 250px"
                ></el-color-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirmation">确 定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  CircleClose as IconCircleClose,
  CirclePlus as IconCirclePlusOutline
} from '@element-plus/icons-vue'
import { $emit } from '../../../../../utils/gogocodeTransfer'
import { deepCopy } from '@/utils'
import { predefineColors } from './config'
export default {
  components: {
    IconCircleClose,
    IconCirclePlusOutline
  },
  name: 'ImgConfig',
  props: {
    configData: {
      typeof: Object,
      default: {}
    },
    index: {
      typeof: Number,
      default: 0
    }
  },
  data() {
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('图片必须上传'))
      }
      callback()
    }
    let bgEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('颜色必填'))
      }
      callback()
    }
    const dateEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('时间必填'))
      }
      callback()
    }
    return {
      updatedData: {},
      dialog: false,
      predefineColors,
      showList: {
        直接展示表单: true,
        点击触发表单: false
      }, // 触发配置
      configNameList: ['姓名', '手机号', '隐私协议'],
      checkList: [],
      statusList: {
        配置: true,
        不配置: false
      },
      rules: {
        triggerShowFromImgUrl: [
          { required: true, validator: imgEnter, trigger: 'change' }
        ],
        // sginUpConfigName: [
        //   {
        //     type: 'array',
        //     required: true,
        //     message: '请至少选择一个需配置字段',
        //     trigger: 'change'
        //   }
        // ],
        sginUpConfigTip: [
          { required: true, message: '请填写未报名按钮文案', trigger: 'blur' }
        ],
        sginUpBeforTitle: [
          { required: true, message: '请填写报名后标题文案', trigger: 'blur' }
        ],
        sginUpBeforButtonTitle: [
          { required: true, message: '请填写报名后按钮文案', trigger: 'blur' }
        ],
        sginUpBeforUpdateTime: [
          {
            type: 'date',
            required: true,
            validator: dateEnter,
            trigger: 'change'
          }
        ],
        sginUpbeforUpdateButtonTitle: [
          { required: true, message: '请填写按钮变更文案', trigger: 'blur' }
        ],
        successTipConfigImgUrl: [
          { required: true, validator: imgEnter, trigger: 'change' }
        ],
        colorBgColor: [{ validator: bgEnter, trigger: 'change' }],
        colorTitleColor: [{ validator: bgEnter, trigger: 'change' }],
        colorSignUpScuccessTitleColor: [
          { validator: bgEnter, trigger: 'change' }
        ],
        colorInputColor: [{ validator: bgEnter, trigger: 'change' }],
        colorInputBgColor: [{ validator: bgEnter, trigger: 'change' }],
        colorNoSignButtonBgColor: [{ validator: bgEnter, trigger: 'change' }],
        colorNoSignButtonTitleColor: [
          { validator: bgEnter, trigger: 'change' }
        ],
        changeMobileColor: [{ validator: bgEnter, trigger: 'change' }],
        colorSignButtonBgColor: [{ validator: bgEnter, trigger: 'change' }],
        colorSignButtonTitleColor: [{ validator: bgEnter, trigger: 'change' }],
        colorUpdateButtonBgColor: [{ validator: bgEnter, trigger: 'change' }],
        colorUpdateButtonTitleColor: [{ validator: bgEnter, trigger: 'change' }]
      }
    }
  },
  computed: {
    daterange: {
      get() {
        if (this.updatedData.beginTime && this.updatedData.endTime) {
          return [this.updatedData.beginTime, this.updatedData.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.updatedData.beginTime = value[0]
          this.updatedData.endTime = value[1]
        } else {
          this.updatedData.beginTime = ''
          this.updatedData.endTime = ''
        }
      }
    }
  },
  methods: {
    showDrawer() {
      this.updatedData = deepCopy(this.configData)
      this.checkList = this.updatedData.sginUpConfigName
      this.dialog = true
    },
    handleClose() {
      this.dialog = false
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 图片返回
    onSuccessUrl(res) {
      if (!res) return
      if (res.name) {
        this.updatedData.triggerShowFromImgUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 成功后图片
    onSuccessImgUrl(res) {
      if (!res) return
      if (res.name) {
        this.updatedData.successTipConfigImgUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    confirmation() {
      this.updatedData.sginUpConfigName = this.checkList
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          $emit(this, 'updatedData', this.updatedData, this.index)
          this.dialog = false
        } else {
          this.$message.error('还有必填信息未填写')
        }
      })
    },
    delItem() {
      $emit(this, 'delItem', this.index)
    }
  },
  emits: ['updatedData', 'delItem']
}
</script>

<style lang="scss" scoped>
.from-config {
  margin-top: 10px;
  .header {
    background-color: #f8f8f8;
    border-radius: 3px;
    overflow: hidden;
    .tabs {
      height: 30px;
      background-color: #f1f1f1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      .delete {
        font-size: 20px;
        cursor: pointer;
      }
    }
    .img-box {
      img {
        width: 100%;
        display: block;
      }
    }
    .add-config {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 150px;
      i {
        font-size: 80px;
      }
    }
  }
  .show-from-bg {
    width: 100%;
    border-radius: 6px;
    padding: 0.2rem;
    .show-from-title {
      font-size: 21px;
      color: #a4f7f3;
      line-height: 29px;
      text-align: center;
      margin-bottom: 25px;
    }
    .show-from-input-content {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      span {
        line-height: 40px;
        font-size: 13px;
        width: 40px;
        margin-right: 5px;
      }
      .show-from-input-content_input {
        flex: 1;
        padding: 0 15px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        input {
          flex: 1;
          border: none;
          height: 40px;
          outline: none;
        }
        .mobile-logic {
          line-height: 40px;
          font-size: 13px;
          margin-left: 10px;
        }
      }
    }
    .show-from-input-button {
      width: 100%;
      height: 44px;
      line-height: 44px;
      text-align: center;
      border-radius: 22px;
      font-size: 15px;
      color: #000;
    }
  }
  .show-from-img {
    width: 100%;
  }
  .drawer-box {
    .title {
      margin: 5px 0;
      font-weight: 600;
    }
    .footer {
      text-align: right;
    }
  }
}
</style>
