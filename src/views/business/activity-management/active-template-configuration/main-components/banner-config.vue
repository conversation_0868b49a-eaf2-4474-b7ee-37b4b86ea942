<template>
  <div class="banner-config">
    <div class="header" @click.stop="showDrawer">
      <div class="tabs">
        <div>轮播 | <span class="copy" @click.stop="copy">复制</span></div>
        <el-icon class="delete" @click.stop="delItem"
          ><IconCircleClose
        /></el-icon>
      </div>
      <div
        v-if="
          configData.displayMode === '1' && configData.displayContent.length
        "
        class="carousel-box"
      >
        <div class="img-box box-height">
          <img :src="configData.displayContent[showIndex].imgUrl" alt="" />
        </div>
        <div class="scroll-box">
          <div
            v-for="(item, index) in configData.displayContent"
            :key="index"
            :class="['scroll-item', { activate: showIndex === index }]"
            @click.stop="showIndex = index"
          >
            <img :src="item.imgUrl" alt="" />
          </div>
        </div>
      </div>
      <div
        v-else-if="
          configData.displayMode === '2' && configData.displayContent.length
        "
        class="carousel-box"
      >
        <el-carousel trigger="click" height="194px">
          <el-carousel-item
            v-for="(item, index) in configData.displayContent"
            :key="index"
          >
            <div class="img-box">
              <img :src="item.imgUrl" alt="" />
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div v-else class="add-config">
        <el-icon><IconCirclePlusOutline /></el-icon>
      </div>
    </div>
    <el-drawer
      title="banner配置"
      :before-close="handleClose"
      v-model="dialog"
      direction="rtl"
      ref="drawer"
      size="45%"
    >
      <div class="drawer-box">
        <div class="banner-title">有效时间段</div>
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            v-model="daterange"
            style="width: 400px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="有效开始日期"
            end-placeholder="有效结束日期"
          />
        <div class="radio-box">
        </div>
        <div class="banner-title">类型展示</div>
        <div class="radio-box">
          <el-radio-group v-model="updatedData.displayMode" @change="initData">
            <el-radio :label="'1'">小图模式</el-radio>
            <el-radio :label="'2'">轮播模式</el-radio>
          </el-radio-group>
        </div>
        <div class="banner-title">
          配置内容
          <el-button type="primary" size="small" @click="append"
            >新增</el-button
          >
        </div>
        <!-- <el-table :data="updatedData.displayContent" stripe style="width: 100%" @row-dblclick="showdialog">
                        <el-table-column prop="imgUrl" label="封面" align="center" width="100">
                          <template v-slot="scope">
                            <img :src="scope.row.imgUrl" alt="" class="row-imgUrl" />
                          </template>
                        </el-table-column>
                        <el-table-column prop="type" label="类型" align="center" width="100">
                          <template v-slot="scope">
                            <span>{{ scope.row.type === '1' ? '直播' : '内容' }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="linkUrl" label="跳转链接" align="center"> </el-table-column>
                        <el-table-column label="操作" align="center">
                          <template v-slot="scope">
                            <el-button type="primary" link @click="rowCopy(scope.row, scope.$index)">复制</el-button>
                            <el-button type="primary" link @click="rowDel(scope.$index)">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table> -->
        <div class="banner-conent">
          <div class="column">
            <span>名称</span>
            <span>封面</span>
            <span>类型</span>
            <span>跳转链接</span>
            <span>操作</span>
          </div>
          <draggable
            class="left-content"
            item-key="name"
            v-model="updatedData.displayContent"
            @end="refreshImageList"
          >
            <template #item="{ element, index }">
              <div class="column" @dblclick="showdialog(element)">
                <span>{{ element.name }}</span>
                <span>
                  <img :src="element.imgUrl" alt="" class="row-imgUrl" />
                </span>
                <span>{{ element.type === '1' ? '直播' : '内容' }}</span>
                <span>{{ element.linkUrl }}</span>
                <span
                  ><el-button
                    type="primary"
                    link
                    @click="rowCopy(element, index)"
                    >复制</el-button
                  >
                  <el-button type="primary" link @click="rowDel(index)"
                    >删除</el-button
                  >
                </span>
              </div>
            </template>
          </draggable>
        </div>
        <div class="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirmation">确 定</el-button>
        </div>
      </div>
    </el-drawer>
    <el-dialog
      :title="addData.isNewAdd ? '新增' : '编辑'"
      v-model="dialogVisible"
      width="580px"
    >
      <el-form :model="addData">
        <el-form-item label="名称" label-width="110px">
          <el-input
            v-model="addData.name"
            clearable
            style="width: 350px"
            placeholder="不必填"
          />
        </el-form-item>
        <el-form-item
          v-if="updatedData.displayMode === '2'"
          label="类型"
          required
          label-width="110px"
        >
          <el-radio-group v-model="addData.type">
            <el-radio :label="'1'">直播</el-radio>
            <el-radio :label="'2'">内容</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="添加图片" required label-width="110px">
          <el-input
            v-model="addData.imgUrl"
            clearable
            style="width: 350px"
            placeholder="请选择图片"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccessUrl"
            name="titlefile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            <el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item
          v-if="addData.type === '1'"
          label="直播配置"
          label-width="110px"
        >
          <el-select @change="setJumpUrl" v-model="chatId">
            <el-option
              v-for="(value, index) in liveList"
              :key="index"
              :label="value.title"
              :value="value.chatId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配置跳转链接" required label-width="110px">
          <el-input
            v-model="addData.linkUrl"
            clearable
            style="width: 350px"
            placeholder="请输入配置跳转链接"
            @change="chatId = ''"
          />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="determine">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  CircleClose as IconCircleClose,
  CirclePlus as IconCirclePlusOutline
} from '@element-plus/icons-vue'
import { $emit } from '../../../../../utils/gogocodeTransfer'
import { deepCopy } from '@/utils'
import draggable from 'vuedraggable'
import { getLiveCountryList } from '@/api/user'
export default {
  components: {
    draggable,
    IconCircleClose,
    IconCirclePlusOutline
  },
  name: 'BannerConfig',
  props: {
    configData: {
      typeof: Object,
      default: {}
    },
    index: {
      typeof: Number,
      default: 0
    }
  },
  data() {
    return {
      updatedData: {},
      dialog: false,
      dialogVisible: false,
      addData: {
        type: '',
        imgUrl: '', // 图片地址
        linkUrl: '', // 跳转链接
        isNewAdd: true, // 新增
        createDate: '' // 创建时间
      },
      showIndex: 0,
      liveList: [],
      chatId: ''
    }
  },
  computed: {
    daterange: {
      get() {
        if (this.updatedData.beginTime && this.updatedData.endTime) {
          return [this.updatedData.beginTime, this.updatedData.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.updatedData.beginTime = value[0]
          this.updatedData.endTime = value[1]
        } else {
          this.updatedData.beginTime = ''
          this.updatedData.endTime = ''
        }
      }
    }
  },
  mounted() {},
  methods: {
    // 获取直播信息
    getLiveCountryList() {
      getLiveCountryList({
        page: 1,
        limit: 100
      }).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data || {}
          this.liveList = data.listData || []
        }
      })
    },
    // 设置直播url
    setJumpUrl() {
      this.liveList.length > 0 &&
        this.liveList.map((item) => {
          if (item.chatId === this.chatId) {
            this.addData.linkUrl = item.liveAddress
          }
        })
    },
    showDrawer() {
      this.updatedData = deepCopy(this.configData)
      this.dialog = true
    },
    copy() {
      $emit(this, 'copyData', this.configData, this.index)
    },
    delItem() {
      $emit(this, 'delItem', this.index)
    },
    handleClose() {
      this.dialog = false
    },
    confirmation() {
      $emit(this, 'updatedData', this.updatedData, this.index)
      this.dialog = false
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 图片返回
    onSuccessUrl(res) {
      if (!res) return
      if (res.name) {
        this.addData.imgUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    showdialog(data) {
      this.addData = deepCopy(data)
      this.chatId = ''
      this.getLiveCountryList()
      this.dialogVisible = true
    },
    append() {
      if (
        this.updatedData.displayMode === '1' &&
        this.updatedData.displayContent.length >= 2
      ) {
        return this.$message.error('小图模式下最多配两条数据')
      }
      this.addData = {
        type: this.updatedData.displayMode === '1' ? '1' : '',
        name: '', // 名称
        imgUrl: '', // 图片地址
        linkUrl: '', // 跳转链接
        isNewAdd: true, // 新增
        createDate: '' // 创建时间
      }
      this.chatId = ''
      this.getLiveCountryList()
      this.dialogVisible = true
    },
    determine() {
      if (!this.addData.type) {
        return this.$message.error('请选择类型')
      }
      if (!this.addData.imgUrl) {
        return this.$message.error('请选择图片')
      }
      if (!this.addData.linkUrl) {
        return this.$message.error('请输入跳转地址')
      }
      if (this.addData.isNewAdd) {
        this.addData.isNewAdd = false
        this.addData.createDate = Math.floor(new Date())
        this.updatedData.displayContent.push(this.addData)
      } else {
        const findIndex = this.updatedData.displayContent.findIndex((item) => {
          return item.createDate === this.addData.createDate
        })
        this.updatedData.displayContent.splice(findIndex, 1, this.addData)
      }
      this.dialogVisible = false
    },
    initData() {
      this.updatedData.displayContent = []
    },
    rowCopy(data, index) {
      if (
        this.updatedData.displayMode === '1' &&
        this.updatedData.displayContent.length >= 2
      ) {
        return this.$message.error('小图模式下最多配两条数据')
      }
      const newData = deepCopy(data)
      newData.createDate = Math.floor(new Date())
      console.log(`index`, index)
      // this.updatedData.displayContent.splice(index + 1, 0, newData)
      this.updatedData.displayContent.push(newData)
    },
    rowDel(index) {
      this.updatedData.displayContent.splice(index, 1)
      this.$message.success('删除成功')
    },
    refreshImageList() {
      // console.log(`updatedData.displayContent`, this.updatedData.displayContent)
    }
  },
  emits: ['copyData', 'delItem', 'updatedData']
}
</script>

<style lang="scss" scoped>
.banner-config {
  margin-top: 10px;
  .header {
    background-color: #f8f8f8;
    border-radius: 3px;
    overflow: hidden;
    .tabs {
      height: 30px;
      background-color: #f1f1f1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      .copy {
        color: #462be2;
        cursor: pointer;
      }
      .delete {
        font-size: 20px;
        cursor: pointer;
      }
    }
    .carousel-box {
      margin: 0 15px;
      background-color: #313133;
      border-radius: 6px;
      .scroll-box {
        padding: 10px 0;
        white-space: nowrap;
        overflow-x: auto;
        font-size: 0;
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        &::-webkit-scrollbar-thumb {
          border-radius: 5px;
          background: #c9c9c9;
        }
        .scroll-item {
          display: inline-block;
          width: 138px;
          height: 78px;
          margin-right: 10px;
          border: 1px solid #feaf3800;
          border-radius: 6px;
          overflow: hidden;
          &:nth-child(1) {
            margin-left: 10px;
          }
          &.activate {
            border: 1px solid #feaf38;
          }
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .img-box {
        width: 100%;
        height: 100%;
        &.box-height {
          height: 194px;
        }
        img {
          display: block;
          width: 100%;
          height: 100%;
          border-radius: 6px;
        }
      }
    }
    .add-config {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 150px;
      i {
        font-size: 80px;
      }
    }
  }
  .drawer-box {
    position: relative;
    height: 100%;
    padding: 0 20px;
    .banner-title {
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .radio-box {
      margin-bottom: 20px;
    }
    .banner-conent {
      padding-bottom: 60px;
      .column {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        padding: 10px 0;
        border-bottom: 1px solid #dedede;
        span:nth-child(1) {
          width: 15%;
        }
        span:nth-child(2) {
          width: 20%;
        }
        span:nth-child(3) {
          width: 15%;
        }
        span:nth-child(4) {
          width: 35%;
          word-wrap: break-word;
          word-break: normal;
        }
        span:nth-child(5) {
          width: 15%;
        }
      }
    }
    .footer {
      position: fixed;
      bottom: 20px;
      right: 20px;
    }
  }
  .dialog-footer {
    text-align: center;
    padding-top: 20px;
  }
  .row-imgUrl {
    width: 100px;
  }
}
</style>
