<template>
  <div class="cartype-config">
    <div class="header" @click.stop="showDrawer">
      <div class="tabs">
        <div>车型 | <span class="copy" @click.stop="copy">复制</span></div>
        <el-icon class="delete" @click.stop="delItem"
          ><IconCircleClose
        /></el-icon>
      </div>
      <div v-if="shuffle.length" class="cartype-box">
        <div
          v-for="(item, index) in shuffle"
          :key="index"
          :class="[
            'cartype-item',
            {
              rowNum2: configData.rowNum === '2',
              rowNum3: configData.rowNum === '3'
            }
          ]"
          :style="{ backgroundColor: configData.bgColor || '#000' }"
        >
          <img
            :src="item.imgUrl"
            alt=""
            :class="[
              'cartype-item_img',
              {
                rowNum1_img: configData.rowNum === '1',
                rowNum2_img: configData.rowNum === '2',
                rowNum3_img: configData.rowNum === '3'
              }
            ]"
          />
          <div
            :class="[
              'cartype-item_content',
              {
                cartypeContentOne: configData.rowNum === '1',
                cartypeContentTwo: configData.rowNum === '2',
                cartypeContentThree: configData.rowNum === '3'
              }
            ]"
          >
            <h4
              class="dotdotdot1"
              :style="{ color: configData.nameColor || '#333' }"
            >
              {{ item.brandName }}
            </h4>
            <p
              class="dotdotdot1"
              :class="{ 'cartype-item_name': configData.rowNum === '2' }"
              :style="{ color: configData.nameColor || '#333' }"
            >
              {{ item.goodsName || item.carName }}
            </p>
            <p
              class="cartype-item_price"
              :style="{ color: configData.priceColor || '#D9001B' }"
            >
              这里是价格
            </p>
            <p
              :class="[
                'cartype-item_btn',
                {
                  cartypeItemBtnRight: ['1', '2'].includes(configData.rowNum)
                }
              ]"
              :style="{
                color: configData.buttonTextColor || '#fff',
                backgroundColor: configData.buttonBgColor || '#D9001B'
              }"
            >
              {{ item.buttonName }}
            </p>
          </div>
        </div>
      </div>
      <div v-else class="add-config">
        <el-icon><IconCirclePlusOutline /></el-icon>
      </div>
    </div>
    <el-drawer
      title="编辑车型"
      :before-close="handleClose"
      v-model="dialog"
      direction="rtl"
      ref="drawer"
      size="45%"
    >
      <div class="drawer-box">
        <el-form :model="updatedData" border>
          <el-form-item label="有效时间段" label-width="120px">
            <el-date-picker
              :default-time="
                ['00:00:00', '23:59:59'].map((d) =>
                  $dayjs(d, 'hh:mm:ss').toDate()
                )
              "
              v-model="daterange"
              style="width: 400px"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="有效开始日期"
              end-placeholder="有效结束日期"
            />
          </el-form-item>
          <el-form-item label="每行数量" required label-width="80px">
            <el-radio-group v-model="updatedData.rowNum">
              <el-radio :label="'1'">1</el-radio>
              <el-radio :label="'2'">2</el-radio>
              <el-radio :label="'3'">3</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="顺序" required label-width="60px">
            <el-radio-group v-model="updatedData.sortord">
              <el-radio :label="'1'">不打乱</el-radio>
              <el-radio :label="'2'">每次随机打乱</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="卡片背景色:" required label-width="100px">
            <el-color-picker
              :predefine="predefineColors"
              v-model="updatedData.bgColor"
              style="width: 250px"
            >
            </el-color-picker>
          </el-form-item>
          <el-row :gutter="24">
            <el-form-item label="卡片名称颜色:" label-width="120px" required>
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.nameColor"
                style="width: 250px"
              >
              </el-color-picker>
            </el-form-item>
            <el-form-item label="卡片金额颜色:" label-width="120px" required>
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.priceColor"
                style="width: 250px"
              >
              </el-color-picker>
            </el-form-item>
            <el-form-item label="其他文案颜色:" label-width="120px" required>
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.otherColor"
                style="width: 250px"
              >
              </el-color-picker>
            </el-form-item>
          </el-row>
          <el-row :gutter="24">
            <el-form-item label="卡片按钮颜色:" label-width="120px" required>
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.buttonBgColor"
                style="width: 250px"
              >
              </el-color-picker>
            </el-form-item>
            <el-form-item label="按钮文案颜色:" label-width="120px" required>
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.buttonTextColor"
                style="width: 250px"
              >
              </el-color-picker>
            </el-form-item>
          </el-row>
        </el-form>
        <div class="add-button">
          <el-button type="primary" @click="append">新增</el-button>
        </div>
        <div class="cartype-conent">
          <div class="column">
            <span>车/款型ID</span>
            <span>车/款型名称</span>
            <span>操作</span>
          </div>
          <draggable
            class="left-content"
            v-model="updatedData.carTypeList"
            @end="refreshImageList"
          >
            <template #item="{ element, index }">
              <div class="column" :key="index">
                <span>{{ element.carId || element.goodsId }}</span>
                <span>{{ element.carName || element.goodsName }}</span>
                <span
                  ><el-button
                    type="primary"
                    link
                    @click="rowEdit(element, index)"
                    >编辑</el-button
                  >
                  <el-button type="primary" link @click="rowDel(index)"
                    >删除</el-button
                  >
                </span>
              </div>
            </template>
          </draggable>
        </div>
        <div class="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirmation">确 定</el-button>
        </div>
      </div>
    </el-drawer>
    <el-dialog
      :title="`${addData.isNewAdd ? '新增' : '编辑'}车型`"
      v-model="dialogVisible"
      width="580px"
    >
      <el-form :model="addData">
        <el-form-item label="选择车型" required label-width="120px">
          <el-autocomplete
            v-model="addData.goodsName"
            :fetch-suggestions="querySearchAllCar"
            placeholder="请选择车型"
            style="width: 200px"
            clearable
            @select="handleSelectCar"
            @clear="clearGoodsName"
            @input="clearCardTypes"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="款型:" label-width="120px">
          <el-select
            v-model="addData.carName"
            placeholder="请选择款型"
            clearable
            @clear="clearCarName"
            @change="changeCarstyle"
          >
            <el-option
              v-for="item in cardStyleList"
              :key="item.carId"
              :label="item.goodsCarName || item.carName"
              :value="item.carId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车型图片" required label-width="110px">
          <el-input
            v-model="addData.imgUrl"
            clearable
            style="width: 350px"
            placeholder="请选择图片"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccessUrl"
            name="titlefile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            <el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="按钮名称" required label-width="120px">
          <el-input
            v-model="addData.buttonName"
            clearable
            style="width: 350px"
            placeholder="不必填"
          />
        </el-form-item>
        <el-form-item label="跳转链接" label-width="110px">
          <el-input
            v-model="addData.linkUrl"
            clearable
            style="width: 350px"
            placeholder="请输入配置跳转链接"
          />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="determine">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  CircleClose as IconCircleClose,
  CirclePlus as IconCirclePlusOutline
} from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { deepCopy } from '@/utils'
import { predefineColors } from './config'
import draggable from 'vuedraggable'
import { searchCarList, getAgeList, getOriginalUrl } from '@/api/garage'
export default {
  components: {
    draggable,
    IconCircleClose,
    IconCirclePlusOutline
  },
  name: 'CartypeConfigNew',
  props: {
    configData: {
      typeof: Object,
      default: {}
    },
    index: {
      typeof: Number,
      default: 0
    }
  },
  data() {
    return {
      predefineColors: predefineColors,
      updatedData: {},
      dialog: false,
      dialogVisible: false,
      addData: {
        carId: '', // 款型id
        carName: '', // 款型名称
        goodsId: '', // 车型id
        goodsName: '', // 车型名称
        imgUrl: '', // 车型图/款型图/自行编辑的图
        linkUrl: '', // 跳转链接
        buttonName: '查看详情', // 查看详情
        isNewAdd: true, // 新增
        createDate: '' // 创建时间
      },
      cardStyleList: [] // 款型数据
    }
  },
  computed: {
    shuffle() {
      let arr = [...this.configData.carTypeList]
      for (let i = arr.length - 1; i > -1; i--) {
        let j = Math.floor(Math.random() * i)
        ;[arr[j], arr[i]] = [arr[i], arr[j]]
      }
      return this.configData.sortord === '2' ? arr : this.configData.carTypeList
    },
    daterange: {
      get() {
        if (this.updatedData.beginTime && this.updatedData.endTime) {
          return [this.updatedData.beginTime, this.updatedData.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.updatedData.beginTime = value[0]
          this.updatedData.endTime = value[1]
        } else {
          this.updatedData.beginTime = ''
          this.updatedData.endTime = ''
        }
      }
    }
  },
  mounted() {},
  methods: {
    showDrawer() {
      this.updatedData = deepCopy(this.configData)
      this.dialog = true
    },
    copy() {
      $emit(this, 'copyData', this.configData, this.index)
    },
    delItem() {
      $emit(this, 'delItem', this.index)
    },
    handleClose() {
      this.dialog = false
    },
    confirmation() {
      if (!this.updatedData.rowNum) {
        return this.$message.error('请选择每行数量')
      }
      if (!this.updatedData.sortord) {
        return this.$message.error('请选择顺序')
      }
      $emit(this, 'updatedData', this.updatedData, this.index)
      this.dialog = false
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 图片返回
    onSuccessUrl(res) {
      if (!res) return
      if (res.name) {
        this.addData.imgUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    append() {
      this.cardStyleList = []
      this.addData = {
        carId: '', // 款型id
        carName: '', // 款型名称
        goodsId: '', // 车型id
        goodsName: '', // 车型名称
        imgUrl: '', // 车型图/款型图/自行编辑的图
        linkUrl: '', // 跳转链接
        buttonName: '查看详情', // 查看详情
        isNewAdd: true, // 新增
        createDate: '' // 创建时间
      }
      this.dialogVisible = true
    },
    determine() {
      if (!this.addData.goodsName) {
        return this.$message.error('请选择车型')
      }
      if (!this.addData.imgUrl) {
        return this.$message.error('请选择图片')
      }
      if (!this.addData.buttonName) {
        return this.$message.error('请输入按钮名称')
      }
      if (this.addData.isNewAdd) {
        this.addData.isNewAdd = false
        this.addData.createDate = Math.floor(new Date())
        this.updatedData.carTypeList.push(this.addData)
      } else {
        const findIndex = this.updatedData.carTypeList.findIndex((item) => {
          return item.createDate === this.addData.createDate
        })
        this.updatedData.carTypeList.splice(findIndex, 1, this.addData)
      }
      this.dialogVisible = false
    },
    rowEdit(data, index) {
      this.addData = deepCopy(data)
      // this.cardStyleList = [
      //   {
      //     carId: data.carId,
      //     carName: data.carName
      //   }
      // ]
      if (this.addData.goodsName) {
        this.querySearchAllcardStyle('')
      }
      this.dialogVisible = true
    },
    rowDel(index) {
      this.updatedData.carTypeList.splice(index, 1)
      this.$message.success('删除成功')
    },
    refreshImageList() {
      // console.log(`updatedData.carTypeList`, this.updatedData.carTypeList)
    },

    // 车型筛选
    querySearchAllCar(queryString, cb) {
      const requestParams = {
        page: 1,
        limit: 100,
        name: queryString,
        isOnStatus: 1
      }
      searchCarList(requestParams)
        .then((response) => {
          if (response.status === 200) {
            const userNameList = []
            const result = response.data.data.list
            result.map(function (value) {
              const newObj = {
                value: value.goodName,
                goodsId: value.goodId,
                goodPic: value.goodPic,
                brandName: value.brandName
              }
              userNameList.push(newObj)
            })
            cb(userNameList)
          }
        })
        .catch(() => {})
    },
    async handleSelectCar(item) {
      this.addData.goodsId = item.goodsId
      this.addData.brandName = item.brandName
      this.addData.goodsName = item.value
      this.addData.linkUrl = `https://m.58moto.com/garage/detail/${item.goodsId}`
      this.addData.imgUrl = await this.getOriginalUrlData(item.goodPic) || this.addData.imgUrl
      this.querySearchAllcardStyle(item.value)
    },
    clearCardTypes() {
      this.cardStyleList = []
      this.addData.carId = ''
      this.addData.carName = ''
    },
    // 款型筛选
    querySearchAllcardStyle(queryString) {
      const me = this
      const requestParams = {
        page: 1,
        limit: 100,
        goodsName: queryString || '',
        goodsId: me.addData.goodsId,
      }
      getAgeList(requestParams)
        .then((response) => {
          if (response.status === 200) {
            me.cardStyleList = response.data.data.list
          }
        })
        .catch(() => {})
    },
    // // 款型选择
    async changeCarstyle(e) {
      const newData = this.cardStyleList.filter((_) => _.carId === e)
      this.addData.carName = newData[0].goodsCarName
      this.addData.imgUrl = await this.getOriginalUrlData(newData[0].goodsThumb) || this.addData.imgUrl
      this.addData.carId = e.toString()
    },
    // 清理车型
    clearGoodsName() {
      this.addData = {
        ...this.addData,
        goodsId: '',
        goodsName: '',
        imgUrl: '',
        linkUrl: ''
      }
      this.clearCarName()
    },
    // 清理款型
    clearCarName() {
      this.addData = {
        ...this.addData,
        carId: '',
        carName: ''
      }
      this.cardStyleList = []
    },
    getOriginalUrlData(url) {
      return getOriginalUrl({
        url
      }).then((response) => {
          const data = response.data.data || ''
          return data || url
        })
        .catch(() => {
          return url
        })
    }
  },
  emits: ['copyData', 'delItem', 'updatedData']
}
</script>

<style lang="scss" scoped>
.cartype-config {
  margin-top: 10px;
  .header {
    background-color: #f8f8f8;
    border-radius: 3px;
    overflow: hidden;
    .tabs {
      height: 30px;
      background-color: #f1f1f1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      .copy {
        color: #462be2;
        cursor: pointer;
      }
      .delete {
        font-size: 20px;
        cursor: pointer;
      }
    }
    .cartype-box {
      padding: 10px 15px 0 15px;
      display: flex;
      flex-wrap: wrap;
      .cartype-item {
        margin-bottom: 10px;
        display: flex;
        width: 100%;
        &.rowNum2 {
          width: calc(100% / 2 - 5px);
          display: block;
          &:nth-child(2n) {
            margin-left: 10px;
          }
        }
        &.rowNum3 {
          width: calc(100% / 3 - 7px);
          display: block;
          &:nth-child(2n) {
            margin-left: 10px;
          }
          &:nth-child(3n) {
            margin-left: 10px;
          }
          .cartype-item_price {
            text-align: center !important;
          }
        }
        .cartype-item_img {
          display: block;
          width: 122px;
          height: 105px;
          width: 30%;
          border-radius: 6px;
        }
        .rowNum1_img {
          min-width:  128px;
          width: 128px;
          object-fit: cover;
        }
        .rowNum2_img {
          width: 100%;
        }
        .rowNum3_img {
          width: 100%;
        }
        .cartype-item_content {
          position: relative;
          h4 {
            margin: 5px 0;
            text-align: center;
          }
          p {
            margin: 0;
            text-align: center;
          }

          .cartype-item_name {
            margin-bottom: 10px;
          }
          .cartype-item_price {
            font-size: 12px;
            margin: 4px 0;
            text-align: left;
          }
        }
        .cartypeContentOne {
          margin-left: 10px;
          width: 100%;
          overflow: hidden;
          h4 {
            text-align: left;
          }
          p {
            text-align: left;
          }
          .cartype-item_price {
            position: absolute;
            bottom: 0;
          }
          .cartype-item_btn {
            bottom: 2px;
            text-align: left;
          }
        }

      .cartypeContentTwo{ 
        .cartype-item_title {
          font-size: 13px;
          line-height: 18px;
        }
        .cartype-item_name {
          font-size: 13px;
          line-height: 18px;
        }
        .cartype-item_btn {
          height: 22px;
          font-size: 11px;
          bottom: -2px;
          line-height: 16px;
          padding: 3px 11px;
        }
        .cartype-item_price {
          font-family: PingFangSC-Semibold, PingFang SC;
          .cartype-item_price_small{
            top: -4px;
          }
        }
      }
      .cartypeContentThree {
        margin: 5px 7px 12px 7px;
        .cartype-item_title {
          margin-bottom: 5px;
        }
        .cartype-item_price{
          margin: 6px 0 4px;
          text-align: center;
        }
        .cartype-item_name {
          margin-bottom: 8px;
        }
        .cartype-item_btn {
          height: 18px;
          line-height: 14px;
          font-size: 10px;
        }
      }
        .cartype-item_btn {
          background-color: #ff4d23;
          border-radius: 20px;
          padding: 2px 0;
          color: #fff;
        }
        .cartypeItemBtnRight {
          position: absolute;
          bottom: 0;
          right: 0;
          padding: 2px 5px;
        }
      }
    }
    .add-config {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 150px;
      i {
        font-size: 80px;
      }
    }
  }
  .drawer-box {
    position: relative;
    height: 100%;
    padding: 0 20px;
    .add-button {
      text-align: right;
    }
    .cartype-conent {
      padding-bottom: 60px;
      .column {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        padding: 10px 0;
        align-items: center;
        text-align: center;
        border-bottom: 1px solid #dedede;
        span:nth-child(1) {
          width: 20%;
        }
        span:nth-child(2) {
          width: 60%;
        }
        span:nth-child(3) {
          width: 20%;
        }
      }
    }
    .footer {
      position: fixed;
      bottom: 20px;
      right: 20px;
    }
  }
  .dialog-footer {
    text-align: center;
    padding-top: 20px;
  }
  .row-imgUrl {
    width: 100px;
  }
}
</style>
