<template>
  <div class="circle-config">
    <div class="header" @click.stop="showDrawer">
      <div class="tabs">
        <div>摩友圈 | <span class="copy" @click.stop="copy">复制</span></div>
        <el-icon class="delete" @click.stop="delItem"
          ><IconCircleClose
        /></el-icon>
      </div>
      <div
        v-if="configData.circleList.length"
        class="circle-list"
        :style="{ backgroundColor: configData.bgColor || '#333' }"
      >
        <div
          v-for="(item, index) in configData.circleList"
          :key="index"
          class="circle-item"
          :style="{ borderColor: configData.lineColor || '#666' }"
        >
          <span :style="{ color: configData.serialColor || '#ccc' }">{{
            index + 1
          }}</span>
          <span :style="{ color: configData.fontColor || '#fff' }">{{
            item.circleName
          }}</span>
        </div>
      </div>
      <div v-else class="add-config">
        <el-icon><IconCirclePlusOutline /></el-icon>
      </div>
    </div>
    <el-drawer
      title="编辑摩友圈"
      :before-close="handleClose"
      v-model="dialog"
      direction="rtl"
      ref="drawer"
      size="45%"
    >
      <div class="drawer-box">
        <p>有效时间段 <br /><br />
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            v-model="daterange"
            style="width: 400px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="有效开始日期"
            end-placeholder="有效结束日期"
          />
        </p>
        <div class="add-button">
          <el-button type="primary" size="small" @click="append"
            >新增</el-button
          >
        </div>
        <!-- <el-table :data="updatedData.circleList" stripe style="width: 100%" @row-dblclick="showdialog">
                        <el-table-column prop="circleId" label="摩友圈ID" align="center" width="100"></el-table-column>
                        <el-table-column prop="circleName" label="摩友圈名称" align="center"> </el-table-column>
                        <el-table-column label="操作" align="center">
                          <template v-slot="scope">
                            <el-button type="primary" link @click="rowCopy(scope.row, scope.$index)">复制</el-button>
                            <el-button type="primary" link @click="rowDel(scope.$index)">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table> -->
        <div class="circle-conent">
          <div class="column">
            <span>摩友圈ID</span>
            <span>摩友圈名称</span>
            <span>操作</span>
          </div>
          <draggable
            class="left-content"
            v-model="updatedData.circleList"
            item-key="circleId"
            @end="refreshImageList"
          >
            <template #item="{ element, index }">
              <div class="column" @dblclick="showdialog(element)">
                <span>{{ element.circleId }}</span>
                <span>{{ element.circleName }}</span>
                <span
                  ><el-button
                    type="primary"
                    link
                    @click="rowCopy(element, index)"
                    >复制</el-button
                  >
                  <el-button type="primary" link @click="rowDel(index)"
                    >删除</el-button
                  >
                </span>
              </div>
            </template>
          </draggable>
        </div>
        <div class="">
          <p>&ensp;&ensp;模块配置</p>
          <el-row :gutter="24">
            <el-col :span="12">
              <span>模块背景颜色:</span>
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.bgColor"
                style="width: 250px"
              ></el-color-picker>
            </el-col>
            <el-col :span="12">
              <span>分隔线颜色配置:</span>
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.lineColor"
                style="width: 250px"
              ></el-color-picker>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <span>序号颜色配置:</span>
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.serialColor"
                style="width: 250px"
              ></el-color-picker>
            </el-col>
            <el-col :span="12">
              <span>文字颜色配置:</span>
              <el-color-picker
                :predefine="predefineColors"
                v-model="updatedData.fontColor"
                style="width: 250px"
              ></el-color-picker>
            </el-col>
          </el-row>
        </div>
        <div class="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirmation">确 定</el-button>
        </div>
      </div>
    </el-drawer>
    <el-dialog
      :title="`${addData.isNewAdd ? '新增' : '编辑'}摩友圈`"
      v-model="dialogVisible"
      width="580px"
    >
      <el-form>
        <el-form-item label="摩友圈Id" required label-width="110px">
          <el-input
            v-model="showCircleId"
            clearable
            style="width: 350px"
            placeholder="请输入，支持多选，以 , 分开"
          />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="determine">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  CircleClose as IconCircleClose,
  CirclePlus as IconCirclePlusOutline
} from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { predefineColors } from './config'
import { deepCopy } from '@/utils'
import { getCircleList } from '@/api/circle'
import draggable from 'vuedraggable'
export default {
  components: {
    draggable,
    IconCircleClose,
    IconCirclePlusOutline
  },
  name: 'CircleConfig',
  props: {
    configData: {
      typeof: Object,
      default: {}
    },
    index: {
      typeof: Number,
      default: 0
    }
  },
  data() {
    return {
      predefineColors: predefineColors,
      updatedData: {},
      dialog: false,
      dialogVisible: false,
      addData: {
        circleId: '', // 摩友圈ID
        circleName: '', // 摩友圈名称
        isNewAdd: true, // 新增
        createDate: '' // 创建时间
      },
      showCircleId: ''
    }
  },
  computed: {
    daterange: {
      get() {
        if (this.updatedData.beginTime && this.updatedData.endTime) {
          return [this.updatedData.beginTime, this.updatedData.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.updatedData.beginTime = value[0]
          this.updatedData.endTime = value[1]
        } else {
          this.updatedData.beginTime = ''
          this.updatedData.endTime = ''
        }
      }
    }
  },
  mounted() {},
  methods: {
    showDrawer() {
      this.updatedData = deepCopy(this.configData)
      this.dialog = true
    },
    copy() {
      $emit(this, 'copyData', this.configData, this.index)
    },
    delItem() {
      $emit(this, 'delItem', this.index)
    },
    handleClose() {
      this.dialog = false
    },
    confirmation() {
      $emit(this, 'updatedData', this.updatedData, this.index)
      this.dialog = false
    },
    showdialog(data) {
      this.addData = deepCopy(data)
      this.showCircleId = this.addData.circleId
      this.dialogVisible = true
    },
    append() {
      this.addData = {
        circleId: '', // 摩友圈ID
        circleName: '', // 摩友圈名称
        isNewAdd: true, // 新增
        createDate: '' // 创建时间
      }
      this.showCircleId = ''
      this.dialogVisible = true
    },
    async determine() {
      if (!this.showCircleId) {
        return this.$message.error('请输入摩友圈ID')
      }
      const list = this.showCircleId.split(',')
      const results = await Promise.all(
        list.map(async (item) => {
          const { data: circleData } = await getCircleList({
            page: 1,
            limit: 20,
            id: item
          })
          return circleData.data.list && circleData.data.list[0]
        })
      )
      const arr = []
      console.log(`results`, results)
      results &&
        results.map((_) => {
          if (_) {
            arr.push({
              circleId: _.id, // 摩友圈ID
              circleName: _.name, // 摩友圈名称
              isNewAdd: false, // 新增
              createDate: Math.floor(new Date()) // 创建时间
            })
          }
        })
      if (this.addData.isNewAdd) {
        this.updatedData.circleList.push(...arr)
      } else {
        const findIndex = this.updatedData.circleList.findIndex((item) => {
          return item.createDate === this.addData.createDate
        })
        this.updatedData.circleList.splice(findIndex, 1, ...arr)
      }
      this.dialogVisible = false
    },
    rowCopy(data, index) {
      const newData = deepCopy(data)
      newData.createDate = Math.floor(new Date())
      console.log(`index`, index)
      // this.updatedData.circleList.splice(index + 1, 0, newData)
      this.updatedData.circleList.push(newData)
    },
    rowDel(index) {
      this.updatedData.circleList.splice(index, 1)
      this.$message.success('删除成功')
    },
    refreshImageList() {
      // console.log(`updatedData.circleList`, this.updatedData.circleList)
    }
  },
  emits: ['copyData', 'delItem', 'updatedData']
}
</script>

<style lang="scss" scoped>
.circle-config {
  margin-top: 10px;
  .header {
    background-color: #f8f8f8;
    border-radius: 3px;
    overflow: hidden;
    .tabs {
      height: 30px;
      background-color: #f1f1f1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      .copy {
        color: #462be2;
        cursor: pointer;
      }
      .delete {
        font-size: 20px;
        cursor: pointer;
      }
    }
    .circle-list {
      margin: 15px;
      padding: 0 15px 10px 15px;
      border-radius: 6px;
      background-color: #313133;
      .circle-item {
        padding: 15px 0px;
        border-bottom: 1px solid #666666;
        span:nth-child(1) {
          color: #f7d529;
          font-size: 12px;
        }
        span:nth-child(2) {
          color: #ffffff;
          font-size: 14px;
          margin-left: 15px;
        }
      }
    }
    .add-config {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 150px;
      i {
        font-size: 80px;
      }
    }
  }
  .drawer-box {
    position: relative;
    height: 100%;
    padding: 0 20px;
    .add-button {
      text-align: right;
    }
    .circle-conent {
      padding-bottom: 60px;
      .column {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        padding: 10px 0;
        align-items: center;
        text-align: center;
        border-bottom: 1px solid #dedede;
        span:nth-child(1) {
          width: 20%;
        }
        span:nth-child(2) {
          width: 60%;
        }
        span:nth-child(3) {
          width: 20%;
        }
      }
    }
    .footer {
      position: fixed;
      bottom: 20px;
      right: 20px;
    }
  }
  .dialog-footer {
    text-align: center;
    padding-top: 20px;
  }
}
</style>
