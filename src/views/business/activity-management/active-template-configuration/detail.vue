<template>
  <div v-loading="loading" class="act-detail">
    <p>
      <el-button type="danger" @click="handleClose()">取消</el-button>
      <el-button type="primary" @click="validateData()">保存</el-button>
    </p>
    <el-form ref="ruleForm" :inline="true" :model="ruleForm" :rules="rules">
      <div class="title">活动基础信息</div>
      <el-form-item label="活动名称：" prop="activeName">
        <el-input
          v-model="ruleForm.activeName"
          maxlength="20"
          style="width: 250px"
          placeholder="请输入活动名称，不超过20个字"
        />
      </el-form-item>
      <el-form-item label="页面名称：" prop="pageName">
        <el-input
          v-model="ruleForm.pageName"
          maxlength="20"
          style="width: 250px"
          placeholder="请输入页面名称，不超过20个字"
        />
      </el-form-item>
      <el-form-item label="背景颜色：" prop="bgCorlor">
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.bgCorlor"
          style="width: 250px"
        ></el-color-picker>
      </el-form-item>
      <el-form-item label="活动开始时间：" prop="beginTime">
        <el-date-picker
          v-model="ruleForm.beginTime"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择开始日期"
          :default-time="
            ['00:00:00'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
        />
      </el-form-item>
      <el-form-item label="活动结束时间：" prop="endTime">
        <el-date-picker
          v-model="ruleForm.endTime"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择结束日期"
          :default-time="
            ['00:00:00'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
        />
      </el-form-item>
      <el-form-item label="活动类型" required>
        <el-select v-model="ruleForm.status" placeholder="请选择活动类型">
          <el-option
            v-for="(value, index) in statusList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div>
      <div class="title">详情配置</div>
      <!-- <signUpTemplate ref="signUpTemplate" /> -->
      <component
        :is="templateEnum[ruleForm.status]"
        :ref="templateEnum[ruleForm.status]"
      ></component>
    </div>
  </div>
</template>

<script>
import { predefineColors } from './sign-components/config'
import signUpTemplate from './sign-components/sign-up-template.vue'
import mainVenueTemplate from './main-components/main-venue-template.vue'
import {
  GetSelectsConfig,
  saveActAndTempletPrize,
  GetTempletDetail
} from '@/api/activeConfiguration'
import { mapGetters } from 'vuex'
export default {
  name: 'ActiveTemplateConfigurationDetail',
  components: {
    signUpTemplate,
    mainVenueTemplate
  },
  data() {
    const bgEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('分享按钮背景色必填'))
      }
      callback()
    }
    const dateEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('时间必填'))
      }
      callback()
    }
    return {
      loading: false,
      postStatus: false,
      predefineColors: predefineColors,
      activityId: '',
      conf: {
        // 表单权限
        personEnterSelects: [],
        clubEnterSelects: []
      },
      statusList: {
        报名活动模版: '1',
        主会场活动: '2'
      },
      ruleForm: {
        status: '1',
        bgCorlor: '#fff'
      },
      rules: {
        activeName: [
          { required: true, message: '请填写活动名称', trigger: 'blur' }
        ],
        pageName: [
          { required: true, message: '请填写页面名称', trigger: 'blur' }
        ],
        bgCorlor: [{ validator: bgEnter, trigger: 'change' }],
        beginTime: [
          {
            type: 'date',
            required: true,
            validator: dateEnter,
            trigger: 'change'
          }
        ],
        endTime: [
          {
            type: 'date',
            required: true,
            validator: dateEnter,
            trigger: 'change'
          }
        ]
      },
      templateEnum: {
        1: 'signUpTemplate',
        2: 'mainVenueTemplate'
      }
    }
  },
  computed: {
    ...mapGetters(['uid'])
  },
  activated() {
    const query = this.$route.query || {}
    this.init(query)
  },
  methods: {
    async init(item) {
      this.ruleForm = {
        status: '1',
        bgCorlor: '#fff'
      }
      const me = this
      me.$refs.signUpTemplate && me.$refs.signUpTemplate.resert()
      me.$refs.mainVenueTemplate && me.$refs.mainVenueTemplate.resert()
      await me.getSelectsConfig()
      if (item.activityId) {
        me.loading = true
        me.activityId = item.activityId
        await GetTempletDetail({
          activityId: item.activityId,
          templetId: item.templetId
        })
          .then((response) => {
            if (response.data.code === 0) {
              const data = response.data.data
              const JsonData = JSON.parse(data.templetJson)
              me.ruleForm = JsonData.activeInfo || {
                status: '1',
                bgCorlor: '#fff'
              }
              me.$nextTick(() => {
                me.$refs[me.templateEnum[me.ruleForm.status]].initData(JsonData)
              })
            }
          })
          .finally(() => {
            me.loading = false
          })
      }
      me.initJson = JSON.stringify(me.ruleForm)
      return new Promise((resolve) => {
        return resolve()
      })
    },
    async getSelectsConfig() {
      await GetSelectsConfig().then((response) => {
        Object.assign(this.conf, response.data.conf)
      })
    },
    // 验证数据
    validateData() {
      const me = this
      me.$refs[me.templateEnum[me.ruleForm.status]].saveData()
      me.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          setTimeout(() => {
            me.saveData()
          }, 100)
        } else {
          me.$message.error('还有未填写项目')
        }
      })
    },
    // 保存数据
    saveData() {
      const me = this
      if (me.ruleForm.beginTime > me.ruleForm.endTime) {
        return me.$message.error('开始时间不得大于结束时间')
      }
      if (me.ruleForm.endTime < new Date()) {
        return me.$message.error('结束时间不得小于当前时间')
      }
      let saveListData =
        me.$refs[me.templateEnum[me.ruleForm.status]].saveListData
      const backStatus =
        me.$refs[me.templateEnum[me.ruleForm.status]].backStatus
      saveListData = {
        ...saveListData,
        activeInfo: me.ruleForm
      }
      if (!backStatus) return me.$message.error('尚有数据未填写')
      if (me.postStatus) return
      me.postStatus = true
      saveActAndTempletPrize({
        activityName: me.ruleForm.activeName,
        shareIcoUrl: saveListData.shareData.shareImage,
        shareTitle: saveListData.shareData.shareDesc,
        shareDesc: saveListData.shareData.shareTitle,
        beginTime: me.ruleForm.beginTime,
        endTime: me.ruleForm.endTime,
        templetId: me.ruleForm.status === '1' ? 3 : 4,
        activityType: me.ruleForm.status === '1' ? 4 : 5,
        templetJson: JSON.stringify(saveListData),
        activityId: me.$route.query.copy ? '' : me.activityId || ''
      })
        .then((res) => {
          if (res.data && res.data.code === 1001) {
            me.$message.error(res.data.msg)
            return
          }
          me.$message.success('配置成功')
          this.$router.go(-1)
        })
        .finally(() => {
          me.postStatus = false
        })
    },
    // 取消
    handleClose() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss">
.act-detail {
  padding: 0 10px;
}
.title {
  font-weight: 500;
  margin-bottom: 10px;
}
</style>
