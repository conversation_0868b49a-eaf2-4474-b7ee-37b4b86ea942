<template>
  <div class="edit-from">
    <div class="title">表单模块配置</div>
    <el-form ref="ruleForm" :model="ruleForm" :rules="rulesFrom">
      <el-form-item label="同步到报名后" required v-if="showSync">
        <el-switch
          v-model="ruleForm.syncStatus"
          active-color="#13ce66"
          inactive-color="#ff4949"
          @change="changeStatus(ruleForm)"
        />
      </el-form-item>
      <div class="title">触发配置</div>
      <el-form-item label="配置的字段" required>
        <el-radio-group v-model="ruleForm.triggerShowFrom">
          <el-radio
            v-for="(show, index) in showList"
            :label="show"
            :key="show"
            >{{ index }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="!ruleForm.triggerShowFrom"
        label="添加图片："
        prop="triggerShowFromImgUrl"
      >
        <el-input
          v-model="ruleForm.triggerShowFromImgUrl"
          placeholder="请选择图片"
        />
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccessUrl"
          name="titlefile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <el-button type="primary" link>选择图片</el-button>
        </el-upload>
      </el-form-item>
      <div class="title">报名配置</div>
      <el-form-item label="需配置字段" prop="sginUpConfigName">
        <el-checkbox-group v-model="checkList">
          <el-checkbox
            v-for="(name, index) in configNameList"
            :label="name"
            :key="index"
          />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item
        v-if="checkList.includes('手机号')"
        label="手机号逻辑"
        prop="mobileLogic"
        required
      >
        <el-radio-group v-model="ruleForm.mobileLogic">
          <el-radio :label="1">取询价手机号逻辑(验证码修改)</el-radio>
          <el-radio :label="2">换绑逻辑</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="未报名按钮文案" prop="sginUpConfigTip">
        <el-input
          v-model="ruleForm.sginUpConfigTip"
          maxlength="20"
          placeholder="请输入未报名按钮文案，最多20个字"
        />
      </el-form-item>
      <div class="title">报名后配置</div>
      <el-form-item label="报名后标题文案" prop="sginUpBeforTitle">
        <el-input
          v-model="ruleForm.sginUpBeforTitle"
          maxlength="20"
          placeholder="请输入报名后标题文案，最多20个字"
        />
      </el-form-item>
      <el-form-item label="报名后按钮文案" prop="sginUpBeforButtonTitle">
        <el-input
          v-model="ruleForm.sginUpBeforButtonTitle"
          maxlength="20"
          placeholder="请输入报名后按钮文案，最多20个字"
        />
      </el-form-item>
      <el-form-item label="配置按钮变更">
        <el-radio-group v-model="ruleForm.sginUpbeforUpdateStatus">
          <el-radio
            v-for="(show, index) in statusList"
            :label="show"
            :key="show"
            >{{ index }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="ruleForm.sginUpbeforUpdateStatus"
        label="按钮变更时间"
        prop="sginUpBeforUpdateTime"
      >
        <el-date-picker
          v-model="ruleForm.sginUpBeforUpdateTime"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择按钮变更时间"
          :default-time="$dayjs('00:00:00', 'hh:mm:ss').toDate()"
        />
      </el-form-item>
      <el-form-item v-if="ruleForm.sginUpbeforUpdateStatus" label="跳转链接">
        <el-input
          v-model="ruleForm.sginUpBeforLinkUrl"
          placeholder="请输入跳转链接"
        />
      </el-form-item>
      <el-form-item
        v-if="ruleForm.sginUpbeforUpdateStatus"
        label="按钮变更文案"
        prop="sginUpbeforUpdateButtonTitle"
      >
        <el-input
          v-model="ruleForm.sginUpbeforUpdateButtonTitle"
          maxlength="20"
          placeholder="请输入按钮变更文案，最多20个字"
        />
      </el-form-item>
      <div class="title">成功弹框配置</div>
      <el-form-item label="报名后弹窗配置">
        <el-radio-group v-model="ruleForm.successTipConfigStatus">
          <el-radio
            v-for="(show, index) in statusList"
            :label="show"
            :key="show"
            >{{ index }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="ruleForm.successTipConfigStatus"
        label="弹框图片："
        prop="successTipConfigImgUrl"
      >
        <el-input
          v-model="ruleForm.successTipConfigImgUrl"
          placeholder="请选择图片"
        />
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccessImgUrl"
          name="titlefile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <el-button type="primary" link>选择图片</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item v-if="ruleForm.successTipConfigStatus" label="跳转链接">
        <el-input
          v-model="ruleForm.successTipConfigLinkUrl"
          placeholder="请输入跳转链接"
        />
      </el-form-item>
      <div class="title">颜色配置</div>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="背景颜色：" prop="colorBgColor" required>
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.colorBgColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字段标题颜色：" prop="colorTitleColor" required>
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.colorTitleColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item
            label="报名后标题颜色："
            prop="colorSignUpScuccessTitleColor"
            required
          >
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.colorSignUpScuccessTitleColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="输入框字体颜色："
            prop="colorInputColor"
            required
          >
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.colorInputColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="输入框背景色：" prop="colorInputBgColor" required>
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.colorInputBgColor"
          style="width: 250px"
        ></el-color-picker>
      </el-form-item>
      <p class="title">未报名</p>
      <el-row :gutter="24">
        <el-col
          :span="
            checkList.includes('手机号') && ruleForm.mobileLogic === 2 ? 8 : 12
          "
        >
          <el-form-item
            label="按钮背景色："
            prop="colorNoSignButtonBgColor"
            required
          >
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.colorNoSignButtonBgColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </el-col>
        <el-col
          :span="
            checkList.includes('手机号') && ruleForm.mobileLogic === 2 ? 8 : 12
          "
        >
          <el-form-item
            label="按钮文案颜色："
            prop="colorNoSignButtonTitleColor"
            required
          >
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.colorNoSignButtonTitleColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </el-col>
        <el-col
          v-if="checkList.includes('手机号') && ruleForm.mobileLogic === 2"
          :span="8"
        >
          <el-form-item
            label="换绑手机号文案颜色："
            prop="changeMobileColor"
            required
          >
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.changeMobileColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <p class="title">已报名</p>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item
            label="按钮背景色："
            prop="colorSignButtonBgColor"
            required
          >
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.colorSignButtonBgColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="按钮文案颜色："
            prop="colorSignButtonTitleColor"
            required
          >
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.colorSignButtonTitleColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <p v-if="ruleForm.successTipConfigStatus" class="title">变更状态</p>
      <el-row :gutter="24" v-if="ruleForm.successTipConfigStatus">
        <el-col :span="12">
          <el-form-item
            label="按钮背景色："
            prop="colorUpdateButtonBgColor"
            required
          >
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.colorUpdateButtonBgColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="按钮文案颜色："
            prop="colorUpdateButtonTitleColor"
            required
          >
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.colorUpdateButtonTitleColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="text-center">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { $emit } from '../../../../../utils/gogocodeTransfer'
import { predefineColors } from './config'
export default {
  name: 'EditFrom',
  props: {
    showSync: {
      typeof: Boolean,
      default: true
    }
  },
  data() {
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('图片必须上传'))
      }
      callback()
    }
    let bgEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('颜色必填'))
      }
      callback()
    }
    const dateEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('时间必填'))
      }
      callback()
    }
    return {
      ruleForm: {
        imgUrl: '', // 图片
        linkUrl: '' // 图片跳转地址
      },
      predefineColors: predefineColors,
      showList: {
        直接展示表单: true,
        点击触发表单: false
      }, // 触发配置
      configNameList: ['姓名', '手机号', '隐私协议'],
      checkList: [],
      statusList: {
        配置: true,
        不配置: false
      },
      rulesFrom: {
        triggerShowFromImgUrl: [
          { required: true, validator: imgEnter, trigger: 'change' }
        ],
        // sginUpConfigName: [
        //   {
        //     type: 'array',
        //     required: true,
        //     message: '请至少选择一个需配置字段',
        //     trigger: 'change'
        //   }
        // ],
        sginUpConfigTip: [
          { required: true, message: '请填写未报名按钮文案', trigger: 'blur' }
        ],
        sginUpBeforTitle: [
          { required: true, message: '请填写报名后标题文案', trigger: 'blur' }
        ],
        sginUpBeforButtonTitle: [
          { required: true, message: '请填写报名后按钮文案', trigger: 'blur' }
        ],
        sginUpBeforUpdateTime: [
          {
            type: 'date',
            required: true,
            validator: dateEnter,
            trigger: 'change'
          }
        ],
        sginUpbeforUpdateButtonTitle: [
          { required: true, message: '请填写按钮变更文案', trigger: 'blur' }
        ],
        successTipConfigImgUrl: [
          { required: true, validator: imgEnter, trigger: 'change' }
        ],
        colorBgColor: [{ validator: bgEnter, trigger: 'change' }],
        colorTitleColor: [{ validator: bgEnter, trigger: 'change' }],
        colorSignUpScuccessTitleColor: [
          { validator: bgEnter, trigger: 'change' }
        ],
        colorInputColor: [{ validator: bgEnter, trigger: 'change' }],
        colorInputBgColor: [{ validator: bgEnter, trigger: 'change' }],
        colorNoSignButtonBgColor: [{ validator: bgEnter, trigger: 'change' }],
        colorNoSignButtonTitleColor: [
          { validator: bgEnter, trigger: 'change' }
        ],
        changeMobileColor: [{ validator: bgEnter, trigger: 'change' }],
        colorSignButtonBgColor: [{ validator: bgEnter, trigger: 'change' }],
        colorSignButtonTitleColor: [{ validator: bgEnter, trigger: 'change' }],
        colorUpdateButtonBgColor: [{ validator: bgEnter, trigger: 'change' }],
        colorUpdateButtonTitleColor: [{ validator: bgEnter, trigger: 'change' }]
      }
    }
  },
  computed: {},
  watch: {},
  activated() {},
  methods: {
    init(data) {
      this.ruleForm = {
        ...data,
        sginUpConfigName: data.sginUpConfigName || []
      }
      this.checkList = this.ruleForm.sginUpConfigName
      this.ruleForm.sginUpConfigName = this.ruleForm.sginUpConfigName || []
      this.ruleForm.mobileLogic = this.ruleForm.mobileLogic || 1
      this.ruleForm.changeMobileColor =
        this.ruleForm.changeMobileColor || '#ffffff'
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 图片返回
    onSuccessUrl(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.triggerShowFromImgUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 成功后图片
    onSuccessImgUrl(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.successTipConfigImgUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 同步开关
    changeStatus() {
      $emit(this, 'syncData', this.ruleForm)
    },
    // 关闭
    handleClose() {
      $emit(this, 'closeActiveData', {}, false)
    },
    // 确认
    confirm() {
      const me = this
      me.ruleForm.sginUpConfigName = me.checkList
      me.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          $emit(me, 'closeActiveData', me.ruleForm, true)
        } else {
          me.$message.error('还有必填信息未填写')
        }
      })
    }
  },
  emits: ['syncData', 'closeActiveData']
}
</script>

<style lang="scss" scoped>
.edit-from {
  overflow-y: scroll;
  .title {
    margin: 5px 0;
    font-weight: 600;
  }
}
</style>
