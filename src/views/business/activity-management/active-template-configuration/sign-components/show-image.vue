<template>
  <div class="show-image" @click.stop="editItem()">
    <p class="tip-content">
      图片 ｜<el-button type="primary" link @click.stop="copyData()"
        >复制</el-button
      >
      <img
        @click.stop="goDel()"
        class="fl-right"
        src="@/assets/image/<EMAIL>"
      />
    </p>
    <img v-if="data.imgUrl" :src="data.imgUrl" class="show-image-url" />
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
export default {
  name: 'ShowImage',
  props: {
    data: {
      typeof: Object,
      default: {}
    },
    checkTypeStatus: {
      typeof: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {},
  activated() {},
  methods: {
    // 复制
    copyData() {
      $emit(this, 'copyData', this.data)
    },
    // 编辑模块
    editItem() {
      if (this.checkTypeStatus && this.data.syncStatus) {
        return
      }
      $emit(this, 'updatedActiveData', this.data)
    },
    // 删除
    goDel() {
      const me = this
      if (me.checkTypeStatus && me.data.syncStatus) {
        return
      }
      if (!me.data.syncStatus) {
        return $emit(me, 'delItem', me.data)
      }
      me.$confirm('删除后，报名后模块卡片同步删除，是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        $emit(me, 'delItem', me.data)
      })
    }
  },
  emits: ['copyData', 'updatedActiveData', 'delItem']
}
</script>

<style lang="scss" scoped>
.show-image {
  border: 1px solid #ccc;
  border-radius: 10px;
  margin-bottom: 10px;
  .tip-content {
    margin: 10px;
  }
  .fl-right {
    width: 30px;
    height: 30px;
    position: relative;
  }
  .show-image-url {
    width: 100%;
  }
}
</style>
