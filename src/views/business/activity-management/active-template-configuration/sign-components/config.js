// 配置的js
// 图片
export const imgDefault = {
  type: 'img',
  name: '', // 图片名称
  imgUrl: '', // 图片地址
  linkUrl: '' // 跳转链接
}

// 表单
export const fromDefault = {
  type: 'from',
  triggerShowFrom: true, // 模块触发配置, 是否直接展示表单
  triggerShowFromImgUrl: '', // 模块触发配置为false时需要配置图片
  mobileLogic: 1, // 报名配置，手机号逻辑
  sginUpConfigName: [], // 报名配置，配置的字段
  sginUpConfigTip: '', // 报名配置，未报名按钮文案
  sginUpBeforTitle: '', // 报名配置，报名后标题
  sginUpBeforButtonTitle: '', // 报名配置，报名后按钮文案
  sginUpbeforUpdateStatus: true, // 报名配置，报名后按钮变更
  sginUpBeforUpdateTime: '', // 报名配置，报名后按钮变更时间
  sginUpBeforLinkUrl: '', // 报名配置，报名后跳转链接
  sginUpbeforUpdateButtonTitle: '', // 报名配置，报名后按钮变更文案
  successTipConfigStatus: false, // 成功弹窗配置 状态
  successTipConfigImgUrl: '', // 成功弹窗配置 弹框图片
  successTipConfigLinkUrl: '', // 成功弹框配置，跳转链接
  colorBgColor: '#ffffff', // 颜色背景
  colorTitleColor: '#000000', // 字段标题颜色
  colorSignUpScuccessTitleColor: '#000000', // 报名成功后标题颜色
  colorInputColor: '#000000', // 输入框字体颜色
  colorInputBgColor: '#FFFFFF', // 输入框背景色
  colorNoSignButtonBgColor: '#D9001B', // 未报名按钮背景颜色
  colorNoSignButtonTitleColor: '#ffffff', // 未报名按钮文案颜色
  changeMobileColor: '#ffffff', // 换绑手机号文案颜色
  colorSignButtonBgColor: '#D9001B', // 已报名按钮背景颜色
  colorSignButtonTitleColor: '#ffffff', // 已报名按钮文案颜色
  colorUpdateButtonBgColor: '#D9001B', // 变更报名按钮背景颜色
  colorUpdateButtonTitleColor: '#ffffff' // 变更报名按钮文案颜色
}

// 奖品
export const awardDefault = {
  type: 'award',
  num: '1', // 每排梳理
  isSort: false, // 是否打乱（false 不打乱）
  goodList: []
}
// 颜色值
export const predefineColors = [
  // 颜色拾取器的自定义颜色
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsv(51, 100, 98)',
  'hsva(120, 40, 94, 0.5)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577',
  '#000000',
  '#ffffff',
  '#D9001B'
]

// 视频
export const videoDefault = {
  type: 'video',
  name: '', // 视频名称
  imgUrl: '', // 视频展示图，
  videoUrl: '' // 视频播放地址
}

// 倒计时
export const countdownDefault = {
  type: 'countdown',
  BgColor: '', // 模块背景色
  titleColor: '', // 报名文案颜色
  subtitleColor: '', // 副标题文案颜色
  inputBgColor: '', // 输入框背景色
  inputColor: '' // 输入文案颜色
}
