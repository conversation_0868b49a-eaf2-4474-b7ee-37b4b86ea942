<template>
  <div class="sign-up-template">
    <div class="title">视频配置</div>
    <el-form ref="ruleForm" :inline="true" :model="ruleForm" :rules="rules">
      <el-form-item label="同步到报名后" required v-if="showSync">
        <el-switch
          v-model="ruleForm.syncStatus"
          active-color="#13ce66"
          inactive-color="#ff4949"
          @change="changeStatus(ruleForm)"
        />
      </el-form-item>
      <br />
      <el-form-item label="名称" label-width="80px">
        <el-input
          v-model="ruleForm.name"
          clearable
          style="width: 350px"
          placeholder="不必填"
        />
      </el-form-item>
      <br />
      <el-form-item
        label="添加视频"
        prop="videoUrl"
        required
        label-width="80px"
      >
        <el-input
          v-model="ruleForm.videoUrl"
          clearable
          style="width: 350px"
          placeholder="请选择视频"
        />
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest1"
          :on-success="updateFile"
          name="upfile"
          class="cover-uploader"
          action
        >
          <el-button type="primary" class="fl-left">选择视频</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="视频封面图片：" label-width="120px">
        <el-input
          v-model="ruleForm.imgUrl"
          style="width: 350px"
          placeholder="请选择图片"
        />
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccessUrl"
          name="titlefile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <el-button type="primary" link>选择图片</el-button>
        </el-upload>
      </el-form-item>
      <div class="text-center">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
export default {
  name: 'EditVideo',
  props: {
    showSync: {
      typeof: Boolean,
      default: true
    }
  },
  data() {
    return {
      ruleForm: {
        name: '',
        imgUrl: '', // 图片
        videoUrl: '' // 播放地址
      },
      rules: {
        videoUrl: [{ required: true, message: '请选择视频', trigger: 'blur' }]
      }
    }
  },
  methods: {
    init(data) {
      this.ruleForm = data
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 图片返回
    onSuccessUrl(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.imgUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 上传视频
    async httpRequest1(option) {
      const file = option.file
      const fileName = ((file && file.name) || '').toLowerCase()
      if (!file) return
      if (file.size > 2 * 1024 * 1024 * 1024) {
        return this.setToast('视频大小不能超过2GB')
      }
      if (
        fileName.indexOf('.mp4') === -1 &&
        fileName.indexOf('.mov') === -1 &&
        fileName.indexOf('.ts') === -1
      ) {
        return this.setToast('视频格式不是mp4、mov、ts')
      }
      option.isVideo = true // 是视频
      this.$oss.ossUploadFile(option)
    },
    updateFile(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        this.ruleForm['videoUrl'] = res.url
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 同步开关
    changeStatus() {
      $emit(this, 'syncData', this.ruleForm)
    },
    // 关闭
    handleClose() {
      $emit(this, 'closeActiveData', {}, false)
    },
    // 确认
    confirm() {
      const me = this
      me.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          $emit(me, 'closeActiveData', this.ruleForm, true)
        } else {
          me.$message.error('请添加视频')
        }
      })
    }
  },
  emits: ['syncData', 'closeActiveData']
}
</script>

<style lang="scss" scoped>
.title-tip {
  font-size: 11px;
  color: #ccc;
}
.title {
  font-weight: 600;
}
</style>
