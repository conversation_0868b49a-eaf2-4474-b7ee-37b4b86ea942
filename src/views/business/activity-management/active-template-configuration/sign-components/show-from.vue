<template>
  <div class="show-from" @click.stop="editItem()">
    <p class="tip-content">
      表单 ｜
      <img
        @click.stop="goDel()"
        class="fl-right"
        src="@/assets/image/<EMAIL>"
      />
    </p>
    <div
      v-if="data.triggerShowFrom"
      :style="{ backgroundColor: data.colorBgColor }"
      class="show-from-bg"
    >
      <template v-for="(name, index) in data.sginUpConfigName" :key="index">
        <div v-if="name !== '隐私协议'" class="show-from-input-content">
          <span :style="{ color: data.colorTitleColor }">{{ name }}</span>
          <div
            class="show-from-input-content_input"
            :style="{ backgroundColor: data.colorInputBgColor }"
          >
            <input type="text" :placeholder="`请输入${name}`" />
            <div
              class="mobile-logic"
              v-if="name === '手机号' && data.mobileLogic === 2"
              :style="{ color: data.changeMobileColor }"
            >
              换绑手机号
            </div>
          </div>
        </div>
      </template>
      <div
        class="show-from-input-button"
        v-if="data.sginUpConfigTip || data.sginUpBeforButtonTitle"
        :style="{
          backgroundColor: !checkTypeStatus
            ? data.colorNoSignButtonBgColor
            : data.colorSignButtonBgColor,
          color: !checkTypeStatus
            ? data.colorNoSignButtonTitleColor
            : data.colorSignButtonTitleColor
        }"
      >
        {{
          !checkTypeStatus ? data.sginUpConfigTip : data.sginUpBeforButtonTitle
        }}
      </div>
    </div>
    <div v-else>
      <img :src="data.triggerShowFromImgUrl" class="show-from-img" />
    </div>
  </div>
</template>

<script>
import { $emit } from '../../../../../utils/gogocodeTransfer'
export default {
  name: 'ShowFrom',
  props: {
    data: {
      typeof: Object,
      default: {}
    },
    checkTypeStatus: {
      typeof: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    // 复制
    copyData() {
      $emit(this, 'copyData', this.data)
    },
    // 编辑模块
    editItem() {
      if (this.checkTypeStatus && this.data.syncStatus) {
        return
      }
      $emit(this, 'updatedActiveData', this.data)
    },
    // 删除
    goDel() {
      const me = this
      if (me.checkTypeStatus && me.data.syncStatus) {
        return
      }
      if (!me.data.syncStatus) {
        return $emit(me, 'delItem', me.data)
      }
      me.$confirm('删除后，报名后模块卡片同步删除，是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        $emit(me, 'delItem', me.data)
      })
    }
  },
  emits: ['copyData', 'updatedActiveData', 'delItem']
}
</script>

<style lang="scss" scoped>
.show-from {
  border: 1px solid #ccc;
  border-radius: 10px;
  margin-bottom: 10px;
  .tip-content {
    margin: 10px;
  }
  .fl-right {
    width: 30px;
    height: 30px;
    position: relative;
    top: -4px;
  }
  .show-image-url {
    width: 100%;
  }
  .show-from-bg {
    width: 100%;
    border-radius: 6px;
    padding: 0.2rem;
    .show-from-title {
      font-size: 21px;
      color: #a4f7f3;
      line-height: 29px;
      text-align: center;
      margin-bottom: 25px;
    }
    .show-from-input-content {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      span {
        line-height: 40px;
        font-size: 13px;
        width: 40px;
        margin-right: 5px;
      }
      .show-from-input-content_input {
        flex: 1;
        padding: 0 15px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        input {
          flex: 1;
          border: none;
          height: 40px;
          outline: none;
        }
        .mobile-logic {
          line-height: 40px;
          font-size: 13px;
          margin-left: 10px;
        }
      }
    }
    .show-from-input-button {
      width: 100%;
      height: 44px;
      line-height: 44px;
      text-align: center;
      border-radius: 22px;
      font-size: 15px;
      color: #000;
    }
  }
  .show-from-img {
    width: 100%;
  }
}
</style>
