<template>
  <div class="show-image" @click.stop="editItem()">
    <p class="tip-content">
      奖项 ｜<el-button type="primary" link @click.stop="copyData()"
        >复制</el-button
      >
      <img
        @click.stop="goDel()"
        class="fl-right"
        src="@/assets/image/<EMAIL>"
      />
    </p>
    <div v-if="parseInt(data.num) === 1">
      <div v-for="(good, index) in goodList" :key="index">
        <img :src="good.imgUrl" class="show-img_img" />
      </div>
    </div>
    <div v-if="parseInt(data.num) === 2" class="flex-2">
      <div v-for="(good, index) in goodList" :key="index" class="show-award">
        <img :src="good.imgUrl" class="show-img_img" />
      </div>
    </div>
    <div v-if="parseInt(data.num) === 3" class="flex-3">
      <div v-for="(good, index) in goodList" :key="index" class="img-list">
        <div v-for="(img, iIndex) in good" :key="iIndex" class="show-award">
          <img :src="img.imgUrl" class="show-img_img" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
export default {
  name: 'ShowAward',
  props: {
    data: {
      typeof: Object,
      default: {}
    },
    checkTypeStatus: {
      typeof: Boolean,
      default: false
    }
  },
  data() {
    return {
      goodList: []
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.initGood()
  },
  methods: {
    // 设置
    initGood() {
      let data = this.data.goodList
      if (parseInt(this.data.num) !== 3) {
        this.goodList = data
      } else {
        this.goodList = setData(data)
        function setData(data) {
          const length = Math.ceil(data.length / 3)
          let backData = []
          // 生成传参
          for (var i = 0; i < length; i++) {
            const newNum = i * 3
            backData.push(data.slice(0 + newNum, 3 + newNum))
          }
          return backData
        }
      }
    },
    // 编辑模块
    editItem() {
      if (this.checkTypeStatus && this.data.syncStatus) {
        return
      }
      $emit(this, 'updatedActiveData', this.data)
    },
    // 复制
    copyData() {
      $emit(this, 'copyData', this.data)
    },
    // 删除
    goDel() {
      const me = this
      if (me.checkTypeStatus && me.data.syncStatus) {
        return
      }
      if (!me.data.syncStatus) {
        return $emit(me, 'delItem', me.data)
      }
      me.$confirm('删除后，报名后模块卡片同步删除，是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        $emit(me, 'delItem', me.data)
      })
    }
  },
  emits: ['updatedActiveData', 'copyData', 'delItem']
}
</script>

<style lang="scss" scoped>
.show-image {
  border: 1px solid #ccc;
  border-radius: 10px;
  margin-bottom: 10px;
  .tip-content {
    margin: 10px;
  }
  .fl-right {
    width: 30px;
    height: 30px;
    position: relative;
    top: -4px;
  }
  .show-img_img {
    width: 100%;
  }
  .flex-2 {
    .show-award {
      display: inline-block;
      width: 49.5%;
    }
    .show-award:nth-of-type(2n + 1) {
      margin-right: 1%;
    }
  }
  .flex-3 {
    .img-list {
      div {
        display: inline-block;
        width: 32.5%;
      }
      div:nth-of-type(2n) {
        margin: 0 1%;
      }
    }
  }
}
</style>
