<template>
  <div class="sign-up-template">
    <div class="title">
      {{ title }}<span class="title-tip">（摩友圈ID与上传图片同时存在）</span>
    </div>
    <el-form ref="ruleForm" :inline="true" :model="ruleForm" :rules="rules">
      <el-form-item label="摩友圈配置" required>
        <el-radio-group v-model="ruleForm.circleStatus">
          <el-radio
            v-for="(show, index) in statusList"
            :label="show"
            :key="show"
            >{{ index }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <br />
      <el-form-item
        label="摩友圈id"
        prop="circleId"
        v-if="ruleForm.circleStatus"
      >
        <el-input
          v-model="ruleForm.circleId"
          maxlength="20"
          style="width: 250px"
          placeholder="请输入摩友圈id"
        />
      </el-form-item>
      <el-form-item
        label="上传图片："
        prop="circleImgage"
        v-if="ruleForm.circleStatus"
      >
        <el-input
          v-model="ruleForm.circleImgage"
          style="width: 350px"
          placeholder="请选择图片"
        />
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccessTitleimageShore"
          name="titlefile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <el-button type="primary" link>选择图片</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
export default {
  name: 'CircleTemplate',
  props: {},
  data() {
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('图片必须上传'))
      }
      callback()
    }
    return {
      validateStatus: false,
      title: '摩友圈配置',
      ruleForm: {
        circleStatus: false,
        circleId: '', // 摩友圈id
        circleImgage: '' // 摩友圈图片
      },
      statusList: {
        配置: true,
        不配置: false
      },
      rules: {
        circleId: [{ required: true, message: '摩友圈id', trigger: 'blur' }],
        circleImgage: [
          { required: true, validator: imgEnter, trigger: 'change' }
        ]
      }
    }
  },
  computed: {},
  watch: {},
  activated() {},
  methods: {
    init(circleData) {
      this.validateStatus = false
      this.ruleForm = circleData
        ? circleData
        : {
            circleId: '', // 摩友圈id
            circleImgage: '' // 摩友圈图片
          }
    },
    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 活动封面
    onSuccessTitleimageShore(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.circleImgage = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    validate() {
      const me = this
      if (
        !me.ruleForm.circleStatus ||
        (me.ruleForm.circleId && me.ruleForm.circleImgage)
      ) {
        me.validateStatus = true
        return
      }
      me.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          me.validateStatus = true
          $emit(me, 'closeActiveData', this.ruleForm, true)
        } else {
          me.validateStatus = false
        }
      })
    }
  },
  emits: ['closeActiveData']
}
</script>

<style lang="scss" scoped>
.title-tip {
  font-size: 11px;
  color: #ccc;
}
</style>
