<template>
  <div class="show-countdown" @click.stop="editItem">
    <p class="tip-content">
      倒计时 ｜<el-button type="primary" link @click.stop="copyData"
        >复制</el-button
      >
      <img
        @click.stop="goDel"
        class="fl-right"
        src="@/assets/image/<EMAIL>"
      />
    </p>
    <div
      v-if="props.data.BgColor"
      class="countdown-content"
      :style="{ backgroundColor: props.data.BgColor }"
    >
      <div class="countdown-title" :style="{ color: props.data.titleColor }">
        报名将在
        <div
          class="time"
          :style="{
            backgroundColor: props.data.inputBgColor,
            color: props.data.inputColor
          }"
        >
          00
        </div>
        天
        <div
          class="time"
          :style="{
            backgroundColor: props.data.inputBgColor,
            color: props.data.inputColor
          }"
        >
          00
        </div>
        :
        <div
          class="time"
          :style="{
            backgroundColor: props.data.inputBgColor,
            color: props.data.inputColor
          }"
        >
          00
        </div>
        :
        <div
          class="time"
          :style="{
            backgroundColor: props.data.inputBgColor,
            color: props.data.inputColor
          }"
        >
          00
        </div>
        后结束
      </div>
      <div
        class="countdown-subtitle"
        :style="{ color: props.data.subtitleColor }"
      >
        截止当前，已有0人报名
      </div>
    </div>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance()

const emit = defineEmits(['copyData', 'updatedActiveData', 'delItem'])

const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  },
  checkTypeStatus: {
    type: Boolean,
    default: false
  }
})

const editItem = () => {
  if (props.checkTypeStatus && props.data.syncStatus) {
    return
  }
  emit('updatedActiveData', props.data)
}

const copyData = () => {
  emit('copyData', props.data)
}

const goDel = () => {
  if (props.checkTypeStatus && props.data.syncStatus) {
    return
  }
  if (!props.data.syncStatus) {
    return emit('delItem', props.data)
  }
  proxy
    .$confirm('删除后，报名后模块卡片同步删除，是否确认删除?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      center: true
    })
    .then(() => {
      emit('delItem', props.data)
    })
}
</script>

<style lang="scss" scoped>
.show-countdown {
  border: 1px solid #ccc;
  border-radius: 10px;
  margin-bottom: 10px;
  .tip-content {
    margin: 10px;
  }
  .fl-right {
    width: 30px;
    height: 30px;
    position: relative;
  }
  .countdown-content {
    width: 100%;
    padding: 15px;
    .countdown-title {
      font-size: 15px;
      line-height: 21px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      .time {
        padding: 2px 4px;
        border-radius: 5px;
        margin: 0 6px;
      }
    }
    .countdown-subtitle {
      margin-top: 10px;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
    }
  }
}
</style>
