<template>
  <div class="sign-up-template">
    <div class="title">图片配置</div>
    <el-form ref="ruleForm" :inline="true" :model="ruleForm" :rules="rules">
      <el-form-item label="同步到报名后" required v-if="showSync">
        <el-switch
          v-model="ruleForm.syncStatus"
          active-color="#13ce66"
          inactive-color="#ff4949"
          @change="changeStatus(ruleForm)"
        />
      </el-form-item>
      <br />
      <el-form-item label="名称：" label-width="110px">
        <el-input
          v-model="ruleForm.name"
          clearable
          style="width: 350px"
          placeholder="不必填"
        />
      </el-form-item>
      <br />
      <el-form-item label="添加图片：" prop="imgUrl" label-width="110px">
        <el-input
          v-model="ruleForm.imgUrl"
          style="width: 350px"
          placeholder="请选择图片"
        />
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccessUrl"
          name="titlefile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <el-button type="primary" link>选择图片</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="配置跳转链接：" label-width="110px">
        <el-input
          v-model="ruleForm.linkUrl"
          style="width: 350px"
          placeholder="请输入配置跳转链接"
        />
      </el-form-item>
      <div class="text-center">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
export default {
  name: 'EditImage',
  props: {
    showSync: {
      typeof: Boolean,
      default: true
    }
  },
  data() {
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('图片必须上传'))
      }
      callback()
    }
    return {
      ruleForm: {
        imgUrl: '', // 图片
        linkUrl: '' // 图片跳转地址
      },
      rules: {
        imgUrl: [{ validator: imgEnter, trigger: 'change' }]
      }
    }
  },
  computed: {},
  watch: {},
  activated() {},
  methods: {
    init(data) {
      this.ruleForm = data
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 图片返回
    onSuccessUrl(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.imgUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 同步开关
    changeStatus() {
      $emit(this, 'syncData', this.ruleForm)
    },
    // 关闭
    handleClose() {
      $emit(this, 'closeActiveData', {}, false)
    },
    // 确认
    confirm() {
      const me = this
      me.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          $emit(me, 'closeActiveData', this.ruleForm, true)
        } else {
          me.$message.error('请添加图片')
        }
      })
    }
  },
  emits: ['syncData', 'closeActiveData']
}
</script>

<style lang="scss" scoped>
.title-tip {
  font-size: 11px;
  color: #ccc;
}
.title {
  font-weight: 600;
}
</style>
