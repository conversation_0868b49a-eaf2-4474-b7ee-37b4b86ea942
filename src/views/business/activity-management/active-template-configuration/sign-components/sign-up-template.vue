<template>
  <div class="sign-up-template">
    <el-radio-group v-model="checkType" @change="updateCheckType()">
      <el-radio-button v-for="city in typeList" :label="city" :key="city">{{
        city
      }}</el-radio-button>
    </el-radio-group>
    <p>
      <el-button type="primary" @click="addItem('img')">+ 图片</el-button>
      <el-button type="primary" @click="addItem('from')">+ 表单</el-button>
      <el-button type="primary" @click="addItem('award')">+ 奖项</el-button>
      <el-button type="primary" @click="addItem('video')">+ 视频</el-button>
      <el-button type="primary" @click="addItem('countdown')">
        + 倒计时
      </el-button>
    </p>
    <div class="show-list">
      <draggable
        class="left-content"
        v-model="showData"
        item-key="createDate"
        @end="refreshImageList"
      >
        <template #item="{ element }">
          <div>
            <template v-if="element.type === 'img'">
              <ShowImage
                :data="element"
                :checkTypeStatus="checkTypeStatus"
                @updatedActiveData="updatedActiveData"
                @copyData="copyItem"
                @delItem="delItem"
              />
            </template>
            <template v-if="element.type === 'award'">
              <ShowAward
                :data="element"
                :checkTypeStatus="checkTypeStatus"
                @updatedActiveData="updatedActiveData"
                @copyData="copyItem"
                @delItem="delItem"
                :ref="`award${element.createDate}`"
              />
            </template>
            <template v-if="element.type === 'from'">
              <ShowFrom
                :data="element"
                :checkTypeStatus="checkTypeStatus"
                @updatedActiveData="updatedActiveData"
                @copyData="copyItem"
                @delItem="delItem"
              />
            </template>
            <template v-if="element.type === 'video'">
              <ShowVideo
                :data="element"
                :checkTypeStatus="checkTypeStatus"
                @updatedActiveData="updatedActiveData"
                @copyData="copyItem"
                @delItem="delItem"
              />
            </template>
            <template v-if="element.type === 'countdown'">
              <ShowCountdown
                :data="element"
                :checkTypeStatus="checkTypeStatus"
                @updatedActiveData="updatedActiveData"
                @copyData="copyItem"
                @delItem="delItem"
              />
            </template>
          </div>
        </template>
      </draggable>
      <el-drawer
        :with-header="false"
        v-model="drawer"
        :modal="false"
        direction="rtl"
        size="50%"
        :before-close="handleClose"
      >
        <div class="right-content">
          <EditImage
            ref="EditImage"
            v-if="activeData.type === 'img'"
            :showSync="!checkTypeStatus"
            @closeActiveData="closeActiveData"
            @syncData="syncData"
          />
          <EditAward
            ref="EditAward"
            v-if="activeData.type === 'award'"
            :showSync="!checkTypeStatus"
            @closeActiveData="closeActiveData"
            @syncData="syncData"
          />
          <EditFrom
            ref="EditFrom"
            v-if="activeData.type === 'from'"
            :showSync="!checkTypeStatus"
            @closeActiveData="closeActiveData"
            @syncData="syncData"
          />
          <EditVideo
            ref="EditVideo"
            v-if="activeData.type === 'video'"
            :showSync="!checkTypeStatus"
            @closeActiveData="closeActiveData"
            @syncData="syncData"
          />
          <EditCountdown
            ref="EditCountdown"
            v-if="activeData.type === 'countdown'"
            :showSync="!checkTypeStatus"
            @closeActiveData="closeActiveData"
            @syncData="syncData"
          />
        </div>
      </el-drawer>
    </div>
    <CircleTemplate ref="SignCircleTemplate" />
    <RuleTemplate ref="RuleTemplate" />
    <ShareTemplate ref="ShareTemplate" />
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import CircleTemplate from './circle-template.vue'
import RuleTemplate from '../components/rule-template.vue'
import ShareTemplate from '../components/share-template.vue'
import ShowImage from './show-image.vue'
import EditImage from './edit-image.vue'
import ShowAward from './show-award.vue'
import EditAward from './edit-award.vue'
import ShowFrom from './show-from.vue'
import EditFrom from './edit-from.vue'
import ShowVideo from './show-video.vue'
import EditVideo from './edit-video.vue'
import ShowCountdown from './show-countdown.vue'
import EditCountdown from './edit-countdown.vue'
import {
  imgDefault,
  awardDefault,
  fromDefault,
  videoDefault,
  countdownDefault
} from './config'
import { deepCopy } from '@/utils'
export default {
  name: 'SignUpTemplate',
  components: {
    draggable,
    CircleTemplate,
    RuleTemplate,
    ShareTemplate,
    ShowImage,
    EditImage,
    ShowAward,
    EditAward,
    ShowFrom,
    EditFrom,
    ShowVideo,
    EditVideo,
    ShowCountdown,
    EditCountdown
  },
  data() {
    return {
      typeList: ['报名前模块', '报名后模块'],
      checkType: '报名前模块',
      checkTypeStatus: false, // 是否是报名后
      drawer: false, // 抽屉状态
      signAfterData: [], // 报名前模块
      signBeforeData: [], // 报名后模块
      showData: [], // 当前展示的模块
      imgDefault: imgDefault, // 图片默认
      awardDefault: awardDefault, // 奖项设置
      fromDefault: fromDefault, // 表单设置
      activeData: {}, // 活动数据
      backStatus: false, // 保存状态
      saveListData: {} // 保存的数据
    }
  },
  computed: {},
  watch: {},
  activated() {
    this.showData = this.signAfterData
  },
  methods: {
    // 变更显示
    updateCheckType() {
      this.checkTypeStatus = this.checkType === '报名后模块'
      this.showData = this.checkTypeStatus
        ? this.signBeforeData
        : this.signAfterData
      this.activeData = {}
    },
    // 增加对象
    addItem(type) {
      const listItemName = {
        img: imgDefault,
        award: awardDefault,
        from: fromDefault,
        video: videoDefault,
        countdown: countdownDefault
      }
      const findStatus = this.showData.findIndex((item) => {
        return item.type === 'from'
      })
      if (findStatus > -1 && type === 'from')
        return this.$message.error('已存在表单模块，不可重复配置')
      const addItem = {
        ...deepCopy(listItemName[type]),
        syncStatus: false, // 是否同步
        createDate: Math.floor(new Date())
      }
      !this.checkTypeStatus
        ? this.signAfterData.push(addItem)
        : this.signBeforeData.push(addItem)
    },
    // 复制对象
    copyItem(data) {
      if (data.type === 'from')
        return this.$message.error('已存在表单模块，不可重复配置')
      const addItem = {
        ...deepCopy(data),
        syncStatus: false, // 是否同步
        createDate: Math.floor(new Date())
      }
      const findData = !this.checkTypeStatus
        ? this.signAfterData
        : this.signBeforeData
      const findIndex = findData.findIndex((item) => {
        return item.createDate === data.createDate
      })
      findData.splice(findIndex + 1, 0, addItem)
    },
    // 删除对象
    delItem(data) {
      if (data.syncStatus) {
        let findData = !this.checkTypeStatus
          ? this.signBeforeData
          : this.signAfterData
        this.spliceItem(findData, data)
      }
      let findData = !this.checkTypeStatus
        ? this.signAfterData
        : this.signBeforeData
      this.$message.success('删除成功')
      this.spliceItem(findData, data)
    },
    // 同步对象
    syncData(data) {
      if (data.syncStatus) {
        this.signBeforeData.push(data)
      } else {
        this.spliceItem(this.signBeforeData, data, 0, true)
      }
      this.spliceItem(this.signAfterData, data, 1, true)
    },
    // 移除或者替换对象
    spliceItem(spliceData, newDate, newDateStatus, coloseStatus) {
      const findIndex = spliceData.findIndex((item) => {
        return item.createDate === newDate.createDate
      })
      newDateStatus
        ? spliceData.splice(findIndex, 1, newDate)
        : spliceData.splice(findIndex, 1)
      if (coloseStatus) return
      this.activeData = {}
    },
    // 改变活动数据
    updatedActiveData(data) {
      const me = this
      me.activeData = data
      me.drawer = true
      const refListName = {
        img: 'EditImage',
        award: 'EditAward',
        from: 'EditFrom',
        video: 'EditVideo',
        countdown: 'EditCountdown'
      }
      setTimeout(() => {
        me.$refs[refListName[data.type]].init(deepCopy(data))
      }, 10)
    },
    // 关闭右侧弹层
    closeActiveData(data, status) {
      this.activeData = {}
      this.drawer = false
      if (status) {
        let findData = !this.checkTypeStatus
          ? this.signAfterData
          : this.signBeforeData
        this.spliceItem(findData, data, true)
        if (!this.checkTypeStatus && data.syncStatus) {
          this.spliceItem(this.signBeforeData, data, true)
        }
        if (data.type === 'award') {
          const me = this
          setTimeout(() => {
            me.$refs[`award${data.createDate}`] &&
              me.$refs[`award${data.createDate}`][0].initGood()
          }, 100)
        }
      }
    },
    // 预报保存
    saveData() {
      const me = this
      me.$refs.RuleTemplate.validate()
      me.$refs.ShareTemplate.validate()
      me.$refs.SignCircleTemplate.validate()
      setTimeout(() => {
        if (
          !me.$refs.SignCircleTemplate.validateStatus ||
          !me.$refs.RuleTemplate.validateStatus ||
          !me.$refs.ShareTemplate.validateStatus
        ) {
          me.backStatus = false
        } else {
          me.backStatus = true
        }
        me.saveListData = {
          ruleData: me.$refs.RuleTemplate.ruleForm,
          shareData: me.$refs.ShareTemplate.ruleForm,
          circleData: me.$refs.SignCircleTemplate.ruleForm,
          signAfterData: me.signAfterData, // 报名前模块
          signBeforeData: me.signBeforeData // 报名后模块
        }
      }, 100)
    },
    // 设置数据
    initData(JsonData) {
      this.$refs.RuleTemplate.init(JsonData.ruleData)
      this.$refs.ShareTemplate.init(JsonData.shareData)
      this.$refs.SignCircleTemplate.init(JsonData.circleData)
      this.signAfterData = JsonData.signAfterData || []
      this.signBeforeData = JsonData.signBeforeData || []
      this.checkType = '报名前模块'
      this.updateCheckType()
    },
    // 重置数据
    resert() {
      this.$refs.RuleTemplate.init()
      this.$refs.ShareTemplate.init()
      this.$refs.SignCircleTemplate.init()
    },
    // 获取排序列表
    refreshImageList() {
      this.checkTypeStatus
        ? (this.signBeforeData = this.showData)
        : (this.signAfterData = this.showData)
    },
    // 关闭抽屉
    handleClose(done) {
      this.drawer = false
      done()
      this.activeData = {}
    }
  }
}
</script>

<style lang="scss" scoped>
.show-list {
  height: 65vh;
}
.left-content {
  height: 60vh;
  width: 420px;
  margin: 10px;
  border: 1px solid #ccc;
  border-radius: 10px;
  overflow-y: scroll;
  padding: 10px;
}
.right-content {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 10px;
}
</style>
