<template>
  <div class="edit-countdown">
    <div class="title">倒计时配置</div>
    <el-form ref="ruleFormRef" :inline="true" :model="ruleForm" :rules="rules">
      <el-form-item label="同步到报名后" required v-if="props.showSync">
        <el-switch
          v-model="ruleForm.syncStatus"
          active-color="#13ce66"
          inactive-color="#ff4949"
          @change="changeStatus(ruleForm)"
        />
      </el-form-item>
      <br />
      <el-form-item label="模块背景色" prop="BgColor" required>
        <div style="width: 50px">
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.BgColor"
          />
        </div>
      </el-form-item>
      <br />
      <el-form-item label="报名文案颜色" prop="titleColor" required>
        <div style="width: 50px">
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.titleColor"
          />
        </div>
      </el-form-item>
      <el-form-item label="副标题文案颜色" prop="subtitleColor" required>
        <div style="width: 50px">
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.subtitleColor"
          />
        </div>
      </el-form-item>
      <br />
      <el-form-item label="输入框背景色" prop="inputBgColor" required>
        <div style="width: 50px">
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.inputBgColor"
          />
        </div>
      </el-form-item>
      <el-form-item label="输入文案颜色" prop="inputColor" required>
        <div style="width: 50px">
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.inputColor"
          />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </div>
  </div>
</template>

<script setup>
import { predefineColors } from './config'

const emit = defineEmits(['syncData', 'closeActiveData'])

const props = defineProps({
  showSync: {
    typeof: Boolean,
    default: true
  }
})

const ruleForm = ref({
  type: 'countdown',
  BgColor: '', // 模块背景色
  titleColor: '', // 报名文案颜色
  subtitleColor: '', // 副标题文案颜色
  inputBgColor: '', // 输入框背景色
  inputColor: '' // 输入文案颜色
})
const ruleFormRef = ref(null)

let bgEnter = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('颜色必填'))
  }
  callback()
}
const rules = {
  BgColor: [{ validator: bgEnter, trigger: 'change' }],
  titleColor: [{ validator: bgEnter, trigger: 'change' }],
  subtitleColor: [{ validator: bgEnter, trigger: 'change' }],
  inputBgColor: [{ validator: bgEnter, trigger: 'change' }],
  inputColor: [{ validator: bgEnter, trigger: 'change' }]
}

const init = (data) => {
  ruleForm.value = data
}

const changeStatus = () => {
  emit('syncData', ruleForm.value)
}

const handleClose = () => {
  emit('closeActiveData', {}, false)
}

const confirm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      emit('closeActiveData', ruleForm.value, true)
    }
  })
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.title {
  font-weight: 600;
}
</style>
