<template>
  <div class="edit-award">
    <div class="title">图片配置</div>
    <el-form ref="ruleForm" :model="ruleForm">
      <el-form-item label="同步到报名后" required v-if="showSync">
        <el-switch
          v-model="ruleForm.syncStatus"
          active-color="#13ce66"
          inactive-color="#ff4949"
          @change="changeStatus(ruleForm)"
        />
      </el-form-item>
      <el-form-item label="每排数量">
        <el-select v-model="ruleForm.num" placeholder="请选择每排数量" required>
          <el-option
            v-for="(value, index) in numList"
            :key="index"
            :label="value"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="顺序">
        <el-radio-group v-model="ruleForm.isSort" required>
          <el-radio-button
            v-for="(city, index) in sortList"
            :label="city"
            :key="city"
            >{{ index }}</el-radio-button
          >
        </el-radio-group>
      </el-form-item>
      <p class="title">
        编辑奖品
        <el-button class="fl-right" @click="addAward()">新增商品</el-button>
      </p>
      <el-row :gutter="24">
        <el-col :span="3" class="text-center"
          ><span class="text-align">索引</span></el-col
        >
        <el-col :span="7" class="text-center"
          ><span class="text-align">奖品图片</span></el-col
        >
        <el-col :span="8" class="text-center"
          ><span class="text-align">跳转链接</span></el-col
        >
        <el-col :span="6" class="text-center"
          ><span class="text-align">操作</span></el-col
        >
      </el-row>
      <draggable
        v-if="ruleForm.goodList && ruleForm.goodList.length"
        item-key="imgUrl"
        v-model="ruleForm.goodList"
      >
        <template #item="{ element, index }">
          <el-row class="item" @click="goDetail(element)" :gutter="24">
            <el-col :span="3" class="text-center">
              <div class="text-align">{{ index }}</div>
            </el-col>
            <el-col :span="7" class="text-center">
              <img
                :src="element.imgUrl"
                alt
                style="display: inline-block; width: 60px; height: 60px"
              />
            </el-col>
            <el-col :span="8" class="text-center link-url">
              <div>{{ element.linkUrl }}</div>
            </el-col>
            <el-col :span="6" class="text-center">
              <el-button
                type="primary"
                link
                size="small"
                @click.stop="copyItem(element)"
                >复制</el-button
              >
              <el-button
                type="primary"
                link
                size="small"
                @click.stop="delItem(element, index)"
                >删除</el-button
              >
            </el-col>
          </el-row>
        </template>
      </draggable>
      <div class="text-center">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
      <DialogEditAward ref="DialogEditAward" @updateAward="updateAward" />
    </el-form>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import DialogEditAward from './dialog-edit-award.vue'
import draggable from 'vuedraggable'
import { deepCopy } from '@/utils'
export default {
  name: 'EditAward',
  components: {
    draggable,
    DialogEditAward
  },
  props: {
    showSync: {
      typeof: Boolean,
      default: true
    }
  },
  data() {
    return {
      numList: [1, 2, 3], // 每排数量
      sortList: {
        不打乱: false,
        每次随机打乱: true
      },
      ruleForm: {}
    }
  },
  methods: {
    init(data) {
      this.ruleForm = data
    },
    // 同步开关
    changeStatus() {
      $emit(this, 'syncData', this.ruleForm)
    },
    // 新增奖品
    addAward() {
      this.$refs.DialogEditAward.init()
    },
    // 编辑奖品
    goDetail(data) {
      this.$refs.DialogEditAward.init(deepCopy(data))
    },
    // 更新奖品信息
    updateAward(data) {
      const findIndex = this.ruleForm.goodList.findIndex((item) => {
        return item.createDate === data.createDate
      })
      const findIndexStatus = findIndex > -1
      this.ruleForm.goodList.splice(
        findIndexStatus ? findIndex : this.ruleForm.goodList.length,
        findIndexStatus ? 1 : 0,
        data
      )
    },
    // 复制对象
    copyItem(data) {
      const addItem = {
        ...deepCopy(data),
        createDate: Math.floor(new Date())
      }
      const findIndex = this.ruleForm.goodList.findIndex((item) => {
        return item.createDate === data.createDate
      })
      this.ruleForm.goodList.splice(findIndex, 0, addItem)
    },
    // delItem
    delItem(data, index) {
      this.ruleForm.goodList.splice(index, 1)
      this.$message.success('删除成功')
    },
    // 关闭
    handleClose() {
      $emit(this, 'closeActiveData', {}, false)
    },
    // 确认
    confirm() {
      const me = this
      me.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          $emit(me, 'closeActiveData', this.ruleForm, true)
        } else {
          me.$message.error('请添加图片')
        }
      })
    }
  },
  emits: ['syncData', 'closeActiveData']
}
</script>

<style lang="scss" scoped>
.edit-award {
  font-size: 11px;
  color: #ccc;
  height: 100vh;
  overflow-y: scroll;
  .fl-right {
    position: relative;
    top: -10px;
  }
  .title {
    color: #000;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
  }
  .text-center {
    margin-top: 10px;
    font-size: 18px;
    color: #333333;
  }
  .text-align {
    font-size: 15px;
    color: #000;
    text-align: center;
  }
  .link-url {
    word-break: break-all;
  }
}
</style>
