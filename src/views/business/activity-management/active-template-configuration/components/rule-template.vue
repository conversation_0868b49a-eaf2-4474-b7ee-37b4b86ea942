<template>
  <div class="rule-template">
    <div class="title">
      规则配置
      <el-switch
        v-model="ruleForm.isOpen"
        width="60"
        inline-prompt
        active-text="打开"
        inactive-text="关闭"
      />
    </div>
    <el-form
      v-if="ruleForm.isOpen"
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
    >
      <el-form-item label="标题" prop="ruleTitle">
        <el-input v-model="ruleForm.ruleTitle" style="width: 250px" />
      </el-form-item>
      <el-form-item label="背景颜色">
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.ruleBg"
        />
      </el-form-item>
      <el-form-item label="字体颜色">
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.ruleColor"
        />
      </el-form-item>
      <el-form-item label="规则正文">
        <div style="width: 800px">
          <quill-editor
            contentType="html"
            ref="prizeQuillEditor"
            v-model:content="ruleForm.ruleDesc"
            :options="editorOption"
            @blur="quillEditorBlur"
          />
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
const validateStatus = ref(false)
const ruleForm = reactive({
  isOpen: false, // 编辑状态
  ruleTitle: '', // 标题
  ruleBg: '', // 背景颜色
  ruleColor: '', // 字体颜色
  ruleDesc: '' // 规则配置
})
const predefineColors = [
  // 颜色拾取器的自定义颜色
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsv(51, 100, 98)',
  'hsva(120, 40, 94, 0.5)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577'
]
const editorOption = {
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
      // ['blockquote', 'code-block'], // 引用  代码块
      [{ header: 1 }, { header: 2 }], // 1、2 级标题
      [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
      // [{ script: 'sub' }, { script: 'super' }], // 上标/下标
      [{ indent: '-1' }, { indent: '+1' }], // 缩进
      // [{ direction: 'rtl' }], // 文本方向
      // [{ size: ['12', '14', '16', '18', '20', '22', '24', '28', '32', '36'] }], // 字体大小
      [{ header: [1, 2, 3, 4, 5, 6] }], // 标题
      [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
      // [{ font: ['songti'] }], // 字体种类
      [{ align: [] }], // 对齐方式
      // ['clean'], // 清除文本格式
      ['link'] // 链接、图片、视频
    ]
  }
}
const rules = {
  ruleTitle: [{ required: true, message: '请填写标题', trigger: 'blur' }]
}
const ruleFormRef = ref()
const prizeQuillEditor = ref()

const init = (ruleData) => {
  const data = ruleData || {}
  Object.keys(ruleForm).forEach((key) => {
    if (key === 'isOpen') {
      ruleForm[key] = !!data[key]
    } else if (key === 'ruleTitle') {
      ruleForm[key] = data[key] || '活动规则'
    } else {
      ruleForm[key] = data[key] || ''
    }
  })
  nextTick(() => {
    if (prizeQuillEditor.value) {
      prizeQuillEditor.value.getQuill().getModule('toolbar')
    }
  })
}

const quillEditorBlur = () => {
  const divNode = document.createElement('div')
  divNode.innerHTML = ruleForm.ruleDesc
  if (!divNode.innerText) {
    ruleForm.ruleDesc = ''
  }
}

const validate = () => {
  if (ruleForm.isOpen) {
    ruleFormRef.value.validate((valid) => {
      validateStatus.value = valid
    })
  } else {
    validateStatus.value = true
  }
}

defineExpose({
  init,
  validate,
  validateStatus,
  ruleForm
})
</script>

<style lang="scss" scoped></style>
