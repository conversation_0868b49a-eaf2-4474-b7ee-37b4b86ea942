<template>
  <div class="sign-up-template">
    <div class="title">
      <span class="mr10">{{ title }}</span>
      <el-switch
        v-model="ruleForm.isOpen"
        width="60"
        inline-prompt
        active-text="打开"
        inactive-text="关闭"
      />
    </div>
    <el-form
      v-if="ruleForm.isOpen"
      ref="ruleForm"
      :inline="true"
      :model="ruleForm"
      :rules="rules"
    >
      <el-form-item label="分享标题" prop="shareTitle">
        <el-input
          v-model="ruleForm.shareTitle"
          maxlength="20"
          style="width: 250px"
          placeholder="请输入分享标题，最多20个字"
        />
      </el-form-item>
      <el-form-item label="分享图片：" prop="shareImage">
        <el-input
          v-model="ruleForm.shareImage"
          style="width: 350px"
          placeholder="请选择图片"
        />
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccessTitleimageShore"
          name="titlefile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <el-button type="primary" link>选择图片</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="分享描述" prop="shareDesc">
        <el-input
          v-model="ruleForm.shareDesc"
          maxlength="50"
          style="width: 350px"
          placeholder="请输入分享描述，最多50个字"
        />
      </el-form-item>
      <br />
      <el-form-item label="按钮文案" prop="shareButtonText">
        <el-input
          v-model="ruleForm.shareButtonText"
          maxlength="20"
          style="width: 250px"
          placeholder="请输入按钮文案"
        />
      </el-form-item>
      <br />
      <el-form-item label="按钮背景色" prop="shareButtonBg" required>
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.shareButtonBg"
          style="width: 250px"
        ></el-color-picker>
      </el-form-item>
      <br />
      <el-form-item label="按钮字体颜色" prop="shareButtonColor" required>
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.shareButtonColor"
          style="width: 250px"
        ></el-color-picker>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'ShareTemplate',
  props: {},
  data() {
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('分享图片必须上传'))
      }
      callback()
    }
    let bgEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('分享按钮背景色必填'))
      }
      callback()
    }
    let colorEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('分享按钮字体颜色必填'))
      }
      callback()
    }
    return {
      validateStatus: false,
      title: '分享配置',
      ruleForm: {
        isOpen: true,
        shareTitle: '', // 分享标题
        shareImage: '', // 分享图片
        shareDesc: '', // 分享描述
        shareButtonText: '', // 按钮文案
        shareButtonBg: '#FFFFFF', // 分享按钮背景色
        shareButtonColor: '#000000' // 分享按钮字体颜色
      },
      predefineColors: [
        // 颜色拾取器的自定义颜色
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],
      rules: {
        shareTitle: [
          { required: true, message: '请填写分享标题', trigger: 'blur' }
        ],
        shareImage: [
          { required: true, validator: imgEnter, trigger: 'change' }
        ],
        shareDesc: [
          { required: true, message: '请填写分享描述', trigger: 'blur' }
        ],
        shareButtonText: [
          { required: true, message: '请填写按钮文案', trigger: 'blur' }
        ],
        shareButtonBg: [{ validator: bgEnter, trigger: 'change' }],
        shareButtonColor: [{ validator: colorEnter, trigger: 'change' }]
      }
    }
  },
  methods: {
    init(shareData) {
      this.ruleForm = shareData
        ? shareData
        : {
            shareTitle: '', // 分享标题
            shareImage: '', // 分享图片
            shareDesc: '', // 分享描述
            shareButtonText: '', // 按钮文案
            shareButtonBg: '#FFFFFF', // 分享按钮背景色
            shareButtonColor: '#000000' // 分享按钮字体颜色
          }
      if (this.ruleForm.isOpen !== false) {
        this.ruleForm.isOpen = true
      }
    },
    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 活动封面
    onSuccessTitleimageShore(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.shareImage = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    validate() {
      const me = this
      me.validateStatus = false
      if (me.ruleForm.isOpen) {
        this.$refs['ruleForm'].validate((valid) => {
          me.validateStatus = valid
        })
      } else {
        me.validateStatus = true
      }
    }
  }
}
</script>
