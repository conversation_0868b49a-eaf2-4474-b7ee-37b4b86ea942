<template>
  <div v-loading="loading" class="ActiveConfiguration">
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <el-form-item label="活动名称">
        <el-input
          v-model="ruleForm.activityName"
          type="text"
          placeholder="请输入活动名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="ruleForm.status" placeholder="请选择">
          <el-option label="全部" value />
          <el-option
            v-for="(item, index) in mapStatus"
            :key="index"
            :label="item"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="活动类型">
        <el-select v-model="ruleForm.activityTypes" placeholder="请选择">
          <el-option label="全部" value="4,5" />
          <el-option
            v-for="(item, index) in mapActivityType"
            :key="index"
            :label="item"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="日期">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          v-model="daterange"
          style="width: 360px"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList()">搜索</el-button>
        <el-button @click="resize()">重置</el-button>
        <el-button @click="newAct()">新建活动</el-button>
      </el-form-item>
    </el-form>
    <!--活动列表-->
    <div>
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          align="center"
          prop="activityId"
          label="活动ID"
          width="80"
        />
        <el-table-column
          align="center"
          prop="activityName"
          label="活动名称"
          width="180"
        />
        <el-table-column
          align="center"
          prop="activitytype"
          label="活动类型"
          width="180"
        >
          <template v-slot="scope">{{
            mapActivityType[scope.row.activityType]
          }}</template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="joinNum"
          label="参与人数"
          width="180"
        />
        <el-table-column align="center" label="活动状态">
          <template v-slot="scope">{{ mapStatus[scope.row.status] }}</template>
        </el-table-column>
        <el-table-column align="center" label="开始时间">
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.beginTime)
          }}</template>
        </el-table-column>
        <el-table-column align="center" label="结束时间">
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.endTime)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="status"
          align="center"
          label="是否有效"
          width="100"
        >
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.rowStatus"
              @change="changeStatus(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template v-slot="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click="newAct(scope.row)"
              >活动配置</el-button
            >
            <el-button
              v-clipboard:copy="clipboardCopy(scope.row)"
              v-clipboard:success="clipboardSuccess"
              type="primary"
              link
              size="small"
              >拷贝链接</el-button
            >
            <el-button type="primary" link @click="lookedetail(scope.row)"
              >预览</el-button
            >
            <el-button type="primary" link @click="copyAdvert(scope.row)"
              >复制</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--分页-->
    <el-pagination
      v-if="total"
      :total="total"
      :current-page="page"
      layout="total, prev, pager, next, jumper"
      @current-change="currentChange"
    />
    <choose-iframe ref="ChooseIframe" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ChooseIframe from '@/components/Dialog/ChooseIframe.vue'
import clipboard from '@/directive/clipboard/index.js'
import {
  activityGetListFactoryActs,
  activityUpdateActStatus
} from '@/api/activeConfiguration'
export default {
  data() {
    return {
      mapStatus: {
        0: '已失效',
        1: '未开始',
        2: '正在进行',
        3: '已结束'
        // 4: '待审核',
        // 5: '审核失败',
        // 6: '已删除'
      },
      mapActivityType: {
        4: '报名活动模板',
        5: '主会场活动'
      },
      multipleSelection: [],
      tableData: [],
      ruleForm: {
        activityName: '',
        status: '',
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        activityTypes: '4,5',
        limit: 10
      },
      page: 1,
      total: 0,
      loading: false,
      dayjs
    }
  },
  name: 'ActiveTemplateConfiguration',
  directives: {
    clipboard
  },
  components: {
    ChooseIframe
  },
  computed: {
    daterange: {
      get() {
        if (this.ruleForm.beginTime && this.ruleForm.endTime) {
          return [this.ruleForm.beginTime, this.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginTime = value[0]
          this.ruleForm.endTime = value[1]
        } else {
          this.ruleForm.beginTime = ''
          this.ruleForm.endTime = ''
        }
      }
    }
  },
  activated() {
    this.getList()
  },
  methods: {
    currentChange(page) {
      this.page = page
      this.getList()
    },
    // 重置
    resize() {
      this.ruleForm = {
        activityName: '',
        status: '',
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        activityTypes: '4,5',
        limit: 10
      }
      this.page = 1
      this.getList()
    },
    // 新建活动
    newAct(row) {
      this.$router.push({
        name: 'ActiveTemplateConfigurationDetail',
        query: {
          activityId: (row && row.activityId) || '',
          templetId: (row && row.templetId) || ''
        }
      })
    },
    // 改变是否有效
    changeStatus(item) {
      const me = this
      let text = ''
      let buttonTitle = ''
      if (item.status === 0) {
        text = '设置有效后，活动可在app查看，请确认是否置为有效?'
        buttonTitle = '有效'
      } else {
        text = '设置失效后，活动无法在app查看，请确认是否置为失效?'
        buttonTitle = '无效'
      }
      me.$confirm(text, '活动状态', {
        confirmButtonText: buttonTitle,
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          activityUpdateActStatus({
            activityIds: item.activityId,
            status: !item.rowStatus ? 0 : 2
          })
            .then((response) => {
              if (response.data.code === 0) {
                me.$message.success('修改成功')
                me.getList()
              }
            })
            .catch((_) => {
              // me.$message.error('修改失败')
            })
        })
        .catch((err) => {
          me.$message({
            type: 'info',
            message: (err && err.message) || '已取消操作'
          })
        })
    },
    // 配置模板  根据模板id选择弹框
    editConfigAct(item) {
      if ([1, 2].indexOf(item.templetId) === -1) {
        return this.$message.error('未有该类型模板')
      }
      this.$refs[`template${item.templetId}`].init(item)
      this.$refs[`template${item.templetId}`].dialogVisible = true
    },
    editAct(item) {
      this.$refs['ActInfo'].init(item.activityId)
      this.$refs['ActInfo'].dialogVisible = true
    },
    getList() {
      this.loading = true
      activityGetListFactoryActs({
        ...this.ruleForm,
        page: this.page
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.tableData = response.data.data.list || []
            this.tableData.map((item) => {
              item.rowStatus = !!item.status
            })
            this.total = response.data.data.total
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .finally((_) => {
          this.loading = false
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    updateDetail(item) {
      this.getList()
      // 在模板配置中更新活动时间
      if (!item.activityId) {
        return
      }
      const templetId = this.tableData.find(
        (_) => _.activityId === item.activityId
      ).templetId
      console.log(templetId)
      this.$refs[`template${templetId}`].init(item).then((_) => {
        this.$refs[`template${templetId}`].saveResult()
      })
    },
    // 复制
    clipboardSuccess() {
      this.$message({
        message: '拷贝成功',
        type: 'success',
        duration: 1500
      })
    },
    // 预览
    lookedetail(data) {
      const url1 = `https://wap.58moto.com/zt/2022/4/1/subject-template-new?activityId=${data.activityId}&share=true`
      const url2 = `https://wap.58moto.com/zt/2022/4/27/main-venue-template?activityId=${data.activityId}&share=true`
      this.$refs.ChooseIframe.init(data.templetId === 3 ? url1 : url2)
    },
    // 复制
    copyAdvert(row) {
      this.$router.push({
        name: 'ActiveTemplateConfigurationDetail',
        query: {
          activityId: row.activityId,
          templetId: row.templetId,
          copy: 1
        }
      })
    },
    clipboardCopy(data) {
      const url1 = `https://wap.58moto.com/zt/2022/4/1/subject-template-new?activityId=${data.activityId}&share=true`
      const url2 = `https://wap.58moto.com/zt/2022/4/27/main-venue-template?activityId=${data.activityId}&share=true`
      return data.templetId === 3 ? url1 : url2
    }
  }
}
</script>

<style lang="scss" scoped>
.ActiveConfiguration {
  padding: 20px;
  .el-pagination {
    text-align: center;
    margin-top: 20px;
  }
  .el-date-editor.el-range-editor {
    width: 400px;
  }
}
</style>
