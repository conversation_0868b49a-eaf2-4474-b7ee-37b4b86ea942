<template>
  <div class="activeShop">
    <el-form
      v-if="isInitForm"
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      :inline="true"
      label-width="120px"
    >
      <!-- 厂商基础信息 -->
      <div class="modules">
        <div style="display: flex">
          <div class="title">
            厂家基础信息
            <el-switch
              style="margin-left: 10px"
              active-text="仅抽奖"
              inactive-text="全部"
              v-model="ruleForm.factoryConfigInfoShow"
            />
          </div>
          <el-button
            style="margin-left: 800px"
            type="primary"
            plain
            @click="eventTemplatePreview"
            >活动模板预览</el-button
          >
        </div>
        <el-form-item label="活动名称：" prop="factoryConfigInfo.activeName">
          <el-input
            v-model="ruleForm.factoryConfigInfo.activeName"
            style="width: 250px"
            placeholder="请输入活动名称"
          />
        </el-form-item>
        <el-form-item label="落地页标题：" prop="factoryConfigInfo.pageName">
          <el-input
            v-model="ruleForm.factoryConfigInfo.pageName"
            style="width: 250px"
            placeholder="请输入落地页标题"
          />
        </el-form-item>

        <el-form-item label="品牌：" v-if="!ruleForm.factoryConfigInfoShow">
          <el-select
            v-model="ruleForm.factoryConfigInfo.brandId"
            :remote-method="searchBrandList"
            :loading="brandloading"
            placeholder="请输入品牌名称"
            filterable
            remote
            clearable
            style="width: 200px"
            @change="addLabel"
          >
            <el-option
              v-for="item in brandList"
              :key="item.labelId"
              :label="item.labelName"
              :value="item.labelId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="背景颜色：" v-if="!ruleForm.factoryConfigInfoShow">
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.factoryConfigInfo.bgColor"
            style="width: 250px"
          ></el-color-picker>
        </el-form-item>

        <el-form-item label="活动开始时间：" prop="factoryConfigInfo.beginTime">
          <el-date-picker
            v-model="ruleForm.factoryConfigInfo.beginTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择开始日期"
            :default-time="$dayjs('00:00:00', 'hh:mm:ss').toDate()"
          />
        </el-form-item>

        <el-form-item label="活动结束时间：" prop="factoryConfigInfo.endTime">
          <el-date-picker
            v-model="ruleForm.factoryConfigInfo.endTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择结束日期"
            :default-time="$dayjs('00:00:00', 'hh:mm:ss').toDate()"
          />
        </el-form-item>

        <template v-if="!ruleForm.factoryConfigInfoShow">
          <el-form-item label="标题图片：" prop="factoryConfigInfo.TitleImage">
            <el-input
              v-model="ruleForm.factoryConfigInfo.TitleImage"
              style="width: 200px"
              placeholder="请选择图片"
            />
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest"
              :on-success="onSuccessTitleimage"
              name="titlefile"
              style="display: inline-block"
              class="avatar-uploader"
              action
            >
              <el-button type="primary" link>选择图片</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item
            label="标题图片跳转："
            prop="factoryConfigInfo.activeJumpUrl"
          >
            <el-input
              v-model="ruleForm.factoryConfigInfo.activeJumpUrl"
              style="width: 250px"
              placeholder="请输入链接"
            /><span>*未设置不跳转</span>
          </el-form-item>
        </template>
      </div>
      <template v-if="!ruleForm.factoryConfigInfoShow">
        <div class="modules">
          <div class="title">活动详情图片</div>
          <div>
            <draggable
              v-model="fileList"
              item-key="name"
              @end="refreshImageList"
            >
              <template #item="{ element }">
                <span class="image-wrap">
                  <img :src="element.url" class="image" alt />
                  <div class="operation">
                    <!-- <el-upload
                      :http-request="httpRequest"
                      :on-success="amend"
                      :show-file-list="false"
                      name="activefile"
                      style="display: inline-block"
                      action
                    >
                      <el-button
                        type="primary"
                        :icon="IconEdit"
                        circle
                        @click="substitute = element"
                      />
                    </el-upload> -->
                    <el-button
                      type="primary"
                      :icon="IconEdit"
                      circle
                      @click="openChangeImgDialog(1, fileList, element.name)"
                    />
                    <el-button
                      type="danger"
                      :icon="IconDelete"
                      class="delete"
                      circle
                      @click="handleRemove(element)"
                    />
                  </div>
                </span>
              </template>
              <template #footer>
                <!-- <el-upload
                  :http-request="httpRequestOrder"
                  :on-success="onSuccessTitleimageMore"
                  :show-file-list="false"
                  name="activefile"
                  style="display: inline-block"
                  action
                  multiple
                  list-type="picture-card"
                >
                  <el-button type="primary" link>选择图片</el-button>
                </el-upload> -->
                <div
                  class="el-upload el-upload--picture-card"
                  @click="openChangeImgDialog(1, fileList)"
                >
                  <el-button type="primary" link>选择图片</el-button>
                </div>
              </template>
            </draggable>
          </div>
        </div>

        <!-- 轮播图配置 -->
        <div class="modules">
          <div class="title">轮播图配置</div>
          <CarouseTemplate
            ref="CarouseTemplate"
            :carouseList="ruleForm.carouseList"
          ></CarouseTemplate>
        </div>
        <!-- 视频配置 -->
        <div class="modules">
          <div class="title">视频配置</div>
          <VideoTemplate
            ref="VideoTemplate"
            :videoList="ruleForm.videoList"
          ></VideoTemplate>
        </div>
        <!-- 活动报名 -->
        <div class="modules">
          <div class="title">活动报名</div>
          <div class="text select-car">
            需填写字段 姓名、手机号码、省市
            <el-checkbox
              v-model="ruleForm.signUpConfigInfo.allocation"
              class="checkbox"
              @change="changeAllocation"
              >配置车型</el-checkbox
            >
            <el-form-item label="选择车型" label-width="80px">
              <el-select
                v-model="ruleForm.signUpConfigInfo.carIds"
                :remote-method="searchCarList"
                :loading="carloading"
                :disabled="!ruleForm.signUpConfigInfo.allocation"
                placeholder="请输入车型名称"
                reserve-keyword
                filterable
                remote
                clearable
                multiple
                style="width: 300px"
                @change="addCar"
              >
                <el-option
                  v-for="item in carList"
                  :key="item.goodsId"
                  :label="item.value"
                  :value="item.goodsId"
                />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item label="按钮字体颜色:">
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.signUpConfigInfo.signUpButtonColor"
            ></el-color-picker>
          </el-form-item>
          <el-form-item label="按钮背景颜色:">
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.signUpConfigInfo.signUpButtonBg"
            ></el-color-picker>
          </el-form-item>
          <el-form-item
            label="表单位置："
            prop="signUpConfigInfo.signUpPosition"
          >
            <el-input
              v-model="ruleForm.signUpConfigInfo.signUpPosition"
              style="width: 250px"
              placeholder="请输入表单位置，例如：200,300"
            />
          </el-form-item>
          <el-form-item
            label="按钮展示文字"
            prop="signUpConfigInfo.signUpButtonText"
          >
            <el-input
              v-model="ruleForm.signUpConfigInfo.signUpButtonText"
              style="width: 250px"
              placeholder="请输入按钮文字"
            />
          </el-form-item>
        </div>
        <!-- 分享配置 -->
        <div class="modules">
          <div class="title">分享配置</div>
          <el-form-item label="分享标题：" prop="shareConfigInfo.shareTitle">
            <el-input
              v-model="ruleForm.shareConfigInfo.shareTitle"
              style="width: 250px"
              placeholder="请输入分享名称"
            />
          </el-form-item>
          <el-form-item label="分享地址" prop="shareConfigInfo.shareUrl">
            <el-input
              v-model="ruleForm.shareConfigInfo.shareUrl"
              style="width: 250px"
              placeholder="保存后自动生成"
            />
          </el-form-item>

          <el-form-item label="分享图片：" prop="shareConfigInfo.shareimage">
            <el-input
              v-model="ruleForm.shareConfigInfo.shareimage"
              style="width: 200px"
              placeholder="请选择图片"
            />
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest"
              :on-success="onSuccessTitleimageShore"
              name="titlefile"
              style="display: inline-block"
              class="avatar-uploader"
              action
            >
              <el-button type="primary" link>选择图片</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="分享描述:" prop="shareConfigInfo.shareText">
            <el-input
              :rows="5"
              v-model="ruleForm.shareConfigInfo.shareText"
              style="width: 250px"
              type="textarea"
            />
          </el-form-item>
          <!-- <el-form-item label="分享描述:" prop="delivery">
                        <div style="width: 800px">
                          <quill-editor ref="shareQuillEditor" v-model="ruleForm.shareConfigInfo.shareText" :options="editorOption" />
                          <input ref="sharefileBtn" type="file" hidden accept=".jpg, .png" @change="handleChange($event, 'share')" />
                        </div>
                      </el-form-item> -->
          <br />
          <el-form-item label="'打开摩托范'按钮背景色:" label-width="230px">
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.shareConfigInfo.shareButtonBg"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
          <br />
          <el-form-item label="'打开摩托范'按钮字体颜色:" label-width="230px">
            <el-color-picker
              :predefine="predefineColors"
              v-model="ruleForm.shareConfigInfo.shareButtonColor"
              style="width: 250px"
            ></el-color-picker>
          </el-form-item>
        </div>
      </template>
      <!-- 活动抽奖配置 -->
      <div class="modules">
        <div class="title">活动抽奖配置</div>
        <el-form-item label="背景图片：">
          <el-input
            v-model="ruleForm.prizeConfigInfo.bgimage"
            style="width: 200px"
            placeholder="请选择图片"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequestBg"
            :on-success="onSuccessTitleimageBg"
            name="titlefile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            <el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="抽奖次数配置" prop="prizeConfigInfo.Number">
          <el-input
            v-model="ruleForm.prizeConfigInfo.Number"
            style="width: 250px"
            placeholder="请输入次数"
          />
        </el-form-item>
        <el-form-item label="活动说明:" prop="prizeConfigInfo.prizeText">
          <div style="width: 800px">
            <quill-editor
              contentType="html"
              ref="prizeQuillEditor"
              v-model:content="ruleForm.prizeConfigInfo.prizeText"
              :options="editorOption"
            />
            <input
              ref="prizefileBtn"
              type="file"
              hidden
              accept=".jpg, .png"
              @change="handleChange($event, 'prize')"
            />
          </div>
        </el-form-item>
        <el-form-item label="抽奖模式:" prop="prizeConfigInfo.lotteryMode">
          <el-radio-group
            v-model="ruleForm.prizeConfigInfo.lotteryMode"
            class="ml-4"
          >
            <el-radio label="1" size="large">彩票模式</el-radio>
            <el-radio label="2" size="large">砸金蛋模式</el-radio>
          </el-radio-group>
        </el-form-item>
        <LuckyTemplate
          ref="LuckyTemplate"
          :prizeList="ruleForm.prizeConfigInfo.prizeList"
          :lotteryMode="ruleForm.prizeConfigInfo.lotteryMode"
          moduleId="2"
        ></LuckyTemplate>
      </div>
      <el-form-item align="center">
        <el-button type="primary" @click="submitForm">立即保存</el-button>
        <el-button @click="handleClose()">取消</el-button>
      </el-form-item>
    </el-form>
    <EventTemplatePreview ref="EventTemplatePreview"></EventTemplatePreview>
    <ChangeImgDialog ref="changeImgDialogRef" @updateImgList="updateImgList" />
  </div>
</template>

<script>
import { Edit as IconEdit, Delete as IconDelete } from '@element-plus/icons-vue'
import { searchBrand } from '@/api/articleModule'
import { searchCarList } from '@/api/garage'
import { mapGetters } from 'vuex'
import LuckyTemplate from '../components/luckyTemplate.vue'
import CarouseTemplate from '../components/carouseTemplate.vue'
import VideoTemplate from '../components/videoTemplate.vue'
import ChangeImgDialog from '../components/changeImgDialog.vue'
import EventTemplatePreview from '../components/eventTemplatePreview.vue'
import {
  saveActAndTempletPrize,
  GetTempletDetail
} from '@/api/activeConfiguration'
import draggable from 'vuedraggable'

export default {
  data() {
    return {
      loading: true,
      // 初始化是否完成
      isInitForm: false,
      dialogVisible: false,
      predefineColors: [
        // 颜色拾取器的自定义颜色
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],
      rules: {
        'factoryConfigInfo.activeName': [
          {
            required: true,
            message: '请填写活动名称',
            trigger: 'blur'
          }
        ],
        'factoryConfigInfo.pageName': [
          {
            required: true,
            message: '请填写落地页标题',
            trigger: 'change'
          }
        ],
        'factoryConfigInfo.beginTime': [
          {
            required: true,
            message: '请选择活动开始时间',
            trigger: 'blur'
          }
        ],
        'factoryConfigInfo.endTime': [
          {
            required: true,
            message: '请选择活动结束时间',
            trigger: 'blur'
          }
        ]
      },
      ruleForm: {},
      editorOption: {
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
            // ['blockquote', 'code-block'], // 引用  代码块
            [{ header: 1 }, { header: 2 }], // 1、2 级标题
            [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
            // [{ script: 'sub' }, { script: 'super' }], // 上标/下标
            [{ indent: '-1' }, { indent: '+1' }], // 缩进
            // [{ direction: 'rtl' }], // 文本方向
            // [{ size: ['12', '14', '16', '18', '20', '22', '24', '28', '32', '36'] }], // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6] }], // 标题
            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
            // [{ font: ['songti'] }], // 字体种类
            [{ align: [] }], // 对齐方式
            // ['clean'], // 清除文本格式
            ['link', 'image', 'video'] // 链接、图片、视频
          ]
        }
      },
      // 存储编辑传递活动id
      activityId: '',
      fileList: [],
      // 替换相关
      substitute: {},
      // 位置数组
      positionArr: [],
      // 数据正常，允许提交
      allowStatus: true,
      // /保存奖项列表-暂存区
      prizeList: [],
      // /保存轮播图列表-暂存区
      carouseList: [],
      // /保存视频列表-暂存区
      videoList: [],
      // 活动模板预览
      previewShow: false,
      brandList: [],
      brandloading: false,
      searchBrandValue: '',
      carList: [],
      carloading: false,
      searchCarValue: '',
      IconEdit: markRaw(IconEdit),
      IconDelete: markRaw(IconDelete)
    }
  },
  name: 'ActiveAdTemplateDetail',
  components: {
    LuckyTemplate,
    CarouseTemplate,
    VideoTemplate,
    EventTemplatePreview,
    draggable,
    ChangeImgDialog
  },
  computed: {
    ...mapGetters(['uid'])
  },
  activated() {
    // 初始化活动配置信息
    this.activityId = (this.$route.query && this.$route.query.activityId) || ''
    this.init()
  },
  methods: {
    // 活动模板预览
    eventTemplatePreview() {
      this.previewShow = true
      this.submitForm()
    },
    //   初始化
    async init() {
      const me = this
      this.isInitForm = false
      this.loading = false
      this.ruleForm = {
        factoryConfigInfoShow: false,
        factoryConfigInfo: {
          activeName: '', // 活动名称
          brandId: '', // 品牌id
          brand: {}, // 品牌
          bgColor: '#FFFFFF', // 背景颜色
          beginTime: '', // 活动开始时间
          endTime: '', // 活动结束时间
          TitleImage: '', // 标题图片
          activeJumpUrl: '', // 标题图片跳转路径
          fileList: [] // 多张图片列表
        },
        shareConfigInfo: {
          shareTitle: '', // 分享标题
          shareUrl: '', // 分享地址
          shareimage: '', // 分享图片
          shareText: '', // 分享描述
          shareButtonBg: '#FFFFFF', // 分享按钮背景色
          shareButtonColor: '#000000' // 分享按钮字体颜色
        },
        signUpConfigInfo: {
          allocation: false,
          carIds: [], // 车型id
          carList: [],
          signUpButtonBg: '#FECF24', // 报名按钮背景色
          signUpButtonColor: '#000000', // 报名按钮字体颜色
          signUpPosition: '0', // 表单位置
          signUpButtonText: '' // 按钮文字
        },
        prizeConfigInfo: {
          bgimage: '', // 背景图片
          Number: '', // 抽奖次数
          prizeText: '', // 抽奖说明
          prizeList: [] // 奖项列表
        },
        carouseList: [], // 轮播图列表
        videoList: [] // 视频列表
      }
      // 初始化编辑器
      me.$nextTick(() => {
        const quillEditors = ['share,prize']
        quillEditors.map(function (value) {
          if (me.$refs[`${value}QuillEditor`]) {
            // personQuillEditor、clubQuillEditor、shownQuillEditor、hideQuillEditor改成自己的
            me.$refs[`${value}QuillEditor`]
              .getQuill()
              .getModule('toolbar')
              .addHandler('image', me[`${value}ImgHandler`])
            // 这里初始化，劫持toolbar的image的handler方法，在mounted中处理
          }
        })
      })

      // 编辑状态时调取配置信息
      if (me.activityId) {
        await GetTempletDetail({
          activityId: me.activityId,
          templetId: '2'
        }).then((response) => {
          if (response.data.code === 0) {
            const templetJson = JSON.parse(response.data.data.templetJson)
            console.log(templetJson, 'templetJson')
            me.ruleForm.factoryConfigInfoShow =
              templetJson.factoryConfigInfoShow
            me.ruleForm.factoryConfigInfo = templetJson.factoryConfigInfo
            me.fileList = templetJson.factoryConfigInfo.fileList
            me.ruleForm.prizeConfigInfo = templetJson.prizeConfigInfo
            me.ruleForm.shareConfigInfo = templetJson.shareConfigInfo
            me.ruleForm.shareConfigInfo.shareUrl = me.$route.query.copy
              ? ''
              : `https://wap.58moto.com/zt/2021/5/7/big-clientele?activityId=${me.activityId}&share=true`
            me.ruleForm.signUpConfigInfo = templetJson.signUpConfigInfo
            me.ruleForm.carouseList = templetJson.carouseList || []
            me.ruleForm.videoList = templetJson.videoList || []
            me.ruleForm.prizeConfigInfo.lotteryMode =
              me.ruleForm.prizeConfigInfo.lotteryMode || '1'
            me.prizeList = me.$route.query.copy
              ? this.copyPrizeListDeal(me.ruleForm.prizeConfigInfo.prizeList)
              : me.ruleForm.prizeConfigInfo.prizeList
            me.isInitForm = true
            me.brandList = []
            if (templetJson.factoryConfigInfo.brand) {
              me.brandList.push(templetJson.factoryConfigInfo.brand)
            }
            me.carList = templetJson.signUpConfigInfo.carList || []
          }
        })
      } else {
        me.isInitForm = true
      }
    },
    copyPrizeListDeal(data) {
      console.log('data==', data)
      const arr = []
      data &&
        data.length > 0 &&
        data.map((item) => {
          delete item.prizeId
          arr.push(item)
        })
      return arr
    },

    // 提交表单
    async submitForm() {
      const me = this
      // 情况位置信息
      me.positionArr = []
      me.positionArr.push(me.ruleForm.signUpConfigInfo.signUpPosition)
      // 允许提交数据
      me.allowStatus = true
      // 获取奖项数据
      const prizeList = me.$refs.LuckyTemplate.arrList || []
      await me.dealPrizeList(prizeList)
      // 获取轮播图列表数据
      const carouseList = me.$refs.CarouseTemplate?.arrList || []
      await me.dealCarouseList(carouseList)
      // 获取视频列表数据
      const videoList = me.$refs.VideoTemplate?.arrList || []
      await me.dealVideoList(videoList)

      if (me.positionArr.length > 0) {
        const positionArrCopy = me.$tools.deepCopy(me.positionArr)
        if (
          Array.from(new Set(positionArrCopy)).length !== me.positionArr.length
        ) {
          me.$message.warning(
            '轮播图，视频，表单位置不能出现重复位置值,请检查数据后在提交！'
          )
          return
        }
      }
      // 预览数据
      if (me.previewShow) {
        const data = {
          signUpPosition: me.ruleForm.signUpConfigInfo.signUpPosition,
          positionArr: me.positionArr,
          fileList: me.ruleForm.factoryConfigInfo.fileList,
          carouseList: me.carouseList,
          videoList: me.videoList
        }
        me.$refs.EventTemplatePreview &&
          me.$refs.EventTemplatePreview.init(data)
        return
      }
      me.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          if (
            me.ruleForm.factoryConfigInfo.endTime >
            me.ruleForm.factoryConfigInfo.beginTime
          ) {
            me.saveResult()
          } else {
            me.$message.warning('活动结束时间要大于开始时间')
            return false
          }
        } else {
          me.$message.warning('有必填项没有填写，请检查')
          return false
        }
      })
    },

    // 奖项列表数据处理
    dealPrizeList(data) {
      console.log(data, 'data=奖项列表数据处理')
      const me = this
      // 删除对象的key值的下标，不会修改原对象的值
      if (data && data.length > 0) {
        let prizeListMark = false
        if (!me.ruleForm.prizeConfigInfo.Number) {
          this.$message.error('抽奖次数没有填写')
          me.allowStatus = false
          return
        }
        me.checkePrizelist().map((item, index) => {
          if (item) {
            this.$message.error('第' + Number(index + 1) + '行奖项配置不完善')
            prizeListMark = true
            me.allowStatus = false
            return
          }
        })
        if (prizeListMark) {
          return
        }
        // 删除对象的key值的下标，不会修改原对象的值
        const prizeArr = []
        data.map((item) => {
          const prizeObj = {}
          let num = 1
          if (item.signPoint >= 10) {
            num = 2
          }
          Object.keys(item).forEach(function (x) {
            let xStr = x
            if (xStr !== 'signPoint') {
              xStr = xStr.substr(0, xStr.length - num)
            }
            prizeObj[xStr] = item[x]
          })
          prizeArr.push(prizeObj)
        })
        // 存储位置数据
        me.prizeList = prizeArr
      } else {
        me.prizeList = []
      }
    },

    // 轮播图列表数据处理
    dealCarouseList(data) {
      // 删除对象的key值的下标，不会修改原对象的值
      const me = this
      const carouseList = []
      console.log(data, 'data-轮播图列表数据处理')
      if (data && data.length > 0) {
        data.map((item) => {
          const carouseObj = {}
          let num = 1
          if (item.signPoint >= 10) {
            num = 2
          }
          Object.keys(item).forEach(function (x) {
            let xStr = x
            if (xStr !== 'signPoint') {
              xStr = xStr.substr(0, xStr.length - num)
            }
            carouseObj[xStr] = item[x]
          })
          carouseList.push(carouseObj)
        })
        carouseList.map((item, index) => {
          if (
            !item.position ||
            !item.image ||
            !(item.image && item.image.length)
          ) {
            const text =
              '第' +
              Number(index + 1) +
              '组轮播图配置缺少参数，请填写后在提交！'
            me.$message.error(text)
            me.allowStatus = false
            return
          }
          // 存储位置数据
          me.positionArr.push(item.position)
        })
        me.carouseList = carouseList
      } else {
        me.carouseList = []
      }
    },
    // 视频配置列表数据处理
    dealVideoList(data) {
      // 删除对象的key值的下标，不会修改原对象的值
      const me = this
      const videoList = []
      console.log(data, 'data-视频配置列表数据处理')
      if (data && data.length > 0) {
        data.map((item) => {
          const videoObj = {}
          let num = 1
          if (item.signPoint >= 10) {
            num = 2
          }
          Object.keys(item).forEach(function (x) {
            let xStr = x
            if (xStr !== 'signPoint') {
              xStr = xStr.substr(0, xStr.length - num)
            }
            videoObj[xStr] = item[x]
          })
          videoList.push(videoObj)
        })
        videoList.map((item, index) => {
          console.log(item, 'item-组视频配置缺少参数')
          if (
            !item.position ||
            !item.video ||
            !(item.video && item.video.link)
          ) {
            const text =
              '第' + Number(index + 1) + '组视频配置缺少参数，请填写后在提交！'
            this.$message.error(text)
            me.allowStatus = false
            return
          }
          // 存储位置数据
          me.positionArr.push(item.position)
        })
        me.videoList = videoList
      } else {
        me.videoList = []
      }
    },
    // 保存数据
    saveResult() {
      const me = this
      // 是否允许数据提交
      if (!me.allowStatus) {
        return
      }
      // 厂家基础信息
      const factoryConfigInfo = me.ruleForm.factoryConfigInfo

      me.ruleForm.prizeConfigInfo.prizeList = me.prizeList
      me.ruleForm.carouseList = me.carouseList
      me.ruleForm.videoList = me.videoList
      const templetJson = JSON.stringify(me.ruleForm)
      console.log(me.ruleForm, 'me.ruleForm=saveResult')

      saveActAndTempletPrize({
        activityName: factoryConfigInfo.activeName,
        shareIcoUrl: '',
        shareTitle: '',
        shareDesc: '',
        beginTime: factoryConfigInfo.beginTime,
        endTime: factoryConfigInfo.endTime,
        templetId: 2,
        templetJson: templetJson,
        activityId: me.$route.query.copy ? '' : me.activityId || ''
      }).then((res) => {
        if (res.data && res.data.code === 1001) {
          me.$message.error(res.data.msg)
          return
        }
        me.$message.success('配置成功')
        this.$router.push({
          name: 'ActiveAdTemplate'
        })
      })
    },

    // 检验奖项
    checkePrizelist() {
      const me = this
      const prizeList = me.$refs.LuckyTemplate.arrList
      return prizeList.map((item) => {
        if (me.objectValueAllEmpty(item)) {
          return true
        } else {
          return false
        }
      })
    },
    // 检测对象是否为空
    objectValueAllEmpty(object) {
      let isEmpty = false
      Object.keys(object).forEach(function (x) {
        if (object[x] === null || object[x] === '') {
          isEmpty = true
          return object
        }
      })
      if (isEmpty) {
        return object
      } else {
        return false
      }
    },

    // 关闭弹窗
    handleClose() {
      this.$confirm('确认关闭？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$router.push({
          name: 'ActiveAdTemplate'
        })
      })
    },

    // 重置表单
    resetForm() {
      this.$refs['ruleForm'].resetFields()
    },

    // // 分享配置富文本编辑器图片上传事件
    // shareImgHandler(state) {
    //   if (state) {
    //     this.$refs.sharefileBtn.click()
    //   }
    // },

    // // 抽奖说明富文本编辑器图片上传事件
    // prizeImgHandler(state) {
    //   if (state) {
    //     this.$refs.prizefileBtn.click()
    //   }
    // },

    // 自定义文件选择框改变事件e：事件，type: 类型
    handleChange(e, type) {
      const me = this
      const files = Array.prototype.slice.call(e.target.files)
      if (!files) {
        return
      }
      const option = {
        file: files[0],
        imageType: 'nowater',
        onError: function () {},
        onSuccess: function () {},
        onProgress: function () {}
      }
      me.$oss.ossUploadImage(option).then((res) => {
        const selection = me.$refs[`${type}QuillEditor`]
          .getQuill()
          .getSelection()
        // 这里就是返回的图片地址，如果接口返回的不是可以访问的地址，要自己拼接
        const imgUrl = res.imgOrgUrl
        // 获取quill的光标，插入图片
        me.$refs[`${type}QuillEditor`]
          .getQuill()
          .insertEmbed(selection != null ? selection.index : 0, 'image', imgUrl)
        // 插入完成后，光标往后移动一位
        me.$refs[`${type}QuillEditor`]
          .getQuill()
          .setSelection(selection.index + 1)
      })
    },

    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 上传图片，同步上传
    async httpRequestOrder(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1 // 不压缩
      this._uploads = this._uploads || []
      this._uploads.push({
        fn: this.$oss.ossUploadImage,
        option
      })
      this.$tools.debounce(this.call, 100)()
    },
    // 上传抽奖背景图片
    async httpRequestBg(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    async call() {
      for (const a of this._uploads) {
        await a.fn(a.option)
      }
      this._uploads = []
    },
    // 活动封面
    onSuccessTitleimage(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.factoryConfigInfo.TitleImage = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 活动封面
    onSuccessTitleimageShore(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.shareConfigInfo.shareimage = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },

    // 上传抽奖背景图片
    onSuccessTitleimageBg(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.prizeConfigInfo.bgimage = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },

    // 多种图片上传
    // 上传成功回调
    onSuccessTitleimageMore(response, file) {
      if (!response) {
        return
      }
      const me = this
      const imgObj = {}
      if (response.name) {
        imgObj.url = file.response.imgUrl.replace('nowater300', 'nowater600')
        imgObj.name = file.uid
        me.ruleForm.factoryConfigInfo.fileList.push(imgObj)
        me.fileList = me.ruleForm.factoryConfigInfo.fileList
      } else {
        me.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 移除图片
    handleRemove(file) {
      const list = this.ruleForm.factoryConfigInfo.fileList
      list.map((item, index) => {
        if (item.url === file.url) {
          list.splice(index, 1)
        }
      })
      this.fileList = list
    },
    // 修改图片
    amend(file) {
      const amendImgUrl = this.substitute.url
      const list = this.ruleForm.factoryConfigInfo.fileList
      if (file) {
        list.map((item) => {
          if (item.url === amendImgUrl) {
            item.url = file.imgUrl.replace('nowater300', 'nowater600')
          }
        })
      }
    },
    refreshImageList() {
      this.ruleForm.factoryConfigInfo.fileList = this.fileList
    },
    searchBrandList(query) {
      this.brandloading = true
      this.$tools.debounce(() => this.getBrandList(query), 300)()
    },
    // 获取品牌列表
    getBrandList(query = '') {
      const me = this
      searchBrand({
        name: query || me.searchBrandValue,
        page: 1,
        limit: 10
      })
        .then((response) => {
          if (response.data.code === 0) {
            const brandList = []
            const result = response.data.data && response.data.data.listData
            result.map(function (value) {
              const newObj = {
                labelName: value.brandName,
                labelId: value.brandId
              }
              brandList.push(newObj)
              me.brandList = brandList
            })
          }
        })
        .finally(() => {
          me.brandloading = false
        })
    },
    addLabel(data) {
      // console.log(data)
      this.ruleForm.factoryConfigInfo.brand = this.brandList.find(
        (_) => _.labelId === data
      )
    },
    searchCarList(query) {
      this.carloading = true
      this.$tools.debounce(() => this.getCarList(query), 300)()
    },
    // 获取车型列表
    getCarList(query = '') {
      const me = this
      const brandName =
        me.ruleForm.factoryConfigInfo.brand &&
        me.ruleForm.factoryConfigInfo.brand.labelName
      if (!brandName) {
        this.carloading = false
        me.$message.error('请先选择品牌')
        return
      }
      searchCarList({
        name: query || me.searchCarValue,
        brand: brandName,
        page: 1,
        limit: 100,
        isOnStatus: 1
      })
        .then((response) => {
          if (response.status === 200) {
            const carList = []
            const result = response.data.data && response.data.data.list
            result.map(function (value) {
              const newObj = {
                value: value.goodName,
                goodsId: value.goodId
              }
              carList.push(newObj)
              me.carList = carList
            })
          }
        })
        .finally(() => {
          me.carloading = false
        })
    },
    addCar(data) {
      // console.log(data)
      const list = []
      this.carList.map((_) => {
        data.map((id) => {
          if (_.goodsId === id) {
            list.push(_)
          }
        })
      })
      this.ruleForm.signUpConfigInfo.carList = list
    },
    changeAllocation(e) {
      if (!e) {
        this.ruleForm.signUpConfigInfo.carIds = []
        this.ruleForm.signUpConfigInfo.carList = []
      }
    },
    openChangeImgDialog(type, list, name) {
      this.$refs.changeImgDialogRef.init(type, list, name)
    },
    updateImgList(list, type) {
      if (type === 1) {
        this.ruleForm.factoryConfigInfo.fileList = list
        this.fileList = list
      }
    }
  }
}
</script>

<style lang="scss">
.modules {
  .ql-container {
    height: 150px;
  }
  .el-dialog {
    background: #ffffff;
  }
}
</style>

<style lang="scss" scoped>
.activeShop {
  padding: 20px 30px;
  width: 1300px;
  .colorBox {
    width: 20px;
    height: 20px;
  }
  .modules {
    border: 1px solid #cac9c9;
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
    .el-form-item {
      min-width: 400px;
    }
    .text {
      margin-left: 20px;
      margin-bottom: 20px;
    }
    .image-wrap {
      position: relative;
      display: inline-block;
      vertical-align: top;
      width: 146px;
      height: 146px;
      margin: 0 10px 10px 0;
      .image {
        width: 146px;
        height: 146px;
        border-radius: 6px;
        border: 1px solid #ccc;
      }
      &:hover {
        .operation {
          visibility: visible;
        }
      }
      .operation {
        display: flex;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        visibility: hidden;
        .delete {
          margin-left: 10px;
        }
      }
    }
  }
  .title {
    font-size: 16px;
    margin: 0 0 20px 0;
    font-weight: bold;
  }
  .topBg {
    height: 230px;
    background-size: contain;
    background-repeat: no-repeat;
  }
  .select-car {
    display: flex;
    align-items: center;
    .checkbox {
      margin-left: 60px;
    }
    .el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
