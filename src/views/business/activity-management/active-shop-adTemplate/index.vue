<template>
  <div v-loading="loading" style="margin: 20px 20px 0">
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <el-form-item label="活动名称">
        <el-input
          v-model="ruleForm.activityName"
          type="text"
          placeholder="请输入活动名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="ruleForm.status" placeholder="请选择">
          <el-option label="全部" value />
          <el-option
            v-for="(item, index) in mapStatus"
            :key="index"
            :label="item"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="日期">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          v-model="daterange"
          style="width: 360px"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="searchactiveList()">搜索</el-button>
        <el-button @click="resize()">重置</el-button>
        <el-button @click="editConfigAct('new')">新建活动</el-button>
      </el-form-item>
    </el-form>
    <el-table ref="dataList" :data="dataList" row-key="dataList" border>
      <el-table-column align="center" type="index" label="序号" width="60">
        <template v-slot="scope">
          {{ (ruleForm.page - 1) * 10 + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="activityId"
        label="专题ID"
        align="center"
        width="100"
      />
      <el-table-column
        prop="activityName"
        label="专题名称"
        align="center"
        width="300"
      />
      <el-table-column
        prop="joinNum"
        label="参与人数"
        align="center"
        width="80"
      />
      <!-- <el-table-column prop="id" label="专题状态" align="center" width="100" /> -->

      <el-table-column align="center" label="专题状态" width="120">
        <template v-slot="scope">{{ mapStatus[scope.row.status] }}</template>
      </el-table-column>

      <el-table-column
        prop="beginTime"
        align="center"
        width="200"
        label="开始时间"
      >
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.beginTime)
        }}</template>
      </el-table-column>
      <el-table-column
        prop="endTime"
        align="center"
        width="200"
        label="结束时间"
      >
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.endTime)
        }}</template>
      </el-table-column>

      <el-table-column
        prop="status"
        align="center"
        label="是否有效"
        width="100"
      >
        <template v-slot="scope">
          <el-switch
            :model-value="scope.row.status === 2"
            @change="changeStatus(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column
        prop="status"
        align="center"
        label="操作"
        class="template_set"
        width="680"
      >
        <template v-slot="scope">
          <el-button type="primary" @click="editConfigAct(scope.row)"
            >专题配置</el-button
          >
          <el-button
            v-clipboard:copy="
              scope.row.shareUrl
                ? scope.row.shareUrl
                : `https://wap.58moto.com/zt/2021/5/7/big-clientele?activityId=${scope.row.activityId}&share=true`
            "
            v-clipboard:success="clipboardSuccess"
            style="margin-bottom: 10px 0"
            type="info"
            plain
            >拷贝链接</el-button
          >
          <el-button type="primary" plain @click="lookedetail(scope.row)"
            >预览</el-button
          >
          <el-button
            style="margin: 10px 0"
            type="success"
            plain
            @click="exportEnterRecord(scope.row)"
            >中奖纪录导出</el-button
          >
          <el-button
            style="margin: 10px 0"
            type="success"
            plain
            @click="exportPaymentRecord(scope.row)"
            >打款记录导出</el-button
          >
          <el-button type="success" plain @click="exportUserRecord(scope.row)"
            >客资导出</el-button
          >
          <el-button
            type="primary"
            plain
            @click="sendConfiguration(scope.row.activityId)"
            >发送配置</el-button
          >
          <el-button type="primary" plain @click="copyAdvert(scope.row)"
            >复制</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="dialogVisible" :title="title" @close="cancleExport">
      <div class="dialogStyle">
        <div>选择时间段</div>
        <div>
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="exportTimer"
            style="width: 360px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="cancleExport">取消</el-button>
          <el-button type="primary" @click="exportExcele">导出</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="urlDiaogShow"
      title="预览"
      width="850px"
      @close="closedialog"
    >
      <div class="iframe_sty">
        <iframe :src="url1" class="iframe" />
        <iframe :src="url2" class="iframe" />
      </div>
    </el-dialog>
    <el-dialog
      v-model="configurationShow"
      width="850px"
      center
      @close="configurationClose()"
    >
      <h2 style="margin-top: 0">发送配置</h2>
      <el-form
        ref="configContent"
        :model="configContent"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="收件人" prop="receiveAddress">
          <el-input
            v-model="configContent.receiveAddress"
            placeholder="按英文逗号分开"
            type="text"
            clearable
            style="width: 650px"
          />
        </el-form-item>
        <el-form-item label="抄送人">
          <el-input
            v-model="configContent.copyAddress"
            placeholder="按英文逗号分开"
            type="text"
            clearable
            style="width: 650px"
          />
        </el-form-item>
        <el-form-item label="主题">
          <el-input
            v-model="configContent.title"
            type="text"
            size="large"
            maxlength="100"
            clearable
            show-word-limit
            style="width: 650px"
          />
        </el-form-item>
        <el-form-item label="内容">
          <div class="config-content">
            <quill-editor
              contentType="html"
              ref="configQuillEditor"
              :options="editorOptions"
              v-model:content="configContent.content"
              @textChange="(val) => onEditorChange(val, 'configQuillEditor')"
            />
            <div style="text-align: right">{{ limit }}/1000</div>
            <input
              ref="configfileBtn"
              type="file"
              hidden
              accept=".jpg, .png"
              @change="handleChange($event, 'config')"
            />
          </div>
        </el-form-item>
        <el-form-item label="发送时间">
          <div class="date-select" style="width: 650px">
            <div class="content">
              <div class="item" v-for="(item, index) in times" :key="index">
                <span>{{ item }}</span>
                <el-divider
                  style="background: #0285bd"
                  direction="vertical"
                ></el-divider>
                <el-icon @click="close(index)" class="icon"
                  ><IconClose
                /></el-icon>
              </div>
            </div>
            <el-time-select
              :start="
                {
                  start: '00:00',
                  step: '00:30',
                  end: '23:30'
                } &&
                {
                  start: '00:00',
                  step: '00:30',
                  end: '23:30'
                }.start
              "
              :end="
                {
                  start: '00:00',
                  step: '00:30',
                  end: '23:30'
                } &&
                {
                  start: '00:00',
                  step: '00:30',
                  end: '23:30'
                }.end
              "
              :step="
                {
                  start: '00:00',
                  step: '00:30',
                  end: '23:30'
                } &&
                {
                  start: '00:00',
                  step: '00:30',
                  end: '23:30'
                }.step
              "
              :min-time="
                {
                  start: '00:00',
                  step: '00:30',
                  end: '23:30'
                } &&
                {
                  start: '00:00',
                  step: '00:30',
                  end: '23:30'
                }.minTime
              "
              :max-time="
                {
                  start: '00:00',
                  step: '00:30',
                  end: '23:30'
                } &&
                {
                  start: '00:00',
                  step: '00:30',
                  end: '23:30'
                }.maxTime
              "
              @change="handleDateChange"
              style="
                width: 650px;
                opacity: 0;
                position: absolute;
                left: 0;
                right: 0;
              "
              v-model="date"
              :editable="false"
              placeholder="选择时间"
            >
            </el-time-select>
          </div>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="configurationClose()"
            >&#x3000;取消&#x3000;</el-button
          >
          <el-button type="primary" @click="saveEmailConfig()"
            >配置完成</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!--分页-->
    <el-pagination
      v-if="total"
      :total="total"
      :current-page="ruleForm.page"
      layout="total, prev, pager, next, jumper"
      @current-change="currentChange"
    />
  </div>
</template>

<script>
import { Close as IconClose } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import {
  activityGetListFactoryActs,
  activityUpdateActStatus,
  activityGetUserEnterRecord,
  activityGetPrizeUserRecord,
  activityGetConfig,
  activitySaveEmailConfig,
  getListRecord
} from '@/api/activeConfiguration'
import { pickerDayOptions } from '@/utils/configData'
export default {
  components: {
    IconClose
  },
  data() {
    return {
      editorOptions: {
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
            // ['blockquote', 'code-block'], // 引用  代码块
            [{ header: 1 }, { header: 2 }], // 1、2 级标题
            [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
            // [{ script: 'sub' }, { script: 'super' }], // 上标/下标
            [{ indent: '-1' }, { indent: '+1' }], // 缩进
            // [{ direction: 'rtl' }], // 文本方向
            // [{ size: ['12', '14', '16', '18', '20', '22', '24', '28', '32', '36'] }], // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6] }], // 标题
            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
            // [{ font: ['songti'] }], // 字体种类
            [{ align: [] }], // 对齐方式
            // ['clean'], // 清除文本格式
            ['link', 'image', 'video'] // 链接、图片、视频
          ]
        }
      },
      loading: false,
      ruleForm: {
        activityName: '',
        status: '',
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        page: 1,
        limit: 10
      },
      date: '',
      times: [],
      mapStatus: ['已失效', '未开始', '正在进行', '已结束'],
      total: 0,
      dataList: [],
      // 导出弹窗控制
      dialogVisible: false,
      enterRecordShow: false,
      userRecordShow: false,
      title: '',
      // 导出表单
      exportFrom: {
        activityId: '',
        beginTime: '',
        endTime: ''
      },
      pickerOptions: pickerDayOptions,
      urlDiaogShow: false,
      url1: '',
      url2: '',
      // 发送配置相关
      configurationShow: false,
      // 发送配置内容
      configContent: {
        id: '',
        receiveAddress: '', // 收件人
        copyAddress: '', // 抄送人
        title: '', // 主题
        content: '' // 内容
      },
      rules: {
        receiveAddress: [
          { required: true, message: '请输入收件人', trigger: 'blur' }
        ]
      },
      // 限制长度
      limit: 0,
      activityId: '',
      dayjs
    }
  },
  name: 'ActiveAdTemplate',
  computed: {
    daterange: {
      get() {
        if (this.ruleForm.beginTime && this.ruleForm.endTime) {
          return [this.ruleForm.beginTime, this.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginTime = value[0]
          this.ruleForm.endTime = value[1]
        } else {
          this.ruleForm.beginTime = ''
          this.ruleForm.endTime = ''
        }
      }
    },
    exportTimer: {
      get() {
        if (this.exportFrom.beginTime && this.exportFrom.endTime) {
          return [this.exportFrom.beginTime, this.exportFrom.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.exportFrom.beginTime = value[0]
          this.exportFrom.endTime = value[1]
        } else {
          this.exportFrom.beginTime = ''
          this.exportFrom.endTime = ''
        }
      }
    }
  },
  created() {},
  activated() {
    this.getList()
    if (this.$refs['configQuillEditor']) {
      this.$refs['configQuillEditor']
        .getQuill()
        .getModule('toolbar')
        .addHandler('image', this.detailImgHandler)
    }
  },
  methods: {
    close(index) {
      this.times.splice(index, 1)
    },
    handleDateChange(val) {
      this.date = ''
      if (this.times.includes(val)) return
      this.times.push(val)
    },
    copyAdvert(row) {
      this.$router.push({
        name: 'AdShopTemplate1',
        query: {
          activityId: row.activityId,
          copy: 1
        }
      })
    },
    // 配置模板  根据模板id选择弹框
    editConfigAct(item) {
      if (item && item.activityId) {
        // 编辑
        this.$router.push({
          name: 'AdShopTemplate1',
          query: {
            activityId: item.activityId
          }
        })
      } else {
        // 新增
        this.$router.push({
          name: 'AdShopTemplate1'
        })
      }
    },
    // 预览
    lookedetail(data) {
      this.url1 = `https://wap.58moto.com/zt/2021/5/7/big-clientele?activityId=${data.activityId}`
      this.url2 = `https://wap.58moto.com/zt/2021/5/7/online-sweepstakes?activityId=${data.activityId}`
      this.urlDiaogShow = true
    },
    closedialog() {
      this.url1 = ''
      this.url2 = ''
      this.urlDiaogShow = false
    },
    // 改变是否有效
    changeStatus(item) {
      const me = this
      let text = ''
      let buttonTitle = ''
      if (item.status === 2) {
        text = '设置失效后，专题无法在app查看，请确认是否置为失效?'
        buttonTitle = '失效'
      } else {
        text = '设置有效后，专题可在app查看，请确认是否置为有效?'
        buttonTitle = '有效'
      }
      me.$confirm(text, '专题状态', {
        confirmButtonText: buttonTitle,
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          activityUpdateActStatus({
            activityIds: item.activityId,
            status: item.status === 2 ? 0 : 2
          })
            .then((response) => {
              if (response.data.code === 0) {
                me.$message.success('修改成功')
                me.getList()
              }
            })
            .catch((_) => {
              me.$message.error('修改失败')
            })
        })
        .catch((err) => {
          me.$message({
            type: 'info',
            message: (err && err.message) || '已取消操作'
          })
        })
    },
    searchactiveList() {
      this.ruleForm.page = 1
      this.getList()
    },
    // 查询列表
    getList() {
      this.loading = true
      activityGetListFactoryActs(this.ruleForm)
        .then((response) => {
          if (response.data.code === 0) {
            this.dataList = response.data.data.list
            this.total = response.data.data.total
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .finally((_) => {
          this.loading = false
        })
    },
    currentChange(page) {
      this.ruleForm.page = page
      this.getList()
    },
    clipboardSuccess() {
      this.$message({
        message: '复制成功',
        type: 'success',
        duration: 1500
      })
    },
    resize() {
      this.ruleForm = {
        activityName: '',
        status: '',
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        page: 1,
        limit: 10
      }
      this.getList()
    },

    // 中奖纪录导出
    exportEnterRecord(item) {
      this.dialogVisible = true
      this.enterRecordShow = true
      this.title = '中奖纪录导出'
      this.exportFrom.activityId = item.activityId
    },
    // 客制纪录导出
    exportUserRecord(item) {
      this.dialogVisible = true
      this.userRecordShow = true
      this.title = '客资纪录导出'
      this.exportFrom.activityId = item.activityId
    },
    // 取消导出
    cancleExport() {
      this.dialogVisible = false
      this.enterRecordShow = false
      this.userRecordShow = false
      this.title = ''
      this.exportFrom = {
        activityId: '',
        beginTime: '',
        endTime: ''
      } // 导出表单
    },
    // 导出处理
    exportExcele() {
      if (this.enterRecordShow) {
        this.exportEnterRecordType()
        return
      }
      if (this.userRecordShow) {
        this.exportUserRecordType()
      }
    },
    // 导出中奖Excel表格
    exportEnterRecordType() {
      const me = this
      me.$confirm('你确认导出到Excel么', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '正在导出，请稍等......',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          activityGetPrizeUserRecord({ ...me.exportFrom }).then(async (res) => {
            const data = res.data.data
            if (data.length === 0) {
              loading.close()
              me.$message.success('暂无数据可以导出')
              return
            }
            const { export_json_to_excel } = await import(
              '@/vendor/Export2Excel'
            )
            // 导出的表头
            const tHeader = [
              '用户姓名',
              '用户id',
              '手机号',
              '奖项',
              '奖品',
              '中奖时间'
            ]
            // 导出表头要对应的数据
            const filterVal = [
              'userName',
              'userId',
              'userPhone',
              'prizeEffect',
              'prizeName',
              'createTimeStr'
            ]
            const exportData = me.formatJsonUser(filterVal, data)
            export_json_to_excel(tHeader, exportData, '中奖纪录')
            me.$message.success('导出成功')
            loading.close()
          })
        })
        .catch()
    },
    formatJsonUser(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          // if (j === 'prizeEffect') {
          //   return v.prizeEffect + v.prizeName
          // }
          return v[j]
        })
      )
    },

    // 导出客制纪录Excel表格
    exportUserRecordType() {
      const me = this
      me.$confirm('你确认导出到Excel么', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '正在导出，请稍等......',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          activityGetUserEnterRecord({ ...me.exportFrom }).then(async (res) => {
            const data = res.data.data
            if (data.length === 0) {
              loading.close()
              me.$message.success('暂无数据可以导出')
              return
            }

            const { export_json_to_excel } = await import(
              '@/vendor/Export2Excel'
            )
            // 导出的表头
            const tHeader = ['用户姓名', '手机号', '省份', '城市', '创建时间']
            // 导出表头要对应的数据
            const filterVal = [
              'userName',
              'userPhone',
              'province',
              'city',
              'createTimeStr'
            ]
            const exportData = me.formatJsonEnter(filterVal, data)
            export_json_to_excel(tHeader, exportData, '客资纪录')
            me.$message.success('导出成功')
            loading.close()
          })
        })
        .catch()
    },
    formatJsonEnter(filterVal, jsonData) {
      const me = this
      return jsonData.map((v) =>
        filterVal.map((j) => {
          if (j === 'createTime') {
            return me.$filters.timeFullS(v.createTime)
          }
          return v[j]
        })
      )
    },
    // 导出打款记录
    exportPaymentRecord(item) {
      const me = this
      me.$confirm('你确认导出到Excel么', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '正在导出，请稍等......',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          getListRecord({ activityId: item.activityId }).then(async (res) => {
            const data = res.data.data || []
            if (data && data.length === 0) {
              loading.close()
              me.$message.success('暂无数据可以导出')
              return
            }
            const tHeader1 = [
              '活动名称',
              '开始时间',
              '结束时间',
              '参与人数',
              '总发放金额'
            ]
            const filterVal1 = [
              'activityName',
              'beginTime',
              'endTime',
              'joinNum',
              'totalFee'
            ]
            const tHeader2 = ['手机号', '金额', '打款时间']
            const filterVal2 = ['mobile', 'prizeCost', 'createTime']
            const exportData1 = me.formatJsonEnter(filterVal1, [data])
            const exportData2 = me.formatJsonEnter(
              filterVal2,
              data.prizeUserRecordENTList
            )
            const { export_json_to_excel } = await import(
              '@/vendor/Export2Excel'
            )
            export_json_to_excel([], [], '打款记录导出', [
              {
                header: tHeader1,
                data: exportData1,
                name: '汇总数据'
              },
              {
                header: tHeader2,
                data: exportData2,
                name: '明细数据'
              }
            ])
            me.$message.success('导出成功')
            loading.close()
          })
        })
        .catch()
    },
    // 获取活动配置
    sendConfiguration(activityId) {
      this.configurationShow = true
      this.activityId = activityId
      activityGetConfig({ activityId }).then((res) => {
        const data = res.data.data
        if (data) {
          this.configContent = {
            id: data.id,
            receiveAddress: data.receiveAddress,
            copyAddress: data.copyAddress,
            title: data.title,
            content: data.content
          }
          this.times = (data.times && data.times.split(',')) || []
        }
      })
    },
    configurationClose() {
      this.configurationShow = false
      this.configContent = {
        id: '',
        receiveAddress: '',
        copyAddress: '',
        title: '',
        content: ''
      }
      this.times = []
    },
    // 活动配置 保存/修改
    saveEmailConfig() {
      this.$refs.configContent.validate((valid) => {
        if (!valid) {
          return
        }
        activitySaveEmailConfig({
          ...this.configContent,
          activityId: this.activityId,
          times: this.times.join(',')
        }).then((res) => {
          if (res.data.code === 0) {
            this.$message.success('配置成功')
            this.configurationShow = false
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },
    // 限制字数
    onEditorChange(event, refKey) {
      const quill = this.$refs[refKey].getQuill()
      quill.deleteText(1000, 1)
      this.limit = quill.getLength() - 1
    },
    handleChange(e, type) {
      const me = this
      const files = Array.prototype.slice.call(e.target.files)
      if (!files) {
        return
      }
      const option = {
        file: files[0],
        imageType: 'nowater',
        onError: function () {},
        onSuccess: function () {},
        onProgress: function () {}
      }
      me.$oss.ossUploadImage(option).then((res) => {
        const selection = me.$refs[`${type}QuillEditor`]
          .getQuill()
          .getSelection()
        // 这里就是返回的图片地址，如果接口返回的不是可以访问的地址，要自己拼接
        const imgUrl = res.imgOrgUrl
        // 获取quill的光标，插入图片
        me.$refs[`${type}QuillEditor`]
          .getQuill()
          .insertEmbed(selection != null ? selection.index : 0, 'image', imgUrl)
        // 插入完成后，光标往后移动一位
        me.$refs[`${type}QuillEditor`]
          .getQuill()
          .setSelection(selection.index + 1)
      })
    },
    detailImgHandler(state) {
      if (state) {
        // 解决文件不能重复上传问题
        this.$refs.configfileBtn.setAttribute('type', 'text')
        this.$refs.configfileBtn.setAttribute('type', 'file')
        // 触发input点击事件
        this.$refs.configfileBtn.click()
      }
    }
  }
}
</script>

<style lang="scss">
.config-content {
  width: 650px;
  .ql-container {
    height: 150px;
  }
}
</style>

<style lang="scss" scoped>
.dialogStyle {
  display: flex;
  align-items: center;
  > div:nth-child(1) {
    margin-right: 20px;
  }
}
.iframe_sty {
  display: flex;
  iframe {
    width: 400px;
    height: 600px;
    margin: 10px;
  }
}
.template_set .cell .el-button {
  margin-bottom: 10px;
}
.date-select {
  height: 40px;
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  overflow-x: hidden;
  flex-wrap: wrap;
  position: relative;
  padding: 0 15px;
  .content {
    display: flex;
    position: relative;
    z-index: 1;
    flex-wrap: wrap;
    pointer-events: none;
  }
  .item {
    display: flex;
    height: 24px;
    padding: 0 10px;
    border: 1px solid #0285bd;
    align-items: center;
    margin-top: 5px;
    border-radius: 10px;
    overflow: hidden;
    margin-right: 15px;
    .icon {
      color: #0285bd;
      pointer-events: auto;
      cursor: pointer;
    }
  }
}
</style>
