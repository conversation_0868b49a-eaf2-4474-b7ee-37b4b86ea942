<template>
  <div class="carouse">
    <el-button type="primary" style="margin-bottom: 20px" @click="addCarouse"
      >添加</el-button
    >
    <div
      v-for="(item, index) in arrList"
      :key="index"
      class="content"
      @click="tapclick(item.signPoint, index)"
    >
      <div class="carouse-title">第{{ index + 1 }}组</div>
      <div class="carouse-content">
        <div class="carouse-content-top">
          <div class="carouse-content-top-left">
            <p class="carouse-content-top-left-text">轮播图位置</p>
            <el-input
              v-model="item['position' + item.signPoint]"
              data-sign="position"
              placeholder="请输入轮播图位置"
            />
          </div>
          <el-button
            type="danger"
            size="small"
            @click="removePrize(item, index)"
            >删除</el-button
          >
        </div>
        <!-- 图片上传与重排 -->
        <div>
          <draggable item-key="url" v-model="item['image' + item.signPoint]">
            <template #item="{ element }">
              <span class="image-wrap">
                <img :src="element.url" class="image" alt />
                <div class="operation">
                  <!-- <el-upload
                    :http-request="httpRequest"
                    :on-success="amend"
                    :show-file-list="false"
                    name="activefile"
                    style="display: inline-block"
                    action
                  >
                    <el-button
                      type="primary"
                      circle
                      @click="substitute = element"
                    >
                      <template #icon>
                        <IconEdit />
                      </template>
                    </el-button>
                  </el-upload> -->
                  <el-button
                    type="primary"
                    circle
                    @click="
                      addGroupImg(
                        2,
                        item['image' + item.signPoint],
                        element.name
                      )
                    "
                  >
                    <template #icon>
                      <IconEdit />
                    </template>
                  </el-button>
                  <el-button
                    type="danger"
                    class="delete"
                    circle
                    @click="handleRemove(element)"
                  >
                    <template #icon>
                      <IconDelete />
                    </template>
                  </el-button>
                </div>
              </span>
            </template>
            <template #footer>
              <!-- <el-upload
                :http-request="httpRequestOrder"
                :on-success="onSuccessTitleimageMore"
                :show-file-list="false"
                name="activefile"
                style="display: inline-block"
                action
                multiple
                list-type="picture-card"
              >
                <el-button type="primary" link>选择图片</el-button>
              </el-upload> -->
              <div
                class="el-upload el-upload--picture-card"
                @click="addGroupImg(2, item['image' + item.signPoint])"
              >
                <el-button type="primary" link>选择图片</el-button>
              </div>
            </template>
          </draggable>
        </div>
      </div>
    </div>
    <ChangeImgDialog ref="changeImgDialogRef" @updateImgList="updateImgList" />
  </div>
</template>

<script>
import { Edit as IconEdit, Delete as IconDelete } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import ChangeImgDialog from './changeImgDialog.vue'

export default {
  data() {
    return {
      arrList: [],
      imageindex: {
        signPoint: '', // 记录奖项的位置值
        index: '' // 记录点击事件的位置值
      },
      // 替换相关
      substitute: {}
    }
  },
  name: 'CarouseTemplate',
  components: {
    draggable,
    IconEdit,
    IconDelete,
    ChangeImgDialog
  },
  props: {
    carouseList: {
      // 接受返回的数组
      type: Array,
      default() {
        return []
      }
    }
  },
  computed: {},
  watch: {
    carouseList: {
      deep: true,
      immediate: true,
      handler(newVal) {
        const me = this
        // 返回的轮播对象的key添加下标
        const prizeArr = []
        newVal.map((item) => {
          const prizeObj = {}
          Object.keys(item).forEach(function (x) {
            let xStr = x
            if (xStr !== 'signPoint') {
              xStr = xStr + item.signPoint
            }
            prizeObj[xStr] = item[x]
          })
          prizeArr.push(prizeObj)
        })
        me.arrList = prizeArr
      }
    }
  },
  activated() {},
  methods: {
    // 获取对应点击事件的下标
    tapclick(signPoint, index) {
      this.imageindex.signPoint = signPoint
      this.imageindex.index = index
    },
    // 新增轮播配置
    addCarouse() {
      const aprizeObj = {}
      let signPoint = 0
      const arrListLength = this.arrList.length - 1
      if (this.arrList.length > 0) {
        signPoint = this.arrList[arrListLength].signPoint + 1
      }
      const arrKeyList = ['image', 'position']
      arrKeyList.map((item) => {
        if (item === 'image') {
          aprizeObj[item + signPoint] = []
        } else {
          aprizeObj[item + signPoint] = ''
        }
      })
      aprizeObj.signPoint = signPoint
      this.arrList.push(aprizeObj)
    },

    // 移除轮播
    removePrize(item, index) {
      this.arrList.splice(index, 1)
    },

    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },

    // 上传图片，同步上传
    async httpRequestOrder(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1 // 不压缩
      this._uploads = this._uploads || []
      this._uploads.push({
        fn: this.$oss.ossUploadImage,
        option
      })
      this.$tools.debounce(this.call, 100)()
    },

    // 多种图片上传
    // 上传成功回调
    onSuccessTitleimageMore(response, file, fileList) {
      if (!response) {
        return
      }
      const me = this
      const imgObj = {}
      if (response.name) {
        const imageindex = this.imageindex

        imgObj.url = file.response.imgUrl.replace('nowater300', 'nowater600')
        imgObj.name = file.uid
        me.arrList[imageindex.index]['image' + imageindex.signPoint].push(
          imgObj
        )
      } else {
        me.$notify.error({
          title: '上传错误'
        })
      }
    },
    async call() {
      for (const a of this._uploads) {
        await a.fn(a.option)
      }
      this._uploads = []
    },
    // 修改图片
    amend(file) {
      const amendImgUrl = this.substitute.url
      const imageindex = this.imageindex
      const list =
        this.arrList[imageindex.index]['image' + imageindex.signPoint]
      if (file) {
        list.map((item) => {
          if (item.url === amendImgUrl) {
            item.url = file.imgUrl
          }
        })
      }
    },
    // 移除图片
    handleRemove(file) {
      const imageindex = this.imageindex
      setTimeout(() => {
        const list =
          this.arrList[imageindex.index]['image' + imageindex.signPoint]
        list.map((item, index) => {
          if (item.url === file.url) {
            list.splice(index, 1)
          }
        })
        this.fileList = list
      }, 0)
    },
    addGroupImg(type, list, name) {
      this.$refs.changeImgDialogRef.init(type, list, name)
    },
    updateImgList(list, type) {
      if (type === 2) {
        const imageindex = this.imageindex
        this.arrList[imageindex.index]['image' + imageindex.signPoint] = list
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.carouse {
  width: 1200px;
  padding: 20px;
  .content {
    display: flex;
    padding: 10px;
    margin-bottom: 20px;
    border: 1px solid rgb(196, 195, 193);
    padding-bottom: 10px;
    .carouse-title {
      margin-top: 14px;
      min-width: 80px;
    }
    .carouse-content {
      .carouse-content-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 1060px;
        margin-bottom: 20px;
        .carouse-content-top-left {
          display: flex;
          align-items: center;
          .carouse-content-top-left-text {
            margin-right: 20px;
            color: #606266;
            font-size: 14px;
            width: 100px;
          }
        }
      }
    }
    .image-wrap {
      position: relative;
      display: inline-block;
      vertical-align: top;
      width: 146px;
      height: 146px;
      margin: 0 10px 10px 0;
      .image {
        width: 146px;
        height: 146px;
        border-radius: 6px;
        border: 1px solid #ccc;
      }
      &:hover {
        .operation {
          visibility: visible;
        }
      }
      .operation {
        display: flex;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        visibility: hidden;
        .delete {
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
