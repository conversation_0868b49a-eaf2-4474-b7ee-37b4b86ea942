<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800"
    class="change-img-dialog"
  >
    <div class="img-dialog">
      <div v-if="!imgName" class="add-button">
        <el-button type="primary" link @click="addImg">添加</el-button>
      </div>
      <template v-for="(item, index) in listData" :key="index">
        <div
          v-if="!imgName || (imgName && item.name === imgName)"
          class="img-box"
        >
          <div v-if="!imgName" class="delete-box">
            <el-button type="danger" size="small" @click="deleteImg(index)"
              >删除</el-button
            >
          </div>
          <div class="img-data">
            <div class="label">图片标题</div>
            <el-input v-model="item.title" clearable />
          </div>
          <div class="img-data">
            <div class="label">跳转链接</div>
            <el-input v-model="item.jumpUrl" clearable />
          </div>
          <div class="img-data">
            <div class="label">添加图片</div>
            <el-upload
              :http-request="httpRequest"
              :on-success="(res, file) => successFun(res, file, item)"
              :show-file-list="false"
              name="activefile"
              action
            >
              <img v-if="item.url" :src="item.url" alt="" class="img" />
              <el-button v-else>添加</el-button>
            </el-upload>
          </div>
        </div>
      </template>
    </div>
    <div class="fooder-center">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleClose">确认</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
const { proxy } = getCurrentInstance()

const emit = defineEmits(['updateImgList'])

const dialogVisible = ref(false)
const title = ref('')
const titleEnum = {
  1: '活动详情图片',
  2: '轮播图配置'
}
const listData = ref([])
const imgTemplate = {
  name: '',
  url: '',
  title: '',
  jumpUrl: ''
}
const imgName = ref('')
const imgtype = ref('')

const init = (type, list, name) => {
  title.value = titleEnum[type] || ''
  imgtype.value = type || ''
  listData.value = []
  imgName.value = name || ''
  if (list && list.length) {
    list.forEach((v) => {
      listData.value.push({
        ...imgTemplate,
        ...v
      })
    })
  }
  dialogVisible.value = true
}

const httpRequest = async (option) => {
  option.imageType = 'nowater' // 无水印
  option.quality = 1
  proxy.$oss.ossUploadImage(option)
}

const successFun = (res, file, item) => {
  if (!res) return
  if (res.name) {
    item.url = res.imgUrl.replace('nowater300', 'nowater600')
    if (!item.name) {
      item.name = file.uid
    }
  } else {
    proxy.$notify.error({ title: '上传错误' })
  }
}

const addImg = () => {
  listData.value.push({
    ...imgTemplate
  })
}

const deleteImg = (index) => {
  listData.value.splice(index, 1)
}

const handleClose = () => {
  let flag = false
  listData.value.forEach((v) => {
    if (!v.url) {
      flag = true
    }
  })
  if (flag) {
    proxy.$message.error('请添加图片')
  } else {
    emit(
      'updateImgList',
      JSON.parse(JSON.stringify(listData.value)),
      imgtype.value
    )
    dialogVisible.value = false
  }
}

defineExpose({
  init
})
</script>

<style lang="scss">
.change-img-dialog {
  .el-dialog__headerbtn {
    top: 10px;
    right: 10px;
    width: 32px;
    height: 32px;
  }
  .el-dialog__body {
    padding-top: 0;
  }
}
</style>
<style lang="scss" scoped>
.img-dialog {
  .add-button {
    text-align: right;
  }
  .img-box {
    padding: 10px 0 10px 15px;
    margin-top: 10px;
    border: 1px solid #dedede;
    .delete-box {
      text-align: right;
      padding-right: 15px;
    }
    .img-data {
      display: inline-flex;
      width: 50%;
      margin-top: 10px;
      padding-right: 15px;
      .label {
        min-width: 65px;
        line-height: 32px;
      }
      .img {
        width: 100%;
        height: 200px;
        object-fit: cover;
      }
    }
  }
}
.fooder-center {
  text-align: center;
  margin-top: 30px;
}
</style>
