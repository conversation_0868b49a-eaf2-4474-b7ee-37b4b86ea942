<template>
  <div class="prize">
    <el-button
      type="primary"
      style="margin-bottom: 20px"
      @click="addPrizeConfig"
      >新增奖品</el-button
    >
    <div class="prize_title">
      <div :class="{ 'prize_title-small': lotteryMode !== '1' }">奖项名称</div>
      <div :class="{ 'prize_title-small': lotteryMode !== '1' }">奖品名称</div>
      <div :class="{ 'prize_title-small': lotteryMode !== '1' }">奖品金额</div>
      <div>奖品图片</div>
      <div>奖品数量</div>
      <div v-if="lotteryMode === '1'">中奖概率</div>
      <div>抽奖位置</div>
      <div>操作</div>
    </div>
    <div v-if="arrList.length > 0">
      <div
        v-for="(item, index) in arrList"
        :key="index"
        class="content"
        @click="tapclick(item.signPoint, index)"
      >
        <div>
          <input
            style="width: 140px"
            v-model="item['activeName' + item.signPoint]"
            data-sign="activeName"
            placeholder="请输入奖项名称"
          />
        </div>
        <div>
          <input
            style="width: 140px"
            v-model="item['title' + item.signPoint]"
            data-sign="title"
            type="text"
            placeholder="请输入奖品名称"
          />
        </div>
        <div>
          <input
            style="width: 100px"
            v-model="item['prizeCost' + item.signPoint]"
            data-sign="prizeCost"
            type="text"
            placeholder="奖品金额"
          />
        </div>
        <div>
          <el-upload
            :http-request="httpRequest"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            class="avatar-uploader"
            name="titlefile"
            style="display: inline-block"
            action
          >
            <img
              v-if="item['image' + item.signPoint]"
              :src="item['image' + item.signPoint]"
              class="avatar"
            />
            <el-icon v-else data-sign="image" class="avatar-uploader-icon"
              ><IconPlus
            /></el-icon>
          </el-upload>
        </div>
        <div style="margin-left: 10px">
          <input
            style="width: 140px"
            v-model="item['num' + item.signPoint]"
            data-sign="num"
            type="text"
            placeholder="请输入奖品数量"
          />
        </div>
        <div
          v-if="lotteryMode === '1'"
          style="display: flex; align-items: center"
        >
          <input
            style="width: 140px"
            v-model="item['probability' + item.signPoint]"
            data-sign="probability"
            type="text"
            placeholder="请输入中奖概率"
          />
          <div>%</div>
        </div>
        <div>
          <el-select
            v-model="item['position' + item.signPoint]"
            placeholder="请选择抽奖位置"
            @change="changeItem"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
            >
            </el-option>
          </el-select>
        </div>
        <div class="content_edit">
          <el-button type="primary" size="small" @click="copyData(item)"
            >复制</el-button
          >
          <el-button
            type="danger"
            size="small"
            @click="removePrize(item, index)"
            >删除</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Plus as IconPlus } from '@element-plus/icons-vue'
export default {
  components: {
    IconPlus
  },
  name: 'LuckyTemplate',
  props: {
    moduleId: {
      // 模块id
      type: String,
      default: ''
    },

    prizeList: {
      // 接受返回的数组
      type: Array,
      default() {
        return []
      }
    },
    lotteryMode: {
      // 抽奖模式
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      arrList: [],
      imageindex: {
        signPoint: '', // 记录奖项的位置值
        index: '' // 记录点击事件的位置值
      },
      options: [
        {
          value: '1',
          label: '1',
          disabled: false
        },
        {
          value: '2',
          label: '2',
          disabled: false
        },
        {
          value: '3',
          label: '3',
          disabled: false
        },
        {
          value: '4',
          label: '4',
          disabled: false
        },
        {
          value: '5',
          label: '5',
          disabled: false
        },
        {
          value: '6',
          label: '6',
          disabled: false
        },
        {
          value: '7',
          label: '7',
          disabled: false
        },
        {
          value: '8',
          label: '8',
          disabled: false
        }
      ]
    }
  },
  computed: {},
  watch: {
    prizeList: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        const me = this
        // 返回的奖项对象的key添加下标
        const prizeArr = []
        me.options.map((item) => {
          item.disabled = false
        })
        newVal.map((item) => {
          const prizeObj = {}
          me.options[[item.position] - 1].disabled = true
          Object.keys(item).forEach(function (x) {
            let xStr = x
            if (xStr !== 'signPoint') {
              xStr = xStr + item.signPoint
            }
            prizeObj[xStr] = item[x]
          })
          prizeArr.push(prizeObj)
        })

        me.arrList = prizeArr
      }
    },
    lotteryMode(oldVal, newVal) {
      console.log(oldVal, newVal)
      if (oldVal !== newVal) {
        this.arrList = []
      }
    }
  },
  activated() {},
  methods: {
    // 选着奖项位置
    changeItem() {
      const me = this
      me.options.map((item) => {
        item.disabled = false
      })
      me.arrList.map((item) => {
        if (item['position' + item.signPoint]) {
          me.options[item['position' + item.signPoint] - 1].disabled = true
        }
      })
    },
    // 获取对应点击事件的下标
    tapclick(signPoint, index) {
      this.imageindex.signPoint = signPoint
      this.imageindex.index = index
    },
    // 新增奖品配置
    addPrizeConfig(text) {
      const aprizeObj = {}
      let signPoint = 0
      const arrListLength = this.arrList.length - 1
      if (arrListLength > 6) {
        this.$message.warning('最多可创建八个奖项')
        return
      }
      if (this.arrList.length > 0) {
        signPoint = this.arrList[arrListLength].signPoint + 1
      }
      let arrKeyList =
        this.lotteryMode === '1'
          ? [
              'activeName',
              'title',
              'prizeCost',
              'image',
              'num',
              'probability',
              'position'
            ]
          : ['activeName', 'title', 'prizeCost', 'image', 'num', 'position']
      if (text.signPoint >= 0) {
        Object.keys(text).map((key) => {
          const str = key.replace(text.signPoint, signPoint)
          if (key.indexOf('position') > -1) {
            aprizeObj[str] = ''
          } else {
            if (key.indexOf('prizeId') > -1) {
              delete text.key
            } else {
              aprizeObj[str] = text[key]
            }
          }
        })
      } else {
        arrKeyList.map((item) => {
          aprizeObj[item + signPoint] = ''
        })
      }
      // if (text) {
      //   arrKeyList.map((item, index) => {
      //     console.log(item, '9877979')
      //     if (item === 'position') {
      //       aprizeObj[item + signPoint] = ''
      //     } else {
      //       aprizeObj[item + signPoint] = text[index]
      //     }
      //   })
      // } else {
      //   arrKeyList.map((item) => {
      //     aprizeObj[item + signPoint] = ''
      //   })
      // }

      aprizeObj.signPoint = signPoint
      this.arrList.push(aprizeObj)
    },

    // 移除奖项
    removePrize(item, index) {
      this.arrList.splice(index, 1)
      if (item['position' + item.signPoint] - 1 >= 0) {
        this.options[item['position' + item.signPoint] - 1].disabled = false
      }
    },

    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },

    handleAvatarSuccess(res, file) {
      const imageindex = this.imageindex
      this.arrList[imageindex.index]['image' + imageindex.signPoint] =
        (file.response && file.response.imgUrl) || ''
    },
    // 拷贝数据
    copyData(object) {
      const me = this
      const text = []
      Object.keys(object).forEach(function (x) {
        text.push(object[x])
      })
      me.copy(text)
      // 复制新增一行
      // me.addPrizeConfig(text)
      me.addPrizeConfig(object)
    },
    copy(text) {
      var textareaEl = document.createElement('textarea')
      textareaEl.setAttribute('readonly', 'readonly') // 防止手机上弹出软键盘
      textareaEl.value = text
      document.body.appendChild(textareaEl)
      textareaEl.select()
      var res = document.execCommand('copy')
      document.body.removeChild(textareaEl)
      return res
    }
  }
}
</script>

<style lang="scss" scoped>
.prize {
  width: 1200px;
  background-color: #f2f2f2;
  padding: 20px;
  .prize_title {
    display: flex;
    margin: 10px 0 20px 0;
    text-align: center;
    > div {
      width: 170px;
    }
    .prize_title-small {
      width: 140px;
    }
  }
  .content {
    display: flex;
    align-items: center;
    > div {
      margin: 10px 15px 0 0;
    }
    input {
      width: 150px;
      height: 30px;
      line-height: 30px;
      border: 1px solid gray;
      border-radius: 5px;
      font-size: 14px;
    }
    border-bottom: 1px solid rgb(196, 195, 193);
    padding-bottom: 10px;
    .content_edit {
      margin-left: 20px;
      width: 170px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      text-align: center;
      > div {
        border: 1px solid gold;
        width: 60px;
        padding: 5px 0;
      }
    }
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #686464;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 150px;
  height: 100px;
  display: block;
}
</style>
