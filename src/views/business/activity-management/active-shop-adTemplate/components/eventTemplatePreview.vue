<template>
  <div class="prize">
    <el-dialog
      v-model="dialogVisible"
      title="厂家活动模板预览"
      width="380px"
      @close="closeDialog()"
    >
      <div style="height: 70vh; overflow-y: scroll">
        <div
          v-for="(item, index) in fileList"
          :key="index"
          class="fileList-sty"
        >
          <div v-if="index == signUpPosition" class="safe-info">
            报名表单-占位
          </div>
          <div
            v-else-if="
              banImgPosiArr && banImgPosiArr.includes(index.toString()) && item
            "
          >
            <el-carousel
              v-if="item.images && item.images.length > 0"
              :interval="1000"
              height="150px"
            >
              <el-carousel-item
                v-for="(banimage, banIndex) in item.images"
                :key="banIndex"
                class="swipe-item"
              >
                <div class="banner-img"><img :src="banimage.url" /></div>
              </el-carousel-item>
            </el-carousel>
          </div>
          <div
            v-else-if="
              videoImgPosiArr &&
              videoImgPosiArr.includes(index.toString()) &&
              item
            "
            class="video-sty"
          >
            <video
              id="Myvideo"
              :src="item.video.link"
              :poster="item.video.img"
              class="video-sty-v"
              webkit-playsinline
              playsinline
            />
          </div>
          <div v-else>
            <img
              v-if="item && item.url"
              :src="item.url"
              class="clientele-image"
              alt=""
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'EventTemplatePreview',
  data() {
    return {
      fileList: [],
      signUpPosition: 0, // 报名表单位置
      banImgPosiArr: [], // banner图位置信息
      videoImgPosiArr: [], // 视频图位置信息
      dialogVisible: false,
      propsData: {},
    }
  },
  computed: {},
  methods: {
    init(data) {
      const me = this
      const dataObj = JSON.parse(JSON.stringify(data))
      me.dialogVisible = true
      me.fileList = dataObj.fileList
      me.signUpPosition = dataObj.signUpPosition || '0'
      me.propsData = dataObj
      me.banImgPosiArr = [] // banner图位置信息
      me.videoImgPosiArr = [] // 视频图位置信息
      // 处理数据位置值
      me.dealPosition(dataObj)
    },
    // // 处理数据位置值
    dealPosition(dataObj) {
      const me = this
      const positionArr = Array(100).fill('')
      const obj = { signUp: true }
      positionArr[dataObj.signUpPosition] = obj

      // banner位置数据处理
      const carouseList = dataObj.carouseList || []
      carouseList &&
        carouseList.length > 0 &&
        carouseList.map((item, index) => {
          positionArr[item.position] = { images: item.image }
          me.banImgPosiArr.push(item.position)
        })

      // // 视频位置数据处理
      const videoList = dataObj.videoList || []
      videoList &&
        videoList.length > 0 &&
        videoList.map((item, index) => {
          positionArr[item.position] = { video: item.video }
          me.videoImgPosiArr.push(item.position)
        })
      if (me.videoImgPosiArr.length > 0) {
        me.$nextTick(() => {
          setTimeout(() => {
            const myVideo = document.getElementsByClassName('video-sty-v')
            for (let index = 0; index < myVideo.length; index++) {
              myVideo[index].controls = true
            }
          }, 1000)
        })
      }

      // 赋值
      let positionIndex = 0
      positionArr.map((item, index) => {
        if (!item) {
          positionArr[index] = me.fileList[positionIndex] || ''
          positionIndex++
        }
      })
      // const positionArrList = positionArr.filter((item) => {
      //   return item
      // })
      // console.log(positionArrList, 'positionArrListpositionArrList')
      // me.fileList = positionArrList
      me.fileList = positionArr
    },
    // 关闭预览
    closeDialog() {
      this.$parent.previewShow = false
    },
  },
}
</script>

<style lang="scss" scoped>
.fileList-sty {
  img {
    width: 300px;
    height: 150px;
    font-size: 0;
    padding: 0;
    margin: 0;
  }
  video {
    width: 300px;
    height: 150px;
  }
}
.safe-info {
  height: 150px;
  width: 300px;
  line-height: 150px;
  text-align: center;
  background-color: #bbb8b8;
}
</style>
