<template>
  <div class="carouse">
    <el-button type="primary" style="margin-bottom: 20px" @click="addCarouse"
      >添加</el-button
    >
    <div
      v-for="(item, index) in arrList"
      :key="index"
      class="content"
      @click="tapclick(item.signPoint, index)"
    >
      <div class="carouse-title">第{{ index + 1 }}组</div>
      <div class="carouse-content">
        <div class="carouse-content-top">
          <div class="carouse-content-top-left">
            <p class="carouse-content-top-left-text">视频位置</p>
            <el-input
              v-model="item['position' + item.signPoint]"
              data-sign="position"
              placeholder="请输入视频位置"
            />
          </div>
          <el-button type="danger" size="small" @click="removePrize(index)"
            >删除</el-button
          >
        </div>
        <!-- 图片上传与重排 -->
        <div class="video">
          <div class="video-title">选择文件</div>
          <div @click="seeVideoPlay(item['video' + item.signPoint].link)">
            <el-image
              v-if="item['video' + item.signPoint].img"
              :src="item['video' + item.signPoint].img"
              class="video-img"
            ></el-image>
          </div>
          <el-button
            type="primary"
            style="margin-bottom: 20px; height: 40px"
            @click="selectVideo(item)"
            >选择视频</el-button
          >
        </div>
      </div>
    </div>
    <el-dialog
      v-model="dialogVisible"
      width="600px"
      height="1000px"
      title="视频配置"
      @close="dialogVisible = false"
    >
      <div v-if="dialogVisible">
        <VideoEditor
          ref="VideoEditor"
          :fromSource="fromSource"
          :isAcitve="true"
          @updatePGCContent="updatePGCContent"
        />
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="corfimVideo">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <choose-video ref="ChooseVideo" />
  </div>
</template>

<script>
import VideoEditor from '@/components/Squire/VideoEditor.vue'
import ChooseVideo from '@/components/Dialog/ChooseVideo.vue'
export default {
  name: 'VideoTemplate',
  components: {
    VideoEditor,
    ChooseVideo,
  },
  props: {
    videoList: {
      // 接受返回的数组
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      arrList: [],
      imageindex: {
        signPoint: '', // 记录奖项的位置值
        index: '', // 记录点击事件的位置值
      },
      dialogVisible: false, // 视频弹窗
      videoInfo: {}, // 视频数据
      fromSource: 'activity',
    }
  },
  computed: {},
  watch: {
    videoList: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        const me = this
        // 返回的轮播对象的key添加下标
        const prizeArr = []
        newVal.map((item) => {
          const prizeObj = {}
          Object.keys(item).forEach(function (x) {
            let xStr = x
            if (xStr !== 'signPoint') {
              xStr = xStr + item.signPoint
            }
            prizeObj[xStr] = item[x]
          })
          prizeArr.push(prizeObj)
        })
        me.arrList = prizeArr
      },
    },
  },
  activated() {},
  methods: {
    // 查看视频
    seeVideoPlay(link) {
      console.log(link, 'link')
      if (!link) {
        return this.$message.error('暂无视频播放地址')
      }
      this.$refs.ChooseVideo.init(link)
    },
    // 选择视频
    selectVideo(item) {
      const me = this
      const carouseObj = {}
      const imageindex = this.imageindex
      let num = 1
      me.dialogVisible = true
      console.log(item, 'item,=========')
      if (item) {
        setTimeout(() => {
          if (item['signPoint' + imageindex.signPoint] >= 10) {
            num = 2
          }
          Object.keys(item).forEach(function (x) {
            let xStr = x
            if (xStr !== 'signPoint') {
              xStr = xStr.substr(0, xStr.length - num)
            }
            carouseObj[xStr] = item[x]
          })
          console.log(carouseObj, 'carouseObjcarouseObj')
          this.videoInfo = carouseObj.video
          me.$refs.VideoEditor && me.$refs.VideoEditor.init(carouseObj.video)
        })
      }
    },
    // 确认
    corfimVideo() {
      const me = this
      if (!this.videoInfo || (this.videoInfo && !this.videoInfo.link)) {
        this.$message.error('请上传视频！')
        return
      }
      if (this.videoInfo && !this.videoInfo.img) {
        this.$message.error('请上传视频封面！')
        return
      }

      const imageindex = this.imageindex
      me.arrList[imageindex.index]['video' + imageindex.signPoint] =
        this.videoInfo || {}
      this.dialogVisible = false
      console.log(me.arrList, 'me.arrList-获取视频返回值')
    },
    // 获取视频返回值
    updatePGCContent(data, fileId) {
      console.log(data, 'data-获取视频返回值')
      this.videoInfo = data || {}
    },

    // 获取对应点击事件的下标
    tapclick(signPoint, index) {
      console.log(signPoint, 'signPoint')
      this.imageindex.signPoint = signPoint
      this.imageindex.index = index
    },
    // 新增视频配置
    addCarouse(text) {
      const me = this
      const aprizeObj = {}
      let signPoint = 0
      me.videoInfo = {} // 视频数据
      const arrListLength = me.arrList.length - 1
      if (me.arrList.length > 0) {
        signPoint = me.arrList[arrListLength].signPoint + 1
      }
      const arrKeyList = ['video', 'position']
      arrKeyList.map((item) => {
        if (item === 'video') {
          aprizeObj[item + signPoint] = {}
        } else {
          aprizeObj[item + signPoint] = ''
        }
      })
      aprizeObj.signPoint = signPoint
      me.arrList.push(aprizeObj)
    },

    // 移除轮播
    removePrize(index) {
      this.arrList.splice(index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.carouse {
  width: 1200px;
  padding: 20px;
  .content {
    display: flex;
    padding: 10px;
    margin-bottom: 20px;
    border: 1px solid rgb(196, 195, 193);
    padding-bottom: 10px;
    .carouse-title {
      margin-top: 14px;
      min-width: 80px;
    }
    .carouse-content {
      .carouse-content-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 1060px;
        margin-bottom: 20px;
        .carouse-content-top-left {
          display: flex;
          align-items: center;
          .carouse-content-top-left-text {
            margin-right: 60px;
            color: #606266;
            font-size: 14px;
            width: 100px;
          }
        }
      }
      .video {
        display: flex;
        .video-title {
          margin-right: 60px;
          color: #606266;
          font-size: 14px;
          width: 65px;
        }
        .video-img {
          width: 300px;
          height: 200px;
          margin-right: 30px;
        }
      }
    }
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #686464;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 150px;
  height: 100px;
  display: block;
}
</style>
