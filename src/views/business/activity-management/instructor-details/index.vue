<template>
  <div v-loading="loading" style="padding: 20px">
    <p class="title">{{ title }}</p>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="ruleForm.title"
          placeholder="直接选一个活动标签"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="评选封面">
        <p>
          <el-button
            v-if="ruleForm.cover"
            type="danger"
            style="margin-left: 20px"
            @click="ruleForm.cover = ''"
            >删除图片</el-button
          >
        </p>
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccess"
          name="upfile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <img
            v-if="ruleForm.cover"
            :src="ruleForm.cover"
            style="max-width: 400px; max-height: 400px"
            alt=""
          />
          <el-button type="primary" style="margin-left: 20px"
            >上传图片</el-button
          >
        </el-upload>
      </el-form-item>
      <el-form-item label="简单介绍">
        <el-input
          v-model="ruleForm.description"
          placeholder="仅支持文字"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="更多介绍">
        <el-input
          :rows="10"
          v-model="ruleForm.details"
          type="textarea"
          style="width: 450px"
        />
      </el-form-item>
      <div class="type-lists">
        <el-form-item label="已选择知识点" class="type-list">
          <label-content
            ref="label"
            :type="'label'"
            @deleteLabel="deleteLabel"
            @labelAllData="updataLabelAllData"
          />
          <search-label
            :type="'label'"
            class="search-label-content"
            @addLabel="addLabel"
          />
        </el-form-item>
      </div>
      <el-form-item label="活动开始时间" prop="beginTime">
        <el-date-picker
          v-model="ruleForm.beginTime"
          type="datetime"
          value-format="x"
          placeholder="选择日期时间"
        />
      </el-form-item>
      <el-form-item label="活动结束时间" prop="endTime">
        <el-date-picker
          v-model="ruleForm.endTime"
          type="datetime"
          value-format="x"
          placeholder="选择日期时间"
        />
      </el-form-item>
      <el-form-item label="作者榜单名称">
        <el-input
          v-model="ruleForm.authorListTitle"
          placeholder="请输入作者榜单名称"
          style="width: 300px"
        />
        <el-checkbox
          v-model="ruleForm.dispalyAuthorList"
          style="margin-left: 20px; margin-top: 10px"
          >显示</el-checkbox
        >
      </el-form-item>
      <el-form-item label="是否有效">
        <el-switch v-model="ruleForm.status" active-text="" inactive-text="" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  GetSelectionInfo,
  PostSaveOrUpdateInfo,
} from '@/api/activeConfiguration'
import searchLabel from '@/components/label/searchLabel.vue'
import labelContent from '@/components/label/labelContent.vue'
import { timeFullS } from '@/filters'
import { deepCopy } from '@/utils'
import { mapGetters } from 'vuex'
import { batchRecordBeforeAlter } from '@/utils/enum/logData'
export default {
  name: 'EditVoting',
  components: {
    searchLabel,
    labelContent,
  },
  data() {
    return {
      loading: false,
      title: '添加',
      itemList: [],
      ruleForm: {},
      rules: {
        title: [{ required: true, message: '请输入投票标题', trigger: 'blur' }],
        beginTime: [
          {
            type: 'date',
            required: true,
            message: '请选择时间',
            trigger: 'blur',
          },
        ],
        endTime: [
          {
            type: 'date',
            required: true,
            message: '请选择时间',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  computed: {
    ...mapGetters(['uid']),
  },
  mounted() {},
  activated() {
    this.$route.query && this.$route.query.id ? this.getData() : this.initData()
    this.title = this.$route.query && this.$route.query.id ? '编辑' : '添加'
  },
  methods: {
    // 获取数据
    getData() {
      const me = this
      GetSelectionInfo({
        id: me.$route.query.id,
      }).then((response) => {
        if (response.data.code === 0) {
          me.ruleForm = response.data.data
          batchRecordBeforeAlter(me.ruleForm, me.$route.query.id)
          me.ruleForm.status = !!me.ruleForm.status
          me.ruleForm.dispalyAuthorList = !!me.ruleForm.dispalyAuthorList
          me.itemList = me.ruleForm.itemList
          me.itemList.map((_) => {
            _.id = _.tagId
            _.selected = true
            _.labelName = _.tagName
          })
          me.$refs.label.addLable(me.itemList)
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    // 添加时，手动写入数据
    initData() {
      this.ruleForm = {
        title: '',
        description: '',
        beginTime: '',
        endTime: '',
        cover: '',
        dispalyAuthorList: true,
        status: true,
      }
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      this.$oss.ossUploadImage(option)
    },
    onSuccess(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        this.ruleForm['cover'] = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误',
        })
      }
    },
    // 更新关联标签状态
    deleteLabel(item, type) {
      const me = this
      setTimeout(() => {
        me.$refs.label.getAllData()
      }, 800)
    },
    // 增加标签
    addLabel(data, type) {
      const me = this
      me.$refs.label.addLable(data)
      setTimeout(() => {
        me.$refs.label.getAllData()
      }, 800)
    },
    // 修改快捷标签
    updataLabelAllData(data) {
      this.itemList = []
      this.itemList = data
    },
    // 提交
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.saveDetail()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    saveDetail() {
      const me = this
      if (me.ruleForm.beginTime > me.ruleForm.endTime) {
        return me.$message.error('开始时间不得大于结束时间')
      }
      me.loading = true
      const postData = deepCopy(me.ruleForm)
      postData.beginTime = timeFullS(postData.beginTime)
      postData.endTime = timeFullS(postData.endTime)
      postData.status = postData.status ? '1' : '0'
      postData.dispalyAuthorList = postData.dispalyAuthorList ? '1' : '0'
      const selectionItems = []
      me.itemList.map((_) => {
        selectionItems.push({
          tagId: _.id,
          labelName: _.labelName,
        })
      }) // 将id转成 tagId, 并删除id
      postData.selectionItems = JSON.stringify(selectionItems)
      const tip = postData.id ? '编辑' : '添加'
      PostSaveOrUpdateInfo(postData)
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success(`${tip}成功`)
            me.loading = false
            setTimeout(() => {
              me.$router.go(-1)
            }, 2000)
          } else {
            me.$message.error(response.data.msg)
            me.loading = false
          }
        })
        .catch(() => {
          this.$message.error('保存异常')
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>
