<template>
  <div v-loading="loading" class="act-detail">
    <p>
      <el-button type="danger" @click="handleClose">取消</el-button>
      <el-button type="primary" @click="validateData">保存</el-button>
    </p>
    <el-row>
      <el-col :span="18">
        <div class="add">
          <basicConfigurationTemplate
            ref="basicConfigurationTemplate"
            @postData="updateData"
          />
          <relatesInformationTemplate
            ref="relatesInformationTemplate"
            @postData="updateData"
          />
          <p>组件配置</p>
          <p>
            <el-button type="primary" size="small" @click="addItem('img')">
              + 图片
            </el-button>
            <el-button type="primary" size="small" @click="addItem('swipe')">
              + 轮播
            </el-button>
            <el-button type="primary" size="small" @click="addItem('car')">
              + 车型
            </el-button>
          </p>
          <draggable
            v-model="showData"
            item-key="createDate"
            handle=".handle-title"
          >
            <template #item="{ element }">
              <div>
                <template v-if="element.type === 'img'">
                  <imgTemplate
                    :data="element"
                    :ref="`img${element.createDate}`"
                    @updateData="updateShowData"
                  />
                </template>
                <template v-if="element.type === 'swipe'">
                  <swipeTemplate
                    :data="element"
                    :ref="`swipe${element.createDate}`"
                    @updateData="updateShowData"
                  />
                </template>
                <template v-if="element.type === 'car'">
                  <carTemplate
                    :data="element"
                    :ref="`car${element.createDate}`"
                    @updateData="updateShowData"
                  />
                </template>
              </div>
            </template>
          </draggable>
          <!-- <div v-for="(data, index) in showData" :key="index">
            <template v-if="data.type === 'img'">
              <imgTemplate
                :data="data"
                :ref="`img${data.createDate}`"
                @updateData="updateShowData"
              />
            </template>
            <template v-if="data.type === 'swipe'">
              <swipeTemplate
                :data="data"
                :ref="`swipe${data.createDate}`"
                @updateData="updateShowData"
              />
            </template>
            <template v-if="data.type === 'car'">
              <carTemplate
                :data="data"
                :ref="`car${data.createDate}`"
                @updateData="updateShowData"
              />
            </template>
          </div> -->
          <p>详情配置</p>
          <detailedConfigurationTemplate
            ref="detailedConfigurationTemplate"
            @postData="updateData"
          />
          <detailConfigTemplate
            ref="detailConfigTemplate"
            @postData="updateData"
          />
          <!-- <tabTemplate ref="tabTemplate" @postData="updateData" /> -->
          <!-- <contentTemplate ref="contentTemplate" @postData="updateData" /> -->
          <awardTemplate ref="awardTemplate" @postData="updateData" />
          <publishConfigurationTemplate
            ref="publishConfigurationTemplate"
            @postData="updateData"
          />
        </div>
        <div>
          <ruleTemplate ref="ruleTemplate" @postData="updateData" />
          <examineRuleTemplate
            ref="examineRuleTemplate"
            @postData="updateData"
          />
          <shareTemplate ref="shareTemplate" @postData="updateData" />
          <contentShareTemplate
            ref="contentShareTemplate"
            @postData="updateData"
          />
        </div>
      </el-col>
      <!-- <el-col :span="1"> &ensp; </el-col>
      <el-col :span="6">
        <p>预览</p>
        <draggable
          class="left-content"
          v-model="showData"
          item-key="createDate"
          @end="refreshImageList"
        >
          <template #item="{ element }">
            <div>
              <template v-if="element.type === 'img'">
                <showImage :data="element" @updateData="updateShowData" />
              </template>
              <template v-if="element.type === 'swipe'">
                <showSwipe :data="element" @updateData="updateShowData" />
              </template>
              <template v-if="element.type === 'car'">
                <showCar :data="element" @updateData="updateShowData" />
              </template>
            </div>
          </template>
        </draggable>
        <showTab
          v-if="ruleForm.tab && ruleForm.tab.tabList"
          :data="ruleForm.tab.tabList"
        />
        <showTab
          v-if="ruleForm.tab && ruleForm.tab.tabAllList"
          :data="ruleForm.tab.tabAllList"
        />
        <showAward v-if="ruleForm.award" :data="ruleForm.award" />
      </el-col> -->
    </el-row>
  </div>
</template>

<script>
import { deepCopy } from '@/utils'
import basicConfigurationTemplate from './components/basic-configuration-template.vue'
import relatesInformationTemplate from './components/relates-information-template.vue'
import imgTemplate from './components/img-template.vue'
import swipeTemplate from './components/swipe-template.vue'
import carTemplate from './components/car-template.vue'
import detailedConfigurationTemplate from './components/detailed-configuration-template.vue'
// import tabTemplate from './components/tab-template.vue'
import detailConfigTemplate from './components/detail-config-template.vue'
// import contentTemplate from './components/content-template.vue'
import awardTemplate from './components/award-template.vue'
import publishConfigurationTemplate from './components/publish-configuration-template.vue'
import ruleTemplate from './components/rule-template.vue'
import examineRuleTemplate from './components/examine-rule-template.vue'
import shareTemplate from './components/share-template.vue'
import contentShareTemplate from './components/content-share-template.vue'
// import showSwipe from './components/show-swipe.vue'
// import showImage from './components/show-image.vue'
// import showCar from './components/show-car.vue'
// import showTab from './components/show-tab.vue'
// import showAward from './components/show-award.vue'
import draggable from 'vuedraggable'
import {
  saveActAndTempletPrize,
  GetTempletDetail
} from '@/api/activeConfiguration'
export default {
  name: 'ugcActiveConfigurationDetail',
  components: {
    basicConfigurationTemplate,
    relatesInformationTemplate,
    imgTemplate,
    swipeTemplate,
    carTemplate,
    detailedConfigurationTemplate,
    // tabTemplate,
    detailConfigTemplate,
    // contentTemplate,
    awardTemplate,
    publishConfigurationTemplate,
    ruleTemplate,
    examineRuleTemplate,
    shareTemplate,
    contentShareTemplate,
    // showImage,
    // showSwipe,
    // showCar,
    // showTab,
    // showAward,
    draggable
  },
  data() {
    return {
      loading: false,
      addItemStatus: false, // 加入数据状态
      postStatus: false, // 发送接口状态
      ruleForm: {}, // json
      showData: [], // 内容展示数据
      activityId: ''
    }
  },
  activated() {
    const query = this.$route.query || {}
    this.init(query)
  },
  methods: {
    async init(item) {
      const me = this
      if (item.activityId) {
        me.loading = true
        me.activityId = item.activityId
        await GetTempletDetail({
          activityId: item.activityId,
          templetId: item.templetId
        })
          .then((response) => {
            if (response.data.code === 0) {
              const data = response.data.data
              const JsonData = JSON.parse(data.templetJson)
              me.ruleForm = JsonData || {}
              me.$nextTick(() => {
                Object.keys(JsonData).map((item) => {
                  if (item === 'showData') {
                    me.showData = JsonData[item] || []
                    // setTimeout(() => {
                    //   me.showData.map((addItem) => {
                    //     me.$refs[
                    //       `${addItem.type}${addItem.createDate}`
                    //     ][0].init(addItem)
                    //   })
                    // }, 200)
                  } else {
                    me.$refs[`${item}Template`] &&
                      me.$refs[`${item}Template`].init(JsonData[item], true)
                  }
                })
              })
            }
          })
          .finally(() => {
            me.loading = false
          })
      } else {
        me.showData = []
        Object.keys(me.$refs).map((item) => {
          me.$refs[item].init({})
        })
      }
    },
    // 增加数据
    addItem(type) {
      const me = this
      if (me.addItemStatus) return
      me.addItemStatus = true
      me.$message.success('加入数据成功')
      const createDate = Math.floor(new Date())
      const addItem = {
        createDate: createDate,
        type
      }
      me.showData.push(addItem)
      // setTimeout(() => {
      //   me.$refs[`${type}${createDate}`][0].init(addItem)
      // }, 200)
      setTimeout(() => {
        me.addItemStatus = false
      }, 1000)
    },
    // 更新数据
    updateData(type, data) {
      this.ruleForm[type] = data
    },
    // 更新内容数据
    updateShowData(type, data) {
      const me = this
      const findIndex = me.showData.findIndex((item) => {
        if (item.createDate === data.createDate && data.type === item.type) {
          return item
        }
      })
      if (type === 'update') {
        me.showData.splice(findIndex, 1, data)
      } else if (type === 'copy') {
        const addItem = {
          ...deepCopy(data),
          createDate: Math.floor(new Date())
        }
        me.showData.splice(findIndex + 1, 0, addItem)
        setTimeout(() => {
          me.showData.map((item) => {
            me.$refs[`${item.type}${item.createDate}`][0].init(
              item,
              `${item.type}${item.createDate}` ===
                `${addItem.type}${addItem.createDate}`
            )
          })
        }, 200)
      } else {
        me.showData.splice(findIndex, 1)
        // me.refreshImageList()
      }
    },
    // 获取排序列表
    refreshImageList() {
      const tempArr = this.showData
      this.showData = []
      this.showData = tempArr
      const me = this
      setTimeout(() => {
        me.showData.map((addItem) => {
          me.$refs[`${addItem.type}${addItem.createDate}`][0].init(addItem)
        })
      }, 200)
    },
    // 保存数据
    validateData() {
      const me = this
      let isPost = true
      const keyList = Object.keys(me.$refs)
      keyList.forEach((key) => {
        if (me.$refs[key] && me.$refs[key].validate) {
          me.$refs[key].validate(() => {
            isPost = false
          })
        }
        // else if (me.$refs[key][0] && me.$refs[key][0].validate) {
        //   me.$refs[key][0].validate(() => {
        //     isPost = false
        //   })
        // }
      })
      setTimeout(() => {
        if (!isPost) return me.$message.error('有必填项未填写')
        me.saveData()
      }, 100)
    },
    // 保存数据
    saveData() {
      const me = this
      if (me.postStatus) return
      me.postStatus = true
      const postData = {
        isNew: true,
        ...me.ruleForm,
        showData: me.showData
      }
      saveActAndTempletPrize({
        activityName: postData.basicConfiguration.activeName,
        shareIcoUrl: (postData.share && postData.share.shareImage) || '',
        shareTitle: (postData.share && postData.share.shareDesc) || '',
        shareDesc: (postData.share && postData.share.shareTitle) || '',
        beginTime: postData.basicConfiguration.startTime || '',
        endTime: postData.basicConfiguration.endTime || '',
        templetId: '2',
        templetJson: JSON.stringify(postData),
        activityId: me.$route.query.copy ? '' : me.activityId || '',
        advertiserType: postData.relatesInformation.adProjectType || '', // 活动方类型(同广告方)
        advertiserId: postData.relatesInformation.orgId || '', // 活动方id(同广告方id)
        brandId: postData.relatesInformation.relationId || '', // 厂家id
        factoryProjectId: postData.relatesInformation.factoryProjectId || '', // 厂家合作项目id
        presetShows: postData.relatesInformation.activeTotalExposure || '', // 配置曝光次数
        singleVoteTotal: postData.detailConfig.voteRuleAll || '', // 单人总投票次数
        singleVoteDaily: postData.detailConfig.voteRuleEveryday || '', // 单人每天投票次数
        singleVoteEssay: postData.detailConfig.voteRuleSingle || '', // 单人每天单篇内容投票次数
        activityType: '6'
      })
        .then((res) => {
          if (res.data && res.data.code === 1001) {
            me.$message.error(res.data.msg)
            return
          }
          me.$message.success('配置成功')
          this.$router.go(-1)
        })
        .finally(() => {
          me.postStatus = false
        })
    },
    // 取消
    handleClose() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss">
.act-detail {
  padding: 0 10px;
}
.title {
  font-weight: 500;
  margin-bottom: 10px;
}
.tip {
  font-weight: 400;
  font-size: 12px;
  color: #999;
}
.sign-up-template {
  border: 1px solid #dedede;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 20px;
}

.award-list {
  line-height: 50px;
}
.award-img {
  width: 200px;
  height: 100px;
  object-fit: cover;
}

.award-index {
  position: relative;
  top: -8px;
}

.share-img {
  width: 100px;
  height: 100px;
  object-fit: cover;
}
</style>
