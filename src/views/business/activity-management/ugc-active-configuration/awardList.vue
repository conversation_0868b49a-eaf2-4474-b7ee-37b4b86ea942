<template>
  <div class="award-list">
    <el-form :inline="true" :model="ruleForm">
      <el-form-item label="用户ID">
        <el-input v-model="ruleForm.uid" clearable class="input-style" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="resize">重置</el-button>
        <el-button @click="exportExcel">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" border max-height="76vh">
      <el-table-column prop="authorId" label="用户ID" align="center" />
      <el-table-column prop="author" label="用户名称" align="center" />
      <el-table-column prop="activityId" label="活动ID" align="center" />
      <el-table-column label="活动名称" align="center">
        <template #default> {{ activityName }} </template>
      </el-table-column>
      <el-table-column label="文章信息" align="center">
        <template #default="{ row }">
          <div>{{ row.essayId }}</div>
          <div>{{ $filters.subString(row.content || row.title, 30) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="awardName" label="奖项" align="center" />
      <el-table-column prop="prizeName" label="奖品名称" align="center" />
      <el-table-column prop="uname" label="姓名" align="center" />
      <el-table-column prop="mobile" label="手机号" align="center" />
      <el-table-column label="省市区" align="center">
        <template #default="{ row }">
          {{ row.province }} {{ row.city }}
        </template>
      </el-table-column>
      <el-table-column prop="address" label="详细地址" align="center" />
      <el-table-column label="提交时间" align="center" width="180">
        <template #default="{ row }">
          {{ $filter.format(row.submitTime, 'YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="80">
        <template #default="{ row }">
          <el-button
            v-if="row.awardName && row.prizeName"
            type="primary"
            link
            size="small"
            @click="modification(row)"
          >
            修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      background
      layout="total, prev, pager, next, jumper"
      :total="total"
      @current-change="currentChange"
      style="justify-content: center; margin-top: 15px"
    />
    <el-dialog v-model="dialogVisible" title="修改" width="500">
      <el-form :model="awardForm" label-width="auto">
        <el-form-item label="用户ID">
          <div>{{ awardForm.uid }}</div>
        </el-form-item>
        <el-form-item label="活动名称">
          <div>{{ activityName }}</div>
        </el-form-item>
        <el-form-item v-if="awardConfig.includes(1)" label="姓名">
          <el-input v-model="awardForm.uname" />
        </el-form-item>
        <el-form-item v-if="awardConfig.includes(2)" label="手机号">
          <el-input v-model="awardForm.mobile" maxlength="11" />
        </el-form-item>
        <el-form-item v-if="awardConfig.includes(3)" label="省市区">
          <el-input v-model="provinceCity" readonly @click="getProvinceCity" />
        </el-form-item>
        <el-form-item v-if="awardConfig.includes(3)" label="详细地址">
          <el-input v-model="awardForm.address" />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmModification">
          确认
        </el-button>
      </div>
    </el-dialog>
    <CArea ref="cAreaRef" @backArea="setProvinceCity" />
  </div>
</template>

<script setup>
import {
  getUgcPrizeList,
  GetTempletDetail,
  updateOSSPrizeReceiveInfo
} from '@/api/activeConfiguration'
import CArea from '@/components/area/c-area.vue'
import { decodeMobile1 } from '@/api/commonModule'

const { proxy } = getCurrentInstance()
const route = useRoute()

const formInit = {
  uid: '',
  activityId: ''
}
const ruleForm = reactive({ ...formInit })
const activityName = ref('')
const page = ref(1)
const limit = ref(20)
const total = ref(0)
const tableData = ref([])

onMounted(() => {
  ruleForm.activityId = route.query.activityId || ''
  activityName.value = route.query.activityName || ''
  getList()
})

const search = () => {
  currentChange(1)
}

const resize = () => {
  ruleForm.uid = ''
  currentChange(1)
}

const getList = () => {
  getUgcPrizeList({
    ...ruleForm,
    page: page.value,
    limit: limit.value
  }).then((res) => {
    if (res.data.code === 0) {
      const data = res.data.data || {}
      tableData.value = data.listData || []
      total.value = data.total || 0
    }
  })
}

const currentChange = (num) => {
  page.value = num
  getList()
}

const dialogVisible = ref(false)
const awardConfig = ref([])
const awardFormInit = {
  id: '',
  uid: '',
  activityId: '',
  uname: '',
  mobile: '',
  province: '',
  city: '',
  address: ''
}
const awardForm = reactive({ ...awardFormInit })
const provinceCity = computed(() => {
  return `${awardForm.province} ${awardForm.city}`
})
const modification = (row) => {
  const query = route.query || {}
  awardConfig.value = []
  awardForm.id = row.id || ''
  awardForm.uid = row.uid || ''
  awardForm.activityId = row.activityId || ''
  awardForm.uname = row.uname || ''
  awardForm.mobile = row.mobile || ''
  awardForm.province = row.province || ''
  awardForm.city = row.city || ''
  awardForm.address = row.address || ''
  if (awardForm.mobile) {
    decodeMobile1({
      mobile: awardForm.mobile
    }).then((res) => {
      if (res.data.code === 0) {
        awardForm.mobile = res.data.data || ''
      }
    })
  }
  GetTempletDetail({
    activityId: query.activityId,
    templetId: query.templetId
  }).then((res) => {
    if (res.data.code === 0) {
      const data = res.data.data || {}
      const JsonData = JSON.parse(data.templetJson || '{}')
      const award = JsonData.award || []
      const item = award.find(
        (_) => _.awardName === row.awardName && _.prizeName === row.prizeName
      )
      awardConfig.value =
        item && item.awardInfo && item.awardInfo.length ? item.awardInfo : []
      dialogVisible.value = true
    }
  })
}

const cAreaRef = ref(null)
const getProvinceCity = () => {
  cAreaRef.value.setArea(awardForm.province, awardForm.city, '')
  cAreaRef.value.init('cache', true, true, false)
}

const setProvinceCity = (data) => {
  awardForm.province = data.provinceName || ''
  awardForm.city = data.cityName || ''
}

const confirmModification = () => {
  if (!awardForm.uname && awardConfig.value.includes(1)) {
    return proxy.$message.error('请填写姓名')
  }
  if (!awardForm.mobile && awardConfig.value.includes(2)) {
    return proxy.$message.error('请填写手机号')
  }
  if (
    (!awardForm.province || !awardForm.city) &&
    awardConfig.value.includes(3)
  ) {
    return proxy.$message.error('请选择省市')
  }
  if (!awardForm.address && awardConfig.value.includes(3)) {
    return proxy.$message.error('请填写详细地址')
  }
  updateOSSPrizeReceiveInfo({
    ...awardForm
  }).then((res) => {
    if (res.data.code === 0) {
      proxy.$message.success('修改成功')
      getList()
      dialogVisible.value = false
    } else {
      proxy.$message.error(res.data.msg || '修改失败')
    }
  })
}

const exportExcel = () => {
  proxy
    .$confirm('你确认导出到Excel么', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      const loading = proxy.$loading({
        lock: true,
        text: '正在导出，请稍等......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      getUgcPrizeList({
        ...ruleForm,
        page: 1,
        limit: 9999
      })
        .then((res) => {
          if (res.data.code === 0) {
            const data = res.data.data || {}
            dataHandling(data.listData || [])
          }
        })
        .finally(() => {
          loading.close()
        })
    })
    .catch(() => {})
}

const dataHandling = async (data) => {
  // 导出的表头
  const tHeader = [
    '用户ID',
    '用户名称',
    '活动ID',
    '活动名称',
    '文章信息',
    '奖项',
    '奖品名称',
    '姓名',
    '手机号',
    '省市区',
    '详细地址',
    '提交时间'
  ]
  // 导出表头要对应的数据
  const filterVal = [
    'authorId',
    'author',
    'activityId',
    'activityName',
    'essayId',
    'awardName',
    'prizeName',
    'uname',
    'mobile',
    'province',
    'address',
    'submitTime'
  ]
  const exportData = data.map((value) => {
    return filterVal.map((key) => {
      if (key === 'activityName') {
        return activityName.value
      }
      if (key === 'essayId') {
        return `${value.essayId}\n${proxy.$filters.subString(
          value.content || value.title,
          30
        )}`
      }
      if (key === 'province') {
        return `${value.province} ${value.city}`
      }
      if (key === 'submitTime') {
        return proxy.$filter.format(value[key], 'YYYY-MM-DD HH:mm:ss')
      }
      return value[key]
    })
  })
  const { export_json_to_excel_new } = await import('@/vendor/Export2Excel')
  export_json_to_excel_new({
    header: tHeader,
    data: exportData,
    filename: `领奖列表`
  })
}
</script>

<style lang="scss" scoped>
.award-list {
  padding: 20px;
  .input-style {
    width: 200px;
  }
  .dialog-footer {
    margin-top: 30px;
    text-align: center;
  }
}
</style>
