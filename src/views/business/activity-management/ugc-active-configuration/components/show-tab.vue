<template>
  <div class="show-tab">
    <p class="tip-content">tab</p>
    <div class="show-tab-all">
      <div v-for="(tab, index) in data" :key="index" class="show-tab-list">
        <img :src="tab.img" class="show-tab-list_img" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShowTab',
  props: {
    data: {
      typeof: Object,
      default: {}
    },
    checkTypeStatus: {
      typeof: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {}
}
</script>

<style lang="scss" scoped>
.show-tab-all {
  display: flex;
  padding: 4px 15px;
  .show-tab-list {
    font-size: 14px;
    color: #fff;
    line-height: 19px;
    font-family: AlibabaPuHuiTiH;
    margin-right: 6px;
    text-align: center;
    position: relative;
    height: 35px;
    line-height: 35px;
    border-radius: 19px;
    flex: 1;
    span {
      z-index: 1;
      display: block;
      position: relative;
    }
  }
  .show-tab-list_img {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border-radius: 19px;
  }
  .show-tab-list:nth-last-child(1) {
    margin-right: 0;
  }
}
</style>
