<template>
  <div>
    <el-dialog
      v-model="dialogFormVisible"
      append-to-body
      :destroy-on-close="true"
      :title="title"
      width="565px"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="110px"
      >
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="ruleForm.name"
            type="text"
            placeholder="请输入名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="ruleForm.type">
            <el-radio :label="'1'">直播</el-radio>
            <el-radio :label="'2'">内容</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="添加图片" prop="img" required>
          <el-input
            v-model="ruleForm.img"
            style="width: 350px"
            placeholder="请选择图片"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccessTitleimageShore"
            name="titlefile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            <el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item v-if="ruleForm.type === '1'" label="直播配置">
          <el-select @change="setJumpUrl" v-model="ruleForm.chatId">
            <el-option
              v-for="(value, index) in liveList"
              :key="index"
              :label="value.title"
              :value="value.chatId"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="配置跳转链接"
          prop="link"
          required
          label-width="110px"
        >
          <el-input
            v-model="ruleForm.link"
            clearable
            style="width: 350px"
            placeholder="请输入配置跳转链接"
          />
        </el-form-item>
        <div class="text-center">
          <el-button @click="cancel()">取消</el-button>
          <el-button type="primary" @click="confirm()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getLiveCountryList } from '@/api/user'
export default {
  name: 'DialogContent',
  props: {},
  data() {
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('图片必须上传'))
      }
      callback()
    }
    return {
      title: '新增轮播', // 标题
      dialogFormVisible: false,
      ruleForm: {
        name: '',
        type: '',
        img: '',
        link: ''
      },
      liveList: [],
      searchLoading: false,
      rules: {
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        img: [{ validator: imgEnter, trigger: 'change' }],
        link: [{ required: true, message: '请输入跳转链接', trigger: 'blur' }]
      }
    }
  },
  methods: {
    init(data) {
      this.dialogFormVisible = true
      this.page = 1
      this.ruleForm = {
        name: '',
        type: '',
        img: '',
        link: '',
        createDate: Math.floor(new Date()), // 创建时间
        ...data
      }
      this.getLiveCountryList()
    },
    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 活动封面
    onSuccessTitleimageShore(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.img = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 获取直播信息
    getLiveCountryList() {
      getLiveCountryList({
        page: 1,
        limit: 100
      }).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data || {}
          this.liveList = data.listData || []
        }
      })
    },
    // 设置直播url
    setJumpUrl() {
      this.liveList.length > 0 &&
        this.liveList.map((item) => {
          if (item.chatId === this.ruleForm.chatId) {
            this.ruleForm.link = `https://m.58moto.com/live-room?shopId=${item.shopInfoVo.shopId}&uid=${item.uid}&liveId=${item.chatId}`
          }
        })
    },
    // 提交
    confirm() {
      const me = this
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          me.$emit('updataSwipe', this.ruleForm)
          me.cancel()
        } else {
          me.$message.warning('还有数据未填写')
        }
      })
    },
    // 取消
    cancel() {
      this.dialogFormVisible = false
    }
  },
  emits: ['updateAward']
}
</script>
