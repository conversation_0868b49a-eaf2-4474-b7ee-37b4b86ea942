<template>
  <div class="show-award">
    <p class="tip-content">奖项</p>
    <div v-for="(tab, index) in data" :key="index" class="show-award-list">
      <span>{{ tab.awardName }}</span>
      <img :src="tab.img" class="show-award-list_img" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShowAward',
  props: {
    data: {
      typeof: Object,
      default: {}
    },
    checkTypeStatus: {
      typeof: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {},
  activated() {},
  methods: {},
  emits: ['updateData']
}
</script>

<style lang="scss" scoped>
.show-award-list {
  display: flex;
  padding: 4px 15px;
  span {
    display: block;
    position: relative;
  }
  .show-award-list_img {
    width: 100%;
    height: 100%;
    border-radius: 19px;
  }
}
</style>
