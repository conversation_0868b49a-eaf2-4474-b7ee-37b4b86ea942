<template>
  <div class="sign-up-template">
    <div class="title">发布配置</div>
    <el-form
      ref="ruleForm"
      :inline="true"
      :model="ruleForm"
      :rules="rules"
      label-width="110px"
    >
      <el-form-item label="发布按钮文案" prop="buttonTitle">
        <el-input
          v-model="ruleForm.buttonTitle"
          maxlength="20"
          style="width: 350px"
          placeholder="请输入发布按钮文案"
        />
      </el-form-item>
      <br />
      <el-form-item label="发布规则名称" prop="ruleName">
        <el-input
          v-model="ruleForm.ruleName"
          style="width: 350px"
          placeholder="请输入发布规则名称"
        />
      </el-form-item>
      <br />
      <el-form-item label="发布规则说明" prop="ruleDesc">
        <div style="width: 800px">
          <quill-editor
            contentType="html"
            ref="publishQuillEditor"
            v-model:content="ruleForm.ruleDesc"
            :options="editorOption"
            @blur="quillEditorBlur"
          />
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'PublishConfigurationTemplate',
  data() {
    return {
      ruleForm: {
        buttonTitle: '', // 发布按钮文案
        ruleName: '', // 发布规则名称
        ruleDesc: '' // 发布规则说明
      },
      rules: {
        buttonTitle: [
          { required: true, message: '请填写发布按钮文案', trigger: 'blur' }
        ]
        // ruleDesc: [
        //   { required: true, message: '请填写发布规则说明', trigger: 'blur' }
        // ]
      },
      editorOption: {
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
            // ['blockquote', 'code-block'], // 引用  代码块
            [{ header: 1 }, { header: 2 }], // 1、2 级标题
            [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
            // [{ script: 'sub' }, { script: 'super' }], // 上标/下标
            [{ indent: '-1' }, { indent: '+1' }], // 缩进
            // [{ direction: 'rtl' }], // 文本方向
            // [{ size: ['12', '14', '16', '18', '20', '22', '24', '28', '32', '36'] }], // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6] }], // 标题
            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
            // [{ font: ['songti'] }], // 字体种类
            [{ align: [] }], // 对齐方式
            // ['clean'], // 清除文本格式
            ['link'] // 链接、图片、视频
          ]
        }
      }
    }
  },
  methods: {
    // 初始化
    init(shareData) {
      this.ruleForm = {
        buttonTitle: '', // 发布按钮文案
        ruleName: '', // 发布规则名称
        ruleDesc: '', // 发布规则说明
        ...shareData
      }
      this.$refs['ruleForm'].clearValidate()
    },
    quillEditorBlur() {
      const divNode = document.createElement('div')
      divNode.innerHTML = this.ruleForm.ruleDesc
      if (!divNode.innerText) {
        this.ruleForm.ruleDesc = ''
      }
      // this.$refs['ruleForm'].validateField('ruleDesc')
    },
    // 确认
    validate(callBack) {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.$emit('postData', 'publishConfiguration', this.ruleForm)
        } else {
          callBack && callBack()
        }
      })
    }
  }
}
</script>
