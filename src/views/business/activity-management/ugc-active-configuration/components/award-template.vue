<template>
  <div class="sign-up-template">
    <div class="title module-style">
      奖项配置
      <el-button type="primary" @click="addAward"> 新增 </el-button>
    </div>
    <div class="homepagelist-content">
      <el-row class="award-list text-center">
        <el-col :span="2">序号</el-col>
        <el-col :span="4">奖项名称</el-col>
        <el-col :span="4">奖品名称</el-col>
        <el-col :span="2">个数</el-col>
        <el-col :span="3">领奖信息字段</el-col>
        <el-col :span="3">备注</el-col>
        <el-col :span="4">奖项标题图片</el-col>
        <el-col :span="2">操作</el-col>
      </el-row>
      <draggable
        v-if="awardList && awardList.length"
        v-model="awardList"
        item-key="createDate"
        handle=".handle"
        class="item-container"
      >
        <template #item="{ element, index }">
          <el-row class="award-list-inside text-center">
            <el-col :span="2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                t="1629783231238"
                class="mr5 handle pointer"
                viewBox="0 0 1024 1024"
                version="1.1"
                p-id="1996"
                width="25"
                height="30"
              >
                <path
                  d="M128 294.4a38.4 38.4 0 0 0 38.4 38.4h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 0 0-38.4 38.4z m38.4 268.8h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z m0 230.4h691.2a38.4 38.4 0 1 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z"
                  p-id="1997"
                />
              </svg>
              <span class="award-index">{{ index + 1 }}</span>
            </el-col>
            <el-col :span="4">
              <el-input
                v-model="element.awardName"
                clearable
                style="width: 100%"
              />
            </el-col>
            <el-col :span="4">
              <el-input
                v-model="element.prizeName"
                clearable
                style="width: 100%"
              />
            </el-col>
            <el-col :span="2">
              <el-input
                v-model="element.number"
                type="number"
                clearable
                style="width: 100%"
              />
            </el-col>
            <el-col :span="3">
              <el-checkbox-group v-model="element.awardInfo">
                <div><el-checkbox :label="1">姓名&#x3000;</el-checkbox></div>
                <div><el-checkbox :label="2">手机号</el-checkbox></div>
                <div><el-checkbox :label="3">地址&#x3000;</el-checkbox></div>
              </el-checkbox-group>
            </el-col>
            <el-col :span="3">
              <el-input
                v-model="element.reason"
                clearable
                style="width: 100%"
              />
            </el-col>
            <el-col :span="4">
              <el-upload
                :show-file-list="false"
                :http-request="httpRequestImg"
                :on-success="(res) => onSuccessImg(res, element)"
                name="titlefile"
                action
              >
                <el-button v-if="!element.img" type="primary" link>
                  选择图片
                </el-button>
                <img
                  v-else
                  :src="element.img"
                  class="award-img-inside"
                  alt=""
                />
              </el-upload>
            </el-col>
            <el-col :span="2">
              <el-button type="primary" @click="copyData(element, index)" link
                >复制</el-button
              >
              <el-button type="primary" @click="delData(index)" link
                >删除</el-button
              >
            </el-col>
          </el-row>
        </template>
      </draggable>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
export default {
  name: 'AwardTemplate',
  components: {
    draggable
  },
  data() {
    return {
      awardList: [] // 奖项设置
    }
  },
  methods: {
    // 初始化
    init(shareData) {
      this.awardList =
        shareData && shareData.length
          ? JSON.parse(JSON.stringify(shareData))
          : []
    },
    addAward() {
      this.awardList.push({
        awardName: '',
        prizeName: '',
        img: '',
        number: '',
        awardInfo: [],
        reason: '',
        createDate: Math.floor(new Date()) // 创建时间
      })
    },
    copyData(data, index) {
      const item = {
        ...data,
        createDate: Math.floor(new Date()) // 创建时间
      }
      this.awardList.splice(index, 0, item)
    },
    async httpRequestImg(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    onSuccessImg(res, data) {
      if (!res) return
      if (res.name) {
        data.img = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 删除奖项
    delData(index) {
      this.awardList.splice(index, 1)
    },
    // 确认
    validate(callBack) {
      let flag = true
      this.awardList.forEach((item) => {
        if (!item.awardName || !item.prizeName || !item.number || !item.img) {
          flag = false
        }
      })
      if (flag) {
        this.$emit('postData', 'award', this.awardList)
      } else {
        callBack && callBack()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.award-list-inside {
  line-height: 30px;
  word-wrap: break-word;
}
.award-img-inside {
  width: 100%;
  height: 100px;
  object-fit: cover;
  padding: 0 5px;
}
.module-style {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.homepagelist-content {
  border-left: 1px solid #dedede;
  border-top: 1px solid #dedede;
  .el-col {
    border: 1px solid #dedede;
    border-left: none;
    border-top: none;
    padding: 5px;
  }
}
</style>
