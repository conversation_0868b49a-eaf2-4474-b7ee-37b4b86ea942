<template>
  <div class="sign-up-template">
    <div class="title">
      TAB配置
      <el-button
        v-if="!isEdit"
        link
        type="primary"
        class="fl-right"
        @click="goEdit()"
        >编辑</el-button
      >
    </div>
    <p>
      tab展示位置前置同步<el-switch
        v-if="isEdit"
        v-model="ruleForm.isOpen"
        width="60"
        inline-prompt
        active-text="打开"
        inactive-text="关闭"
      /><span v-else>{{ showData.isOpen ? '开启' : '关闭' }}</span
      >&ensp; 放置第
      <el-input
        v-if="isEdit"
        v-model="ruleForm.number"
        maxlength="20"
        :disabled="!isEdit"
        style="width: 50px"
        placeholder=""
      /><span v-else>{{ showData.number }}</span>
      位
    </p>
    <div class="title">活动进行中</div>
    <tabList
      ref="tabList"
      :listData="tabList"
      :showAssociation="true"
      :isEdit="isEdit"
      :name="'tabList'"
      @updateList="updateList"
    />
    <p class="title">活动已结束</p>
    <tabList
      ref="tabAllList"
      :listData="tabAllList"
      :isEdit="isEdit"
      :name="'tabAllList'"
      @updateList="updateList"
    />
    <p v-if="isEdit" class="text-center">
      <el-button type="danger" @click="handleClose()">取 消</el-button>
      <el-button type="primary" @click="validate()">保 存</el-button>
    </p>
  </div>
</template>

<script>
import tabList from './tab-list.vue'
import { deepCopy } from '@/utils'
export default {
  name: 'TabTemplate',
  components: {
    tabList
  },
  props: {},
  data() {
    return {
      isEdit: true,
      ruleForm: {},
      showData: {}, // 展示数据
      tabList: [], // 进行中列表
      tabAllList: [] // 全部列表
    }
  },
  watch: {},
  activated() {},
  methods: {
    // 初始化
    init(ruleForm, isEdit) {
      this.ruleForm = ruleForm || {}
      this.showData = ruleForm || {}
      const defaultList = {
        tabName: '',
        img: '',
        selectedImg: ''
      }
      this.tabList =
        this.ruleForm.tabList && this.ruleForm.tabList.length
          ? this.ruleForm.tabList
          : [
              {
                ...deepCopy(defaultList),
                tabName: '全部投稿'
              },
              {
                ...deepCopy(defaultList),
                tabName: '我的投稿'
              }
            ]
      this.tabAllList =
        this.ruleForm.tabAllList && this.ruleForm.tabAllList.length
          ? this.ruleForm.tabAllList
          : [
              {
                ...deepCopy(defaultList),
                tabName: '评奖结果'
              },
              {
                ...deepCopy(defaultList),
                tabName: '全部投稿'
              },
              {
                ...deepCopy(defaultList),
                tabName: '我的投稿'
              }
            ]
      this.isEdit = isEdit
    },
    // 编辑
    goEdit() {
      this.ruleForm = {
        ...this.showData
      }
      this.isEdit = true
    },
    // 更新数据
    updateList(name, data, updateIndex) {
      this[name][updateIndex] = data
    },
    // 确认
    validate() {
      this.showData = {
        number: this.ruleForm.number,
        isOpen: this.ruleForm.isOpen
      }
      this.$emit('postData', 'tab', {
        ...this.ruleForm,
        tabList: this.tabList,
        tabAllList: this.tabAllList
      })
      this.isEdit = false
    },
    // 取消
    handleClose() {
      this.isEdit = false
    }
  }
}
</script>
