<template>
  <div class="sign-up-template">
    <div class="title">关联信息</div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="110px"
    >
      <el-form-item label="活动方类型" prop="adProjectType">
        <el-select
          v-model="ruleForm.adProjectType"
          @change="getAdvertiserList"
          placeholder="请选择活动方类型"
          style="width: 250px"
        >
          <el-option
            v-for="(value, index) in advertisingType"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="活动方">
        <el-select
          v-model="ruleForm.orgId"
          filterable
          remote
          :remote-method="selectAdvertiserList"
          placeholder="请选择活动方"
          @change="campaignManageList"
          style="width: 250px"
          clearable
        >
          <el-option
            v-for="(value, index) in adProjectList"
            :key="index"
            :label="value.advertiserName"
            :value="value.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="车辆">
        <searchLabel :type="'car'" @addLabel="addLabel" class="mr10" />
        <el-tag
          @close="closeTag(tag, carLabels, 'car')"
          closable
          v-for="tag in carLabels"
          :key="tag.goodsId"
          type="info"
          class="mr10"
        >
          {{ tag.labelName }}
        </el-tag>
      </el-form-item>
      <el-form-item label="摩友圈">
        <el-select
          v-model="labelId"
          :remote-method="remoteMethod"
          :loading="loading"
          placeholder="请输入圈子名称"
          filterable
          remote
          clearable
          @change="addCircleLabel"
          class="mr10"
        >
          <el-option
            v-for="item in circleList"
            :key="item.id"
            :label="item.labelName"
            :value="item.id"
          />
        </el-select>
        <el-tag
          @close="closeTag(tag, hoopIds)"
          v-for="tag in hoopIds"
          :key="tag"
          :closable="!tag.disabled"
          class="mr10"
        >
          {{ tag.labelName }}
        </el-tag>
      </el-form-item>
      <el-form-item
        label="合作项目"
        v-show="[1, 3].includes(ruleForm.adProjectType)"
      >
        <div style="width: 100%">
          <el-button
            @click="$refs.cooperateSelected.init(ruleForm.relationId || '', 9)"
          >
            选择项目
          </el-button>
          <el-button type="primary" @click="updateSelectedData({})">
            取消关联
          </el-button>
          <div class="mt5">
            <CooperateList
              ref="cooperateList"
              :isUgc="true"
              :setHeight="'auto'"
            />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="活动总曝光">
        <el-input
          v-model="ruleForm.activeTotalExposure"
          maxlength="20"
          style="width: 250px"
          placeholder="请输入活动总曝光"
        />
        <span>次</span>
      </el-form-item>
    </el-form>
    <CooperateSelected
      ref="cooperateSelected"
      :isUgc="true"
      @updateSelectedData="updateSelectedData"
    />
  </div>
</template>

<script>
import { deepCopy } from '@/utils'
import { getAdvertiserList } from '@/api/advertModule'
import { getCircleList } from '@/api/circle'
import { advertisingType } from '@/utils/enum/adConfigEnum.js'
import { getOrderProjectListByParam } from '@/api/garage'
import { convertKeyValueEnum } from '@/utils/convert'
import CooperateSelected from '@/views/business/contentTreasure/managementActivity/commponents/CooperateSelected.vue'
import CooperateList from '@/views/business/contentTreasure/managementActivity/commponents/CooperateList.vue'
import searchLabel from '@/components/label/searchLabel.vue'
export default {
  name: 'RelatesInformationTemplate',
  components: {
    searchLabel,
    CooperateList,
    CooperateSelected
  },
  data() {
    return {
      labelId: '', // 圈子id
      advertisingType,
      loading: false, // 圈子加载
      adProjectList: [], // 活动方数据
      circleList: [], // 圈子列表
      hoopIds: [], // 圈子id
      carLabels: [], // 车型
      // showHoopIds: [], // 圈子id
      // showCarLabels: [], // 车型
      selectedList: {}, // 合作项目
      // showSelectedList: {}, // 展示的合作项目
      ruleForm: {},
      predefineColors: [
        // 颜色拾取器的自定义颜色
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],
      rules: {
        adProjectType: [
          { required: true, message: '请选择活动方类型', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    // 初始化
    init(shareData) {
      this.ruleForm = deepCopy(shareData || {})
      this.hoopIds = shareData.hoopIds
      this.carLabels = shareData.carLabels
      this.$nextTick(() => {
        if (shareData.factoryProjectId) {
          this.getOrderProjectListByParamList(shareData.factoryProjectId)
        }
        if (shareData.orgId) {
          this.selectAdvertiserList()
        }
      })
    },
    getAdvertiserList() {
      this.ruleForm.orgId = ''
      this.ruleForm.adProjectTypeName = convertKeyValueEnum(
        this.advertisingType
      )[this.ruleForm.adProjectType]
      this.selectAdvertiserList()
      this.hoopIds = []
      this.carLabels = []
    },
    // 获取广告方
    selectAdvertiserList(advertiserName) {
      getAdvertiserList({
        typeId: this.ruleForm.adProjectType,
        advertiserName,
        limit: 999
      }).then((res) => {
        if (res.data.code === 0) {
          this.adProjectList = res.data.data || []
        }
      })
    },
    campaignManageList(e) {
      this.ruleForm.orgId = e
      const findData =
        this.adProjectList.find((item) => {
          if (item.id === e) return item
        }) || {}
      this.ruleForm.orgIdName = findData.advertiserName || ''
      this.ruleForm.relationId = findData.relationId || ''
      this.hoopIds = []
      this.carLabels = []
      this.updateSelectedData({})
      if (this.adProjectType === 4) {
        this.adProjectList &&
          this.adProjectList.map((_) => {
            if (_.id === e) {
              this.ruleForm.linkType = _.thirdLinkType || ''
            }
          })
      }
    },
    // 查询合作项目
    getOrderProjectListByParamList(id) {
      const me = this
      getOrderProjectListByParam({
        id,
        availableCntZero: 1,
        extraIncludeId: id
      }).then((res) => {
        if (res.data.code == 0) {
          const list = res.data.data || []
          me.selectedList = list.length ? list[0] : {}
          if (me.$refs.cooperateList) {
            me.$refs.cooperateList.init(
              me.selectedList.id ? [me.selectedList] : [],
              me.selectedList.contractName || ''
            )
          }
        }
      })
    },
    // 增加标签
    addLabel(data, type, disabled) {
      const me = this
      let updateData = (type === 'circle' ? me.hoopIds : me.carLabels) || []
      const findIndex = updateData.findIndex((item) => {
        return item.id === data.id
      })
      updateData =
        findIndex > -1
          ? updateData
          : updateData.concat({
              ...data,
              disabled: disabled?.id ? false : disabled || false
            })
      if (findIndex === -1 && type !== 'circle') {
        this.getList(data.labelName.split(' | ')[1], true)
      }
      type === 'circle'
        ? (me.hoopIds = updateData)
        : (me.carLabels = updateData)
    },
    // 删除标签
    closeTag(item, data, type) {
      const findIndex = data.findIndex((itemData) => {
        return itemData.id === item.id
      })
      data.splice(findIndex, 1)
      if (type === 'car') {
        const hoopName = item.labelName.split(' | ')[1]
        const circleFindIndex = this.hoopIds.findIndex((itemData) => {
          return itemData.name || itemData.labelName === hoopName
        })
        circleFindIndex >= 0 ? this.hoopIds.splice(circleFindIndex, 1) : null
      }
    },
    // 修改页面展示的列表
    updateSelectedData(val, status) {
      this.$refs.cooperateList.init(
        val?.id ? [val] : [],
        val.contractName || ''
      )
      this.ruleForm.campaignName = !status
        ? val.projectName || ''
        : this.ruleForm.campaignName || ''
      this.selectedList = val || {}
      this.ruleForm.factoryProjectId = val.id || ''
    },
    remoteMethod(query) {
      if (!query) return
      this.loading = true
      this.$tools.debounce(() => this.getList(query), 300)()
    },
    // 标签搜索
    getList(query, addStatus) {
      const me = this
      const getData = {
        page: 1,
        limit: 20,
        type: addStatus ? 1 : '',
        hoopName: query
      }
      getCircleList(getData)
        .then((response) => {
          me.loading = false
          if (response.status === 200) {
            const data = response.data.data || {}
            const showList = (data.list || []).map((item) => {
              return {
                labelName: item.name,
                id: item.id
              }
            })
            if (addStatus) {
              showList.length ? me.addLabel(showList[0], 'circle', true) : null
            } else {
              me.circleList = showList
            }
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.loading = false
          me.$message.error(err.data.msg || '获取列表失败')
        })
    },
    // 增加圈子标签
    addCircleLabel(item) {
      if (item) {
        const addItem = this.circleList.find((data) => {
          return data.id === item
        })
        this.addLabel(addItem, 'circle', false)
      }
    },
    // 确认
    validate(callBack) {
      const me = this
      me.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          me.$emit('postData', 'relatesInformation', {
            ...me.ruleForm,
            carLabels: me.carLabels,
            hoopIds: me.hoopIds
          })
        } else {
          callBack && callBack()
        }
      })
    }
  }
}
</script>
