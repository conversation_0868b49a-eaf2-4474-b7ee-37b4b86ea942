<template>
  <div class="sign-up-template">
    <div class="title-box">
      <div class="title-style">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          t="1629783231238"
          class="mr5 handle-title pointer"
          viewBox="0 0 1024 1024"
          version="1.1"
          p-id="1996"
          width="25"
          height="30"
        >
          <path
            d="M128 294.4a38.4 38.4 0 0 0 38.4 38.4h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 0 0-38.4 38.4z m38.4 268.8h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z m0 230.4h691.2a38.4 38.4 0 1 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z"
            p-id="1997"
          />
        </svg>
        车型配置
      </div>
      <el-button link type="primary" @click="goUpdate('del')"> 删除 </el-button>
    </div>
    <el-form
      ref="ruleForm"
      :inline="true"
      :model="ruleForm"
      :rules="rules"
      label-width="110px"
    >
      <div>
        <el-form-item label="展示时间段">
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            v-model="daterange"
            style="width: 400px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="有效开始日期"
            end-placeholder="有效结束日期"
          />
        </el-form-item>
        <el-form-item label="模块背景色" prop="modelBgColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.modelBgColor"
          />
        </el-form-item>
      </div>
      <div>
        <el-form-item label="顺序" required prop="sortord">
          <div style="width: 400px">
            <el-radio-group v-model="ruleForm.sortord">
              <el-radio :label="'1'">不打乱</el-radio>
              <el-radio :label="'2'">每次随机打乱</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
        <el-form-item label="卡片背景图" prop="img" required>
          <el-input
            v-model="ruleForm.img"
            style="width: 400px"
            placeholder="请选择图片"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccessTitleimageShore"
            name="titlefile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            <el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>
      </div>
      <div>
        <el-form-item label="每行数量" required prop="rowNum">
          <div style="width: 400px">
            <el-radio-group v-model="ruleForm.rowNum">
              <el-radio :label="'1'">1</el-radio>
              <el-radio :label="'2'">2</el-radio>
              <el-radio :label="'3'">3</el-radio>
              <el-radio :label="'4'">不限</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
        <el-form-item label="卡片名称颜色" prop="nameColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.nameColor"
          />
        </el-form-item>
        <el-form-item label="卡片金额颜色" prop="priceColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.priceColor"
          />
        </el-form-item>
        <el-form-item label="其他文案颜色" prop="otherColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.otherColor"
          />
        </el-form-item>
      </div>
      <div>
        <el-form-item label="展示行数">
          <el-input
            v-model="ruleForm.numberRows"
            maxlength="20"
            style="width: 400px"
            placeholder="请输入行数，不填默认全展示"
          />
        </el-form-item>
        <el-form-item label="卡片按钮颜色" prop="cardButtonColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.cardButtonColor"
          />
        </el-form-item>
        <el-form-item label="卡片文案颜色" prop="buttonFontColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.buttonFontColor"
          />
        </el-form-item>
      </div>
      <div>
        <el-form-item label="展示按钮文案">
          <el-input
            v-model="ruleForm.displayButton"
            maxlength="20"
            style="width: 400px"
            placeholder="请输入展示按钮文案"
          />
        </el-form-item>
        <el-form-item label="收起按钮文案">
          <el-input
            v-model="ruleForm.collapseButton"
            maxlength="20"
            style="width: 400px"
            placeholder="请输入展示按钮文案"
          />
        </el-form-item>
      </div>
      <el-form-item label="按钮颜色">
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.bottonColor"
        />
      </el-form-item>
      <div>
        <el-form-item label="字体颜色">
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.fontColor"
          />
        </el-form-item>
        <el-button type="primary" class="fl-right" @click="goEditAdd()">
          新增
        </el-button>
      </div>
      <el-row class="award-list text-center">
        <el-col :span="2"></el-col>
        <el-col :span="4">车/款型ID</el-col>
        <el-col :span="11">车/款型名称</el-col>
        <el-col :span="3">操作</el-col>
      </el-row>
      <draggable
        v-if="rowNumList && rowNumList.length"
        v-model="rowNumList"
        item-key="createDate"
        handle=".handle"
        class="item-container"
      >
        <template #item="{ element, index }">
          <el-row class="award-list-inside text-center">
            <el-col :span="2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                t="1629783231238"
                class="mr5 handle pointer"
                viewBox="0 0 1024 1024"
                version="1.1"
                p-id="1996"
                width="25"
                height="30"
              >
                <path
                  d="M128 294.4a38.4 38.4 0 0 0 38.4 38.4h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 0 0-38.4 38.4z m38.4 268.8h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z m0 230.4h691.2a38.4 38.4 0 1 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z"
                  p-id="1997"
                />
              </svg>
              <span class="award-index">{{ index + 1 }}</span>
            </el-col>
            <el-col :span="4">{{ element.carId || element.goodsId }}</el-col>
            <el-col :span="11">
              {{ element.carName || element.goodsName }}
            </el-col>
            <el-col :span="3">
              <el-button link type="primary" @click="goEditCopy(element)">
                编辑
              </el-button>
              <el-button link type="primary" @click="goEditDel(index)">
                删除
              </el-button>
            </el-col>
          </el-row>
        </template>
      </draggable>
    </el-form>
    <dialogCar ref="dialogCar" @updataCar="updataCar" />
    <el-dialog
      v-model="dialogFormVisible"
      append-to-body
      :destroy-on-close="true"
      :title="'所有车辆'"
      width="40%"
    >
      <carList ref="carList" :listData="dataList" :height="'60vh'" />
    </el-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import { deepCopy } from '@/utils'
import dialogCar from './dialog-car.vue'
import carList from './car-list.vue'
export default {
  name: 'carTemplate',
  components: {
    draggable,
    dialogCar,
    carList
  },
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    let colorEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('颜色必填'))
      }
      callback()
    }
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('图片必须上传'))
      }
      callback()
    }
    return {
      dialogFormVisible: false,
      ruleForm: {
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        sortord: '', // 顺序
        numberRows: '', // 每行数量
        displayRows: '', // 展示行数
        displayButton: '', // 展开按钮文案
        collapseButton: '', // 收起按钮文案
        bottonColor: '', // 按钮颜色
        fontColor: '', // 字体颜色
        modelBgColor: '', // 模块背景色
        img: '', // 图片地址
        nameColor: '', // 名称颜色
        priceColor: '', // 金额颜色
        otherColor: '', // 其他文案颜色
        cardButtonColor: '', // 卡片按钮颜色
        buttonFontColor: '' // 按钮文件颜色
      },
      dataList: [], // 列表数据
      rowNumList: [], // 编辑列表展示数据
      predefineColors: [
        // 颜色拾取器的自定义颜色
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],
      rules: {
        sortord: [
          { required: true, message: '请选择排序顺序', trigger: 'change' }
        ],
        rowNum: [
          { required: true, message: '请选择每行数量', trigger: 'change' }
        ],
        modelBgColor: [{ validator: colorEnter, trigger: 'change' }],
        nameColor: [{ validator: colorEnter, trigger: 'change' }],
        priceColor: [{ validator: colorEnter, trigger: 'change' }],
        otherColor: [{ validator: colorEnter, trigger: 'change' }],
        cardButtonColor: [{ validator: colorEnter, trigger: 'change' }],
        buttonFontColor: [{ validator: colorEnter, trigger: 'change' }],
        img: [{ required: true, validator: imgEnter, trigger: 'change' }]
      }
    }
  },
  computed: {
    daterange: {
      get() {
        if (this.ruleForm.beginTime && this.ruleForm.endTime) {
          return [this.ruleForm.beginTime, this.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginTime = value[0]
          this.ruleForm.endTime = value[1]
        } else {
          this.ruleForm.beginTime = ''
          this.ruleForm.endTime = ''
        }
      }
    }
  },
  mounted() {
    this.init(this.data)
  },
  methods: {
    // 初始化
    init(shareData) {
      this.ruleForm = {
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        sortord: '', // 顺序
        numberRows: '', // 每行数量
        displayRows: '', // 展示行数
        displayButton: '', // 展开按钮文案
        collapseButton: '', // 收起按钮文案
        bottonColor: '', // 按钮颜色
        fontColor: '', // 字体颜色
        modelBgColor: '', // 模块背景色
        img: '', // 图片地址
        nameColor: '', // 名称颜色
        priceColor: '', // 金额颜色
        otherColor: '', // 其他文案颜色
        cardButtonColor: '', // 卡片按钮颜色
        buttonFontColor: '', // 按钮文件颜色
        ...shareData
      }
      this.dataList = this.ruleForm.list || [] // 列表数据
      this.rowNumList = this.ruleForm.list || [] // 编辑列表展示数据
    },
    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 活动封面
    onSuccessTitleimageShore(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.img = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 列表新增
    goEditAdd() {
      this.$refs.dialogCar.init({})
    },
    // 列表添加数据
    updataCar(data) {
      const findIndex = this.rowNumList.findIndex((item) => {
        return item.createDate === data.createDate
      })
      findIndex >= 0
        ? this.rowNumList.splice(findIndex, 1, data)
        : this.rowNumList.push(data)
    },
    // 列表复制
    goEditCopy(data) {
      this.$refs.dialogCar.init(data)
    },
    // 列表删除
    goEditDel(index) {
      this.rowNumList.splice(index, 1)
    },
    // 确认
    validate(callBack) {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.dataList = this.rowNumList.map((item) => {
            return deepCopy(item)
          })
          this.ruleForm = {
            ...this.ruleForm,
            list: this.dataList
          }
          this.$emit('updateData', 'update', this.ruleForm)
        } else {
          callBack && callBack()
        }
      })
    },
    // 更新数据
    goUpdate(type) {
      this.$emit('updateData', type, this.ruleForm)
    },
    // 查看所有车辆
    seeMoreCar() {
      this.dialogFormVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.award-list-inside {
  line-height: 30px;
  word-wrap: break-word;
}
.title-box {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title-style {
    font-weight: 500;
    display: flex;
    align-items: center;
  }
}
</style>
