<template>
  <div class="sign-up-template">
    <div class="title">基础信息</div>
    <el-form
      ref="ruleForm"
      :inline="true"
      :model="ruleForm"
      :rules="rules"
      label-width="110px"
    >
      <el-form-item label="活动名称" prop="activeName">
        <el-input
          v-model="ruleForm.activeName"
          maxlength="20"
          style="width: 310px"
          placeholder="请输入活动名称，不超过20个字"
          clearable
        />
      </el-form-item>
      <el-form-item label="页面名称" prop="pageName">
        <el-input
          v-model="ruleForm.pageName"
          style="width: 310px"
          placeholder="请输入页面名称"
          clearable
        />
      </el-form-item>
      <div>
        <el-form-item label="活动开始时间" prop="startTime" required>
          <el-date-picker
            v-model="ruleForm.startTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择活动开始时间"
            style="width: 310px"
          />
        </el-form-item>
        <el-form-item label="活动结束时间" prop="endTime" required>
          <el-date-picker
            v-model="ruleForm.endTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择活动结束时间"
            style="width: 310px"
          />
        </el-form-item>
      </div>
      <el-form-item label="背景色" prop="bgColor" required>
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.bgColor"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'BasicConfigurationTemplate',
  data() {
    let bgEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('背景色必填'))
      }
      callback()
    }
    const dateEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('时间必填'))
      }
      callback()
    }
    return {
      ruleForm: {
        activeName: '', // 活动名称
        pageName: '', // 页面名称
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        bgColor: '' // 背景色
      },
      showData: {}, // 展示数据
      predefineColors: [
        // 颜色拾取器的自定义颜色
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],
      rules: {
        activeName: [
          { required: true, message: '请填写活动名称', trigger: 'blur' }
        ],
        pageName: [
          { required: true, message: '请填写页面名称', trigger: 'blur' }
        ],
        bgColor: [{ validator: bgEnter, trigger: 'change' }],
        startTime: [
          {
            type: 'date',
            required: true,
            validator: dateEnter,
            trigger: 'change'
          }
        ],
        endTime: [
          {
            type: 'date',
            required: true,
            validator: dateEnter,
            trigger: 'change'
          }
        ]
      }
    }
  },
  methods: {
    // 初始化
    init(shareData) {
      const data = shareData || {}
      Object.keys(this.ruleForm).forEach((key) => {
        this.ruleForm[key] = ''
      })
      this.ruleForm = {
        ...this.ruleForm,
        ...data
      }
      this.$refs['ruleForm'].clearValidate()
    },
    // 确认
    validate(callBack) {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.$emit('postData', 'basicConfiguration', this.ruleForm)
        } else {
          callBack && callBack()
        }
      })
    }
  }
}
</script>
