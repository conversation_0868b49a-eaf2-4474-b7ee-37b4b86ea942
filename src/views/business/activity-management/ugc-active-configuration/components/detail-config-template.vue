<template>
  <div class="detail-config-template">
    <el-form
      ref="ruleFormRef"
      :inline="true"
      :model="ruleForm"
      :rules="rules"
      label-width="110px"
    >
      <div class="sign-up-template">
        <div class="title">
          排名配置
          <span class="tip">
            涉及位置：我的投稿模块-排名、全部投稿-排名、评奖结果-排名、分享页面-排名
          </span>
        </div>
        <el-form-item label="排名背景色" prop="rankBackColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.rankBackColor"
          />
        </el-form-item>
        <el-form-item label="排名字体颜色" prop="rankFontColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.rankFontColor"
          />
        </el-form-item>
      </div>
      <div class="sign-up-template">
        <div class="title">
          详情按钮配置
          <span class="tip">
            涉及位置：我的投稿模块-按钮（去拉票、查看领奖信息、填写领奖信息、）、全部投稿-投票
          </span>
        </div>
        <el-form-item label="按钮背景色" prop="detailsButtonBackColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.detailsButtonBackColor"
          />
        </el-form-item>
        <el-form-item
          label="按钮字体颜色"
          prop="detailsButtonFontColor"
          required
        >
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.detailsButtonFontColor"
          />
        </el-form-item>
      </div>
      <div class="sign-up-template">
        <div class="title">
          发布按钮配置
          <span class="tip">涉及位置：活动发布按钮、发动态、发视频</span>
        </div>
        <el-form-item label="按钮背景色" prop="publishButtonBackColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.publishButtonBackColor"
          />
        </el-form-item>
        <el-form-item
          label="按钮字体颜色"
          prop="publishButtonFontColor"
          required
        >
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.publishButtonFontColor"
          />
        </el-form-item>
      </div>
      <div class="sign-up-template">
        <div class="title">
          注意文案颜色配置
          <span class="tip">
            涉及位置：我的投稿模块-（注意文案、距上一名还需xx票、以及截止活动结束那行）、全部投稿-投票、分享页面-注意
          </span>
        </div>
        <el-form-item label="文案颜色" prop="noteCopyColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.noteCopyColor"
          />
        </el-form-item>
      </div>
      <div class="sign-up-template">
        <div class="title">
          tab配置
          <span class="tip">
            涉及位置：我的投稿模块-我的投稿、内容模块-（全部排名、评奖结果）、分享页面-我的投稿模块-我的投稿、分享页面-内容模块（全部排名、评奖结果）
          </span>
        </div>
        <el-form-item
          label="未选中tab颜色"
          prop="tabColorUnchecked"
          required
          label-width="115px"
        >
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.tabColorUnchecked"
          />
        </el-form-item>
        <el-form-item label="选中tab颜色" prop="tabColorSelected" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.tabColorSelected"
          />
        </el-form-item>
      </div>
      <div class="sign-up-template">
        <div class="title">
          排序及查看投票文案颜色配置
          <span class="tip">
            涉及位置：我的投稿模块-给我的投票、内容模块-排序模块
          </span>
        </div>
        <el-form-item label="字体颜色" prop="sortAndViewColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.sortAndViewColor"
          />
        </el-form-item>
      </div>
      <div class="sign-up-template">
        <div class="title">
          奖项配置
          <span class="tip">
            涉及位置：我的投稿模块-中奖（x等奖：具体奖品）
          </span>
        </div>
        <el-form-item label="背景色" prop="awardsBackColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.awardsBackColor"
          />
        </el-form-item>
        <el-form-item label="字体颜色" prop="awardsFontColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.awardsFontColor"
          />
        </el-form-item>
      </div>
      <div class="sign-up-template">
        <div class="title">
          不通过&参与失败提示配置
          <span class="tip">涉及位置：我的投稿模块-不通过、参与失败提示</span>
        </div>
        <el-form-item label="背景色" prop="notGoBackColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.notGoBackColor"
          />
        </el-form-item>
        <el-form-item label="字体颜色" prop="notGoFontColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.notGoFontColor"
          />
        </el-form-item>
      </div>
      <div class="sign-up-template">
        <div class="title">我的投稿模块</div>
        <el-form-item label="模块背景色" prop="mySubmissionBackColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.mySubmissionBackColor"
          />
        </el-form-item>
        <br />
        <el-form-item
          label="内容标题颜色"
          prop="mySubmissionTitleColor"
          required
        >
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.mySubmissionTitleColor"
          />
        </el-form-item>
        <el-form-item
          label="票数、浏览数据颜色"
          prop="mySubmissionVoteColor"
          required
          label-width="150px"
        >
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.mySubmissionVoteColor"
          />
        </el-form-item>
        <br />
        <el-form-item
          label="注意文案配置（进行中）"
          prop="noteCopyConfig1"
          label-width="250px"
        >
          <el-input v-model="ruleForm.noteCopyConfig1" style="width: 350px" />
        </el-form-item>
        <br />
        <el-form-item
          label="注意文案配置（已中奖&未填写内容）"
          prop="noteCopyConfig2"
          label-width="250px"
        >
          <el-input v-model="ruleForm.noteCopyConfig2" style="width: 350px" />
        </el-form-item>
        <br />
        <el-form-item
          label="注意文案配置（已中奖&已填写内容）"
          prop="noteCopyConfig3"
          label-width="250px"
        >
          <el-input v-model="ruleForm.noteCopyConfig3" style="width: 350px" />
        </el-form-item>
      </div>
      <div class="sign-up-template">
        <div class="title">内容列表</div>
        <el-form-item label="模块背景色" prop="contentListBackColor" required>
          <el-color-picker
            :predefine="predefineColors"
            v-model="ruleForm.contentListBackColor"
          />
        </el-form-item>
      </div>
      <div class="sign-up-template">
        <div class="title">投票规则配置</div>
        <el-form-item
          label="单人总投票次数"
          prop="voteRuleAll"
          label-width="180px"
        >
          <el-input v-model="ruleForm.voteRuleAll" style="width: 200px" />
        </el-form-item>
        <br />
        <el-form-item
          label="单人每天投票总次数"
          prop="voteRuleEveryday"
          label-width="180px"
        >
          <el-input v-model="ruleForm.voteRuleEveryday" style="width: 200px" />
        </el-form-item>
        <br />
        <el-form-item
          label="单人每天单篇内容投票次数"
          prop="voteRuleSingle"
          label-width="180px"
        >
          <el-input v-model="ruleForm.voteRuleSingle" style="width: 200px" />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup>
const predefineColors = [
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsv(51, 100, 98)',
  'hsva(120, 40, 94, 0.5)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577'
]
const formInit = {
  rankBackColor: '#FF5925', // 排名配置-排名背景色
  rankFontColor: '#FFFFFF', // 排名配置-排名字体颜色
  detailsButtonBackColor: '#FF5925', // 详情按钮配置-按钮背景色
  detailsButtonFontColor: '#FFFFFF', // 详情按钮配置-按钮字体颜色
  publishButtonBackColor: '#FF5925', // 发布按钮配置-按钮背景色
  publishButtonFontColor: '#FFFFFF', // 发布按钮配置-按钮字体颜色
  noteCopyColor: '#FF5925', // 注意文案颜色配置-文案颜色
  tabColorUnchecked: '#000000', // tab配置-未选中tab颜色
  tabColorSelected: '#000000', // tab配置-选中tab颜色
  sortAndViewColor: '#000000', // 排序及查看投票文案颜色配置-字体颜色
  awardsBackColor: '#FF5925', // 奖项配置-背景色
  awardsFontColor: '#FFFFFF', // 奖项配置-字体颜色
  notGoBackColor: '#FF5925', // 不通过&参与失败提示配置-背景色
  notGoFontColor: '#FFFFFF', // 不通过&参与失败提示配置-字体颜色
  mySubmissionBackColor: '#000000', // 我的投稿模块-模块背景色
  mySubmissionTitleColor: '#000000', // 我的投稿模块-内容标题颜色
  mySubmissionVoteColor: '#A5A5A5', // 我的投稿模块-票数、浏览数据颜色
  noteCopyConfig1: '', // 我的投稿模块-注意文案配置（进行中）
  noteCopyConfig2: '', // 我的投稿模块-注意文案配置（已中奖&未填写内容）
  noteCopyConfig3: '', // 我的投稿模块-注意文案配置（已中奖&已填写内容）
  contentListBackColor: '#000000', // 内容列表-模块背景色
  voteRuleAll: '', // 投票规则配置-单人总投票次数
  voteRuleEveryday: '', // 投票规则配置-单人每天投票总次数
  voteRuleSingle: '' // 投票规则配置-单人每天单篇内容投票次数
}
const ruleForm = reactive({ ...formInit })

const colorEnter = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('颜色必填'))
  }
  callback()
}
const rules = {
  rankBackColor: [{ validator: colorEnter, trigger: 'change' }],
  rankFontColor: [{ validator: colorEnter, trigger: 'change' }],
  detailsButtonBackColor: [{ validator: colorEnter, trigger: 'change' }],
  detailsButtonFontColor: [{ validator: colorEnter, trigger: 'change' }],
  publishButtonBackColor: [{ validator: colorEnter, trigger: 'change' }],
  publishButtonFontColor: [{ validator: colorEnter, trigger: 'change' }],
  noteCopyColor: [{ validator: colorEnter, trigger: 'change' }],
  tabColorUnchecked: [{ validator: colorEnter, trigger: 'change' }],
  tabColorSelected: [{ validator: colorEnter, trigger: 'change' }],
  sortAndViewColor: [{ validator: colorEnter, trigger: 'change' }],
  awardsBackColor: [{ validator: colorEnter, trigger: 'change' }],
  awardsFontColor: [{ validator: colorEnter, trigger: 'change' }],
  notGoBackColor: [{ validator: colorEnter, trigger: 'change' }],
  notGoFontColor: [{ validator: colorEnter, trigger: 'change' }],
  mySubmissionBackColor: [{ validator: colorEnter, trigger: 'change' }],
  mySubmissionTitleColor: [{ validator: colorEnter, trigger: 'change' }],
  mySubmissionVoteColor: [{ validator: colorEnter, trigger: 'change' }],
  contentListBackColor: [{ validator: colorEnter, trigger: 'change' }]
}
const ruleFormRef = ref(null)

const init = (shareData, isEdit) => {
  Object.keys(formInit).forEach((key) => {
    const initData = isEdit ? '' : formInit[key]
    ruleForm[key] = shareData[key] || initData
  })
  ruleFormRef.value.clearValidate()
}

const emit = defineEmits(['postData'])
const validate = (callBack) => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      emit('postData', 'detailConfig', { ...ruleForm })
    } else {
      callBack && callBack()
    }
  })
}

defineExpose({
  init,
  validate
})
</script>

<style lang="scss" scoped></style>
