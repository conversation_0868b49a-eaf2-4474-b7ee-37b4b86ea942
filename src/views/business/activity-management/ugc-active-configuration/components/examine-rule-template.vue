<template>
  <div class="sign-up-template">
    <div class="title">审核规则配置（仅审核平台可见）</div>
    <el-form
      ref="ruleForm"
      :inline="true"
      :model="ruleForm"
      label-width="110px"
    >
      <el-form-item label="规则">
        <div style="width: 800px">
          <quill-editor
            contentType="html"
            ref="prizeQuillEditor"
            v-model:content="ruleForm.ruleDesc"
            :options="editorOption"
            @blur="quillEditorBlur"
          />
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'RuleTemplate',
  data() {
    return {
      ruleForm: {
        ruleDesc: '' // 审核规则配置
      },
      editorOption: {
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
            // ['blockquote', 'code-block'], // 引用  代码块
            [{ header: 1 }, { header: 2 }], // 1、2 级标题
            [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
            // [{ script: 'sub' }, { script: 'super' }], // 上标/下标
            [{ indent: '-1' }, { indent: '+1' }], // 缩进
            // [{ direction: 'rtl' }], // 文本方向
            // [{ size: ['12', '14', '16', '18', '20', '22', '24', '28', '32', '36'] }], // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6] }], // 标题
            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
            // [{ font: ['songti'] }], // 字体种类
            [{ align: [] }], // 对齐方式
            // ['clean'], // 清除文本格式
            ['link'] // 链接、图片、视频
          ]
        }
      }
    }
  },
  methods: {
    // 初始化
    init(shareData) {
      const me = this
      me.ruleForm = {
        ruleDesc: '', // 审核规则配置
        ...shareData
      }
      // 初始化编辑器
      me.$nextTick(() => {
        const quillEditors = ['share,prize']
        quillEditors.map(function (value) {
          if (me.$refs[`${value}QuillEditor`]) {
            // personQuillEditor、clubQuillEditor、shownQuillEditor、hideQuillEditor改成自己的
            me.$refs[`${value}QuillEditor`]
              .getQuill()
              .getModule('toolbar')
              .addHandler('image', me[`${value}ImgHandler`])
            // 这里初始化，劫持toolbar的image的handler方法，在mounted中处理
          }
        })
      })
    },
    quillEditorBlur() {
      const divNode = document.createElement('div')
      divNode.innerHTML = this.ruleForm.ruleDesc
      if (!divNode.innerText) {
        this.ruleForm.ruleDesc = ''
      }
    },
    // 确认
    validate() {
      this.$emit('postData', 'examineRule', this.ruleForm)
    }
  }
}
</script>
