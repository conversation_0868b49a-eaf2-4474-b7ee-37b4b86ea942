<template>
  <div class="sign-up-template">
    <div class="title">
      倒计时配置
      <span class="tip">(活动结束后，倒计时模块不展示)</span>
    </div>
    <el-form
      ref="ruleForm"
      :inline="true"
      :model="ruleForm"
      :rules="rules"
      label-width="110px"
    >
      <el-form-item label="标题颜色" prop="titleColor" required>
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.titleColor"
        ></el-color-picker>
      </el-form-item>
      <el-form-item label="字体颜色" prop="fontColor" required>
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.fontColor"
        ></el-color-picker>
      </el-form-item>
      <br />
      <el-form-item label="背景颜色1" prop="bgColorOne" required>
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.bgColorOne"
        ></el-color-picker>
      </el-form-item>
      <el-form-item label="背景颜色2" prop="bgColorTwo" required>
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.bgColorTwo"
        ></el-color-picker>
      </el-form-item>
      <el-form-item label="倒计时收起">
        <el-input
          v-model="ruleForm.number"
          maxlength="20"
          style="width: 250px"
          placeholder="请输入倒计时收起秒数"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'ContentTemplate',
  data() {
    let colorEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('颜色必填'))
      }
      callback()
    }
    return {
      ruleForm: {
        titleColor: '', // 标题颜色
        fontColor: '', // 字体颜色
        bgColorOne: '', // 背景颜色1
        bgColorTwo: '' // 背景颜色2
      },
      predefineColors: [
        // 颜色拾取器的自定义颜色
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],
      rules: {
        titleColor: [{ validator: colorEnter, trigger: 'change' }],
        fontColor: [{ validator: colorEnter, trigger: 'change' }],
        bgColorOne: [{ validator: colorEnter, trigger: 'change' }],
        bgColorTwo: [{ validator: colorEnter, trigger: 'change' }]
      }
    }
  },
  methods: {
    // 初始化
    init(shareData) {
      this.ruleForm = {
        titleColor: '', // 标题颜色
        fontColor: '', // 字体颜色
        bgColorOne: '', // 背景颜色1
        bgColorTwo: '', // 背景颜色2
        ...shareData
      }
    },
    // 确认
    validate(callBack) {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.$emit('postData', 'detailedConfiguration', this.ruleForm)
        } else {
          callBack && callBack()
        }
      })
    }
  }
}
</script>
