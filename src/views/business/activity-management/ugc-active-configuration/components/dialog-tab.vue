<template>
  <div>
    <el-dialog
      v-model="dialogFormVisible"
      append-to-body
      :destroy-on-close="true"
      :title="title"
      width="600px"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <el-form-item label="未选中图片：" prop="img" required>
          <el-input
            v-model="ruleForm.img"
            style="width: 350px"
            placeholder="请选择图片"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccessTitleimageShore"
            name="titlefile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            &ensp;<el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="已选中图片：" prop="selectedImg" required>
          <el-input
            v-model="ruleForm.selectedImg"
            style="width: 350px"
            placeholder="请选择图片"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest1"
            :on-success="onSuccessTitleimage"
            name="titlefile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            &ensp;<el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>
        <div class="text-center">
          <el-button @click="cancel()">取消</el-button>
          <el-button type="primary" @click="confirm()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DialogTab',
  props: {},
  data() {
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('图片必须上传'))
      }
      callback()
    }
    return {
      title: '编辑tab', // 标题
      dialogFormVisible: false,
      ruleForm: {
        img: '',
        selectedImg: '',
        associationName: ''
      },
      searchLoading: false,
      rules: {
        img: [{ validator: imgEnter, trigger: 'change' }],
        selectedImg: [{ validator: imgEnter, trigger: 'change' }]
      }
    }
  },
  methods: {
    init(data) {
      this.dialogFormVisible = true
      this.ruleForm = data || {
        img: '',
        selectedImg: '',
        associationName: ''
      }
    },
    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 活动封面
    onSuccessTitleimageShore(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.img = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 上传标题图片
    async httpRequest1(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 活动封面
    onSuccessTitleimage(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.selectedImg = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 提交
    confirm() {
      const me = this
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          me.$emit('updateList', this.ruleForm)
          me.cancel()
        } else {
          me.$message.warning('还有数据未填写')
        }
      })
    },
    // 取消
    cancel() {
      this.dialogFormVisible = false
    }
  },
  emits: ['updateAward']
}
</script>
