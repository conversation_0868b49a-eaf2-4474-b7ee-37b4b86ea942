<template>
  <div class="show-image">
    <p class="tip-content">
      轮播 ｜<el-button type="primary" link @click.stop="copyData()"
        >复制</el-button
      >
      <img
        @click.stop="goDel()"
        class="fl-right"
        src="@/assets/image/<EMAIL>"
      />
    </p>
    <el-carousel height="200px" direction="vertical" :autoplay="false">
      <el-carousel-item v-for="(img, index) in data.list" :key="index">
        <div class="detail-link details-link">
          <img
            :src="img.img"
            style="width: 100%; height: 100%"
            onerror='onerror=null;src="/static/img/detail/<EMAIL>"'
          />
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
export default {
  name: 'ShowSwipe',
  props: {
    data: {
      typeof: Object,
      default: {}
    },
    checkTypeStatus: {
      typeof: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {},
  activated() {},
  methods: {
    // 复制
    copyData() {
      $emit(this, 'updateData', 'copy', this.data)
    },
    // 删除
    goDel() {
      $emit(this, 'updateData', 'del', this.data)
    }
  },
  emits: ['updateData']
}
</script>

<style lang="scss" scoped>
.show-image {
  border: 1px solid #ccc;
  border-radius: 10px;
  margin-bottom: 10px;
  .tip-content {
    margin: 10px;
  }
  .fl-right {
    width: 30px;
    height: 30px;
    position: relative;
  }
  .show-image-url {
    width: 100%;
  }
}
</style>
