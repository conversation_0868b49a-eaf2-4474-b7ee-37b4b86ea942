<template>
  <div class="sign-up-template">
    <div class="title-box">
      <div class="title-style">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          t="1629783231238"
          class="mr5 handle-title pointer"
          viewBox="0 0 1024 1024"
          version="1.1"
          p-id="1996"
          width="25"
          height="30"
        >
          <path
            d="M128 294.4a38.4 38.4 0 0 0 38.4 38.4h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 0 0-38.4 38.4z m38.4 268.8h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z m0 230.4h691.2a38.4 38.4 0 1 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z"
            p-id="1997"
          />
        </svg>
        轮播配置
      </div>
      <el-button link type="primary" @click="goUpdate('del')"> 删除 </el-button>
    </div>
    <el-form
      ref="ruleForm"
      :inline="true"
      :model="ruleForm"
      label-width="110px"
    >
      <div>
        <el-form-item label="展示时间段">
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            v-model="daterange"
            style="width: 400px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="有效开始日期"
            end-placeholder="有效结束日期"
          />
        </el-form-item>
      </div>
      <div class="module-style">
        <el-form-item label="模块背景图">
          <el-input
            v-model="ruleForm.img"
            style="width: 400px"
            placeholder="请选择图片"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccessTitleimageShore"
            name="titlefile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            <el-button type="primary" link>添加图片</el-button>
          </el-upload>
        </el-form-item>
        <el-button type="primary" @click="goEditAdd()"> 新增 </el-button>
      </div>
      <el-row class="award-list text-center">
        <el-col :span="2"></el-col>
        <el-col :span="3">名称</el-col>
        <el-col :span="5">封面</el-col>
        <el-col :span="3">类型</el-col>
        <el-col :span="8">跳转链接</el-col>
        <el-col :span="3">操作</el-col>
      </el-row>
      <draggable
        v-if="dataList && dataList.length"
        v-model="dataList"
        item-key="createDate"
        handle=".handle"
        class="item-container"
      >
        <template #item="{ element, index }">
          <el-row class="award-list-inside text-center">
            <el-col :span="2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                t="1629783231238"
                class="mr5 handle pointer"
                viewBox="0 0 1024 1024"
                version="1.1"
                p-id="1996"
                width="25"
                height="30"
              >
                <path
                  d="M128 294.4a38.4 38.4 0 0 0 38.4 38.4h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 0 0-38.4 38.4z m38.4 268.8h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z m0 230.4h691.2a38.4 38.4 0 1 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z"
                  p-id="1997"
                />
              </svg>
              <span class="award-index">{{ index + 1 }}</span>
            </el-col>
            <el-col :span="3">{{ element.name }} </el-col>
            <el-col :span="5">
              <img :src="element.img" class="award-img-inside" alt="" />
            </el-col>
            <el-col :span="3">{{
              element.type === '2' ? '内容' : '直播'
            }}</el-col>
            <el-col :span="8">{{ element.link }}</el-col>
            <el-col :span="3">
              <el-button link @click="goEditCopy(element)">编辑</el-button>
              <el-button link @click="goEditDel(index)">删除</el-button>
            </el-col>
          </el-row>
        </template>
      </draggable>
    </el-form>
    <dialogSwipe ref="dialogSwipe" @updataSwipe="updataSwipe" />
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import dialogSwipe from './dialog-swipe.vue'
export default {
  name: 'SwipeTemplate',
  components: {
    draggable,
    dialogSwipe
  },
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      ruleForm: {
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        img: '' // 图片地址
      },
      dataList: [], // 列表数据
      predefineColors: [
        // 颜色拾取器的自定义颜色
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ]
    }
  },
  computed: {
    daterange: {
      get() {
        if (this.ruleForm.beginTime && this.ruleForm.endTime) {
          return [this.ruleForm.beginTime, this.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginTime = value[0]
          this.ruleForm.endTime = value[1]
        } else {
          this.ruleForm.beginTime = ''
          this.ruleForm.endTime = ''
        }
      }
    }
  },
  mounted() {
    this.init(this.data)
  },
  methods: {
    // 初始化
    init(shareData) {
      this.ruleForm = {
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        img: '', // 图片地址
        ...shareData
      }
      this.dataList = shareData.list || []
    },
    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 活动封面
    onSuccessTitleimageShore(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.img = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 列表新增
    goEditAdd() {
      this.$refs.dialogSwipe.init({})
    },
    // 列表添加数据
    updataSwipe(data) {
      const findIndex = this.dataList.findIndex((item) => {
        return item.createDate === data.createDate
      })
      findIndex >= 0
        ? this.dataList.splice(findIndex, 1, data)
        : this.dataList.push(data)
    },
    // 列表编辑
    goEditCopy(data) {
      this.$refs.dialogSwipe.init(data)
    },
    // 列表删除
    goEditDel(index) {
      this.dataList.splice(index, 1)
    },
    // 确认
    validate(callBack) {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.ruleForm = {
            ...this.ruleForm,
            list: this.dataList
          }
          this.$emit('updateData', 'update', this.ruleForm)
        } else {
          callBack && callBack()
        }
      })
    },
    // 更新数据
    goUpdate(type) {
      this.$emit('updateData', type, this.ruleForm)
    }
  }
}
</script>

<style lang="scss" scoped>
.award-list-inside {
  line-height: 30px;
  word-wrap: break-word;
}
.award-img-inside {
  width: 100%;
  height: 100px;
  object-fit: cover;
}
.module-style {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.title-box {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title-style {
    font-weight: 500;
    display: flex;
    align-items: center;
  }
}
</style>
