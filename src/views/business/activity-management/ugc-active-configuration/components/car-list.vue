<template>
  <div>
    <el-table :data="listData" border :style="{'height': height, width: '100%'}">
      <el-table-column align="center" prop="name" label="车/款型ID">
        <template v-slot="scope">{{
          scope.row.carId || scope.row.goodsId
        }}</template>
      </el-table-column>
      <el-table-column align="center" prop="name" label="车/款型名称">
        <template v-slot="scope">{{
          scope.row.carName || scope.row.goodsName
        }}</template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'CarList',
  props: {
    listData: {
      type: Array,
      default: []
    },
    height: {
      type: String,
      default: 'auto'
    }
  },
  data() {
    return {}
  }
}
</script>
