<template>
  <div>
    <el-table :data="listData" border style="width: 1200px">
      <el-table-column
        align="center"
        prop="tabName"
        label="tab名称"
        width="200"
      />
      <el-table-column
        align="center"
        prop="activityName"
        label="未选中图片"
        width="400"
      >
        <template v-slot="scope">
          <img
            v-if="scope.row.img"
            :src="scope.row.img"
            class="award-img"
            alt=""
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="activityName"
        label="已选中图片"
        width="400"
      >
        <template v-slot="scope">
          <img
            v-if="scope.row.selectedImg"
            :src="scope.row.selectedImg"
            class="award-img"
            alt=""
          />
        </template>
      </el-table-column>
      <el-table-column
        v-if="isEdit"
        align="center"
        prop="activityName"
        label="操作"
        width="200"
      >
        <template v-slot="scope">
          <el-button link type="primary" @click="goEdit(scope)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <dialogTab ref="dialogTab" @updateList="updateList" />
  </div>
</template>

<script>
import { deepCopy } from '@/utils'
import dialogTab from './dialog-tab.vue'
export default {
  name: 'TabList',
  components: {
    dialogTab
  },
  props: {
    listData: {
      type: Array,
      default: []
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    showAssociation: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      updateIndex: 0
    }
  },
  watch: {},
  activated() {},
  methods: {
    // 编辑
    goEdit(data) {
      const me = this
      me.updateIndex = data.$index
      me.$refs.dialogTab.init(deepCopy(data.row))
    },
    updateList(data) {
      console.log(data)
      this.$emit('updateList', this.name, data, this.updateIndex)
    }
  }
}
</script>
