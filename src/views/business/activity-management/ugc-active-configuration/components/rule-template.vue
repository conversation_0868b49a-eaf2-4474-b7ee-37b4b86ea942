<template>
  <div class="sign-up-template">
    <div class="title">
      规则配置
      <el-switch
        v-model="ruleForm.isOpen"
        inline-prompt
        active-text="打开"
        inactive-text="关闭"
        @change="updatePostData"
      />
    </div>
    <el-form
      v-if="ruleForm.isOpen"
      ref="ruleForm"
      :inline="true"
      :model="ruleForm"
      :rules="rules"
      label-width="110px"
    >
      <el-form-item label="标题" prop="ruleTitle">
        <el-input v-model="ruleForm.ruleTitle" style="width: 250px" />
      </el-form-item>
      <br />
      <el-form-item label="背景颜色">
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.ruleBg"
        />
      </el-form-item>
      <br />
      <el-form-item label="字体颜色">
        <el-color-picker
          :predefine="predefineColors"
          v-model="ruleForm.ruleColor"
        />
      </el-form-item>
      <br />
      <el-form-item label="规则正文" prop="shareTitle">
        <div style="width: 800px">
          <quill-editor
            contentType="html"
            ref="prizeQuillEditor"
            v-model:content="ruleForm.ruleDesc"
            :options="editorOption"
            @blur="quillEditorBlur"
          />
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'RuleTemplate',
  props: {},
  data() {
    return {
      ruleForm: {
        isOpen: false, // 编辑状态
        ruleTitle: '', // 标题
        ruleBg: '', // 背景颜色
        ruleColor: '', // 字体颜色
        ruleDesc: '' // 规则配置
      },
      rules: {
        ruleTitle: [{ required: true, message: '请填写标题', trigger: 'blur' }]
      },
      predefineColors: [
        // 颜色拾取器的自定义颜色
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],
      editorOption: {
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
            // ['blockquote', 'code-block'], // 引用  代码块
            [{ header: 1 }, { header: 2 }], // 1、2 级标题
            [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
            // [{ script: 'sub' }, { script: 'super' }], // 上标/下标
            [{ indent: '-1' }, { indent: '+1' }], // 缩进
            // [{ direction: 'rtl' }], // 文本方向
            // [{ size: ['12', '14', '16', '18', '20', '22', '24', '28', '32', '36'] }], // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6] }], // 标题
            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
            // [{ font: ['songti'] }], // 字体种类
            [{ align: [] }], // 对齐方式
            // ['clean'], // 清除文本格式
            ['link'] // 链接、图片、视频
          ]
        }
      }
    }
  },
  methods: {
    // 初始化
    init(shareData) {
      const data = shareData || {}
      const me = this
      Object.keys(me.ruleForm).forEach((key) => {
        if (key === 'isOpen') {
          me.ruleForm[key] = !!data[key]
        } else if (key === 'ruleTitle') {
          me.ruleForm[key] = data[key] || '活动规则'
        } else {
          me.ruleForm[key] = data[key] || ''
        }
      })
      // 初始化编辑器
      me.$nextTick(() => {
        const quillEditors = ['share,prize']
        quillEditors.map(function (value) {
          if (me.$refs[`${value}QuillEditor`]) {
            // personQuillEditor、clubQuillEditor、shownQuillEditor、hideQuillEditor改成自己的
            me.$refs[`${value}QuillEditor`]
              .getQuill()
              .getModule('toolbar')
              .addHandler('image', me[`${value}ImgHandler`])
            // 这里初始化，劫持toolbar的image的handler方法，在mounted中处理
          }
        })
      })
    },
    quillEditorBlur() {
      const divNode = document.createElement('div')
      divNode.innerHTML = this.ruleForm.ruleDesc
      if (!divNode.innerText) {
        this.ruleForm.ruleDesc = ''
      }
    },
    // 确认
    validate(callBack) {
      if (this.ruleForm.isOpen) {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            this.$emit('postData', 'rule', this.ruleForm)
          } else {
            callBack && callBack()
          }
        })
      } else {
        this.$emit('postData', 'rule', this.ruleForm)
      }
    },
    // 更新数据
    updatePostData() {
      this.$emit('postData', 'rule', this.ruleForm)
    }
  }
}
</script>
