<template>
  <div class="show-image" @click.stop="editItem()">
    <p class="tip-content">
      车型 ｜<el-button type="primary" link @click.stop="copyData()"
        >复制</el-button
      >
      <img
        @click.stop="goDel()"
        class="fl-right"
        src="@/assets/image/<EMAIL>"
      />
    </p>
    <div v-if="shuffle.length" class="cartype-box" :class="{'cartype-box-slide': data.rowNum === '4'}">
      <div
        v-for="(item, index) in shuffle"
        :key="index"
        :class="[
          'cartype-item',
          {
            rowNum2: data.rowNum === '2',
            rowNum3: data.rowNum === '3' || data.rowNum === '4'
          }
        ]"
        :style="{ backgroundColor: data.bgColor || '#000' }"
      >
        <img
          :src="item.imgUrl"
          alt=""
          :class="[
            'cartype-item_img',
            {
              rowNum1_img: data.rowNum === '1',
              rowNum2_img: data.rowNum === '2',
              rowNum3_img: data.rowNum === '3'
            }
          ]"
        />
        <div
          :class="[
            'cartype-item_content',
            {
              cartypeContentOne: data.rowNum === '1',
              cartypeContentTwo: data.rowNum === '2',
              cartypeContentThree: data.rowNum === '3'
            }
          ]"
        >
          <h4 class="dotdotdot1" :style="{ color: data.nameColor || '#333' }">
            {{ item.brandName }}
          </h4>
          <h4
            class="dotdotdot1"
            :class="{ 'cartype-item_name': data.rowNum === '2' }"
            :style="{ color: data.nameColor || '#333' }"
          >
            {{ item.goodsName || item.carName }}
          </h4>
          <p
            class="cartype-item_price"
            :style="{ color: data.priceColor || '#D9001B' }"
          >
            这里是价格
          </p>
          <p
            class="cartype-item_btn"
            :style="{
              color: data.buttonTextColor || '#fff',
              backgroundColor: data.buttonBgColor || '#D9001B'
            }"
          >
            {{ item.buttonName }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
export default {
  name: 'ShowImage',
  props: {
    data: {
      typeof: Object,
      default: {}
    },
    checkTypeStatus: {
      typeof: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    shuffle() {
      const me = this
      let arr = [...me.data.list]
      if (me.data.sortord === '2') {
        // 排序打乱
        for (let i = arr.length - 1; i > -1; i--) {
          let j = Math.floor(Math.random() * i)
          ;[arr[j], arr[i]] = [arr[i], arr[j]]
        }
      }
      if (me.data.rowNum === '4') {
        return arr
      }
      let showList = []
      if (me.data.numberRows) {
        // 行数限制存在的情况下
        showList = arr.slice(0, me.data.numberRows * me.data.rowNum)
      }
      return showList.length ? showList : arr
    }
  },
  watch: {},
  activated() {},
  methods: {
    // 复制
    copyData() {
      $emit(this, 'copyData', this.data)
    },
    // 编辑模块
    editItem() {
      if (this.checkTypeStatus && this.data.syncStatus) {
        return
      }
      $emit(this, 'updatedActiveData', this.data)
    },
    // 删除
    goDel() {
      const me = this
      if (me.checkTypeStatus && me.data.syncStatus) {
        return
      }
      if (!me.data.syncStatus) {
        return $emit(me, 'delItem', me.data)
      }
      me.$confirm('删除后，报名后模块卡片同步删除，是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        $emit(me, 'delItem', me.data)
      })
    }
  },
  emits: ['copyData', 'updatedActiveData', 'delItem']
}
</script>

<style lang="scss" scoped>
.show-image {
  border: 1px solid #ccc;
  border-radius: 10px;
  margin-bottom: 10px;
  .tip-content {
    margin: 10px;
  }
  .fl-right {
    width: 30px;
    height: 30px;
    position: relative;
  }
  .show-image-url {
    width: 100%;
  }
  .cartype-box {
    padding: 10px 15px 0 15px;
    display: flex;
    flex-wrap: wrap;
    .cartype-item {
      display: flex;
      width: 100%;
      height: 160px;
      border-radius: 7px;
      position: relative;
      margin-bottom: 10px;
      &.rowNum1 {
        padding: 1.75px 15px;
        margin-bottom: 8px;
        position: relative;
      }
      &.rowNum1:nth-last-child(1) {
        margin-bottom: 0;
      }
      &.rowNum2 {
        width: calc(100% / 2 - 5px);
        display: block;
        margin-bottom: 10px;
        &:nth-child(2n) {
          margin-left: 10px;
        }
      }
      &.rowNum3 {
        width: calc(100% / 3 - 6px);
        display: block;
        margin-bottom: 10px;
        margin-right: 9px;
        &:nth-child(3n) {
          margin-right: 0;
        }
      }
      .cartype-item_img {
        display: block;
        width: 100%;
        height: 48.5px;
        border-radius: 8px 8px 0 0;
        object-fit: cover;
      }
      .rowNum1_img {
        position: absolute;
        right: 15px;
        top: 6.5px;
        width: 140px;
        height: 105px;
        padding: 0;
        border-radius: 0;
      }
      .rowNum2_img {
        width: 100%;
        height: 48.5px;
        padding: 15px 40px 0;
      }
      .rowNum3_img {
        width: 100%;
        height: 48.5px;
        padding: 15px 10.5px 0;
      }
      .cartype-item_content {
        position: relative;
        h4 {
          margin: 5px 0;
          text-align: center;
        }
        p {
          margin: 0;
          text-align: center;
        }

        .cartype-item_name {
          margin-bottom: 10px;
        }
        .cartype-item_price {
          font-size: 12px;
          margin: 4px 0;
          text-align: center;
        }
      }
      .cartypeContentOne {
        width: 100%;
        overflow: hidden;
        position: relative;
        padding: 25px 155px 0 10px;
        h4 {
          text-align: left;
        }
        p {
          text-align: left;
        }
        .cartype-item_price {
          bottom: 0;
        }
        .cartype-item_btn {
          position: absolute;
          margin-top: 20px;
          width: 94%;
          height: 30px;
          text-align: center;
          line-height: 26px;
          font-size: 12px;
          transform: scale(1, 1);
        }
      }

      .cartypeContentTwo {
        .cartype-item_btn {
          height: 22px;
          font-size: 11px;
          bottom: -2px;
          line-height: 16px;
          padding: 3px 11px;
        }
        .cartype-item_price {
          font-family: PingFangSC-Semibold, PingFang SC;
          .cartype-item_price_small {
            top: -4px;
          }
        }
      }
      .cartypeContentThree {
        margin: 5px 7px 12px 7px;
        .cartype-item_price {
          margin: 6px 0 4px;
          text-align: center;
        }
        .cartype-item_name {
          margin-bottom: 8px;
        }
        .cartype-item_btn {
          height: 18px;
          line-height: 14px;
          font-size: 10px;
        }
      }
      .cartype-item_btn {
        background-color: #ff4d23;
        border-radius: 20px;
        padding: 2px 0;
        color: #fff;
      }
    }
  }
  .cartype-box-slide {
    display: block;
    overflow-x: scroll;
    overflow-y: hidden;
    white-space: nowrap;
    flex-wrap: nowrap;
    .cartype-item {
      &.rowNum3 {
        display: inline-block;
        width: 100px;
      }
    }
  }
}
</style>
