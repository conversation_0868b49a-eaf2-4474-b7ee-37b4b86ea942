<template>
  <div>
    <el-dialog
      v-model="dialogFormVisible"
      append-to-body
      :destroy-on-close="true"
      :title="title"
      width="40%"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <el-form-item label="奖项名称" prop="awardName" required>
          <el-input
            v-model="ruleForm.awardName"
            type="text"
            placeholder="请输入奖项名称"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="奖品名称" prop="prizeName" required>
          <el-input
            v-model="ruleForm.prizeName"
            type="text"
            placeholder="请输入奖项名称"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="奖品个数" prop="number" required>
          <el-input
            v-model="ruleForm.number"
            type="text"
            placeholder="请输入奖品个数"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="添加图片" prop="img" required>
          <el-input
            v-model="ruleForm.img"
            style="width: 350px"
            placeholder="请选择图片"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccessTitleimageShore"
            name="titlefile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            &ensp;<el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button @click="cancel()">取消</el-button>
          <el-button type="primary" @click="confirm()">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { tools } from '@haluo/util'
export default {
  name: 'dialogAward',
  props: {},
  data() {
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('图片必须上传'))
      }
      callback()
    }
    return {
      title: '', // 标题
      dialogFormVisible: false,
      ruleForm: {
        awardName: '',
        prizeName: '',
        img: '',
        number: ''
      },
      searchLoading: false,
      rules: {
        img: [{ validator: imgEnter, trigger: 'change' }],
        awardName: [
          { required: true, message: '请填写奖项名称', trigger: 'blur' }
        ],
        prizeName: [
          { required: true, message: '请填写奖品名称', trigger: 'blur' }
        ],
        number: [{ required: true, message: '请填写奖品个数', trigger: 'blur' }]
      }
    }
  },
  methods: {
    init(data) {
      this.title = data.awardName ? '新增奖项' : '编辑奖项'
      this.dialogFormVisible = true
      this.ruleForm = data.awardName
        ? data
        : {
            awardName: '',
            prizeName: '',
            img: '',
            number: ''
          }
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 图片
    onSuccessTitleimageShore(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.img = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 提交
    confirm() {
      const me = this
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          me.$emit('updataAward', this.ruleForm)
          me.cancel()
        } else {
          me.$message.warning('还有数据未填写')
        }
      })
    },
    // 取消
    cancel() {
      this.dialogFormVisible = false
    }
  },
  emits: ['updateAward']
}
</script>
