<template>
  <div class="sign-up-template">
    <div class="title-box">
      <div class="title-style">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          t="1629783231238"
          class="mr5 handle-title pointer"
          viewBox="0 0 1024 1024"
          version="1.1"
          p-id="1996"
          width="25"
          height="30"
        >
          <path
            d="M128 294.4a38.4 38.4 0 0 0 38.4 38.4h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 0 0-38.4 38.4z m38.4 268.8h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z m0 230.4h691.2a38.4 38.4 0 1 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z"
            p-id="1997"
          />
        </svg>
        图片配置
      </div>
      <el-button link type="primary" @click="goUpdate('del')"> 删除 </el-button>
    </div>
    <el-form
      ref="ruleForm"
      :inline="true"
      :model="ruleForm"
      :rules="rules"
      label-width="110px"
    >
      <el-form-item label="展示时间段">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          v-model="daterange"
          style="width: 412px"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="有效开始日期"
          end-placeholder="有效结束日期"
        />
      </el-form-item>
      <el-form-item label="名称">
        <el-input
          v-model="ruleForm.name"
          maxlength="20"
          style="width: 310px"
          placeholder="请输入名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="添加图片" prop="img" required>
        <el-input
          v-model="ruleForm.img"
          style="width: 350px"
          placeholder="请选择图片"
          clearable
        />
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccessTitleimageShore"
          name="titlefile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <el-button type="primary" link>选择图片</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="配置跳转链接">
        <el-input
          v-model="ruleForm.link"
          style="width: 310px"
          placeholder="请输入跳转链接"
          clearable
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'ImgTemplate',
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('图片必须上传'))
      }
      callback()
    }
    return {
      ruleForm: {
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        name: '', // 名称
        img: '', // 图片地址
        link: '' // 链接
      },
      predefineColors: [
        // 颜色拾取器的自定义颜色
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],
      rules: {
        img: [{ validator: imgEnter, trigger: 'change' }]
      }
    }
  },
  computed: {
    daterange: {
      get() {
        if (this.ruleForm.beginTime && this.ruleForm.endTime) {
          return [this.ruleForm.beginTime, this.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginTime = value[0]
          this.ruleForm.endTime = value[1]
        } else {
          this.ruleForm.beginTime = ''
          this.ruleForm.endTime = ''
        }
      }
    }
  },
  mounted() {
    this.init(this.data)
  },
  methods: {
    // 初始化
    init(shareData) {
      const data = shareData || {}
      Object.keys(this.ruleForm).forEach((key) => {
        this.ruleForm[key] = ''
      })
      this.ruleForm = {
        ...this.ruleForm,
        ...data
      }
      this.$refs['ruleForm'].clearValidate()
    },
    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 活动封面
    onSuccessTitleimageShore(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.img = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 确认
    validate(callBack) {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.$emit('updateData', 'update', this.ruleForm)
        } else {
          callBack && callBack()
        }
      })
    },
    // 更新数据
    goUpdate(type) {
      this.$emit('updateData', type, this.ruleForm)
    }
  }
}
</script>

<style lang="scss" scoped>
.title-box {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title-style {
    font-weight: 500;
    display: flex;
    align-items: center;
  }
}
</style>
