<template>
  <div>
    <el-dialog
      v-model="dialogFormVisible"
      append-to-body
      :destroy-on-close="true"
      :title="title"
      width="565px"
    >
      <div class="tip mb10">
        自动同步车库数据，图片支持可修改，优先展示配置的车型图片
      </div>
      <el-form
        ref="ruleForm"
        :model="addData"
        :rules="rules"
        label-width="110px"
      >
        <el-form-item label="选择车型" required>
          <el-autocomplete
            v-model="addData.goodsName"
            :fetch-suggestions="querySearchAllCar"
            placeholder="请选择车型"
            style="width: 350px"
            clearable
            @select="handleSelectCar"
            @clear="clearGoodsName"
            @input="clearCardTypes"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="款型:" required>
          <el-select
            v-model="addData.carName"
            placeholder="请选择款型"
            style="width: 350px"
            clearable
            @clear="clearCarName"
            @change="changeCarstyle"
          >
            <el-option
              v-for="item in cardStyleList"
              :key="item.carId"
              :label="item.goodsCarName || item.carName"
              :value="item.carId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="同步车库图片" required>
          <el-radio-group v-model="addData.syncImg">
            <el-radio :label="'1'">是</el-radio>
            <el-radio :label="'2'">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="车型图片" prop="imgUrl">
          <el-input
            v-model="addData.imgUrl"
            clearable
            style="width: 350px"
            placeholder="请选择图片"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccessTitleimageShore"
            name="titlefile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            <el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="展示按钮" required>
          <el-radio-group v-model="addData.showButton">
            <el-radio :label="'1'">是</el-radio>
            <el-radio :label="'2'">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="addData.showButton === '1'"
          label="按钮名称"
          required
          prop="buttonName"
        >
          <el-input
            v-model="addData.buttonName"
            clearable
            style="width: 350px"
            placeholder="不必填"
          />
        </el-form-item>
        <el-form-item
          v-if="addData.showButton === '1'"
          label="跳转链接"
          required
        >
          <el-input
            v-model="addData.linkUrl"
            clearable
            style="width: 350px"
            placeholder="请输入配置跳转链接"
          />
        </el-form-item>
        <div class="text-center">
          <el-button @click="cancel()">取消</el-button>
          <el-button type="primary" @click="confirm()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { tools } from '@haluo/util'
import { searchCarList, getAgeList, getOriginalUrl } from '@/api/garage'
import { buttonEmits } from 'element-plus'
export default {
  name: 'DialogCar',
  props: {},
  data() {
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('图片必须上传'))
      }
      callback()
    }
    return {
      title: '新增车型', // 标题
      dialogFormVisible: false,
      addData: {
        carId: '', // 款型id
        carName: '', // 款型名称
        goodsId: '', // 车型id
        goodsName: '', // 车型名称
        imgUrl: '', // 车型图/款型图/自行编辑的图
        showButton: '', // 展示按钮
        linkUrl: '', // 跳转链接
        buttonName: '查看详情', // 查看详情
        isNewAdd: true, // 新增
        createDate: '' // 创建时间
      },
      cardStyleList: [], // 款型数据
      liveList: [],
      searchLoading: false,
      rules: {
        imgUrl: [
          { required: true, message: '请输入车库图片', trigger: 'blur' }
        ],
        // syncImg: [
        //   {
        //     required: true,
        //     message: '请选择是否同步车库图片',
        //     trigger: 'change'
        //   }
        // ],
        // showButton: [
        //   { required: true, message: '请选择是否展示按钮', trigger: 'change' }
        // ],
        buttonName: [
          { required: true, message: '请输入按钮名称', trigger: 'blur' }
        ],
        linkUrl: [
          { required: true, message: '请输入跳转链接', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(data) {
      this.dialogFormVisible = true
      this.goodList = []
      this.page = 1
      this.addData = {
        carId: '', // 款型id
        carName: '', // 款型名称
        goodsId: '', // 车型id
        goodsName: '', // 车型名称
        syncImg: '1', // 同步
        imgUrl: '', // 车型图/款型图/自行编辑的图
        showButton: '2', // 展示按钮
        linkUrl: '', // 跳转链接
        buttonName: '查看详情', // 查看详情
        createDate: Math.floor(new Date()), // 创建时间
        ...data
      }
    },
    // 车型筛选
    querySearchAllCar(queryString, cb) {
      const requestParams = {
        page: 1,
        limit: 100,
        name: queryString,
        isOnStatus: 1
      }
      searchCarList(requestParams)
        .then((response) => {
          if (response.status === 200) {
            const userNameList = []
            const result = response.data.data.list
            result.map(function (value) {
              const newObj = {
                value: value.goodName,
                goodsId: value.goodId,
                goodPic: value.goodPic,
                brandName: value.brandName,
                maxPrice: value.maxPrice,
                minPrice: value.minPrice
              }
              userNameList.push(newObj)
            })
            cb(userNameList)
          }
        })
        .catch(() => {})
    },
    async handleSelectCar(item) {
      this.addData.goodsId = item.goodsId
      this.addData.brandName = item.brandName
      this.addData.goodsName = item.value
      this.addData.maxPrice = item.maxPrice || ''
      this.addData.minPrice = item.minPrice || ''
      this.addData.linkUrl = `https://m.58moto.com/garage/detail/${item.goodsId}`
      this.addData.imgUrl =
        (await this.getOriginalUrlData(item.goodPic)) || this.addData.imgUrl
      this.querySearchAllcardStyle(item.value)
    },
    clearCardTypes() {
      this.cardStyleList = []
      this.addData.carId = ''
      this.addData.carName = ''
    },
    // 款型筛选
    querySearchAllcardStyle(queryString) {
      const me = this
      const requestParams = {
        page: 1,
        limit: 100,
        goodsName: queryString || '',
        goodsId: me.addData.goodsId
      }
      getAgeList(requestParams)
        .then((response) => {
          if (response.status === 200) {
            me.cardStyleList = response.data.data.list
          }
        })
        .catch(() => {})
    },
    // // 款型选择
    async changeCarstyle(e) {
      const newData = this.cardStyleList.filter((_) => _.carId === e)
      this.addData.carName = newData[0].goodsCarName
      this.addData.imgUrl =
        (await this.getOriginalUrlData(newData[0].goodsThumb)) ||
        this.addData.imgUrl
      this.addData.carId = e.toString()
    },
    // 清理车型
    clearGoodsName() {
      this.addData = {
        ...this.addData,
        goodsId: '',
        goodsName: '',
        imgUrl: '',
        linkUrl: ''
      }
      this.clearCarName()
    },
    // 清理款型
    clearCarName() {
      this.addData = {
        ...this.addData,
        carId: '',
        carName: ''
      }
      this.cardStyleList = []
    },
    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 活动封面
    onSuccessTitleimageShore(res) {
      if (!res) return
      if (res.name) {
        this.addData.imgUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    getOriginalUrlData(url) {
      return getOriginalUrl({
        url
      })
        .then((response) => {
          const data = response.data.data || ''
          return data || url
        })
        .catch(() => {
          return url
        })
    },
    // 提交
    confirm() {
      const me = this
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          me.$emit('updataCar', this.addData)
          me.cancel()
        } else {
          me.$message.warning('还有数据未填写')
        }
      })
    },
    // 取消
    cancel() {
      this.dialogFormVisible = false
    }
  },
  emits: ['updateAward']
}
</script>
