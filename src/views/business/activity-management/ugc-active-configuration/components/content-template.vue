<template>
  <div class="sign-up-template">
    <div class="title">
      内容列表
      <el-button
        v-if="!isEdit"
        link
        type="primary"
        class="fl-right"
        @click="goEdit()"
        >编辑</el-button
      >
    </div>
    <el-form ref="ruleForm" :inline="true" :model="ruleForm" :rules="rules">
      <el-form-item
        label="背景颜色"
        prop="bgColor"
        required
        style="width: 200px"
        label-width="110px"
      >
        <el-color-picker
          v-if="isEdit"
          :predefine="predefineColors"
          v-model="ruleForm.bgColor"
          style="width: 200px"
        ></el-color-picker>
        <div v-else>{{ ruleForm.bgColor }}</div>
      </el-form-item>
      <br />
      <el-form-item
        label="点赞按钮颜色"
        prop="clickButtonColor"
        required
        style="width: 200px"
        label-width="110px"
      >
        <el-color-picker
          v-if="isEdit"
          :predefine="predefineColors"
          v-model="ruleForm.clickButtonColor"
          style="width: 200px"
        ></el-color-picker>
        <div v-else>{{ ruleForm.clickButtonColor }}</div>
      </el-form-item>
      <el-form-item
        label="排序选中背景颜色"
        prop="sortSelectedBgColor"
        required
        style="width: 200px"
        label-width="140px"
      >
        <el-color-picker
          v-if="isEdit"
          :predefine="predefineColors"
          v-model="ruleForm.sortSelectedBgColor"
          style="width: 200px"
        ></el-color-picker>
        <div v-else>{{ ruleForm.sortSelectedBgColor }}</div>
      </el-form-item>
      <el-form-item
        label="排序选中字体颜色"
        prop="sortSelectedFontColor"
        required
        style="width: 200px"
        label-width="140px"
      >
        <el-color-picker
          v-if="isEdit"
          :predefine="predefineColors"
          v-model="ruleForm.sortSelectedFontColor"
          style="width: 200px"
        ></el-color-picker>
        <div v-else>{{ ruleForm.sortSelectedFontColor }}</div>
      </el-form-item>
      <br />
      <el-form-item
        label="点赞字体颜色"
        prop="likedFontColor"
        required
        style="width: 200px"
        label-width="110px"
      >
        <el-color-picker
          v-if="isEdit"
          :predefine="predefineColors"
          v-model="ruleForm.likedFontColor"
          style="width: 200px"
        ></el-color-picker>
        <div v-else>{{ ruleForm.likedFontColor }}</div>
      </el-form-item>
      <el-form-item
        label="排序未选中背景颜色"
        prop="sortBgColor"
        required
        style="width: 200px"
        label-width="150px"
      >
        <el-color-picker
          v-if="isEdit"
          :predefine="predefineColors"
          v-model="ruleForm.sortBgColor"
          style="width: 200px"
        ></el-color-picker>
        <div v-else>{{ ruleForm.sortBgColor }}</div>
      </el-form-item>
      <el-form-item
        label="排序未选中字体颜色"
        prop="sortFontColor"
        required
        style="width: 200px"
        label-width="150px"
      >
        <el-color-picker
          v-if="isEdit"
          :predefine="predefineColors"
          v-model="ruleForm.sortFontColor"
          style="width: 200px"
        ></el-color-picker>
        <div v-else>{{ ruleForm.sortFontColor }}</div>
      </el-form-item>
      <p class="text-center" v-if="isEdit">
        <el-button type="danger" @click="handleClose()">取 消</el-button>
        <el-button type="primary" @click="validate()">保 存</el-button>
      </p>
    </el-form>
  </div>
</template>

<script>
import { deepCopy } from '@/utils'
export default {
  name: 'ContentTemplate',
  data() {
    let colorEnter = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('颜色必填'))
      }
      callback()
    }
    return {
      validateStatus: false,
      isEdit: true, // 是否配置
      ruleForm: {
        bgColor: '', // 背景颜色
        clickButtonColor: '', // 分享标题
        sortSelectedBgColor: '', // 排序选中背景颜色
        sortSelectedFontColor: '', // 排序选中字体颜色
        likedFontColor: '', // 点赞字体颜色
        sortBgColor: '', // 排序未选中背景颜色
        sortFontColor: '' // 排序未选中字体颜色
      },
      showData: {}, // 展示数据
      predefineColors: [
        // 颜色拾取器的自定义颜色
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],
      rules: {
        bgColor: [{ validator: colorEnter, trigger: 'change' }],
        clickButtonColor: [{ validator: colorEnter, trigger: 'change' }],
        sortSelectedBgColor: [{ validator: colorEnter, trigger: 'change' }],
        sortSelectedFontColor: [{ validator: colorEnter, trigger: 'change' }],
        likedFontColor: [{ validator: colorEnter, trigger: 'change' }],
        sortBgColor: [{ validator: colorEnter, trigger: 'change' }],
        sortFontColor: [{ validator: colorEnter, trigger: 'change' }]
      }
    }
  },
  watch: {},
  activated() {},
  methods: {
    // 初始化
    init(shareData, isEdit) {
      this.showData = shareData
        ? shareData
        : {
            shareTitle: '', // 分享标题
            shareImage: '', // 分享图片
            shareDesc: '', // 分享描述
            shareButtonText: '', // 按钮文案
            shareButtonBg: '#FFFFFF', // 分享按钮背景色
            shareButtonColor: '#000000' // 分享按钮字体颜色
          }
      this.ruleForm = deepCopy(this.showData)
      this.isEdit = isEdit
    },
    // 编辑
    goEdit() {
      this.ruleForm = deepCopy(this.showData)
      this.isEdit = true
    },
    // 确认
    validate() {
      const me = this
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.showData = deepCopy(this.ruleForm)
          this.isEdit = false
          this.$emit('postData', 'content', this.showData)
        } else {
          me.$message.warning('还有数据未填写')
        }
      })
    },
    // 取消
    handleClose() {
      this.isEdit = false
    }
  }
}
</script>
