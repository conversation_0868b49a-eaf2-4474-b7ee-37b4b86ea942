<template>
  <div>
    <el-dialog
      v-model="dialogFormVisible"
      append-to-body
      :destroy-on-close="true"
      :title="title"
      width="1100px"
    >
      <el-form
        ref="activitySearch"
        :model="ruleForm"
        :inline="true"
        class="activitySearch"
      >
        <el-form-item label="内容ID">
          <el-input
            v-model="ruleForm.essayId"
            type="text"
            placeholder="请输入内容ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="关联车型">
          <el-select
            v-model="ruleForm.goodsId"
            :remote-method="remoteGoodMethod"
            :loading="searchLoading"
            placeholder="请输入关联车型"
            filterable
            remote
            clearable
            @change="setGoodName"
          >
            <el-option
              v-for="(item, index) in goodList"
              :key="index"
              :label="item.carTypeName"
              :value="item.carTypeId"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList()">搜索</el-button>
          <el-button @click="resize()">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="tableData"
        border
        style="width: 100%; max-height: 60vh; overflow: auto"
      >
        <el-table-column
          align="center"
          prop="essayId"
          label="内容ID"
          width="80"
        />
        <el-table-column label="内容标题/内容" align="center">
          <template #default="{ row }">
            <cFeedListCtr :item="row" />
          </template>
        </el-table-column>
        <el-table-column
          prop="goodsName"
          label="关联车型"
          align="center"
          width="100"
        />
        <el-table-column
          prop="votes"
          label="活动投票量"
          align="center"
          width="140"
        />
        <el-table-column
          prop="likes"
          label="活动点赞量"
          align="center"
          width="140"
        />
        <el-table-column align="center" label="操作" width="100">
          <template v-slot="scope">
            <el-button type="primary" link @click="goSee(scope.row)"
              >查看</el-button
            >
            <el-button type="primary" link @click="goCancel(scope.row)"
              >不符合</el-button
            >
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作日志" width="100">
          <template v-slot="scope">
            <el-button type="primary" link @click="seeLog(scope.row)"
              >查看日志</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <SeeLog ref="SeeLog" />
    <ChooseIframe ref="ChooseIframe" />
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { searchController } from '@/api/articleModule'
import { tools } from '@haluo/util'
import cFeedListCtr from '@/components/CFeedList/cFeedListCtr.vue'
import { getUgcEssayList, refuseUgcEssay } from '@/api/activeConfiguration'
import SeeLog from '@/components/Dialog/SeeLog.vue'
import ChooseIframe from '@/components/Dialog/ChooseIframe.vue'
export default {
  name: 'DialogContent',
  components: {
    cFeedListCtr,
    SeeLog,
    ChooseIframe
  },
  data() {
    let imgEnter = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('图片必须上传'))
      }
      callback()
    }
    return {
      title: '参与内容', // 标题
      activityId: '',
      page: 1,
      dialogFormVisible: false,
      ruleForm: {
        essayId: '',
        goodsId: ''
      },
      goodList: [],
      tableData: [],
      searchLoading: false,
      rules: {
        imgUrl: [{ validator: imgEnter, trigger: 'change' }]
      }
    }
  },
  methods: {
    init(data) {
      this.dialogFormVisible = true
      this.goodList = []
      this.tableData = []
      this.page = 1
      this.activityId = data.activityId
      this.ruleForm = {
        essayId: '',
        goodsId: ''
      }
      this.getList()
    },
    getList() {
      const me = this
      getUgcEssayList({
        ...me.ruleForm,
        activityId: me.activityId,
        page: me.page,
        limit: 999
      }).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data || {}
          me.tableData = data.listData || []
        }
      })
    },
    // 重置
    resize() {
      this.page = 1
      this.ruleForm = {
        essayId: '',
        goodsId: ''
      }
      this.getList()
    },
    // 查询车型索引
    remoteGoodMethod(query) {
      this.searchLoading = true
      tools.debounce(() => this.getGoodList(query), 300)()
    },
    // 查询车型
    getGoodList(query = '') {
      const me = this
      if (!(query || me.ruleForm.carTypeName)) return
      searchController({
        type: 'car_detail',
        version: '3.6.0',
        key: query || me.ruleForm.carTypeName,
        page: 1,
        limit: 20
      })
        .then((response) => {
          if (response.data.code === 0) {
            const list = []
            const result = response.data.data && response.data.data[1].list
            result.map(function (value) {
              const obj = {
                carTypeId: value.id,
                carTypeName: value.subject.replace(/(<\w*>||<\/\w*>)?/g, '')
              }
              return list.push(obj)
            })
            me.goodList = list
          }
        })
        .finally((_) => {
          me.searchLoading = false
        })
    },
    // 车型id
    setGoodName(goodsId) {
      this.ruleForm.carId = goodsId || ''
      this.goodList = []
    },
    // 去详情
    goSee(data) {
      const url = `https://wap.corp.mddmoto.com/details-article/${data.essayId}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
      this.$refs.ChooseIframe.init(url)
    },
    // 取消
    goCancel(data) {
      const me = this
      me.$confirm('确认操作后，该内容参与活动失败？', {
        confirmButtonText: '确认不符合',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        refuseUgcEssay({
          id: data.id,
          activityQualify: 1
        }).then((response) => {
          if (response.data.code === 0) {
            me.$message.success('操作成功')
            me.getList()
          }
        })
      })
    },
    // 查看日志
    seeLog(data) {
      this.$refs.SeeLog.init(data.essayId)
    }
  }
}
</script>
