<template>
  <div v-loading="loading" class="ActiveConfiguration">
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <el-form-item label="活动ID">
        <el-input
          v-model="ruleForm.activityId"
          type="text"
          placeholder="请输入活动ID"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="活动名称">
        <el-input
          v-model="ruleForm.activityName"
          type="text"
          placeholder="请输入活动名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="关联品牌">
        <el-select
          v-model="brandName"
          :remote-method="remoteMethod"
          :loading="loading"
          placeholder="请输入品牌名称"
          filterable
          remote
          clearable
          @change="setBrandName"
        >
          <el-option
            v-for="item in brandList"
            :key="item.name"
            :label="item.title"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合作名称">
        <el-select
          v-model="projectName"
          :remote-method="remoteMethodList"
          :loading="loading"
          placeholder="请输入合作名称"
          filterable
          remote
          clearable
          @change="setProjectName"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="活动状态">
        <el-select v-model="ruleForm.status" placeholder="请选择">
          <el-option label="全部" value />
          <el-option
            v-for="(item, index) in mapStatus"
            :key="index"
            :label="item"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="活动开始时间" prop="beginTime">
        <el-date-picker
          v-model="ruleForm.beginTime"
          type="date"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="选择日期时间"
        />
      </el-form-item>
      <el-form-item label="活动结束时间" prop="endTime">
        <el-date-picker
          v-model="ruleForm.endTime"
          type="date"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="选择日期时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList()">搜索</el-button>
        <el-button @click="resize()">重置</el-button>
        <el-button @click="newAct()">新建活动</el-button>
      </el-form-item>
    </el-form>
    <!--活动列表-->
    <div>
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column
          align="center"
          prop="activityId"
          label="活动ID"
          width="80"
        />
        <el-table-column
          align="center"
          prop="activityName"
          label="活动名称"
          width="180"
        />
        <el-table-column
          align="center"
          prop="brandName"
          label="关联品牌"
          width="180"
        />
        <el-table-column
          align="center"
          prop="factoryContractName"
          label="合同名称"
          width="180"
        />
        <el-table-column
          align="center"
          prop="factoryProjectName"
          label="合作项目"
          width="180"
        />
        <el-table-column
          align="center"
          prop="joinNum"
          label="参与人数"
          width="90"
        />
        <el-table-column
          align="center"
          prop="joinEssayNum"
          label="参与内容数"
          width="100"
        />
        <el-table-column align="center" label="活动状态" width="90">
          <template v-slot="scope">{{ mapStatus[scope.row.status] }}</template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="presetShows"
          label="总曝光量"
          width="90"
        />
        <el-table-column
          align="center"
          prop="shows"
          label="已曝光量"
          width="90"
        />
        <el-table-column
          align="center"
          prop="leftShows"
          label="实际余量"
          width="90"
        />
        <el-table-column align="center" prop="process" label="进度" width="90">
          <template v-slot="scope">{{ scope.row.process }}%</template>
        </el-table-column>
        <el-table-column align="center" label="活动开始时间" width="180">
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.beginTime)
          }}</template>
        </el-table-column>
        <el-table-column align="center" label="活动结束时间" width="180">
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.endTime)
          }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="120">
          <template v-slot="scope">
            <div v-if="showButton(scope.row.templetJson)">
              <el-button
                type="primary"
                link
                size="small"
                @click="newAct(scope.row)"
              >
                编辑
              </el-button>
            </div>
            <div>
              <el-button
                type="primary"
                link
                size="small"
                @click="seeContent(scope.row)"
              >
                查看参与内容
              </el-button>
            </div>
            <div>
              <el-button
                type="primary"
                link
                size="small"
                @click="toAwardList(scope.row)"
              >
                领奖列表
              </el-button>
            </div>
            <div>
              <el-button
                v-clipboard:copy="clipboardCopy(scope.row)"
                v-clipboard:success="clipboardSuccess"
                type="primary"
                link
                size="small"
              >
                拷贝链接
              </el-button>
            </div>
            <div>
              <el-button
                type="primary"
                link
                size="small"
                @click="lookedetail(scope.row)"
              >
                预览
              </el-button>
              <el-button
                v-if="showButton(scope.row.templetJson)"
                type="primary"
                link
                size="small"
                @click="copyAdvert(scope.row)"
              >
                复制
              </el-button>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column align="center" label="操作日志" width="100">
          <template v-slot="scope">
            <el-button type="primary" link @click="copyAdvert(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <!--分页-->
    <el-pagination
      v-if="total"
      :total="total"
      :current-page="page"
      layout="total, prev, pager, next, jumper"
      @current-change="currentChange"
    />
    <choose-iframe ref="ChooseIframe" />
    <dialogContent ref="DialogContent" />
  </div>
</template>

<script>
import { getOrderProjectListByParam } from '@/api/garage'
import { searchBrand } from '@/api/articleModule'
import ChooseIframe from '@/components/Dialog/ChooseIframe.vue'
import DialogContent from './components/dialog-content.vue'
import clipboard from '@/directive/clipboard/index.js'
import { activityGetListFactoryActs } from '@/api/activeConfiguration'
export default {
  data() {
    return {
      mapStatus: {
        0: '已失效',
        1: '未开始',
        2: '正在进行',
        3: '已结束'
        // 4: '待审核',
        // 5: '审核失败',
        // 6: '已删除'
      },
      brandName: '', // 查询的brandName
      projectName: '', // 合作名称
      loading: false, // 加载状态
      brandList: [], // 品牌列表
      projectList: [], // 计划列表
      tableData: [],
      ruleForm: {
        activityId: '',
        activityName: '',
        status: '',
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        advertiserType: '', // 活动方类型
        advertiserId: '', // 活动方类型
        brandId: '', // 厂家id
        factoryProjectId: '', // 厂家合作项目id
        activityTypes: '6',
        limit: 10
      },
      page: 1,
      total: 0
    }
  },
  name: 'ActiveTemplateConfiguration',
  directives: {
    clipboard
  },
  components: {
    ChooseIframe,
    DialogContent
  },
  computed: {
    daterange: {
      get() {
        if (this.ruleForm.beginTime && this.ruleForm.endTime) {
          return [this.ruleForm.beginTime, this.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginTime = value[0]
          this.ruleForm.endTime = value[1]
        } else {
          this.ruleForm.beginTime = ''
          this.ruleForm.endTime = ''
        }
      }
    }
  },
  activated() {
    const query = this.$route.query || {}
    if (query.brandId) {
      this.ruleForm.brandId = query.brandId
    }
    if (query.brandName) {
      this.brandName = query.brandName
    }
    this.getList()
  },
  methods: {
    currentChange(page) {
      this.page = page
      this.getList()
    },
    // 重置
    resize() {
      this.ruleForm = {
        activityId: '',
        activityName: '',
        status: '',
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        advertiserType: '', // 活动方类型
        advertiserId: '', // 活动方类型
        brandId: '', // 厂家id
        factoryProjectId: '', // 厂家合作项目id
        activityTypes: '6',
        limit: 10
      }
      this.brandName = ''
      this.projectName = ''
      this.page = 1
      this.getList()
    },
    // 查询品牌索引
    remoteMethod(query) {
      if (!query) return
      this.loading = true
      this.$tools.debounce(() => this.getBrandList(query), 300)()
    },
    // 查询品牌
    getBrandList(query = '') {
      const me = this
      searchBrand({
        name: query || me.brandName,
        page: 1,
        limit: 10
      })
        .then((response) => {
          me.brandList = []
          if (response.data.code === 0) {
            const brandList = []
            const result = response.data.data && response.data.data.listData
            result.map(function (value) {
              const newObj = {
                title: value.brandName,
                name: value.brandId
              }
              brandList.push(newObj)
              me.brandList = brandList
            })
          }
        })
        .finally((_) => {
          me.loading = false
        })
    },
    // 品牌id
    setBrandName(brandId) {
      this.ruleForm.brandId = brandId || ''
    },
    // 查询合作项目
    remoteMethodList(query) {
      if (!query) return
      this.loading = true
      this.$tools.debounce(
        () => this.getOrderProjectListByParamList(query),
        300
      )()
    },
    // 查询品牌
    getOrderProjectListByParamList(query = '') {
      const me = this
      me.loading = true
      getOrderProjectListByParam({
        contractName: query,
        brandId: me.ruleForm.brandId
      })
        .then((res) => {
          me.loading = false
          if (res.data.code == 0) {
            const list = res.data.data || []
            me.projectList = list
          }
        })
        .catch((e) => console.log(e))
    },
    // 品牌id
    setProjectName(id) {
      this.ruleForm.factoryProjectId = id || ''
    },
    // 列表数据
    getList() {
      this.loading = true
      activityGetListFactoryActs({
        ...this.ruleForm,
        page: this.page
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.tableData = response.data.data.list || []
            this.tableData.map((item) => {
              item.rowStatus = !!item.status
            })
            this.total = response.data.data.total
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .finally((_) => {
          this.loading = false
        })
    },
    // 新建活动
    newAct(row) {
      this.$router.push({
        name: 'UgcActiveConfigurationDetail',
        query: {
          activityId: (row && row.activityId) || '',
          templetId: (row && row.templetId) || ''
        }
      })
    },
    // 查看参与内容
    seeContent(row) {
      this.$refs.DialogContent.init(row)
      console.log(row)
    },
    // 领奖列表
    toAwardList(row) {
      this.$router.push({
        name: 'AwardList',
        query: {
          templetId: row.templetId,
          activityId: row.activityId,
          activityName: row.activityName
        }
      })
    },
    // 复制
    clipboardSuccess() {
      this.$message({
        message: '拷贝成功',
        type: 'success',
        duration: 1500
      })
    },
    // 预览
    lookedetail(data) {
      let url = `https://wap.58moto.com/zt/2023/10/main-venue-template?activityId=${data.activityId}&share=true`
      const item = JSON.parse(data.templetJson || '{}')
      if (item.isNew) {
        url = `https://wap.58moto.com/zt/2024/4/ugc-activity-template?activityId=${data.activityId}&share=true`
      }
      this.$refs.ChooseIframe.init(url)
    },
    // 复制
    copyAdvert(row) {
      this.$router.push({
        name: 'UgcActiveConfigurationDetail',
        query: {
          activityId: row.activityId,
          templetId: row.templetId,
          copy: 1
        }
      })
    },
    clipboardCopy(data) {
      let url = `https://wap.58moto.com/zt/2023/10/main-venue-template?activityId=${data.activityId}&share=true`
      const item = JSON.parse(data.templetJson || '{}')
      if (item.isNew) {
        url = `https://wap.58moto.com/zt/2024/4/ugc-activity-template?activityId=${data.activityId}&share=true`
      }
      return url
    },
    showButton(data) {
      const item = JSON.parse(data || '{}')
      return item.isNew
    }
  }
}
</script>

<style lang="scss" scoped>
.ActiveConfiguration {
  padding: 20px;
  .el-pagination {
    text-align: center;
    margin-top: 20px;
  }
  .el-date-editor.el-range-editor {
    width: 400px;
  }
}
</style>
