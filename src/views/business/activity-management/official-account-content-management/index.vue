/** * 公众号内容管理列表*/
<template>
  <div v-loading="loading" class="content-manage-list">
    <el-form :model="form" :inline="true">
      <el-form-item label="ID">
        <el-input
          v-model="form.id"
          clearable
          style="width: 300px"
          placeholder="请输入ID"
        />
      </el-form-item>
      <el-form-item label="标题">
        <el-input
          v-model="form.title"
          clearable
          style="width: 300px"
          placeholder="请输入标题"
        />
      </el-form-item>
      <el-form-item label="品牌类型">
        <el-select v-model="form.brandType" clearable @change="brandList = []">
          <el-option label="全部" :value="''" />
          <el-option
            v-for="(value, key) in brandTypeEnum"
            :key="key"
            :label="value"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="品牌">
        <el-select
          v-model="form.brandName"
          :remote-method="getBrands"
          :loading="false"
          placeholder="请输入车型品牌"
          filterable
          remote
          clearable
          @change="addLabel"
        >
          <el-option
            v-for="item in brandList"
            :key="item.labelId"
            :label="item.labelName"
            :value="item.labelName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="位置">
        <el-select v-model="form.position" placeholder="请选择位置">
          <el-option
            v-for="(value, index) in positionList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布时间">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          v-model="daterange"
          style="width: 400px"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="至"
          start-placeholder="发布开始日期"
          end-placeholder="发布结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="initGetList">重置</el-button>
        <el-button type="primary" @click="goEdit({})">创建</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="shopList" highlight-current-row max-height="71vh" border>
      <el-table-column prop="id" label="ID" align="center" />
      <el-table-column prop="title" label="标题" align="center" />
      <el-table-column label="品牌类型" align="center">
        <template v-slot="scope">
          {{ brandTypeEnum[scope.row.brandType] || '' }}
        </template>
      </el-table-column>
      <el-table-column prop="brandName" label="品牌" align="center" />
      <el-table-column prop="brandId" label="品牌ID" align="center" />
      <el-table-column label="位置" align="center">
        <template v-slot="scope">
          {{ positionEnumList[scope.row.position] }}
        </template>
      </el-table-column>
      <el-table-column prop="url" label="链接" align="center" />
      <el-table-column label="发布时间" align="center" width="140">
        <template v-slot="scope">
          {{ $filters.timeFull(scope.row.publishTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="orderName" label="订单名称" align="center" />
      <el-table-column
        prop="orderProjectName"
        label="合作项目"
        align="center"
      />
      <el-table-column label="状态" align="center">
        <template v-slot="scope">
          {{ scope.row.status === 0 ? '无效' : '有效' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template v-slot="scope">
          <el-button
            type="primary"
            link
            size="small"
            v-if="scope.row.status"
            @click="goEdit(scope.row)"
          >
            修改 </el-button
          ><el-button
            type="primary"
            link
            size="small"
            v-if="scope.row.status"
            @click="goToVoid(scope.row)"
          >
            作废
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="155">
        <template v-slot="scope">
          {{ $filters.timeFullS(scope.row.createTime) }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="20"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      style="justify-content: center; margin-top: 15px"
      @current-change="currentChange"
    />
    <editDetail ref="editDetail" @updateItem="success" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { convertKeyValueEnum } from '@/utils/convert'
import { searchBrand } from '@/api/articleModule'
import { getAdvertiserList } from '@/api/advertModule'
import {
  getOfficialAccountContentPage,
  updateofficialAccountContentAbandon
} from '@/api/activeConfiguration'
import editDetail from './components/editDetail.vue'
import { deepCopy } from '@/utils'
export default {
  name: 'coolingManagementList',
  components: {
    editDetail
  },
  computed: {
    daterange: {
      get() {
        if (this.form.publishTimeStart && this.form.publishTimeEnd) {
          return [this.form.publishTimeStart, this.form.publishTimeEnd]
        }
        return []
      },
      set(value) {
        if (value) {
          this.form.publishTimeStart = value[0]
          this.form.publishTimeEnd = value[1]
        } else {
          this.form.publishTimeStart = ''
          this.form.publishTimeEnd = ''
        }
      }
    }
  },
  data() {
    return {
      loading: false,
      addDataStatus: false,
      shopList: [], // 所有申请列表
      page: 1,
      total: 0,
      form: {
        position: '',
        id: '',
        title: '',
        brandId: '',
        brandName: '',
        publishTimeStart: '',
        publishTimeEnd: '',
        brandType: ''
      },
      positionList: {
        全部: '',
        首条: '0',
        次条: '1',
        三条及以上: '2'
      },
      positionEnumList: {},
      searchValue: '', // 搜索品牌关键字
      brandList: [], // 搜索车型品牌列表
      dayjs,
      brandTypeEnum: {
        1: '车企',
        2: '非车企'
      }
    }
  },
  activated() {
    this.positionEnumList = convertKeyValueEnum(this.positionList)
    this.page = 1
    const query = this.$route.query || {}
    if (query.brandId) {
      this.form.brandId = query.brandId
      this.form.brandName = query.brandName
      this.searchValue = query.brandName
    }
    if (query.orderProjectId) {
      this.form.orderProjectId = query.orderProjectId
    }
    this.getList()
  },
  methods: {
    getBrands(query) {
      this.searchValue = query
      this.$tools.debounce(this.getBrandList, 300)()
    },
    // 获取品牌列表
    async getBrandList() {
      const me = this
      me.brandList = []
      const params = {
        typeIds: '3,4',
        advertiserName: this.searchValue,
        extraShowBrandName: this.searchValue,
        page: 1,
        limit: 10
      }
      const urls = !me.form.brandType
        ? [searchBrand(params), getAdvertiserList(params)]
        : me.form.brandType === '1'
        ? [searchBrand(params)]
        : [getAdvertiserList(params)]
      const [carInfo, adInfo] = await Promise.all(urls)
      const brandList = []
      if (
        carInfo.data.code === 0 ||
        (adInfo && adInfo.data && adInfo.data.msg && adInfo.data.code === 0)
      ) {
        const carResult =
          (carInfo.data.data && carInfo.data.data.listData) ||
          carInfo.data.data ||
          []
        const adResult = adInfo?.data?.data || []
        carResult.map((item) => {
          brandList.push({
            labelName:
              item.extraShowBrandName || item.brandName || item.advertiserName,
            labelId: item.brandId || item.id
          })
        })
        adResult.map((item) => {
          brandList.push({
            labelName: item.brandName || item.advertiserName,
            labelId: item.brandId || item.id
          })
        })
        me.brandList = brandList
      }
    },
    // 选择品牌
    addLabel(brandName) {
      const me = this
      const brand = me.brandList.filter((_) => _.labelName === brandName)
      me.form.brandId = (brand[0] && brand[0].labelId) || ''
    },
    // 获取列表数据
    getList() {
      const me = this
      const requestParams = {
        ...this.form,
        page: me.page,
        limit: 20
      }
      me.loading = true
      getOfficialAccountContentPage(requestParams)
        .then((response) => {
          me.loading = false
          if (response.data.code === 0) {
            const data = response.data.data
            me.shopList = data.listData
            me.total = data.total
          }
        })
        .catch(() => {
          me.loading = false
        })
    },
    // 更新列表页码
    currentChange(page) {
      this.page = page
      this.getList()
    },
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 重置
    initGetList() {
      this.page = 1
      this.form = {
        position: '',
        id: '',
        title: '',
        brandId: '',
        brandName: '',
        publishTimeStart: '',
        publishTimeEnd: '',
        brandType: ''
      }
      this.getList()
    },
    goToVoid(item) {
      const me = this
      me.$confirm('作废后订单条数将被释放，是否确认作废？', '作废', {
        confirmButtonText: '作废',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          deleteAction(item)
        })
        .catch((err) => {
          me.$message({
            type: 'info',
            message: (err && err.message) || '已取消废除'
          })
        })

      function deleteAction(item) {
        updateofficialAccountContentAbandon({
          id: item.id
        })
          .then(function (res) {
            res.data.code === 0
              ? me.$message.success('作废成功')
              : me.$message.error(res.data.msg)
            me.getList()
          })
          .catch(function (e) {
            me.$message.error(e.message)
          })
      }
    },
    // 新增或者编辑
    goEdit(data) {
      let postData = deepCopy(data)
      postData.publishTime = postData.publishTime
        ? dayjs(postData.publishTime).format('YYYY-MM-DD')
        : ''
      postData.position =
        postData.position !== undefined ? postData.position.toString() : ''
      this.$refs.editDetail.init(postData)
    },
    success() {
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.content-manage-list {
  padding: 20px;
}
</style>
