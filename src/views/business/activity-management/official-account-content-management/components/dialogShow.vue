<template>
  <div class="dialog-content">
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :title="dialogTitle"
      :append-to-body="true"
      width="1000px"
    >
      <el-form
        ref="clueForm"
        :model="ruleForm"
        label-width="165px"
        class="dialog-content-item"
      >
        <el-form-item label="标题">
          <span>{{ ruleForm.title }}</span>
        </el-form-item>
        <el-form-item label="品牌类型">
          <span>{{ brandTypeEnum[ruleForm.brandType] || '' }}</span>
        </el-form-item>
        <el-form-item label="品牌">
          <span>{{ ruleForm.brandName }}</span>
        </el-form-item>
        <el-form-item label="合作项目">
          <CooperateList
            ref="CooperateList"
            :isOfficial="true"
            :setHeight="'100px'"
          />
        </el-form-item>
        <el-form-item label="选择发布时间" prop="remark">
          <span>{{ ruleForm.publishTime }} </span>
        </el-form-item>
        <el-form-item label="位置" prop="remark">
          <span>{{ positionEnum[ruleForm.position] }} </span>
        </el-form-item>
        <el-form-item label="链接" prop="price">
          <span>{{ ruleForm.url }}</span>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose()">取消</el-button>
          <el-button v-if="second" type="info"
            >确认提交({{ second }} 秒)</el-button
          >
          <el-button v-else type="primary" @click="submitForm()"
            >确认提交</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  updateofficialAccountContentAdd,
  updateofficialAccountContentUpdate
} from '@/api/activeConfiguration'
import CooperateList from '../../../contentTreasure/managementActivity/commponents/CooperateList.vue'
export default {
  name: 'dialogShow',
  props: {},
  components: {
    CooperateList
  },
  data() {
    return {
      dialogTitle: '确认信息',
      dialogVisible: false,
      typeList: {},
      ruleForm: {},
      second: 0,
      timeInterval: null,
      positionEnum: {
        0: '首条',
        1: '次条',
        2: '三条及以上'
      },
      brandTypeEnum: {
        1: '车企',
        2: '非车企'
      }
    }
  },
  mounted() {},
  methods: {
    init(ruleForm, list) {
      const me = this
      me.ruleForm = {
        ...ruleForm,
        contractName: ruleForm.contractName || ''
      }
      me.dialogVisible = true
      setTimeout(() => {
        me.$refs.CooperateList.init(
          list?.id ? [list] : [],
          list.contractName || ''
        )
      }, 100)
      me.second = 5
      me.timeInterval = setInterval(() => {
        me.second = --me.second
        if (me.second <= 0) {
          clearInterval(me.timeInterval)
          me.second = 0
          return
        }
      }, 1000)
    },
    submitForm() {
      const me = this
      const postData = {
        ...me.ruleForm
      }
      delete postData.createTime
      delete postData.updateTime
      const url = me.ruleForm.id
        ? updateofficialAccountContentUpdate
        : updateofficialAccountContentAdd
      url({
        ...postData
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('绑定成功')
          me.$emit('updateItem', 'add')
          me.handleClose()
        } else {
          me.$message.error(response.data.msg || '操作失败')
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped></style>
