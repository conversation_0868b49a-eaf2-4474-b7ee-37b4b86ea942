<template>
  <div class="dialog-content">
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :title="dialogTitle"
      :append-to-body="true"
      width="1000px"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="90px"
        style="margin: 10px 0"
        @submit.prevent
      >
        <el-form-item label="标题">
          <el-input
            v-model="form.title"
            clearable
            style="width: 300px"
            placeholder="请输入标题"
          />
        </el-form-item>
        <el-form-item label="品牌类型">
          <el-radio-group v-model="form.brandType" @change="updateBrandType">
            <el-radio :label="1">车企</el-radio>
            <el-radio :label="2">非车企</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="品牌" prop="brand">
          <el-select
            v-model="form.brandName"
            :remote-method="getBrands"
            :loading="false"
            placeholder="请输入车型品牌"
            filterable
            remote
            clearable
            @change="addLabel"
          >
            <el-option
              v-for="item in brandList"
              :key="item.labelId"
              :label="item.labelName"
              :value="item.labelName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="合作项目">
          <el-button @click="$refs.cooperateSelected.init(form.brandId)"
            >选择项目</el-button
          >
          <el-button type="primary" @click="updateSelectedData({})"
            >取消关联</el-button
          >
          <CooperateList
            ref="CooperateList"
            :isOfficial="true"
            :setHeight="'100px'"
          />
        </el-form-item>
        <el-form-item label="发布时间">
          <el-date-picker
            v-model="form.publishTime"
            style="width: 400px"
            value-format="YYYY-MM-DD"
            type="date"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="位置">
          <el-select v-model="form.position" placeholder="请选择位置">
            <el-option
              v-for="(value, index) in postionList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="链接">
          <el-input
            v-model="form.url"
            clearable
            style="width: 300px"
            placeholder="请输入链接"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">取消</el-button>
          <el-button
            style="margin-left: 20px"
            type="primary"
            @click="submitForm"
            >确认</el-button
          >
        </el-form-item>
      </el-form>
      <CooperateSelected
        ref="cooperateSelected"
        :isOfficial="true"
        @updateSelectedData="updateSelectedData"
      />
      <dialogShow ref="dialogShow" @updateItem="updateData" />
    </el-dialog>
  </div>
</template>

<script>
import { manufacturerBusinessType } from '@/utils/enum'
import { convertKeyValueEnum } from '@/utils/convert'
import { searchBrand } from '@/api/articleModule'
import { getOrderProjectListByParam } from '@/api/garage'
import CooperateSelected from '../../../contentTreasure/managementActivity/commponents/CooperateSelected.vue'
import CooperateList from '../../../contentTreasure/managementActivity/commponents/CooperateList.vue'
import dialogShow from './dialogShow.vue'
import { getAdvertiserList } from '@/api/advertModule'

export default {
  name: 'dialogShowBusiness',
  props: {
    showTop: {
      type: Boolean,
      default: false
    }
  },
  components: {
    CooperateList,
    CooperateSelected,
    dialogShow
  },
  data() {
    return {
      dialogTitle: '订单信息',
      dialogVisible: false,
      isAgain: false,
      manufacturerBusinessType,
      typeList: {},
      form: {},
      second: 0,
      timeInterval: null,
      searchValue: '',
      brandList: [],
      postionList: {
        首条: '0',
        次条: '1',
        三条及以上: '2'
      }
    }
  },
  mounted() {
    this.typeList = convertKeyValueEnum(manufacturerBusinessType)
  },
  methods: {
    init(ruleForm) {
      const me = this
      me.form = ruleForm
      me.dialogVisible = true
      me.selectedLis = []
      me.form.orderProjectId
        ? me.getOrderProjectListByParamList(me.form.orderProjectId)
        : setTimeout(() => {
            me.updateSelectedData({})
          }, 100)
    },
    // 查询合作项目
    getOrderProjectListByParamList(id) {
      const me = this
      getOrderProjectListByParam({
        id,
        availableCntZero: 1,
        extraIncludeId: id
      }).then((res) => {
        if (res.data.code == 0) {
          const list = res.data.data || []
          me.selectedList = list.length ? list[0] : {}
          if (me.$refs.CooperateList) {
            me.$refs.CooperateList.init(
              me.selectedList.id ? [me.selectedList] : [],
              me.selectedList.contractName || ''
            )
          }
        }
      })
    },
    updateBrandType() {
      this.form.brandName = ''
      this.form.brandId = ''
      this.brandList = []
      this.updateSelectedData({})
    },
    getBrands(query) {
      this.searchValue = query
      this.$tools.debounce(this.getBrandList, 300)()
    },
    // 获取品牌列表
    async getBrandList() {
      const me = this
      me.brandList = []
      const params = {
        typeIds: '3,4',
        advertiserName: this.searchValue,
        extraShowBrandName: this.searchValue,
        page: 1,
        limit: 10
      }
      const urls = !me.form.brandType
        ? [searchBrand(params), getAdvertiserList(params)]
        : me.form.brandType === 1
        ? [searchBrand(params)]
        : [getAdvertiserList(params)]
      const [carInfo, adInfo] = await Promise.all(urls)
      const brandList = []
      if (
        carInfo.data.code === 0 ||
        (adInfo && adInfo.data && adInfo.data.msg && adInfo.data.code === 0)
      ) {
        const carResult =
          (carInfo.data.data && carInfo.data.data.listData) ||
          carInfo.data.data ||
          []
        const adResult = adInfo?.data?.data || []
        carResult.map((item) => {
          brandList.push({
            labelName:
              item.extraShowBrandName || item.brandName || item.advertiserName,
            labelId: item.brandId || item.id
          })
        })
        adResult.map((item) => {
          brandList.push({
            labelName: item.brandName || item.advertiserName,
            labelId: item.brandId || item.id
          })
        })
        me.brandList = brandList
      }
    },
    // 选择品牌
    addLabel(brandName) {
      const me = this
      const brand = me.brandList.filter((_) => _.labelName === brandName)
      me.form.brandId = (brand[0] && brand[0].labelId) || ''
      me.form.brand = brandName
    },
    // 修改页面展示的列表
    updateSelectedData(val, status) {
      this.$refs.CooperateList.init(
        val?.id ? [val] : [],
        val.contractName || ''
      )
      this.form.campaignName = !status
        ? val.projectName || ''
        : this.form.campaignName || ''
      this.selectedList = val || {}
      this.form.orderProjectId = val.id || ''
    },
    submitForm() {
      const me = this
      if (!me.form.title) return me.$message.error('请输入标题')
      if (!me.form.brandType) return me.$message.error('请选择品牌类型')
      if (!me.form.brandName) return me.$message.error('请输入车型品牌')
      // if (!me.form.orderProjectId) return me.$message.error('请选择合作项目')
      if (!me.form.position) return me.$message.error('请选择位置')
      me.$refs.dialogShow.init(me.form, me.selectedList)
    },
    updateData() {
      this.$emit('updateItem')
      this.handleClose()
    },
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped></style>
