<template>
  <el-dialog
    title="关联专题编辑"
    v-model="activityDialog"
    center
    modal-append-to-body
    width="30%"
    :before-close="closeDialog"
  >
    <el-button type="primary" @click="addActivity">新增专题活动</el-button>

    <draggable :list="activityList" item-key="id" @change="change">
      <template #item="{ element }">
        <div class="item-box">
          <div class="icon" @click="deleteItem(element)">
            <el-icon><CloseBold /></el-icon>
          </div>
          <img :src="element.coverImg" />
          <div class="text">
            专题ID：{{ element.activityId }} | {{ element.activityName }}
          </div>
        </div>
      </template>
    </draggable>
    <el-dialog
      title="新增品牌"
      v-model="brandDialog"
      center
      modal-append-to-body
      width="480px"
      :before-close="closeBrand"
    >
      <el-form ref="form" :model="brandForm" label-width="68px">
        <el-form-item label="专题类型">
          <el-radio-group v-model="brandForm.activityType">
            <el-radio
              v-for="(item, key) in activityTypeEnum"
              :key="key"
              :label="key"
              >{{ item }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="活动ID">
          <el-input v-model="brandForm.activityId"></el-input>
        </el-form-item>
        <el-form-item label="封面图">
          <div>
            <el-upload
              class="cover-uploader"
              action
              :show-file-list="false"
              :http-request="httpRequest"
              :on-success="onSuccess"
            >
              <img v-if="coverImg" :src="coverImg" class="cover-img" />
              <el-icon v-else class="cover-uploader-icon"><IconPlus /></el-icon>
            </el-upload>
            <div class="mt10">请上传尺寸为345*100 的封面图片</div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="confirm">确认</el-button>
        <el-button type="primary" @click="closeBrand">取消</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import {
  postAddCarActivity,
  postDelCarActivity,
  getCarActivityList,
  sortCarActivity
} from '@/api/activeConfiguration'
import draggable from 'vuedraggable'
import { CloseBold } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus as IconPlus } from '@element-plus/icons-vue'
const emit = defineEmits(['change'])
const { proxy } = getCurrentInstance()
const brandDialog = ref(false)
const activityDialog = ref(false)
const activityList = ref([])
const carBrandId = ref('')
const brandForm = ref({
  activityType: '', // 0 专题 6 UGC
  activityId: ''
})
const coverImg = ref('')
const activityTypeEnum = {
  0: '专题活动',
  6: 'UGC活动',
  3: '厂家专题',
  4: '报名活动',
  5: '主会场活动'
}
const init = (id) => {
  carBrandId.value = id
  activityDialog.value = true
  getList()
}

const getList = async () => {
  try {
    const res = await getCarActivityList({
      carBrandId: carBrandId.value
    })
    if (res.data.code === 0) {
      activityList.value = res.data.data
    }
  } catch (error) {
    console.log(error)
  }
}

const addActivity = () => {
  brandDialog.value = true
}
const deleteItem = (item) => {
  ElMessageBox.confirm('是否删除品牌活动？')
    .then(async () => {
      try {
        const res = await postDelCarActivity({
          id: item.id
        })
        if (res.data.code === 0) {
          ElMessage.success('删除成功')
          getList()
        }
      } catch (error) {
        console.log(error)
      }
    })
    .catch((err) => {
      console.log(err)
    })
}

const closeDialog = () => {
  activityDialog.value = false
  emit('change')
}

const confirm = async () => {
  const parameter = {
    carBrandId: carBrandId.value,
    ...brandForm.value,
    coverImg: coverImg.value
  }
  if (!parameter.activityType) {
    return ElMessage.error('请选择专题类型')
  }
  if (!parameter.activityId) {
    return ElMessage.error('请填写活动ID')
  }
  if (!parameter.coverImg) {
    return ElMessage.error('请上传封面图')
  }
  const res = await postAddCarActivity(parameter)
  if (res.data.code === 0) {
    ElMessage.success('添加成功')
    getList()
    closeBrand()
  }
}
const closeBrand = () => {
  brandForm.value.activityType = ''
  brandForm.value.activityId = ''
  coverImg.value = ''
  brandDialog.value = false
}

const change = async (e) => {
  const id = e.moved.element.id
  const newIndex = e.moved.newIndex
  try {
    const res = await sortCarActivity({
      curId: id,
      index: newIndex + 1
    })
    if (res.data.code === 0) {
      ElMessage.success('修改成功')
      getList()
    }
  } catch (error) {
    console.log(error)
  }
}

const httpRequest = async (option) => {
  option.imageType = 'nowater' // 无水印
  option.quality = 1
  proxy.$oss.ossUploadImage(option)
}

const onSuccess = (res) => {
  if (res) {
    coverImg.value = res.imgOrgUrl || ''
  }
}

defineExpose({
  init
})
</script>

<style>
.cover-uploader .el-upload {
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.2s;
}

.cover-uploader .el-upload:hover {
  border-color: #409eff;
}

.el-icon.cover-uploader-icon {
  font-size: 20px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
}
</style>
<style lang="scss" scoped>
.cover-uploader {
  .cover-img {
    width: 345px;
    height: 100px;
    object-fit: cover;
  }
}
.item-box {
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: #f0f2f5;
  border-radius: 5px;
  padding: 15px;
  margin-top: 10px;
  img {
    width: 100%;
    height: 100px;
  }
  .icon {
    position: absolute;
    right: 2px;
    top: 2px;
  }
  .text {
    margin-top: 10px;
    font-size: 20px;
    font-weight: blod;
  }
}
</style>
