<template>
  <div class="content">
    <el-button type="primary" @click="addBrand">新増活动品牌</el-button>
    <el-table
      :data="brandListData"
      border
      style="margin-top: 20px; max-height: 70vh"
    >
      <el-table-column
        label="品牌名称"
        prop="brandName"
        align="center"
      ></el-table-column>
      <el-table-column
        label="关联专题数"
        prop="relationActCnt"
        align="center"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="text" @click="editItem(scope.row)">编辑</el-button>
          <el-button type="text" @click="deleteItem(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="新增品牌"
      v-model="brandDialog"
      width="25%"
      center
      :before-close="closeDialog"
    >
      <el-form-item label="车型品牌" prop="brand">
        <el-select
          v-model="brand"
          :remote-method="getBrands"
          value-key="labelId"
          :loading="false"
          placeholder="请输入车型品牌"
          filterable
          remote
          clearable
        >
          <el-option
            v-for="item in brandList"
            :key="item.labelId"
            :label="item.labelName"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <template #footer>
        <el-button type="primary" @click="confirm">确认</el-button>
        <el-button type="info" @click="closeDialog">取消</el-button>
      </template>
    </el-dialog>
    <popActivity ref="popActivityref" @change="getList" />
  </div>
</template>

<script setup>
import {
  getCarBrandList,
  postAddCarBrand,
  postDelCarBrand
} from '@/api/activeConfiguration'
import { searchBrand } from '@/api/articleModule'
import { ElMessage } from 'element-plus'
import popActivity from './components/popActivity.vue'
import { ElMessageBox } from 'element-plus'

const { proxy } = getCurrentInstance()

const brandListData = ref([])
const brandDialog = ref(false)
const brand = ref({})
const brandList = ref([])
const popActivityref = ref()
onMounted(() => {
  getList()
  getBrandList()
})

const getList = async () => {
  try {
    const res = await getCarBrandList()
    if (res.data.code === 0) {
      brandListData.value = res.data.data
    }
  } catch (error) {
    console.log(error)
  }
}

const addBrand = () => {
  brandDialog.value = true
}
const editItem = (item) => {
  popActivityref.value.init(item.carBrandId)
}
const deleteItem = (item) => {
  ElMessageBox.confirm('是否删除品牌活动？')
    .then(async () => {
      try {
        const res = await postDelCarBrand({
          carBrandId: item.carBrandId
        })
        if (res.data.code === 0) {
          ElMessage.success('删除成功')
          getList()
        }
      } catch (error) {
        console.log(error)
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
const getBrands = (query) => {
  proxy.$tools.debounce(getBrandList, 300)(query)
}
// 获取品牌列表
const getBrandList = (query) => {
  searchBrand({
    name: query,
    page: 1,
    limit: 10
  }).then((response) => {
    if (response.data.code === 0) {
      const list = []
      const result = response.data.data && response.data.data.listData
      result.map(function (value) {
        const newObj = {
          labelName: value.brandName,
          labelId: value.brandId
        }
        list.push(newObj)
        brandList.value = list
      })
    }
  })
}

const closeDialog = () => {
  brand.value = {}
  brandDialog.value = false
}
const confirm = async () => {
  try {
    const res = await postAddCarBrand({
      carBrandId: brand.value.labelId,
      brandName: brand.value.labelName
    })
    if (res.data.code === 0) {
      ElMessage.success('添加成功')
      getList()
      closeDialog()
    }
  } catch (error) {
    console.log(error)
  }
}
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
}
</style>
