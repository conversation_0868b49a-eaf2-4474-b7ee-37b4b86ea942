<template>
  <div class="edit-detail">
    <h3>
      内容池名称-{{ shopList.name }}
      <el-button
        type="primary"
        class="fl-right"
        style="margin-right: 100px"
        @click="confirm()"
      >
        提交
      </el-button>
    </h3>
    <el-radio-group
      v-model="selectedRadio"
      class="edit-detail-margin"
      @change="updateSelectedRadio()"
    >
      <el-radio-button label="待审核" />
      <el-radio-button label="全部" />
    </el-radio-group>
    <el-form
      v-show="selectedRadio === '全部'"
      ref="form"
      :model="form"
      :inline="true"
      @submit.prevent
    >
      <el-form-item label="内容ID">
        <el-input
          v-model="form.id"
          clearable
          style="width: 300px"
          placeholder="请输入内容池ID"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="form.status">
          <el-option
            v-for="(value, index) in allStatusList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="initGetList">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="eassyList"
      highlight-current-row
      :max-height="selectedRadio === '全部' ? '72vh' : '77vh'"
      border
    >
      <el-table-column prop="id" label="内容ID" align="center" width="100" />
      <el-table-column
        prop="authUser"
        label="作者信息"
        align="center"
        width="200"
      >
        <template v-slot="scope">
          {{ scope.row.author }}<br />
          作者id：{{ scope.row.authorId }}
        </template>
      </el-table-column>
      <el-table-column prop="sourceName" label="标题/内容" align="center">
        <template v-slot="scope">
          <CFeedListCtr :item="scope.row" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="120">
        <template v-slot="scope">
          {{ statusList[scope.row.placingAuditStatus] }}
        </template>
      </el-table-column>

      <el-table-column
        v-if="selectedRadio === '全部'"
        prop="updateTime"
        label="操作时间"
        align="center"
        width="140"
      >
        <template v-slot="scope">
          {{ $filters.timeFullS(scope.row.updateTime * 1000) }}
        </template>
      </el-table-column>
      <el-table-column prop="brandName" label="操作" align="center" width="200">
        <template v-slot="scope">
          <el-button
            type="primary"
            v-if="scope.row.placingAuditStatus !== 2"
            link
            size="small"
            @click="goPost(scope.row, 2)"
          >
            不通过 </el-button
          ><el-button
            type="primary"
            v-if="scope.row.placingAuditStatus !== 1"
            link
            size="small"
            @click="goPost(scope.row, 1)"
          >
            通过 </el-button
          ><el-button
            type="primary"
            link
            size="small"
            @click="showH5Preview(scope.row)"
          >
            预览
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <ChooseDetail ref="ChooseDetail" />
  </div>
</template>

<script>
import { $emit } from '../../../../../utils/gogocodeTransfer'
import CFeedListCtr from '@/components/CFeedList/cFeedListCtr.vue'
import { mapGetters } from 'vuex'
import { getPagePlacingTreasuresEssays, auditPlacingEssay } from '@/api/garage'
import {
  getPageAuditPlacingTreasures,
  confirmPlacingTreasures
} from '@/api/activeConfiguration'
import ChooseDetail from '@/components/CDetail/ChooseDetail.vue'
export default {
  name: 'editDetail',
  components: {
    CFeedListCtr,
    ChooseDetail
  },
  computed: {
    ...mapGetters(['uid', 'name'])
  },
  props: {},
  data() {
    return {
      id: '',
      brandloading: false,
      carLoading: false,
      shopList: {},
      eassyList: [], // 展示数据
      allEassyList: [], // 全部数据
      auditEassyList: [], // 待审核数据
      selectedRadio: '待审核',
      form: {
        id: '',
        status: ''
      },
      statusList: {
        0: '待审核',
        1: '通过',
        2: '不通过'
      },
      allStatusList: {
        全部: '',
        待审核: 0,
        通过: 1,
        不通过: 2
      } // 全部状态
    }
  },
  activated() {
    this.id = this.$route.query.id
    this.form = {
      id: '',
      status: ''
    }
    this.getList()
    this.getEassyList()
  },
  methods: {
    // 获取列表数据
    getList() {
      const me = this
      const requestParams = {
        page: 1,
        limit: 20,
        id: me.id,
        orderType: 2
      }
      me.loading = true
      getPageAuditPlacingTreasures(requestParams)
        .then((response) => {
          me.loading = false
          if (response.data.code === 0) {
            const data = response.data.data
            me.shopList = data.listData ? data.listData[0] : {}
            me.total = data.total
          }
        })
        .catch(() => {
          me.loading = false
        })
    },
    // 获取列表数据
    getEassyList() {
      const me = this
      const requestParams = {
        limit: 1000,
        page: 1,
        id: me.id
      }
      getPagePlacingTreasuresEssays(requestParams)
        .then((response) => {
          me.loading = false
          if (response.data.code === 0) {
            const data = response.data.data
            me.auditEassyList = []
            data.listData &&
              data.listData.map((item) => {
                if (item.placingAuditStatus === 0) {
                  me.auditEassyList.push(item)
                }
              })
            me.allEassyList = data.listData || []
            me.updateSelectedRadio()
            // me.eassyList = me.selectedRadio === '待审核' ? me.auditEassyList : me.allEassyList
          }
        })
        .catch(() => {
          me.loading = false
        })
    },
    goPost(data, type) {
      const me = this
      auditPlacingEssay({
        id: me.id,
        essayId: data.id,
        auditStatus: type
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('操作成功')
          me.getEassyList()
          $emit(me, 'success')
        }
      })
    },
    confirm() {
      const me = this
      confirmPlacingTreasures({
        id: me.id
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('确认审核成功')
          me.$router.go(-1)
        } else {
          me.$message.error('操作失败')
        }
      })
    },
    showH5Preview(item) {
      const url = `https://wap.corp.mddmoto.com/details-article/${item.id}?sourceType=C4CA4238A0B923820DCC509A6F75849B`
      // this.$refs.ChooseIframe.init(url)
      const data = {
        itemId: item.id,
        sourceType: 'C4CA4238A0B923820DCC509A6F75849B'
      }
      this.$refs.ChooseDetail.init(data, url)
    },
    // 修改展示
    updateSelectedRadio() {
      this.eassyList =
        this.selectedRadio === '待审核'
          ? this.auditEassyList
          : this.allEassyList
    },
    // 筛选
    search() {
      let backData = []
      const me = this
      if (!me.form.id && me.form.status === '')
        return (me.eassyList = me.allEassyList)
      const isStatus = me.form.status !== ''
      const isId = me.form.id
      if (isStatus && isId) {
        me.allEassyList.map((item) => {
          if (
            item.placingAuditStatus === me.form.status &&
            Number(me.form.id) === item.id
          ) {
            backData.push(item)
          }
        })
      } else {
        me.allEassyList.map((item) => {
          if (
            (isStatus && item.placingAuditStatus === me.form.status) ||
            (isId && Number(me.form.id) === item.id)
          ) {
            backData.push(item)
          }
        })
      }
      me.eassyList = backData || []
    },
    initGetList() {
      this.form = {
        id: '',
        status: ''
      }
      this.eassyList = this.allEassyList
    }
  },
  emits: ['success']
}
</script>

<style lang="scss" scoped>
.edit-detail {
  margin: 20px;
}
.edit-detail-margin {
  margin-bottom: 15px;
}
</style>
