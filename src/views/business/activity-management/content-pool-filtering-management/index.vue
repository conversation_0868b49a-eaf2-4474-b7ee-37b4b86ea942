/** * 内容池过滤管理*/
<template>
  <div v-loading="loading">
    <el-form
      ref="form"
      :model="form"
      :inline="true"
      label-width="90px"
      style="margin: 10px 0"
      @submit.prevent
    >
      <el-form-item label="内容池ID">
        <el-input
          v-model="form.id"
          clearable
          style="width: 300px"
          placeholder="请输入内容池ID"
        />
      </el-form-item>
      <el-form-item label="内容池名称">
        <el-input
          v-model="form.name"
          clearable
          style="width: 300px"
          placeholder="请输入内容池名称"
        />
      </el-form-item>
      <el-form-item>
        <el-button style="margin-left: 20px" type="primary" @click="search"
          >查询</el-button
        >
        <el-button @click="initGetList">重置</el-button>
      </el-form-item>
    </el-form>
    <div style="max-height: 80vh">
      <el-table
        ref="shopList"
        :data="shopList"
        highlight-current-row
        row-key="shopList"
        style="height: 75vh; overflow-y: auto"
        max-height="80vh"
        border
      >
        <el-table-column prop="id" label="内容池ID" align="center" />
        <el-table-column prop="name" label="内容池名称" align="center" />
        <el-table-column prop="goodsName" label="车型名称" align="center" />
        <el-table-column prop="sourceName" label="待审核数量" align="center">
          <template v-slot="scope"> {{ scope.row.waitAuditNum }}篇 </template>
        </el-table-column>
        <el-table-column prop="sourceName" label="已审核数量" align="center">
          <template v-slot="scope"> {{ scope.row.hasAuditNum }}篇 </template>
        </el-table-column>
        <el-table-column prop="brandName" label="操作" align="center">
          <template v-slot="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click="goNext(scope.row)"
            >
              去审核 </el-button
            ><el-button
              type="primary"
              link
              size="small"
              @click="confirm(scope.row)"
            >
              确认
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="page"
        :page-size="20"
        :page-sizes="[10, 20, 40, 60]"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        style="justify-content: center; margin-top: 10px"
        @size-change="currentChange"
        @current-change="currentChange"
      />
    </div>
  </div>
</template>

<script>
import {
  getPageAuditPlacingTreasures,
  confirmPlacingTreasures
} from '@/api/activeConfiguration'
import { convertKeyValueEnum } from '@/utils/convert'
export default {
  name: 'coolingManagementList',
  data() {
    return {
      loading: false,
      addDataStatus: false,
      shopList: [], // 所有申请列表
      brands: [], // 厂商列表
      page: 1,
      total: 0,
      statusList: {
        全部: '',
        待投放: '0',
        进行中: '1',
        已投放: '2'
      },
      statusEnumList: {},
      form: {
        id: '', //
        name: ''
      },
      dialogVisible: false,
      dialogBrands: []
    }
  },
  activated() {
    this.statusEnumList = convertKeyValueEnum(this.statusList)
    this.page = 1
    this.getList()
  },
  methods: {
    // 获取列表数据
    getList() {
      const me = this
      const requestParams = {
        ...this.form,
        page: me.page,
        limit: 20
      }
      me.loading = true
      getPageAuditPlacingTreasures(requestParams)
        .then((response) => {
          me.loading = false
          if (response.data.code === 0) {
            const data = response.data.data
            console.log(data)
            me.shopList = data.listData
            me.total = data.total
          }
        })
        .catch(() => {
          me.loading = false
        })
    },
    // 更新列表页码
    currentChange(page) {
      this.page = page
      this.getList()
    },
    // 查询
    search() {
      this.page = 1
      this.getList()
    },
    // 重置
    initGetList() {
      this.page = 1
      this.form = {
        id: '', //
        name: ''
      }
      this.getList()
    },
    confirm(data) {
      console.log(data)
      const me = this
      confirmPlacingTreasures({
        id: data.id
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('确认审核成功')
          me.getList()
        } else {
          me.$message.error('操作失败')
        }
      })
    },
    goNext(data) {
      console.log(data)
      this.$router.push({
        name: 'ContentPoolFilteringDetail',
        query: {
          id: data.id,
          name: data.name
        }
      })
    },
    success() {
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.factory-entry {
  // padding: 0 0 50px 0;
  .factory-entry-select {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  .factory-entry-input {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .div_title {
    width: 100px;
    text-align: right;
    margin-right: 20px;
  }
  .lookeMore {
    color: rgb(54, 54, 241);
  }
}
</style>
