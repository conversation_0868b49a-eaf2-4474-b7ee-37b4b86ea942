<template>
  <div v-loading="loading" style="margin: 20px 20px 0">
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <el-form-item label="评选名称">
        <el-input
          v-model="ruleForm.title"
          type="text"
          placeholder="请输入评选名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="ruleForm.status">
          <el-option
            v-for="(value, index) in statusList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button @click="add()">增加新的评选</el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="dataList"
      :data="dataList"
      row-key="dataList"
      border
      style="width: 100%"
      @row-dblclick="rowClick"
    >
      <el-table-column prop="id" label="id" align="center" />
      <el-table-column prop="title" label="评选列表" align="center" />
      <el-table-column prop="status" align="center" label="状态">
        <template v-slot="scope">
          <el-switch
            v-model="scope.row.status"
            active-text=""
            inactive-text=""
            @change="changeStatus(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="status" align="center" label="预览">
        <template v-slot="scope">
          <el-button type="primary" @click="see(scope.row.id, 'instructor')"
            >首页</el-button
          >
          <el-button
            type="primary"
            @click="see(scope.row.id, 'instructor-list')"
            >榜单</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="beginTime"
        align="center"
        width="100"
        label="开始时间"
      >
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.beginTime)
        }}</template>
      </el-table-column>
      <el-table-column
        prop="endTime"
        align="center"
        width="110"
        label="结束时间"
      >
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.endTime)
        }}</template>
      </el-table-column>
    </el-table>
    <choose-iframe ref="ChooseIframe" />
  </div>
</template>

<script>
import {
  GetSelectionList,
  PostUpdateSelectionStatus
} from '@/api/activeConfiguration'
import ChooseIframe from '@/components/Dialog/ChooseIframe.vue'
import { recordOldData, recordBeforeAlter } from '@/utils/enum/logData'
export default {
  name: 'VotingManagement',
  components: {
    ChooseIframe
  },
  data() {
    return {
      loading: false,
      ruleForm: {
        title: '',
        status: '1'
      },
      statusList: {
        有效: '1',
        无效: '0'
      },
      dataList: []
    }
  },
  created() {},
  activated() {
    this.search()
  },
  methods: {
    // 查询列表
    search() {
      const me = this
      me.loading = true
      GetSelectionList(me.ruleForm)
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            recordOldData(data)
            data.map((_) => {
              _.status = !!_.status
            })
            me.dataList = data
            me.loading = false
          } else {
            me.loading = false
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.loading = false
          me.$message.error(err.message || '获取列表失败')
        })
      console.log('search')
    },
    // 新增数据
    add() {
      this.$router.push({
        name: 'InstructorDetails'
      })
    },
    // 变更状态
    changeStatus(data) {
      const me = this
      recordBeforeAlter(data, 'id')
      PostUpdateSelectionStatus({
        id: data.id,
        status: data.status ? '1' : '0'
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('更改成功')
        } else {
          me.$message.success('更改失败')
          data.status = !data.status
        }
      })
    },
    // 双击选中数据
    rowClick(data) {
      this.$router.push({
        name: 'InstructorDetails',
        query: {
          id: data && data.id
        }
      })
      console.log('rowClick', data)
    },
    // 查看数据
    see(id, type) {
      const url = `https://wap.58moto.com/${type}/${id}`
      console.log(url)
      this.$refs.ChooseIframe.init(url)
    }
  }
}
</script>
