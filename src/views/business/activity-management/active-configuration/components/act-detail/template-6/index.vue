<template>
  <div v-loading="loading" class="wd-m-4 act-detail">
    <el-form :model="ruleForm" label-width="120px" label-position="right">
      <el-form-item label="精选内容标题">
        <el-input
          v-model="ruleForm.primeName"
          placeholder="请输入精选内容标题"
          style="width: 400px"
        />
      </el-form-item>
      <!-- <el-form-item label="事件描述">
        <el-input
          v-model="ruleForm.eventDesc"
          type="textarea"
          :rows="4"
          style="width: 400px"
          placeholder="请输入事件描述"
        />
      </el-form-item> -->
      <div class="title">精选内容</div>
      <div class="border-content">
        <el-button type="primary" @click="addArticle">+添加文章</el-button>
        <div class="wd-text-center wd-m-4">
          <el-row :gutter="24" class="essay-list">
            <el-col :span="3" class="essay-list-head"> 序号 </el-col>
            <el-col :span="3" class="essay-list-head"> ID </el-col>
            <el-col :span="4" class="essay-list-head"> 作者信息 </el-col>
            <el-col :span="11" class="essay-list-head"> 标题/内容 </el-col>
            <el-col :span="3" class="essay-list-head"> 操作 </el-col>
          </el-row>
          <draggable1
            :list="ruleForm.essayList"
            @update="(item) => getDrapValue(item, 'essayList')"
            @deleteItem="(item) => deleteItem(item, 'essayList')"
            @setTopItem="(item, index) => setTopItem(item, index, 'essayList')"
          />
        </div>
      </div>
      <div style="text-align: center">
        <el-button type="primary" @click="submitForm">立即保存</el-button>
        <el-button @click="handleClose()">取消</el-button>
      </div>
    </el-form>
  </div>
  <ChooseArticle ref="chooseArticle" @chooseArticle="updateArticle" />
</template>

<script setup>
import draggable1 from '../template-1/components/templateDraggable.vue'
import ChooseArticle from '../../choose-article/index.vue'
import { ref, getCurrentInstance } from 'vue'
import {
  GetSelectsConfig,
  SaveTemplet,
  GetTempletDetail
} from '@/api/activeConfiguration'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['submitForm', 'handleClose'])
const proxy = getCurrentInstance().proxy
const loading = ref(false)
const ruleForm = ref({
  primeName: '',
  essayList: []
})

const itemData = ref({})
const choseType = ref(null)
const chooseArticle = ref(null)
const addArticle = () => {
  chooseArticle.value.dialogVisible = true
  chooseArticle.value.init()
  choseType.value = 'essayList'
}
const updateArticle = (list) => {
  ruleForm.value[choseType.value] = ruleForm.value[choseType.value] || []
  ruleForm.value[choseType.value].unshift(
    ...list.filter(
      (_) => !ruleForm.value[choseType.value].some((item) => item.id === _.id)
    )
  )
}
const deleteItem = (item, type) => {
  ruleForm.value[type] = ruleForm.value[type].filter((_) => _.id !== item.id)
}
// 排序置顶
const setTopItem = (item, index, type) => {
  const allList = ruleForm.value[type]
  allList.splice(index, 1)
  allList.unshift(item)
  ruleForm.value[type] = allList
}

const getDrapValue = (value, type) => {
  ruleForm.value[type] = value
}
const init = async (item) => {
  itemData.value = item || {}
  if (!item.activityId) return
  await GetTempletDetail({
    activityId: item.activityId,
    templetId: item.templetId
  })
    .then((response) => {
      if (response.data.code === 0) {
        const data = JSON.parse(response.data.data.templetJson)
        ruleForm.value = {
          ...data
        }
      } else {
        proxy.$message.error(response.data.msg)
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const handleClose = () => {
  ruleForm.value = {
    primeName: '',
    essayList: []
  }
  emit('close')
}
const submitForm = () => {
  loading.value = true
  const data = {
    ...ruleForm.value
  }
  const templetJson = JSON.stringify(data)
  SaveTemplet({
    activityId: itemData.value.activityId || '',
    templetId: itemData.value.templetId || '',
    templetJson
  })
    .then((response) => {
      if (response.data.code === 0) {
        proxy.$message.success('保存成功')
        proxy.$router.go(-1)
        emit('success')
      } else {
        proxy.$message.error(response.data.msg)
      }
    })
    .finally(() => {
      loading.value = false
    })
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.title {
  text-align: center;
  font-size: 16px;
  margin: 20px 0 10px 0;
  font-weight: 700;
}

.border-content {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 20px;
}
</style>
