<template>
  <draggable v-model="hotSearchList" item-key="id" class="draggable">
    <template #item="{ element: item, index }">
      <el-row :gutter="24" class="essay-list">
        <el-col :span="3" class="essay-list-head essay-list-head-more">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            t="1629783231238"
            class="essay-list-head-icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            p-id="1996"
            width="25"
            height="30"
          >
            <path
              d="M128 294.4a38.4 38.4 0 0 0 38.4 38.4h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 0 0-38.4 38.4z m38.4 268.8h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z m0 230.4h691.2a38.4 38.4 0 1 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z"
              p-id="1997"
            />
          </svg>
          {{ index + 1 }}
        </el-col>
        <el-col
          :span="5"
          class="essay-list-head"
          style="align-content: center; word-break: break-all"
        >
          {{ item.title }}
        </el-col>
        <el-col
          :span="5"
          class="essay-list-head"
          style="align-content: center; word-break: break-all"
        >
          {{ item.routeUrl }}
        </el-col>
        <el-col
          :span="4"
          class="essay-list-head"
          style="align-content: center; word-break: break-all"
        >
          {{
            (item.startTime &&
              $filter.format(
                new Date(item.startTime),
                'YYYY-MM-DD HH:mm:ss'
              )) ||
            ''
          }}
        </el-col>
        <el-col
          :span="4"
          class="essay-list-head"
          style="align-content: center; word-break: break-all"
        >
          {{
            (item.endTime &&
              $filter.format(new Date(item.endTime), 'YYYY-MM-DD HH:mm:ss')) ||
            ''
          }}
        </el-col>
        <el-col :span="3" class="essay-list-head essay-list-head-more">
          <el-button type="primary" link @click="alterItem(item, index)"
            >修改</el-button
          >
          <el-button type="primary" link @click="_deleteItem(item, index)"
            >删除</el-button
          >
        </el-col>
      </el-row>
    </template>
  </draggable>
</template>

<script>
import draggable from 'vuedraggable'
import { $emit } from '@/utils/gogocodeTransfer'
export default {
  name: 'TempalteDraggable',
  components: {
    draggable
  },
  props: {
    list: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    options() {
      return {
        draggable: '.item'
      }
    },
    hotSearchList: {
      get() {
        return this.list || []
      },
      set(value) {
        $emit(this, 'update', value)
      }
    }
  },
  methods: {
    alterItem(item, index) {
      $emit(this, 'alterItem', item, index)
    },
    _deleteItem(item, index) {
      $emit(this, 'deleteItem', item, index)
    }
  },
  emits: ['update', 'deleteItem']
}
</script>

<style lang="scss" scoped>
.draggable {
  .item {
    background-color: #eee;
    margin-bottom: 10px;
    box-sizing: border-box;
    padding: 0 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .img {
    height: 40px;
    background-repeat: no-repeat;
    background-size: contain;
  }
  .circle-box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .box {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin: 5px;
      .icon {
        width: 15px;
        height: 15px;
      }
    }
  }

  .content-foot {
    border: 1px solid #ebeef5;
    border-radius: 5px;
    margin: 5px;
    padding: 5px;
    &-box {
      display: flex;
      justify-content: space-between;
      padding: 2px;
      color: #909399;
    }
  }
  .essay-list {
    border: 1px solid #eee;
  }
  .essay-list-head {
    border-right: 1px solid #eee;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .essay-list-head-more {
    padding-top: 50px;
  }
  .essay-list-head-icon {
    position: relative;
    top: 0.5rem;
  }
}
</style>
