<template>
  <div v-loading="loading" class="wd-m-4 act-detail">
    <el-form :model="ruleForm" label-width="120px" label-position="right">
      <div class="title">实时热点</div>
      <div class="border-content">
        <el-button type="primary" @click="addHot">+新增热点</el-button>
        <div class="wd-text-center wd-m-4">
          <el-row :gutter="24" class="essay-list">
            <el-col :span="3" class="essay-list-head"> 序号 </el-col>
            <el-col :span="5" class="essay-list-head"> 热点标题 </el-col>
            <el-col :span="5" class="essay-list-head"> 跳转链接 </el-col>
            <el-col :span="4" class="essay-list-head"> 开始时间 </el-col>
            <el-col :span="4" class="essay-list-head"> 结束时间 </el-col>
            <el-col :span="3" class="essay-list-head"> 操作 </el-col>
          </el-row>
          <draggable1
            :list="ruleForm.hotSearchList"
            @update="(item) => getDrapValue(item)"
            @deleteItem="(item, index) => deleteItem(item, index)"
            @alterItem="(item, index) => alterItem(item, index)"
          />
        </div>
      </div>
      <div style="text-align: center">
        <el-button type="primary" @click="submitForm">立即保存</el-button>
        <el-button @click="handleClose()">取消</el-button>
      </div>
    </el-form>
  </div>
  <AddHot ref="addHotRef" @chooseContent="updateContent" />
</template>

<script setup>
import { date } from '@haluo/util'
import draggable1 from '../template-7/components/templateDraggable.vue'
import AddHot from '../../add-hot/index.vue'
import { ref, getCurrentInstance } from 'vue'
import { SaveTemplet, GetTempletDetail } from '@/api/activeConfiguration'

const emit = defineEmits(['submitForm'])
const proxy = getCurrentInstance().proxy
const loading = ref(false)
const ruleForm = ref({
  hotSearchList: []
})

const itemData = ref({})
const addHotRef = ref(null)
const addHot = (params) => {
  addHotRef.value.dialogVisible = true
  addHotRef.value.init(params)
}
const updateContent = (content = {}) => {
  content.startTime =
    (content.startTime &&
      date.format(new Date(content.startTime), 'YYYY-MM-DD HH:mm:ss')) ||
    undefined
  content.endTime =
    (content.endTime &&
      date.format(new Date(content.endTime), 'YYYY-MM-DD HH:mm:ss')) ||
    undefined

  if (content.index > -1) {
    ruleForm.value.hotSearchList[content.index] = {
      title: content.title,
      startTime: content.startTime,
      endTime: content.endTime,
      routeUrl: content.routeUrl
    }
  } else {
    ruleForm.value.hotSearchList.unshift({
      title: content.title,
      startTime: content.startTime,
      endTime: content.endTime,
      routeUrl: content.routeUrl
    })
  }
  // console.log('updateContent', content, ruleForm.value.hotSearchList)
}
const deleteItem = (item, index) => {
  ruleForm.value.hotSearchList.splice(index, 1)
}
const alterItem = (item, index) => {
  const params = {
    index,
    title: item.title,
    startTime: item.startTime,
    endTime: item.endTime,
    routeUrl: item.routeUrl
  }
  addHotRef.value.dialogVisible = true
  addHotRef.value.init(params)
  // console.log('alterItem', params)
}

const getDrapValue = (value) => {
  ruleForm.value.hotSearchList = value
}
const init = async (item) => {
  itemData.value = item || {}
  if (!item.activityId) return
  await GetTempletDetail({
    activityId: item.activityId,
    templetId: item.templetId
  })
    .then((response) => {
      if (response.data.code === 0) {
        const data = JSON.parse(response.data.data.templetJson || '{}')
        ruleForm.value.hotSearchList = data.hotSearchList || []
        console.log('ruleForm', ruleForm.value)
      } else {
        proxy.$message.error(response.data.msg)
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const handleClose = () => {
  // ruleForm.value = {
  //   hotSearchList: []
  // }
  proxy.$router.go(-1)
}
const submitForm = () => {
  loading.value = true
  const data = {
    ...ruleForm.value
  }
  // 兼容后端排序，没啥用
  data.hotSearchList = data.hotSearchList.map((item, index) => {
    return {
      ...item,
      sort: index
    }
  })
  const templetJson = JSON.stringify(data)
  SaveTemplet({
    activityId: itemData.value.activityId || '',
    templetId: itemData.value.templetId || '',
    templetJson
  })
    .then((response) => {
      if (response.data.code === 0) {
        proxy.$message.success('保存成功')
        proxy.$router.go(-1)
        emit('success')
      } else {
        proxy.$message.error(response.data.msg)
      }
    })
    .finally(() => {
      loading.value = false
    })
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.title {
  text-align: center;
  font-size: 16px;
  margin: 20px 0 10px 0;
  font-weight: 700;
}

.border-content {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 20px;
}
</style>
