<template>
  <div v-loading="loading" class="wd-m-4 act-detail">
    <el-form
      v-if="isInitForm"
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="100px"
    >
      <div class="title">活动基础信息</div>
      <div class="modules">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="背景颜色">
              <el-input
                v-model="ruleForm.pageInfo.bgColor"
                style="width: 250px"
                placeholder="输入色值如：#ccc,rgb(100,100,100),red"
              >
                <template #append>
                  <div
                    :style="{ background: ruleForm.pageInfo.bgColor }"
                    class="colorBox"
                  ></div>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="页面名称" prop="pageInfo.pageName">
              <el-input
                v-model="ruleForm.pageInfo.pageName"
                style="width: 250px"
              />
            </el-form-item>
            <el-form-item label="活动封面" prop="userFace">
              <el-input
                v-model="ruleForm.pageInfo.actCover"
                style="width: 250px"
              />
              <el-upload
                :show-file-list="false"
                :http-request="httpRequest"
                :on-success="onSuccessActCover"
                name="upfile"
                style="display: inline-block"
                class="avatar-uploader"
                action
              >
                <el-button type="primary" link>选择图片</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item label="活动封面跳转" prop="location">
              <el-input
                v-model="ruleForm.pageInfo.redirectUrl"
                style="width: 250px"
              />
              <span>*未设置不跳转</span>
            </el-form-item>
            <el-form-item label="专题类型" prop="tagId">
              <el-select
                v-model="ruleForm.tagId"
                placeholder="请选择专题类型"
                @change="changeTagid"
              >
                <el-option
                  v-for="(tagVal, tagName) in tagItems"
                  :key="tagVal"
                  :label="tagName"
                  :value="tagVal"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <div v-if="ruleForm.tagId == 5">
              <el-form-item label="经销商:" prop="shopQueryPrice.shopId">
                <el-autocomplete
                  v-model="DealerState"
                  :fetch-suggestions="querySearchAllDealer"
                  placeholder="请选择经销商"
                  style="width: 200px"
                  clearable
                  @select="handleSelectdealer"
                ></el-autocomplete>
              </el-form-item>
              <el-form-item label="车型:" prop="shopQueryPrice.goodsId">
                <el-autocomplete
                  v-model="cardTypestate"
                  :fetch-suggestions="querySearchAllCar"
                  placeholder="请选择车型"
                  style="width: 200px"
                  clearable
                  @select="handleSelectCar"
                  @input="clearCardTypes"
                ></el-autocomplete>
              </el-form-item>

              <el-form-item label="款型:" prop="shopQueryPrice.carIdsArr">
                <el-select
                  v-model="ruleForm.shopQueryPrice.carIdsArr"
                  multiple
                  placeholder="请选择款型"
                  @change="changeCarstyle"
                >
                  <el-option
                    v-for="item in cardStyleList"
                    :key="item.carId"
                    :label="item.goodsCarName"
                    :value="item.carId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col
            :span="8"
            :style="{ backgroundImage: `url(${ruleForm.pageInfo.actCover})` }"
            class="topBg"
          />
        </el-row>
      </div>
      <div class="title">活动说明</div>
      <div class="modules">
        <el-form-item label="标题背景图">
          <el-input v-model="ruleForm.actDesc.bgImg" style="width: 470px" />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccessActDescBgImg"
            name="upfile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            <el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="背景颜色">
              <el-input
                v-model="ruleForm.actDesc.bgColor"
                placeholder="输入色值如：#ccc,rgb(100,100,100),red"
              >
                <template #append>
                  <div
                    :style="{ background: ruleForm.actDesc.bgColor }"
                    class="colorBox"
                  ></div>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字体背景颜色">
              <el-input
                v-model="ruleForm.actDesc.fontColor"
                placeholder="输入色值如：#ccc,rgb(100,100,100),red"
              >
                <template #append>
                  <div
                    :style="{ background: ruleForm.actDesc.fontColor }"
                    class="colorBox"
                  ></div>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="默认显示" prop="delivery">
          <div>
            <quill-editor
              ref="showQuillEditor"
              contentType="html"
              v-model:content="ruleForm.actDesc.showDesc"
              :options="editorOption"
            />
            <input
              ref="showfileBtn"
              type="file"
              hidden
              accept=".jpg, .png"
              @change="handleChange($event, 'show')"
            />
          </div>
        </el-form-item>
        <el-form-item label="默认隐藏" prop="delivery1">
          <div>
            <quill-editor
              ref="hideQuillEditor"
              contentType="html"
              v-model:content="ruleForm.actDesc.hideDesc"
              :options="editorOption"
            />
            <input
              ref="hidefileBtn"
              type="file"
              hidden
              accept=".jpg, .png"
              @change="handleChange($event, 'hide')"
            />
          </div>
        </el-form-item>
      </div>
      <div class="title">内容模块</div>
      <div class="modules">
        <div class="essay-config-box">
          <div class="essay-config-item">
            <div class="wd-w-250px">
              <el-form-item label="默认显示" label-width="125px">
                <el-input
                  v-model="ruleForm.essayMoudel.defaultSize"
                  style="width: 50px"
                />条
              </el-form-item>
            </div>
            <el-form-item label="标题背景图" label-width="125px">
              <el-input
                v-model="ruleForm.essayMoudel.bgImg"
                style="width: 360px"
              />
              <el-upload
                :show-file-list="false"
                :http-request="httpRequest"
                :on-success="onSuccessBgImg"
                name="upfile"
                style="display: inline-block"
                class="avatar-uploader"
                action
              >
                <el-button type="primary" link>选择图片</el-button>
              </el-upload>
            </el-form-item>
          </div>
          <el-button @click="chooseArticle">添加文章</el-button>
        </div>
        <div class="essay-config-style">
          <div class="wd-w-250px">
            <el-form-item label="模块背景颜色" label-width="125px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="ruleForm.essayMoudel.bgColor"
              />
            </el-form-item>
          </div>
          <div class="wd-w-250px">
            <el-form-item label="分隔线颜色配置" label-width="125px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="ruleForm.essayMoudel.lineColor"
              />
            </el-form-item>
          </div>
        </div>
        <div class="essay-config-style">
          <div class="wd-w-250px">
            <el-form-item label="标题颜色配置" label-width="125px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="ruleForm.essayMoudel.titleColor"
              />
            </el-form-item>
          </div>
          <div class="wd-w-250px">
            <el-form-item label="详情颜色配置" label-width="125px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="ruleForm.essayMoudel.detailColor"
              />
            </el-form-item>
          </div>
        </div>
        <div class="essay-config-style">
          <div class="wd-w-400px">
            <el-form-item label="按钮文案" label-width="125px">
              <el-input
                v-model="ruleForm.essayMoudel.buttonText"
                clearable
                placeholder="不设置，默认文案为查看更多"
              />
            </el-form-item>
          </div>
        </div>
        <div class="essay-config-style">
          <div class="wd-w-250px">
            <el-form-item label="按钮文案颜色配置" label-width="125px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="ruleForm.essayMoudel.buttonTextColor"
              />
            </el-form-item>
          </div>
          <div class="wd-w-250px">
            <el-form-item label="按钮颜色配置" label-width="125px">
              <el-color-picker
                :predefine="predefineColors"
                v-model="ruleForm.essayMoudel.buttonBgColor"
              />
            </el-form-item>
          </div>
        </div>
        <el-form-item label="列表顺序" label-width="125px">
          <div class="radio-group-style">
            <el-radio-group v-model="ruleForm.essayMoudel.listOrder">
              <el-radio label="1" size="large">同专题顺序</el-radio>
              <el-radio label="2" size="large">随机打乱</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
        <div class="wd-text-center wd-m-4">
          <el-row :gutter="24" class="essay-list">
            <el-col :span="3" class="essay-list-head"> 序号 </el-col>
            <el-col :span="3" class="essay-list-head"> ID </el-col>
            <el-col :span="4" class="essay-list-head"> 作者信息 </el-col>
            <el-col :span="11" class="essay-list-head"> 标题/内容 </el-col>
            <el-col :span="3" class="essay-list-head"> 操作 </el-col>
          </el-row>
          <draggable1
            :list="ruleForm.essayMoudel.essayList"
            @update="getDrapValue"
            @deleteItem="deleteItem"
            @setTopItem="setTopItem"
          />
        </div>
      </div>
      <div class="title">活动报名</div>
      <div class="modules">
        <el-row :gutter="20">
          <el-col :span="2">
            <el-form-item label="未报名" />
            <el-form-item label="已报名" />
            <el-form-item label="报名" />
          </el-col>
          <el-col :span="11">
            <el-form-item label="按钮背景色" label-width="150px">
              <el-input
                v-model="ruleForm.pageInfo.ableBtnBgColor"
                placeholder="输入色值如：#ccc,rgb(100,100,100),red"
              >
                <template #append>
                  <div
                    :style="{ background: ruleForm.pageInfo.ableBtnBgColor }"
                    class="colorBox"
                  ></div>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="按钮背景色" label-width="150px">
              <el-input
                v-model="ruleForm.pageInfo.unableBtnBgColor"
                placeholder="输入色值如：#ccc,rgb(100,100,100),red"
              >
                <template #append>
                  <div
                    :style="{
                      background: ruleForm.pageInfo.unableBtnBgColor
                    }"
                    class="colorBox"
                  ></div>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="按钮背景色" label-width="150px">
              <el-input
                v-model="ruleForm.pageInfo.applybtnBgColor"
                placeholder="输入色值如：#ccc,rgb(100,100,100),red"
              >
                <template #append>
                  <div
                    :style="{ background: ruleForm.pageInfo.applybtnBgColor }"
                    class="colorBox"
                  ></div>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="按钮字体颜色" label-width="150px">
              <el-input
                v-model="ruleForm.pageInfo.ablebtnColor"
                placeholder="输入色值如：#ccc,rgb(100,100,100),red"
              >
                <template #append>
                  <div
                    :style="{ background: ruleForm.pageInfo.ablebtnColor }"
                    class="colorBox"
                  ></div>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="按钮字体颜色" label-width="150px">
              <el-input
                v-model="ruleForm.pageInfo.unablebtnColor"
                placeholder="输入色值如：#ccc,rgb(100,100,100),red"
              >
                <template #append>
                  <div
                    :style="{ background: ruleForm.pageInfo.unablebtnColor }"
                    class="colorBox"
                  ></div>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="按钮字体颜色" label-width="150px">
              <el-input
                v-model="ruleForm.pageInfo.applybtnColor"
                placeholder="输入色值如：#ccc,rgb(100,100,100),red"
              >
                <template #append>
                  <div
                    :style="{ background: ruleForm.pageInfo.applybtnColor }"
                    class="colorBox"
                  ></div>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!--按钮颜色配置-->
        <el-form-item label="'打开摩托范'按钮背景色" label-width="230px">
          <el-input
            v-model="ruleForm.pageInfo.downBtnBgColor"
            placeholder="输入色值如：#ccc,rgb(100,100,100),red"
          >
            <template #append>
              <div
                :style="{ background: ruleForm.pageInfo.downBtnBgColor }"
                class="colorBox"
              ></div>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="'打开摩托范'按钮字体颜色" label-width="230px">
          <el-input
            v-model="ruleForm.pageInfo.btnColor"
            placeholder="输入色值如：#ccc,rgb(100,100,100),red"
          >
            <template #append>
              <div
                :style="{ background: ruleForm.pageInfo.btnColor }"
                class="colorBox"
              ></div>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="'发布动态'按钮背景色" label-width="230px">
          <el-input
            v-model="ruleForm.pageInfo.releaseBtnBgColor"
            placeholder="输入色值如：#ccc,rgb(100,100,100),red"
          >
            <template #append>
              <div
                :style="{ background: ruleForm.pageInfo.releaseBtnBgColor }"
                class="colorBox"
              ></div>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="'发布动态'按钮字体颜色" label-width="230px">
          <el-input
            v-model="ruleForm.pageInfo.rebtnColor"
            placeholder="输入色值如：#ccc,rgb(100,100,100),red"
          >
            <template #append>
              <div
                :style="{ background: ruleForm.pageInfo.rebtnColor }"
                class="colorBox"
              ></div>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="报名方式">
          <el-checkbox v-model="ruleForm.conf.personEnter"
            >个人报名</el-checkbox
          >
          <el-checkbox v-model="ruleForm.conf.clubEnter"
            >俱乐部报名</el-checkbox
          >
        </el-form-item>
        <!--个人报名-->
        <div v-show="ruleForm.conf.personEnter" class="modules">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="个人报名">
                <el-input v-model="ruleForm.enterInfo.personEnter.name" />
              </el-form-item>
              <el-form-item label="输入框背景色">
                <el-input
                  v-model="ruleForm.enterInfo.personEnter.bgColor"
                  placeholder="输入色值如：#ccc,rgb(100,100,100),red"
                >
                  <template #append>
                    <div
                      :style="{
                        background: ruleForm.enterInfo.personEnter.bgColor
                      }"
                      class="colorBox"
                    ></div>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标题字体颜色" label-width="120px">
                <el-input
                  v-model="ruleForm.enterInfo.personEnter.fontColor"
                  placeholder="输入色值如：#ccc,rgb(100,100,100),red"
                >
                  <template #append>
                    <div
                      :style="{
                        background: ruleForm.enterInfo.personEnter.fontColor
                      }"
                      class="colorBox"
                    ></div>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item label="输入框字体颜色" label-width="120px">
                <el-input
                  v-model="ruleForm.enterInfo.personEnter.inputColor"
                  placeholder="输入色值如：#ccc,rgb(100,100,100),red"
                >
                  <template #append>
                    <div
                      :style="{
                        background: ruleForm.enterInfo.personEnter.inputColor
                      }"
                      class="colorBox"
                    ></div>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="备注">
            <quill-editor
              contentType="html"
              ref="personQuillEditor"
              v-model:content="ruleForm.enterInfo.personEnter.remark"
              :options="editorOption"
            />
            <input
              ref="personfileBtn"
              type="file"
              hidden
              accept=".jpg, .png"
              @change="handleChange($event, 'person')"
            />
          </el-form-item>
          <el-form-item prop="conf.personEnterSelects">
            <el-checkbox-group v-model="ruleForm.conf.personEnterSelects">
              <el-checkbox
                v-for="item in conf.personEnterSelects"
                :label="item.desc"
                :key="item.name"
              />
            </el-checkbox-group>
          </el-form-item>
        </div>
        <!--俱乐部报名-->
        <div v-show="ruleForm.conf.clubEnter" class="modules">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="俱乐部报名">
                <el-input v-model="ruleForm.enterInfo.clubEnter.name" />
              </el-form-item>
              <el-form-item label="输入框背景色">
                <el-input
                  v-model="ruleForm.enterInfo.clubEnter.bgColor"
                  placeholder="输入色值如：#ccc,rgb(100,100,100),red"
                >
                  <template #append>
                    <div
                      :style="{
                        background: ruleForm.enterInfo.clubEnter.bgColor
                      }"
                      class="colorBox"
                    ></div>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标题字体颜色" label-width="120px">
                <el-input
                  v-model="ruleForm.enterInfo.clubEnter.fontColor"
                  placeholder="输入色值如：#ccc,rgb(100,100,100),red"
                >
                  <template #append>
                    <div
                      :style="{
                        background: ruleForm.enterInfo.clubEnter.fontColor
                      }"
                      class="colorBox"
                    ></div>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item label="输入框字体颜色" label-width="120px">
                <el-input
                  v-model="ruleForm.enterInfo.clubEnter.inputColor"
                  placeholder="输入色值如：#ccc,rgb(100,100,100),red"
                >
                  <template #append>
                    <div
                      :style="{
                        background: ruleForm.enterInfo.clubEnter.inputColor
                      }"
                      class="colorBox"
                    ></div>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="备注">
            <quill-editor
              contentType="html"
              ref="clubQuillEditor"
              v-model:content="ruleForm.enterInfo.clubEnter.remark"
              :options="editorOption"
            />
            <input
              ref="clubfileBtn"
              type="file"
              hidden
              accept=".jpg, .png"
              @change="handleChange($event, 'club')"
            />
          </el-form-item>
          <el-form-item prop="conf.clubEnterSelects">
            <el-checkbox-group v-model="ruleForm.conf.clubEnterSelects">
              <el-checkbox
                v-for="item in conf.clubEnterSelects"
                :label="item.desc"
                :key="item.name"
              />
            </el-checkbox-group>
          </el-form-item>
        </div>
      </div>
      <div class="title">banner配置</div>
      <div class="modules">
        <!--<p class="title"><el-button @click="ruleForm.imgInfo.push({imgCover:'',url:''})">添加banner图片</el-button></p>-->
        <template v-for="(item, index) in ruleForm.imgInfo" :key="index + 'a'">
          <el-form-item :label="`图片封面${index + 1}`" prop="location">
            <el-input v-model="item.imgCover" style="width: 460px" />
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest"
              :on-success="(value) => onSuccessImgInfo(value, index)"
              name="upfile"
              style="display: inline-block"
              class="avatar-uploader"
              action
            >
              <el-button type="primary" link>选择图片</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item :label="`跳转${index + 1}`" prop="location">
            <el-input v-model="item.url" style="width: 460px" />
          </el-form-item>
        </template>
        <el-button type="primary" @click="addImgInfo()"
          >添加banner图片</el-button
        >
      </div>
      <div class="title">短话题</div>
      <div class="modules">
        <el-form-item label="标题背景图">
          <el-input
            v-model="ruleForm.shortTopicMoudel.bgImg"
            style="width: 460px"
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccessTopicBgImg"
            name="upfile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            <el-button type="primary" link>选择图片</el-button>
          </el-upload>
        </el-form-item>

        <el-form-item label="关联短话题">
          <el-tag
            v-if="ruleForm.shortTopicMoudel.shortTopicInfo.title"
            type="success"
            >{{ ruleForm.shortTopicMoudel.shortTopicInfo.title }}</el-tag
          >
          <el-button type="primary" link @click="chooseTopic"
            >{{
              ruleForm.shortTopicMoudel.shortTopicInfo.title ? '更换' : '选择'
            }}短话题</el-button
          >
          <el-button
            v-if="ruleForm.shortTopicMoudel.shortTopicInfo.title"
            type="primary"
            link
            @click="ruleForm.shortTopicMoudel.shortTopicInfo = {}"
            >取消关联</el-button
          >
        </el-form-item>
      </div>
      <div class="modules">
        <el-form-item
          label="车辆"
          class="set-padding"
          style="max-height: 130px; overflow-y: scroll"
        >
          <labelContent
            pageName="activeConfigurationDetail"
            ref="car"
            :type="'car'"
            @deleteLabel="deleteLabel"
            @labelAllData="updateLabelAllData"
          />
          <searchLabel
            :type="'car'"
            class="search-label-content"
            @addLabel="addLabel"
          />
        </el-form-item>
      </div>
      <!-- <div class="title">自定义JSON</div>
        <div class="modules">
          <el-form-item label="自定义内容">
            <el-input v-model="ruleForm.custom" :rows="2" type="textarea" />
          </el-form-item>
        </div>-->

      <el-form-item align="center">
        <el-button type="primary" @click="submitForm">立即保存</el-button>
        <el-button @click="handleClose()">取消</el-button>
      </el-form-item>
    </el-form>

    <choose-article ref="chooseArticle" @chooseArticle="updateArticle" />
    <short-topic ref="ShortTopic" @chooseTopic="updateTopic" />
  </div>
</template>

<script>
// import { resetData } from '@/utils'
import searchLabel from '@/components/label/searchLabel.vue'
import labelContent from '@/components/label/labelContent.vue'
import draggable1 from './components/templateDraggable.vue'
import {
  GetSelectsConfig,
  SaveTemplet,
  GetTempletDetail
} from '@/api/activeConfiguration'
import {
  searchArticleList // 文章搜索
} from '@/api/articleModule'
// 获取经销商信息
import { searchCarList, getAgeList, GetShopApplyByName } from '@/api/garage'
import ChooseArticle from '../../choose-article/index.vue'
import ShortTopic from '../../short-topic/index.vue'
import { batchRecordBeforeAlter } from '@/utils/enum/logData'
import { isArray, isObject } from 'lodash-es'

import { mapGetters } from 'vuex'
export default {
  name: 'ActDetail',
  components: {
    draggable1,
    ChooseArticle,
    ShortTopic,
    searchLabel,
    labelContent
  },
  data() {
    var personEnter = (rule, value, callback) => {
      if (value.length === 0 && this.ruleForm.conf.personEnter) {
        return callback(new Error('选择报名必填项'))
      }
      callback()
    }
    var clubEnter = (rule, value, callback) => {
      if (value.length === 0 && this.ruleForm.conf.clubEnter) {
        return callback(new Error('选择报名必填项'))
      }
      callback()
    }
    return {
      loading: false,
      isInitForm: false,
      item: {},
      editorOption: {},
      conf: {
        // 表单权限
        personEnterSelects: [],
        clubEnterSelects: []
      },
      dialogVisible: false,
      initJson: '',
      carLabels: [], // 车辆标签
      tagItems: { 热点: 1, 知识: 2, 合集: 3, 活动: 4, 经销商询价广告: 5 },
      ruleForm: null,
      rules: {
        'pageInfo.pageName': [
          {
            required: true,
            message: '请填写活动名称',
            trigger: 'blur'
          }
        ],
        'conf.personEnterSelects': [
          {
            validator: personEnter,
            trigger: 'change'
          }
        ],
        'conf.clubEnterSelects': [
          {
            validator: clubEnter,
            trigger: 'change'
          }
        ],
        tagId: [
          {
            required: true,
            message: '请选择专题类型',
            trigger: 'change'
          }
        ]
      },
      DealerState: '', // 经销商shopID
      cardTypestate: '', // 车型goodsId
      cardStyleList: [], // 款型列表
      predefineColors: [
        // 颜色拾取器的自定义颜色
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577',
        '#000000',
        '#ffffff',
        '#D9001B'
      ]
    }
  },
  computed: {
    ...mapGetters(['uid'])
  },
  methods: {
    // 经销商筛选
    querySearchAllDealer(queryString, cb) {
      const requestParams = {
        page: 1,
        limit: 100,
        shopName: queryString
      }
      GetShopApplyByName(requestParams)
        .then((response) => {
          if (response.status === 200) {
            const userNameList = []
            const result = response.data && response.data.data
            result.map(function (value) {
              const newObj = {
                value: value.shopName,
                shopId: value.shopId
              }
              userNameList.push(newObj)
            })
            cb(userNameList)
          }
        })
        .catch(() => {})
    },
    handleSelectdealer(item) {
      this.ruleForm.shopQueryPrice.shopId = item.shopId
      this.ruleForm.shopQueryPrice.shopName = item.value
    },

    // 车型筛选
    querySearchAllCar(queryString, cb) {
      const requestParams = {
        page: 1,
        limit: 100,
        name: queryString,
        isOnStatus: 1
      }
      searchCarList(requestParams)
        .then((response) => {
          if (response.status === 200) {
            const userNameList = []
            const result = response.data.data.list
            result.map(function (value) {
              const newObj = {
                value: value.goodName,
                goodsId: value.goodId
              }
              userNameList.push(newObj)
            })
            cb(userNameList)
          }
        })
        .catch(() => {})
    },
    handleSelectCar(item) {
      this.ruleForm.shopQueryPrice.goodsId = item.goodsId
      this.ruleForm.shopQueryPrice.goodName = item.value
      this.querySearchAllcardStyle(item.value)
    },
    clearCardTypes() {
      this.cardStyleList = []
      this.ruleForm.shopQueryPrice.carIds = ''
      this.ruleForm.shopQueryPrice.carIdsArr = []
    },

    // 款型筛选
    querySearchAllcardStyle(queryString) {
      const me = this
      const requestParams = {
        page: 1,
        limit: 100,
        goodsName: queryString || ''
      }
      getAgeList(requestParams)
        .then((response) => {
          if (response.status === 200) {
            me.cardStyleList = response.data.data.list
          }
        })
        .catch(() => {})
    },

    // // 款型选择
    changeCarstyle(e) {
      this.ruleForm.shopQueryPrice.carIdsArr = e
      this.ruleForm.shopQueryPrice.carIds = e.toString()
    },

    // 选择专题
    changeTagid(item) {
      var personEnter = (rule, value, callback) => {
        if (value.length === 0 && this.ruleForm.conf.personEnter) {
          return callback(new Error('选择报名必填项'))
        }
        callback()
      }
      var clubEnter = (rule, value, callback) => {
        if (value.length === 0 && this.ruleForm.conf.clubEnter) {
          return callback(new Error('选择报名必填项'))
        }
        callback()
      }
      if (item === 5) {
        this.rules = {
          'pageInfo.pageName': [
            {
              required: true,
              message: '请填写活动名称',
              trigger: 'blur'
            }
          ],
          'conf.personEnterSelects': [
            {
              validator: personEnter,
              trigger: 'change'
            }
          ],
          'conf.clubEnterSelects': [
            {
              validator: clubEnter,
              trigger: 'change'
            }
          ],
          tagId: [
            {
              required: true,
              message: '请选择专题类型',
              trigger: 'change'
            }
          ],
          'shopQueryPrice.shopId': [
            {
              required: true,
              message: '请选择经销商',
              trigger: 'change'
            }
          ],
          'shopQueryPrice.goodsId': [
            {
              required: true,
              message: '请选择经车型',
              trigger: 'change'
            }
          ],
          'shopQueryPrice.carIdsArr': [
            {
              required: true,
              message: '请选择款型',
              trigger: 'change'
            }
          ]
        }
        if (this.cardTypestate) {
          this.querySearchAllcardStyle(this.cardTypestate)
        }
      } else {
        this.rules = {
          'pageInfo.pageName': [
            {
              required: true,
              message: '请填写活动名称',
              trigger: 'blur'
            }
          ],
          'conf.personEnterSelects': [
            {
              validator: personEnter,
              trigger: 'change'
            }
          ],
          'conf.clubEnterSelects': [
            {
              validator: clubEnter,
              trigger: 'change'
            }
          ],
          tagId: [
            {
              required: true,
              message: '请选择专题类型',
              trigger: 'change'
            }
          ]
        }
        this.DealerState = ''
        this.cardTypestate = ''
        this.cardStyleList = []
        this.ruleForm.shopQueryPrice.shopId = ''
        this.ruleForm.shopQueryPrice.goodsId = ''
        this.ruleForm.shopQueryPrice.carIds = ''
        this.ruleForm.shopQueryPrice.carIdsArr = ''
      }
    },

    // 更新关联标签状态
    deleteLabel(item, type) {
      const me = this
      setTimeout(() => {
        me.$refs[type].getAllData()
      }, 800)
    },
    // 增加标签
    addLabel(data, type) {
      const me = this
      me.isModifyStatus = true
      console.log(data, type, 999)
      this.$refs[type].addLable(data)
      setTimeout(() => {
        me.$refs[type].getAllData()
      }, 800)
    },
    // 修改快捷标签
    updateLabelAllData(data, type) {
      switch (type) {
        case 'car':
          this.carLabels = []
          this.carLabels = data
          break
      }
    },
    // 自定义文件选择框改变事件e：事件，type: 类型
    handleChange(e, type) {
      const me = this
      const files = Array.prototype.slice.call(e.target.files)
      if (!files) {
        return
      }
      const option = {
        file: files[0],
        imageType: 'nowater',
        onError: function () {},
        onSuccess: function () {},
        onProgress: function () {}
      }
      me.$oss.ossUploadImage(option).then((res) => {
        const selection = me.$refs[`${type}QuillEditor`]
          .getQuill()
          .getSelection()
        // 这里就是返回的图片地址，如果接口返回的不是可以访问的地址，要自己拼接
        const imgUrl = res.imgOrgUrl
        // 获取quill的光标，插入图片
        me.$refs[`${type}QuillEditor`]
          .getQuill()
          .insertEmbed(selection != null ? selection.index : 0, 'image', imgUrl)
        // 插入完成后，光标往后移动一位
        me.$refs[`${type}QuillEditor`]
          .getQuill()
          .setSelection(selection.index + 1)
      })
    },
    // 个人报名富文本编辑器图片上传事件
    personImgHandler(state) {
      if (state) {
        // 触发input的单击 ，fileBtn换成自己的
        this.$refs.personfileBtn.click()
      }
    },
    // 俱乐部报名富文本编辑器图片上传事件
    clubImgHandler(state) {
      if (state) {
        this.$refs.clubfileBtn.click()
      }
    },
    // 默认显示富文本编辑器图片上传事件
    showImgHandler(state) {
      if (state) {
        this.$refs.showfileBtn.click()
      }
    },
    // 默认隐藏富文本编辑器图片上传事件
    hideImgHandler(state) {
      if (state) {
        this.$refs.hidefileBtn.click()
      }
    },
    chooseTopic() {
      this.$refs['ShortTopic'].dialogVisible = true
      this.$refs['ShortTopic'].init()
    },
    deleteItem(item) {
      this.ruleForm.essayMoudel.essayList =
        this.ruleForm.essayMoudel.essayList.filter((_) => _.id !== item.id)
    },
    // 排序置顶
    setTopItem(item, index) {
      const allList = this.ruleForm.essayMoudel.essayList
      allList.splice(index, 1)
      allList.unshift(item)
      this.ruleForm.essayMoudel.essayList = allList
    },
    async init(item) {
      this.isInitForm = false
      this.DealerState = ''
      this.cardTypestate = ''
      ;(this.cardStyleList = []), (this.carIdsArr = [])
      this.ruleForm = {
        conf: {
          personEnter: false,
          clubEnter: false,
          personEnterSelects: [],
          clubEnterSelects: []
        },
        pageInfo: {
          bgColor: '',
          pageName: '',
          actCover: '',
          redirectUrl: '',
          ableBtnBgColor: '',
          ablebtnColor: '',
          unableBtnBgColor: '',
          unablebtnColor: '',
          applybtnBgColor: '',
          applybtnColor: '',
          downBtnBgColor: '',
          btnColor: '',
          releaseBtnBgColor: '',
          rebtnColor: ''
        },
        shopQueryPrice: {
          shopId: '',
          shopName: '',
          goodsId: '',
          goodName: '',
          carIds: '',
          carIdsArr: [],
          carName: ''
        },
        actDesc: {
          showDesc: '',
          hideDesc: '',
          bgImg: '',
          bgColor: '',
          fontColor: '',
          moudelName: ''
        },
        essayMoudel: {
          moudelName: '',
          fontColor: '',
          bgImg: '',
          defaultSize: '',
          essayList: [],
          bgColor: '',
          lineColor: '',
          titleColor: '',
          detailColor: '',
          buttonText: '',
          buttonTextColor: '',
          buttonBgColor: '',
          listOrder: '1'
        },
        enterInfo: {
          personEnter: {
            name: '',
            fontColor: '',
            inputBgColor: '',
            inputColor: '',
            username: '',
            sex: '',
            idCard: '',
            address: '',
            remark: ''
          },
          clubEnter: {
            name: '',
            fontColor: '',
            inputBgColor: '',
            inputColor: '',
            username: '',
            sex: '',
            idCard: '',
            address: '',
            remark: ''
          }
        },
        imgInfo: [
          {
            imgCover: '',
            url: ''
          },
          {
            imgCover: '',
            url: ''
          }
        ],
        shortTopicMoudel: {
          moudelName: '',
          fontColor: '',
          bgImg: '',
          shortTopicInfo: {}
        },
        tagId: ''
        // custom: '', // 配置自定义的，JSON格式的字符串
      }
      const me = this
      me.item = item
      function setLabel(label = []) {
        label.map(function (value) {
          value.labelName = value.content
          value.selected = true
        })
        return label
      }
      await me.getSelectsConfig()
      if (item.activityId) {
        me.loading = true
        await GetTempletDetail({
          activityId: item.activityId,
          templetId: item.templetId
        })
          .then((response) => {
            if (response.data.code === 0) {
              me.isInitForm = true
              const templetJson = JSON.parse(response.data.data.templetJson)
              this.DealerState =
                (templetJson.shopQueryPrice &&
                  templetJson.shopQueryPrice.shopName) ||
                ''
              this.cardTypestate =
                (templetJson.shopQueryPrice &&
                  templetJson.shopQueryPrice.goodsName) ||
                ''
              this.ruleForm.shopQueryPrice.carIds =
                (templetJson.shopQueryPrice &&
                  templetJson.shopQueryPrice.carIds) ||
                ''
              this.ruleForm.shopQueryPrice.carIdsArr =
                (templetJson.shopQueryPrice &&
                  templetJson.shopQueryPrice.carIdsArr) ||
                []
              this.ruleForm.shopQueryPrice.carName =
                (templetJson.shopQueryPrice &&
                  templetJson.shopQueryPrice.carName) ||
                ''

              me.changeTagid(templetJson.tagId)

              batchRecordBeforeAlter(templetJson, item.activityId)
              // 初始化 ruleForm
              const ruleFormKeys = Object.keys(me.ruleForm)
              Object.keys(templetJson).map((key) => {
                if (ruleFormKeys.includes(key) && templetJson[key]) {
                  // 数组里面值的初始化
                  if (isArray(me.ruleForm[key])) {
                    me.ruleForm[key].map((item, index) => {
                      // 目前只支持数组一层嵌套
                      if (isObject(item) && templetJson[key][index]) {
                        Object.assign(item, templetJson[key][index])
                      } else {
                        item = templetJson[key][index]
                      }
                    })
                  } else if (isObject(me.ruleForm[key])) {
                    // 对象的初始化
                    Object.assign(me.ruleForm[key], templetJson[key])
                  } else {
                    // 普通类型的赋值
                    me.ruleForm[key] = templetJson[key]
                  }
                }
              })
              // me.ruleForm.tagId = Object.values(this.tagItems).includes(response.data.data.tagId) ? response.data.data.tagId : ''
              me.ruleForm.conf.personEnterSelects =
                me.ruleForm.conf.personEnterSelects.map((_) => _.desc)
              me.ruleForm.conf.clubEnterSelects =
                me.ruleForm.conf.clubEnterSelects.map((_) => _.desc)
              // me.ruleForm.custom = me.ruleForm.custom || '' // 自定义默认为空

              const essayList = me.ruleForm.essayMoudel.essayList || []
              // 修改文章列表essayMoudel.essayList数据格式，防止数据库数据太多，造成问题
              if (me.ruleForm.essayMoudel && essayList.length > 0) {
                const newEssayList = []
                essayList.map(function (value) {
                  // const obj = {
                  //   id: value.id,
                  //   title: value.title || '',
                  //   img:
                  //     (value.mediaInfo &&
                  //       value.mediaInfo[0] &&
                  //       value.mediaInfo[0].images[0].imgUrl) ||
                  //     value.img ||
                  //     ''
                  // }
                  newEssayList.push(value.id)
                })
                me.getSearchArticleList(newEssayList)
                // me.ruleForm.essayMoudel.essayList = newEssayList
              }
              me.ruleForm.imgInfo = templetJson.imgInfo || []
              // eslint-disable-next-line vue/valid-next-tick
              me.$nextTick(() => {
                me.$refs.car &&
                  me.$refs.car.addLable(setLabel(templetJson.carList))
              }, 1000)
            }
          })
          .finally(() => {
            me.loading = false
            const quillEditors = ['person', 'club', 'show', 'hide']
            quillEditors.map(function (value) {
              if (me.$refs[`${value}QuillEditor`]) {
                // personQuillEditor、clubQuillEditor、shownQuillEditor、hideQuillEditor改成自己的
                me.$refs[`${value}QuillEditor`]
                  .getQuill()
                  .getModule('toolbar')
                  .addHandler('image', me[`${value}ImgHandler`])
                // 这里初始化，劫持toolbar的image的handler方法，在mounted中处理
              }
            })
          })
      }
      me.initJson = JSON.stringify(me.ruleForm)
      return new Promise((resolve) => {
        return resolve()
      })
    },
    async getSelectsConfig() {
      await GetSelectsConfig().then((response) => {
        Object.assign(this.conf, response.data.conf)
      })
    },
    updateArticle(list) {
      this.ruleForm.essayMoudel.essayList =
        this.ruleForm.essayMoudel.essayList || []
      this.ruleForm.essayMoudel.essayList.unshift(
        ...list.filter(
          (_) =>
            !this.ruleForm.essayMoudel.essayList.some(
              (item) => item.id === _.id
            )
        )
      )
    },
    updateTopic(item) {
      this.ruleForm.shortTopicMoudel.shortTopicInfo = item
    },
    chooseArticle() {
      this.$refs['chooseArticle'].dialogVisible = true
      this.$refs['chooseArticle'].init()
    },
    // banner图片配置
    // updateImgInfo(res, index) {
    //   if (res.code === 0) {
    //     this.ruleForm.imgInfo[index].imgCover = res.data
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },
    updateActDescBgImg(res) {
      if (res.code === 0) {
        this.ruleForm.actDesc.bgImg = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取排序列表
    getDrapValue(value) {
      this.ruleForm.essayMoudel.essayList = value
    },
    // 活动封面
    updateActCover(res) {
      if (res.code === 0) {
        this.ruleForm.pageInfo.actCover = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 活动封面
    onSuccessActCover(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.pageInfo.actCover = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 背景图
    // updateBgImg(res) {
    //   if (res.code === 0) {
    //     this.ruleForm.essayMoudel.bgImg = res.data
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },
    // updateTopicBgImg(res) {
    //   if (res.code === 0) {
    //     this.ruleForm.shortTopicMoudel.bgImg = res.data
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 短话题标题背景图
    onSuccessTopicBgImg(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.shortTopicMoudel.bgImg = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // banner图片配置
    onSuccessImgInfo(res, index) {
      if (!res) return
      if (res.name) {
        this.ruleForm.imgInfo[index].imgCover = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 背景图
    onSuccessBgImg(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.essayMoudel.bgImg = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 活动说明标题背景图
    onSuccessActDescBgImg(res) {
      if (!res) return
      if (res.name) {
        this.ruleForm.actDesc.bgImg = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    handleClose() {
      this.$refs['car'].deleteAllLabel()
      if (JSON.stringify(this.ruleForm) === this.initJson) {
        this.dialogVisible = false
        this.$router.go(-1)
        return
      }
      this.$confirm('确认关闭？')
        .then(() => {
          this.dialogVisible = false
          this.$router.go(-1)
        })
        .catch(() => {})
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.saveResult()
        } else {
          this.$message.warning('有必填项没有填写，请检查')
          return false
        }
      })
    },
    saveResult() {
      const me = this
      const data = {
        activityId: me.item.activityId,
        templetId: me.item.templetId,
        tagId: me.ruleForm.tagId,
        carIds:
          me.carLabels &&
          me.carLabels
            .map((_) => {
              return _.id
            })
            .join(',')
      }
      const temData = me.ruleForm
      me.loading = true
      temData.conf.personEnterSelects = me.conf.personEnterSelects.filter((_) =>
        temData.conf.personEnterSelects.includes(_.desc)
      )
      temData.conf.clubEnterSelects = me.conf.clubEnterSelects.filter((_) =>
        temData.conf.clubEnterSelects.includes(_.desc)
      )
      temData.date = {
        beginTime: me.item.beginTime,
        endTime: me.item.endTime
      }
      if (!this.$refs.showQuillEditor?.getText()?.trim()) {
        temData.actDesc.showDesc = ''
      }

      if (!this.$refs.hideQuillEditor?.getText()?.trim()) {
        temData.actDesc.hideDesc = ''
      }

      data.checkParams = JSON.stringify(temData.conf)
      data.templetJson = JSON.stringify(temData)
      SaveTemplet(data)
        .then((response) => {
          if (response.data.code === 0) {
            me.$emit('success')
            me.$message.success('保存成功')
            me.$refs['car'].deleteAllLabel()
            me.dialogVisible = false
            me.$router.go(-1)
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 添加barnner
    addImgInfo() {
      this.ruleForm.imgInfo.push({
        imgCover: '',
        url: ''
      })
    },
    // 重置表单
    resetForm() {
      this.$refs['ruleForm'].resetFields()
    },
    // 查询文章列表
    getSearchArticleList(value) {
      const me = this
      searchArticleList({
        essayIds: value.join(','),
        page: 1,
        limit: 200
      }).then((response) => {
        if (response.data.code === 0) {
          const tableData = response.data.data.listData
          let listData = []
          value.map((item) => {
            listData.push(
              tableData.find((_) => {
                return _.id === item
              })
            )
          })
          me.ruleForm.essayMoudel.essayList = listData
        } else {
          this.$message.error(response.data.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss">
.act-detail {
  .ql-container {
    height: 150px;
  }
  .el-dialog {
    background: #ffffff;
  }
}
</style>
<style lang="scss" scoped>
.act-detail {
  .colorBox {
    width: 20px;
    height: 20px;
  }
  .modules {
    border: 2px solid #eeeeee;
    border-radius: 5px;
    padding: 20px 10px;
    margin-bottom: 15px;
  }
  .title {
    text-align: center;
    font-size: 16px;
    margin: 20px 0 10px 0;
    font-weight: 700;
  }
  .topBg {
    height: 230px;
    background-size: contain;
    background-repeat: no-repeat;
  }
  .essay-config-box {
    display: flex;
    justify-content: space-between;
    .essay-config-item {
      display: flex;
    }
  }
  .essay-config-style {
    display: flex;
  }
  .radio-group-style {
    .el-radio.el-radio--large {
      height: 32px;
    }
  }
  .essay-list {
    border: 1px solid #eee;
  }
  .essay-list-head {
    border-right: 1px solid #eee;
    padding-top: 10px;
    padding-bottom: 10px;
  }
}
</style>
