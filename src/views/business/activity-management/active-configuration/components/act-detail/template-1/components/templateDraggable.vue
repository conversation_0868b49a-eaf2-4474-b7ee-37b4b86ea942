<template>
  <draggable v-model="articleList" item-key="id" class="draggable">
    <template #item="{ element: item, index }">
      <el-row :gutter="24" class="essay-list">
        <el-col :span="3" class="essay-list-head essay-list-head-more">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            t="1629783231238"
            class="essay-list-head-icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            p-id="1996"
            width="25"
            height="30"
          >
            <path
              d="M128 294.4a38.4 38.4 0 0 0 38.4 38.4h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 0 0-38.4 38.4z m38.4 268.8h691.2a38.4 38.4 0 0 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z m0 230.4h691.2a38.4 38.4 0 1 0 0-76.8H166.4a38.4 38.4 0 1 0 0 76.8z"
              p-id="1997"
            />
          </svg>
          {{ index + 1 }}
        </el-col>
        <el-col :span="3" class="essay-list-head essay-list-head-more">
          {{ item.id }}
        </el-col>
        <el-col :span="4" class="essay-list-head essay-list-head-more">
          {{ item.author }}
        </el-col>
        <el-col :span="11" class="essay-list-head">
          <c-feedList :card="item" />
          <div class="circle-box">
            <div
              v-for="hoop in item.esMotorHoopList"
              :key="hoop.id"
              class="box"
            >
              <el-icon class="icon"><IconHelp /></el-icon>{{ hoop.name }}
            </div>
          </div>
          <div class="content-foot">
            <div class="content-foot-box">
              <span>CTR {{ (item.ctr * 100).toFixed(2) }}%</span>
              <span>阅读完成率 {{ item.readPercent || '0.00%' }}</span>
              <span>曝光量 {{ item.exposureTimes || 0 }}</span>
              <span>评论数 {{ item.replycnt || 0 }} </span>
            </div>
            <div class="content-foot-box">
              <span>浏览量 {{ item.viewNum || 0 }} </span>
              <span
                >发布时间：
                {{
                  $filters.timeFullS((item.dateLine || item.createTime) * 1000)
                }}</span
              >
            </div>
          </div>
        </el-col>
        <el-col :span="3" class="essay-list-head essay-list-head-more">
          <el-button type="primary" link @click="setTop(item, index)"
            >置顶</el-button
          >
          <el-button type="primary" link @click="delete_(item)">删除</el-button>
        </el-col>
      </el-row>
    </template>
  </draggable>
</template>

<script>
import CFeedList from '@/components/CFeedList/index.vue'
import draggable from 'vuedraggable'
import { Help as IconHelp } from '@element-plus/icons-vue'
import { $emit } from '../../../../../../../../utils/gogocodeTransfer'
export default {
  name: 'TempalteDraggable',
  components: {
    draggable,
    CFeedList,
    IconHelp
  },
  props: {
    list: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    options() {
      return {
        draggable: '.item'
      }
    },
    articleList: {
      get() {
        return this.list || []
      },
      set(value) {
        $emit(this, 'update', value)
      }
    }
  },
  methods: {
    setTop(item, index) {
      $emit(this, 'setTopItem', item, index)
    },
    delete_(item) {
      $emit(this, 'deleteItem', item)
    }
  },
  emits: ['update', 'setTopItem', 'deleteItem']
}
</script>

<style lang="scss" scoped>
.draggable {
  .item {
    background-color: #eee;
    margin-bottom: 10px;
    box-sizing: border-box;
    padding: 0 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .img {
    height: 40px;
    background-repeat: no-repeat;
    background-size: contain;
  }
  .circle-box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .box {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin: 5px;
      .icon {
        width: 15px;
        height: 15px;
      }
    }
  }

  .content-foot {
    border: 1px solid #ebeef5;
    border-radius: 5px;
    margin: 5px;
    padding: 5px;
    &-box {
      display: flex;
      justify-content: space-between;
      padding: 2px;
      color: #909399;
    }
  }
  .essay-list {
    border: 1px solid #eee;
  }
  .essay-list-head {
    border-right: 1px solid #eee;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .essay-list-head-more {
    padding-top: 50px;
  }
  .essay-list-head-icon {
    position: relative;
    top: 0.5rem;
  }
}
</style>
