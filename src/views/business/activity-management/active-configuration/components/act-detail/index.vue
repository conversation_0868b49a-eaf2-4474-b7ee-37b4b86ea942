<template>
  <div>
    <template1 ref="template1" @success="getList" v-if="dialogVisible1" />
    <template2 ref="template2" @success="getList" v-if="dialogVisible2" />
    <template5 ref="template5" @success="getList" v-if="dialogVisible5" />
    <template6 ref="template6" @success="getList" v-if="dialogVisible6" />
    <template7 ref="template7" @success="getList" v-if="dialogVisible7" />
  </div>
</template>

<script>
import { GetActList } from '@/api/activeConfiguration'
import Template1 from './template-1/index.vue'
import Template2 from './template-2/index.vue'
import Template5 from './template-5/index.vue'
import Template6 from './template-6/index.vue'
import Template7 from './template-7/index.vue'
export default {
  name: 'ActiveConfigurationDetail',
  components: {
    Template1,
    Template2,
    Template5,
    Template6,
    Template7
  },
  data() {
    return {
      dialogVisible1: false,
      dialogVisible2: false,
      dialogVisible5: false,
      dialogVisible6: false,
      dialogVisible7: false
    }
  },
  // activated
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      const me = this
      const activityId = me.$route.query.activityId
      if (!activityId) return
      GetActList({
        activityId
      })
        .then((response) => {
          if (response.data.code === 0) {
            const tableData = response.data.data.list
            const item = tableData[0]
            // const debug = this.$route.query.debug === 'true'
            me[`dialogVisible${item.templetId}`] = true
            setTimeout(() => {
              me.$refs[`template${item.templetId}`].init(item)
            }, 10)
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .finally((_) => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
