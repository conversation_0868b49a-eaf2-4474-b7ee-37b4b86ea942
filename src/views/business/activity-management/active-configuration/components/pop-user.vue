<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    append-to-body
    class="choose-article"
    title="关联用户编辑"
    width="600px"
  >
    <el-form ref="ruleForm" :model="ruleForm" label-width="100px">
      <el-form-item label="用户">
        <el-input
          v-model="ruleForm.authorIds"
          style="width: 300px"
          placeholder="请输入用户id，多个用,拼接"
        />
      </el-form-item>
      <el-form-item label="文章分类">
        <el-select
          v-model="ruleForm.essayTagIds"
          clearable
          multiple
          style="width: 300px"
        >
          <el-option
            v-for="value in contentTypeList"
            :key="value.labelId"
            :label="value.value"
            :value="value.labelId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="视频分类">
        <el-select
          v-model="ruleForm.videoTagIds"
          clearable
          multiple
          style="width: 300px"
        >
          <el-option
            v-for="value in videoTypeList"
            :key="value.labelId"
            :label="value.value"
            :value="value.labelId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="截至时间" style="width: 400px">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <span style="display: block; text-align: center">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { postSaveOrUpdateUser, getByActId } from '@/api/articleModule'
import { getClassify } from '@/api/article'

export default {
  name: 'ChooseArticle',
  components: {},
  props: {},
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        authorIds: '',
        essayTagIds: [], //文章标签ids
        videoTagIds: [], //视频标签ids
        beginTime: '',
        endTime: ''
      },
      actId: '',
      contentTypeList: [],
      videoTypeList: []
    }
  },
  computed: {
    searchDate: {
      get() {
        if (!this.ruleForm.beginTime && !this.ruleForm.endTime) {
          return ''
        }
        return [this.ruleForm.beginTime, this.ruleForm.endTime]
      },
      set(value) {
        const temp = value || ['', '']
        this.ruleForm.beginTime = temp[0]
        this.ruleForm.endTime = temp[1]
      }
    }
  },
  methods: {
    async init(id) {
      this.ruleForm = {
        authorIds: '',
        essayTagIds: [], //文章标签ids
        videoTagIds: [], //视频标签ids
        beginTime: '',
        endTime: ''
      }
      this.actId = ''
      this.contentTypeList = []
      this.videoTypeList = []
      this.actId = id
      await this.getClassifyList()
      this.dialogVisible = true
      this.getData(id)
    },

    handleClose() {
      this.dialogVisible = false
    },

    getData(id) {
      getByActId({
        actId: id
      }).then((response) => {
        if (response.data.code === 0) {
          const result = response.data.data || {}
          for (const item in this.ruleForm) {
            this.ruleForm[item] = result[item] || ''
          }
          const essayTagIds =
            result.essayTagVOList?.map((tag) => tag.tagId) || ''
          const videoTagIds =
            result.videoTagVOList?.map((tag) => tag.tagId) || ''
          this.ruleForm.essayTagIds = essayTagIds || []
          this.ruleForm.videoTagIds = videoTagIds || []
          console.log(this.ruleForm)
        }
      })
    },

    // 获取分类数据
    async getClassifyList() {
      this.contentTypeList = await searchAsync('1')
      this.videoTypeList = await searchAsync('2')
      async function searchAsync(type) {
        const list = []
        const response = await getClassify({
          status: '1',
          type: type
        })
        if (response.data.code === 0) {
          const result = response.data.data
          result.map(function (value) {
            list.push({
              value: value.name, // 必需
              labelId: value.id
            })
          })
        }
        return list
      }
    },

    confirm() {
      const me = this
      const params = {
        ...this.ruleForm,
        actId: this.actId,
        essayTagIds: this.ruleForm.essayTagIds?.join(',') || '',
        videoTagIds: this.ruleForm.videoTagIds?.join(',') || ''
      }
      postSaveOrUpdateUser({
        ...params
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message({ message: '操作成功', type: 'success' })
          this.dialogVisible = false
        }
      })
    }
  },
  emits: ['chooseArticle']
}
</script>

<style lang="scss">
.choose-article {
  .el-dialog {
    margin-top: 10px !important;
  }
}
</style>
