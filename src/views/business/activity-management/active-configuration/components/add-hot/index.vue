<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    append-to-body
    class="add-hot"
    title="新增热点"
    width="500px"
  >
    <el-form-item label="热搜标题">
      <el-input
        v-model.trim="ruleForm.title"
        type="textarea"
        :rows="4"
        maxlength="200"
        style="width: 400px"
        placeholder="请输入热搜标题"
      />
    </el-form-item>
    <el-form-item label="开始时间">
      <el-date-picker
        v-model="ruleForm.startTime"
        type="datetime"
        placeholder="请选择开始时间"
        format="YYYY-MM-DD HH:mm:ss"
      />
    </el-form-item>
    <el-form-item label="结束时间">
      <el-date-picker
        v-model="ruleForm.endTime"
        type="datetime"
        placeholder="请选择结束时间"
        format="YYYY-MM-DD HH:mm:ss"
      />
    </el-form-item>
    <el-form-item label="跳转链接">
      <div>
        <!-- <el-radio-group v-model="linkType">
          <el-radio :label="1">内容</el-radio>
          <el-radio :label="2">专题</el-radio>
          <el-radio :label="3">自定义链接</el-radio>
        </el-radio-group>
        <br /> -->
        <el-input
          v-model.trim="ruleForm.routeUrl"
          placeholder="请输入链接"
          maxlength="100"
          style="width: 400px"
        />
        <div class="tip">
          <p>提示：</p>
          <p>1、内容链接模板：https://m.58moto.com/news/10839326</p>
          <p>
            2、专题链接模板：https://wap.58moto.com/zt/2018/12/defining-templates?activityId=36721
          </p>
          <p>3、自定义链接模板：https://www.baidu.com</p>
        </div>
      </div>
    </el-form-item>
    <template v-slot:footer>
      <span style="display: block; text-align: center">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { $emit } from '@/utils/gogocodeTransfer'
export default {
  name: 'AddHot',
  components: {},
  props: {
    moduleId: {
      // 模块id
      type: Number,
      default: 0
    },
    pagetype: {
      // 页面类型（用于摩托学院教官添加用户时特殊处理动作）
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      // linkType: 1, // 1:内容 2:专题 3:自定义链接
      // tempLink: '',
      // linkTips: {
      //   1: '请输入内容ID',
      //   2: '请输入专题ID',
      //   3: '请输入跳转链接'
      // },
      ruleForm: {
        title: '',
        startTime: '',
        endTime: '',
        routeUrl: ''
      }
    }
  },
  methods: {
    init(params = {}) {
      // console.log(params)
      // this.linkType = params.linkType || 1
      // this.tempLink = params.routeUrl ||''
      this.ruleForm = {
        index: params.index >= 0 ? params.index : -1,
        title: params.title || '',
        startTime: params.startTime || '',
        endTime: params.endTime || '',
        routeUrl: params.routeUrl || ''
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    confirm() {
      if (!this.ruleForm.title) {
        return this.$message.error('热搜标题不能为空')
      }
      if (!this.ruleForm.routeUrl) {
        return this.$message.error('跳转链接不能为空')
      }
      if (!this.ruleForm.routeUrl.includes('http')) {
        return this.$message.error('请输入正确跳转链接')
      }
      $emit(this, 'chooseContent', this.ruleForm)
      this.init()
      this.dialogVisible = false
    }
  },
  emits: ['chooseContent']
}
</script>

<style lang="scss">
.add-hot {
  .el-dialog {
    margin-top: 10px !important;
  }

  .tip {
    font-size: 10px;
    line-height: 12px;
    padding-top: 10px;
  }
}
</style>
