<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    append-to-body
    class="choose-article"
    title="添加文章"
    width="1000px"
  >
    <el-row :gutter="20">
      <el-col :span="20">
        <el-form ref="ruleForm" :model="ruleForm" inline>
          <el-form-item label="文章/视频ID">
            <el-input
              v-model="ruleForm.id"
              style="width: 200px"
              :placeholder="
                pagetype === 'motoCollege'
                  ? '请输入内容id'
                  : '请输入内容id，多个用,拼接'
              "
            />
          </el-form-item>
          <el-form-item label="标题">
            <el-input v-model="ruleForm.title" style="width: 200px" />
          </el-form-item>
          <el-form-item label="标签">
            <el-select
              v-model="ruleForm.labelId"
              :remote-method="remoteMethodLabel"
              :loading="labelLoading"
              multiple
              filterable
              remote
              reserve-keyword
              placeholder="请输入关键词"
            >
              <el-option
                v-for="item in labelOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <seleted-user
            ref="selectUser"
            :name="'发布人'"
            :placeholder="'请输入关键词'"
            :is-all="true"
            @sendData="setUid"
          />
        </el-form>
      </el-col>
      <el-col :span="4">
        <el-button
          type="primary"
          link
          @click="
            () => {
              page = 1
              searchArticle()
            }
          "
          >搜索</el-button
        >
      </el-col>
    </el-row>
    <el-table
      ref="multipleTable"
      :data="tableData"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" width="70" />
      <el-table-column prop="id" align="center" label="文章ID" width="70" />
      <el-table-column
        prop="author"
        align="center"
        label="发布人"
        width="100"
      />
      <el-table-column align="center" prop="title" label="文章标题" />
      <el-table-column align="center" label="正文">
        <template v-slot="scope">
          <template v-if="scope.row.content">{{
            $filters.subString(scope.row.content, 20)
          }}</template>
          <template v-else>
            <img
              :src="
                scope.row.mediaInfo &&
                scope.row.mediaInfo[0] &&
                scope.row.mediaInfo[0].images[0].imgUrl
              "
              style="height: 40px"
              alt=""
            />
          </template>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-if="total"
      :total="total"
      :current-page="page"
      align="center"
      layout="total, prev, pager, next, jumper"
      @current-change="currentChange"
    />
    <template v-slot:footer>
      <span style="display: block; text-align: center">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { $emit } from '../../../../../../utils/gogocodeTransfer'
import {
  searchArticleList // 文章搜索
} from '@/api/articleModule'
import { SearchLabel, SearchArticle } from '@/api/activeConfiguration'
import { SaveAcademyEssay } from '@/api/motoCollege' // 摩托学院新建文章
import SeletedUser from '@/components/SeletedUser/SeletedUser.vue'
export default {
  name: 'ChooseArticle',
  components: {
    SeletedUser
  },
  props: {
    moduleId: {
      // 模块id
      type: Number,
      default: 0
    },
    pagetype: {
      // 页面类型（用于摩托学院教官添加用户时特殊处理动作）
      type: String,
      default: ''
    }
  },
  data() {
    return {
      total: 0,
      page: 1,
      dialogVisible: false,
      labelLoading: false,
      multipleSelection: [],
      labelOptions: [],
      searchLabelValue: '1',
      tableData: [],
      ruleForm: {
        id: '',
        title: '',
        labelId: [],
        userId: ''
      }
    }
  },
  methods: {
    init() {
      this.ruleForm = {
        id: '',
        title: '',
        labelId: [],
        userId: ''
      }
      this.tableData = []
      this.multipleSelection = []
      this.page = 1
      this.total = 0
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.clearSelection()
      }
      this.$refs.selectUser?.clearData()
      this.searchArticle()
    },
    currentChange(page) {
      this.page = page
      this.searchArticle()
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    remoteMethodLabel(query) {
      this.labelLoading = true
      this.$tools.debounce(() => this.searchLabel(query), 300)()
    },
    searchArticle() {
      const postData = {
        id: this.pagetype === 'motoCollege' ? this.ruleForm.id : '',
        title: this.ruleForm.title,
        autherid: this.ruleForm.userId,
        page: this.page,
        status: '1',
        type: "'essay_detail','activity','car_detail','moment_detail','riding_detail','video_detail'",
        // prime: 1, // 去掉优质限制
        limit: 10,
        essayIds: this.pagetype === 'motoCollege' ? '' : this.ruleForm.id
      }
      const url =
        this.pagetype === 'motoCollege' ? SearchArticle : searchArticleList
      // 摩托学院文章选择文章时，不需要只取优质的文章
      this.pagetype === 'motoCollege'
        ? delete postData.prime
        : delete postData.type
      if (this.ruleForm.labelId && this.ruleForm.labelId.join(',')) {
        postData.labels =
          this.ruleForm.labelId && this.ruleForm.labelId.join(',')
      }
      url(postData).then((response) => {
        if (response.data.code === 0) {
          this.tableData = response.data.data.listData
          this.total = response.data.data.total
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    // 设置返回uid
    setUid(id) {
      this.ruleForm.userId = id
    },
    confirm() {
      // this.$emit('chooseArticle', this.multipleSelection)
      // 修改传回父组件的文章列表数据格式，防止数据库数据太多，造成问题
      const newEssayList = []
      const me = this

      if (this.multipleSelection && this.multipleSelection.length > 0) {
        this.multipleSelection.map(function (value) {
          const obj = {
            id: value.id,
            title: value.title || '',
            img:
              (value.mediaInfo &&
                value.mediaInfo[0] &&
                value.mediaInfo[0].images[0].imgUrl) ||
              ''
          }
          newEssayList.push(
            me.pagetype === 'motoCollege'
              ? obj
              : {
                  ...value,
                  img: obj.img
                }
          )
        })
      }
      if (this.pagetype === 'motoCollege') {
        if (newEssayList && newEssayList.length === 0) {
          return this.$message.error('您还没有选择任何文章')
        }
        SaveAcademyEssay({
          ids: newEssayList.map((_) => _.id).join(','),
          moduleId: this.moduleId
        }).then((response) => {
          if (response.data.code === 0) {
            this.$message.success('操作成功')
            this.dialogVisible = false
            $emit(this, 'chooseArticle')
          } else {
            this.$message.error(response.data.msg)
          }
        })
      } else {
        $emit(this, 'chooseArticle', newEssayList)
      }
      this.init()
      this.dialogVisible = false
    },
    searchLabel(query = '') {
      SearchLabel({
        // page: 1,
        // limit: 20,
        name: query || this.searchLabelValue
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.labelOptions = response.data.data
          } else {
            this.labelOptions = []
          }
        })
        .finally(() => {
          this.labelLoading = false
        })
    }
  },
  emits: ['chooseArticle']
}
</script>

<style lang="scss">
.choose-article {
  .el-dialog {
    margin-top: 10px !important;
  }
}
</style>
