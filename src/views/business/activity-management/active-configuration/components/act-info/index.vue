<template>
  <div v-loading="loading">
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :title="title"
      class="actInfo"
      width="500px"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="活动名称" prop="activityName">
          <el-input v-model="ruleForm.activityName" />
        </el-form-item>
        <!-- required -->
        <el-form-item label="分享图标" prop="shareIcoUrl">
          <el-input v-model="ruleForm.shareIcoUrl" style="width: 238px" />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccess"
            name="upfile"
            style="display: inline-block"
            class="avatar-uploader"
            action
          >
            <el-button>选择图片</el-button>
          </el-upload>
        </el-form-item>
        <!-- required -->
        <el-form-item label="分享标题" prop="shareTitle">
          <el-input v-model="ruleForm.shareTitle" />
        </el-form-item>
        <!-- required -->
        <el-form-item label="分享描述" prop="shareDesc">
          <el-input v-model="ruleForm.shareDesc" type="textarea" />
        </el-form-item>
        <!-- required -->
        <el-form-item label="分享地址" prop="shareUrl">
          <el-input v-model="ruleForm.shareUrl" />
        </el-form-item>
        <el-form-item label="活动开始时间" prop="beginTime">
          <el-date-picker
            v-model="ruleForm.beginTime"
            type="datetime"
            value-format="x"
            placeholder="选择日期时间"
          />
        </el-form-item>

        <el-form-item label="活动结束时间" prop="endTime">
          <el-date-picker
            v-model="ruleForm.endTime"
            type="datetime"
            value-format="x"
            placeholder="选择日期时间"
          />
        </el-form-item>

        <el-form-item v-if="!activityId" label="选择活动模版" prop="templetId">
          <el-select v-model="ruleForm.templetId" placeholder="选择活动模版">
            <el-option
              v-for="(item, index) in temList"
              :key="index"
              :label="item.templetName"
              :value="item.templetId"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="handleClose">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit
} from '../../../../../../utils/gogocodeTransfer'
import {
  GetActDetail,
  SaveActDetail,
  GetTemList
} from '@/api/activeConfiguration'
import { timeFullS } from '@/filters'
import { mapGetters } from 'vuex'
import { batchRecordBeforeAlter } from '@/utils/enum/logData'
export default {
  name: 'ActInfo',
  data() {
    return {
      loading: false,
      dialogVisible: false,
      temList: [],
      imageUrl: '',
      activityId: '',
      ruleForm: {
        activityName: '',
        shareIcoUrl: '',
        shareTitle: '',
        shareDesc: '',
        beginTime: '',
        endTime: '',
        templetId: '',
        shareUrl: ''
      },
      initJson: '',
      rules: {
        activityName: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ],
        userFace: [
          { required: true, message: '请上传分享图标', trigger: 'blur' }
        ],
        templetId: [
          { required: true, message: '请选择活动模板', trigger: 'change' }
        ],
        title: [{ required: true, message: '请填写分享标题', trigger: 'blur' }],
        des: [{ required: true, message: '请填写分享描述', trigger: 'blur' }],
        beginTime: [
          {
            type: 'date',
            required: true,
            message: '请选择时间',
            trigger: 'blur'
          }
        ],
        endTime: [
          {
            type: 'date',
            required: true,
            message: '请选择时间',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['uid']),
    title: {
      get() {
        return this.activityId ? '修改活动' : '新建活动'
      }
    }
  },
  created() {
    this.getTemList()
  },
  methods: {
    async init(id) {
      this.activityId = id
      if (this.$refs['ruleForm']) {
        this.$refs['ruleForm'].resetFields()
      }
      if (id) {
        await this.getDetail()
      }
      this.initJson = JSON.stringify(this.ruleForm)
      console.log(this.initJson, 'this.initJson')
    },
    // 获取模板列表
    getTemList() {
      GetTemList().then((response) => {
        if (response.data.code === 0) {
          this.temList = response.data.data
        }
      })
    },
    // updateShareIco(res) {
    //   if (res.code === 0) {
    //     this.ruleForm.shareIcoUrl = res.data
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    onSuccess(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        this.ruleForm.shareIcoUrl = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 获取活动详情
    async getDetail() {
      this.loading = true
      await GetActDetail({
        activityId: this.activityId
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.ruleForm = {
              activityName: response.data.data.activityName,
              shareIcoUrl: response.data.data.shareIcoUrl,
              shareTitle: response.data.data.shareTitle,
              shareDesc: response.data.data.shareDesc,
              beginTime: response.data.data.beginTime,
              endTime: response.data.data.endTime,
              templetId: response.data.data.templetId,
              shareUrl: response.data.data.shareUrl
                ? response.data.data.shareUrl
                : `https://wap.58moto.com/${
                    response.data.data.tagId === 5
                      ? 'zt/2018/12/defining-templates-price'
                      : 'zt/2018/12/defining-templates'
                  }?activityId=${response.data.data.activityId}&share=true`
            }
            batchRecordBeforeAlter(this.ruleForm, this.activityId)
          }
        })
        .finally((_) => {
          this.loading = false
        })
    },
    saveDetail() {
      const data = {
        activityName: this.ruleForm.activityName,
        shareIcoUrl: this.ruleForm.shareIcoUrl,
        shareTitle: this.ruleForm.shareTitle,
        shareDesc: this.ruleForm.shareDesc,
        beginTime: timeFullS(this.ruleForm.beginTime),
        endTime: timeFullS(this.ruleForm.endTime),
        templetId: this.ruleForm.templetId,
        shareUrl: this.ruleForm.shareUrl
      }
      if (this.activityId) {
        data.activityId = this.activityId
      }
      SaveActDetail(data).then((response) => {
        if (response.data.code === 0) {
          this.$message.success('保存成功')
          this.init('')
          this.dialogVisible = false
          $emit(this, 'success', data)
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    handleClose() {
      if (JSON.stringify(this.ruleForm) === this.initJson) {
        this.dialogVisible = false
        return
      }
      this.$confirm('确认关闭？')
        .then((_) => {
          this.dialogVisible = false
        })
        .catch((_) => {})
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.saveDetail()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  },
  emits: ['success']
}
</script>

<style lang="scss" scoped>
.actInfo {
  .line {
    text-align: center;
  }
}
</style>
