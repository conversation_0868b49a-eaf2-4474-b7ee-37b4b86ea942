<template>
  <el-dialog
    v-model="dialogVisible"
    :before-close="handleClose"
    append-to-body
    class="short-topic"
    title="关联短话题"
    width="1000px"
  >
    <el-form ref="ruleForm" :model="ruleForm" inline label-width="80px">
      <el-form-item label="话题名称">
        <el-input
          v-model="ruleForm.title"
          style="width: 220px"
          type="text"
          placeholder="请输入话题名称"
        />
      </el-form-item>
      <seleted-user
        ref="selectUser"
        :name="'发布人'"
        :placeholder="'请输入关键词'"
        :is-all="true"
        @sendData="setUid"
      />
      <el-form-item label="">
        <el-button
          type="primary"
          link
          @click="
            () => {
              page = 1
              searchTopic()
            }
          "
          >搜索</el-button
        >
      </el-form-item>

      <el-table :data="tableData" style="width: 100%" @row-click="rowClick">
        <el-table-column label="文章ID" align="left" width="105">
          <template v-slot="scope">
            <el-radio v-model="topicId" :label="scope.row.id" />
          </template>
        </el-table-column>
        <el-table-column
          prop="author"
          align="center"
          label="发布人"
          width="100"
        />
        <el-table-column align="center" prop="title" label="文章标题" />
      </el-table>

      <el-pagination
        v-if="total"
        :total="total"
        :current-page="page"
        align="center"
        layout="total, prev, pager, next, jumper"
        @current-change="currentChange"
      />
    </el-form>

    <template v-slot:footer>
      <span style="text-align: center">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit
} from '../../../../../../utils/gogocodeTransfer'
import { SearchTopic } from '@/api/activeConfiguration'
import SeletedUser from '@/components/SeletedUser/SeletedUser.vue'
export default {
  name: 'ShortTopic',
  components: {
    SeletedUser
  },
  data() {
    return {
      total: 0,
      page: 1,
      topicId: 0,
      item: {},
      dialogVisible: false,
      tableData: [],
      ruleForm: {
        title: '',
        userId: ''
      }
    }
  },
  watch: {
    topicId: function () {
      this.item = this.tableData.find((_) => _.id === this.topicId)
    }
  },
  methods: {
    rowClick(row) {
      this.topicId = row.id
    },
    currentChange(page) {
      this.page = page
      this.searchTopic()
    },
    init() {
      this.ruleForm = {
        title: '',
        labelId: [],
        userId: ''
      }
      this.searchTopic()
    },
    handleClose() {
      // this.$confirm('确认关闭？')
      //   .then(_ => {
      //     this.dialogVisible = false
      //     this.init()
      //   })
      //   .catch(_ => {})
      this.dialogVisible = false
    },
    // 设置返回uid
    setUid(id) {
      this.ruleForm.userId = id
    },
    searchTopic() {
      SearchTopic({
        title: this.ruleForm.title,
        autherid: this.ruleForm.userId,
        page: this.page,
        limit: 10
      }).then((response) => {
        if (response.data.code === 0) {
          this.tableData = response.data.data.listData
          this.total = response.data.data.total
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    confirm() {
      $emit(this, 'chooseTopic', this.item)
      this.init()
      this.dialogVisible = false
    }
  },
  emits: ['chooseTopic']
}
</script>

<style lang="scss">
.short-topic {
  .el-dialog {
    margin-top: 10px !important;
  }
}
</style>
