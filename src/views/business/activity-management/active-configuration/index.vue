<template>
  <div v-loading="loading" class="ActiveConfiguration">
    <div class="mb15">
      <el-button @click="newAct">新建活动</el-button>
      <el-button type="primary" @click="setActive">生效</el-button>
      <el-button type="danger" @click="unActive">失效</el-button>
    </div>
    <!--搜索-->
    <el-form :inline="true" :model="searchParams">
      <el-form-item label="活动名称">
        <el-input v-model="searchParams.activityName" style="width: 200px" />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchParams.status" placeholder="请选择">
          <el-option label="全部" value />
          <el-option
            v-for="(item, index) in mapStatus"
            :key="index"
            :label="item"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="专题类型">
        <el-select v-model="searchParams.templetId" placeholder="请选择">
          <el-option label="全部" value />
          <el-option
            v-for="(item, index) in temList"
            :key="index"
            :label="item.templetName"
            :value="item.templetId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 400px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="currentChange(1)">搜索</el-button>
      </el-form-item>
    </el-form>

    <!--活动列表-->
    <el-table
      :data="tableData"
      border
      max-height="70vh"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column align="center" type="index" label="序号" width="80" />
      <el-table-column
        align="center"
        prop="activityId"
        label="专题ID"
        width="120"
      />
      <el-table-column align="center" prop="activityName" label="专题名称" />
      <el-table-column align="center" prop="activityName" label="模版类型">
        <template v-slot="scope">{{
          findTemplate(scope.row.templetId)
        }}</template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="joinNum"
        label="参与人数"
        width="180"
      />
      <el-table-column align="center" label="专题状态" width="150">
        <template v-slot="scope">{{ mapStatus[scope.row.status] }}</template>
      </el-table-column>
      <el-table-column align="center" label="开始时间" width="200">
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.beginTime)
        }}</template>
      </el-table-column>
      <el-table-column align="center" label="结束时间" width="200">
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.endTime)
        }}</template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="220">
        <template v-slot="scope">
          <el-button
            type="primary"
            link
            size="small"
            @click="editConfigAct(scope.row)"
            >专题配置</el-button
          >
          <el-button
            type="primary"
            link
            size="small"
            @click="editAct(scope.row)"
            >修改</el-button
          >
          <el-button
            v-if="![6].includes(scope.row.templetId)"
            v-clipboard:copy="getShareUrl(scope)"
            v-clipboard:success="clipboardSuccess"
            type="primary"
            link
            size="small"
            >拷贝链接</el-button
          >
          <el-button
            v-if="![5, 6].includes(scope.row.templetId)"
            type="primary"
            link
            size="small"
            @click="openUser(scope.row)"
            >关联用户</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!--分页-->
    <el-pagination
      v-if="total"
      :total="total"
      :current-page="searchParams.page"
      layout="total, prev, pager, next, jumper"
      @current-change="currentChange"
    />

    <act-info ref="ActInfo" @success="updateDetail" />
    <PopUser ref="popUser" @success="getList" />
    <div v-show="false">
      <template1 ref="template1" @success="getList" />
      <template2 ref="template2" @success="getList" />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import Template1 from './components/act-detail/template-1/index.vue'
import Template2 from './components/act-detail/template-2/index.vue'
import PopUser from './components/pop-user.vue'
import ActInfo from './components/act-info/index.vue'
import clipboard from '@/directive/clipboard/index.js'
import {
  GetActList,
  SetActive,
  DeleteActDetail,
  GetTemList
} from '@/api/activeConfiguration'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
export default {
  data() {
    return {
      mapStatus: ['已失效', '未开始', '正在进行', '已结束'],
      multipleSelection: [],
      tableData: [],
      temList: [],
      searchParams: {
        activityName: '',
        status: '',
        beginTime: '',
        endTime: '',
        templetId: '',
        page: 1,
        limit: 10
      },
      total: 0,
      loading: false,
      dayjs
    }
  },
  name: 'ActiveConfiguration',
  directives: {
    clipboard
  },
  components: {
    ActInfo,
    Template1,
    Template2,
    PopUser
  },
  computed: {
    searchDate: {
      get() {
        if (!this.searchParams.beginTime && !this.searchParams.endTime) {
          return ''
        }
        return [this.searchParams.beginTime, this.searchParams.endTime]
      },
      set(value) {
        const temp = value || ['', '']
        this.searchParams.beginTime = temp[0]
        this.searchParams.endTime = temp[1]
      }
    }
  },
  mounted() {
    this.getList()
    this.getTemplate()
  },
  methods: {
    getTemplate() {
      GetTemList().then((response) => {
        if (response.data.code === 0) {
          this.temList = response.data.data
        }
      })
    },

    findTemplate(id) {
      const item = this.temList.find((_) => _.templetId === id)
      return item ? item.templetName : ''
    },

    currentChange(page) {
      this.searchParams.page = page
      this.getList()
    },
    // 活动生效
    setActive() {
      if (this.multipleSelection.length === 0) {
        this.$message.error('请先选择活动')
        return
      }
      this.loading = true
      SetActive({
        activityIds: this.multipleSelection.map((_) => _.activityId).join(',')
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.$message.success('操作成功')
            this.getList()
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 活动失效
    unActive() {
      const me = this
      if (me.multipleSelection.length === 0) {
        me.$message.error('请先选择活动')
        return
      }
      me.$confirm(
        '设为失效后，专题无法在app查看，请确认是否置为失效？',
        '专题状态',
        {
          confirmButtonText: '失效',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }
      ).then(function () {
        me.loading = true
        DeleteActDetail({
          activityIds: me.multipleSelection.map((_) => _.activityId).join(',')
        })
          .then((response) => {
            if (response.data.code === 0) {
              me.$message.success('操作成功')
              me.getList()
            } else {
              me.$message.error(response.data.msg)
            }
          })
          .finally(() => {
            me.loading = false
          })
      })
    },
    // 新建活动
    newAct() {
      this.$refs['ActInfo'].init('')
      this.$refs['ActInfo'].dialogVisible = true
    },
    // 配置模板  根据模板id选择弹框
    editConfigAct(item) {
      if ([1, 2, 5, 6, 7].indexOf(item.templetId) === -1) {
        return this.$message.error('未有该类型模板')
      }
      this.$router.push({
        name: 'ActiveConfigurationDetail',
        query: {
          activityId: item.activityId
        }
      })
      // this.$refs[`template${item.templetId}`].init(item)
      // this.$refs[`template${item.templetId}`].dialogVisible = true
    },
    editAct(item) {
      this.$refs['ActInfo'].init(item.activityId)
      this.$refs['ActInfo'].dialogVisible = true
    },
    getList() {
      this.loading = true
      GetActList(this.searchParams)
        .then((response) => {
          if (response.data.code === 0) {
            this.tableData = response.data.data.list
            this.total = response.data.data.total
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    updateDetail(item) {
      this.getList()
      // 在模板配置中更新活动时间
      if (!item.activityId) {
        return
      }
      const templetId = this.tableData.find(
        (_) => _.activityId === item.activityId
      ).templetId
      this.$refs[`template${templetId}`].init(item).then(() => {
        this.$refs[`template${templetId}`].saveResult()
      })
    },
    openUser(row) {
      this.$refs['popUser'].init(row.activityId)
    },

    getShareUrl(scope) {
      let url = scope.row.shareUrl
        ? scope.row.shareUrl
        : `https://wap.58moto.com/${
            scope.row.tagId === 5
              ? 'zt/2018/12/defining-templates-price'
              : scope.row.templetUrl
          }?activityId=${scope.row.activityId}&share=true`
      if (scope.row.templetId === 5) {
        url = `https://wap.58moto.com/hot-events?activityId=${scope.row.activityId}&share=true`
      }
      return url
    },

    clipboardSuccess() {
      this.$message({
        message: '拷贝成功',
        type: 'success',
        duration: 1500
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.ActiveConfiguration {
  padding: 20px;
  .el-pagination {
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
