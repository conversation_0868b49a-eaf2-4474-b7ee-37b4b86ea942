/** * 公众号合作项目列表*/
<template>
  <div class="cooperation-list">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="订单编号">
        <el-input
          v-model="ruleForm.orderNum"
          placeholder="请输入订单编号"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="所属订单名称">
        <el-input
          v-model="ruleForm.orderName"
          placeholder="请输入所属订单名称"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="品牌ID">
        <el-input
          v-model="ruleForm.brandId"
          placeholder="请输入品牌ID"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="品牌名称">
        <el-select
          v-model="brandName"
          :remote-method="remoteMethodSecond"
          :loading="loadingBrand"
          value-key="name"
          placeholder="请输入品牌名称"
          filterable
          remote
          clearable
          @change="setBrandNameSecond"
          style="width: 200px"
        >
          <el-option
            v-for="item in brandList"
            :key="item.name"
            :label="item.title"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="执行状态">
        <el-select v-model="ruleForm.processStatus" clearable>
          <el-option
            v-for="(value, index) in implementList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterange"
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          clearable
          range-separator="至"
          start-placeholder="创建开始日期"
          end-placeholder="创建结束日期"
          style="width: 360px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      border
      max-height="70vh"
      @expand-change="expandChange"
    >
      <el-table-column type="expand" fixed>
        <template #default="{ row }">
          <div class="ph10">
            <el-table
              :data="tableDetails[row.id] || []"
              border
              max-height="50vh"
            >
              <el-table-column label="id" prop="id" align="center" />
              <el-table-column label="标题" prop="title" align="center" />
              <el-table-column label="品牌" prop="brandName" align="center" />
              <el-table-column label="品牌ID" prop="brandId" align="center" />
              <el-table-column label="位置" prop="brandName" align="center">
                <template v-slot="scope">
                  {{ positionEnum[scope.row.position] }}
                </template>
              </el-table-column>
              <el-table-column label="链接" prop="url" align="center" />
              <el-table-column label="发布时间" align="center" width="160">
                <template v-slot="scope">
                  {{
                    $date.format(scope.row.publishTime, 'YYYY.MM.DD HH:mm:ss')
                  }}
                </template>
              </el-table-column>
              <el-table-column label="创建时间" align="center" width="160">
                <template v-slot="scope">
                  {{
                    $date.format(scope.row.createTime, 'YYYY.MM.DD HH:mm:ss')
                  }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="订单编号"
        prop="orderNum"
        align="center"
        width="200"
      />
      <el-table-column
        label="所属订单名称"
        prop="orderName"
        align="center"
        width="150"
      />
      <el-table-column
        label="品牌ID"
        prop="brandId"
        align="center"
        width="120"
      />
      <el-table-column
        label="品牌名称"
        prop="brandName"
        align="center"
        width="150"
      />
      <el-table-column label="总条数" align="center" width="120">
        <template #default="{ row }">
          {{ (row.cooperateCnt * 10000).toFixed(0) }}条
        </template>
      </el-table-column>
      <el-table-column label="可用条数" align="center" width="120">
        <template #default="{ row }">
          {{ (row.availableCount * 10000).toFixed(0) }}条
        </template>
      </el-table-column>
      <el-table-column label="执行状态" align="center" width="120">
        <template #default="{ row }">
          {{ implementListEnum[row.processStatus] || '' }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="160">
        <template #default="{ row }">
          {{ $date.format(row.createTime, 'YYYY.MM.DD HH:mm:ss') }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      style="justify-content: center; margin-top: 15px"
      @current-change="currentChange"
    />
  </div>
</template>

<script setup>
import { convertKeyValueEnum } from '@/utils/convert'
import {
  getOfficialAccountContentPage,
  getOfficialAccountProjectPage
} from '@/api/activeConfiguration'
import { actSimpleList } from '@/api/article'
import { searchBrand } from '@/api/articleModule'

const router = useRouter()
const { proxy } = getCurrentInstance()

const implementList = {
  全部: '',
  未开始: 0,
  进行中: 1,
  已结束: 2
}
const positionEnum = {
  0: '首条',
  1: '次条',
  2: '三条及以上'
}
const implementListEnum = convertKeyValueEnum(implementList)
const formInit = {
  orderNum: '',
  orderName: '',
  brandId: '',
  processStatus: '',
  createTimeStart: '',
  createTimeEnd: ''
}

const ruleForm = reactive({ ...formInit })
const searchValue = ref('')
const brandName = ref('')
const loadingBrand = ref(false)
const brandList = ref([])
const page = ref(1)
const limit = ref(20)
const total = ref(0)
const tableData = ref([])
const tableDetails = ref({})

const daterange = computed({
  get() {
    if (ruleForm.createTimeStart && ruleForm.createTimeEnd) {
      return [ruleForm.createTimeStart, ruleForm.createTimeEnd]
    }
    return []
  },
  set(value) {
    if (value) {
      ruleForm.createTimeStart = value[0]
      ruleForm.createTimeEnd = value[1]
    } else {
      ruleForm.createTimeStart = ''
      ruleForm.createTimeEnd = ''
    }
  }
})

onMounted(() => {
  getList()
})

const search = () => {
  currentChange(1)
}

const reset = () => {
  Object.keys(formInit).forEach((key) => {
    ruleForm[key] = formInit[key]
  })
  brandName.value = ''
  currentChange(1)
}

const remoteMethodSecond = (query) => {
  searchValue.value = query
  proxy.$tools.debounce(getBrandListSecond, 300)()
}

const getBrandListSecond = async () => {
  brandList.value = []
  const params = {
    name: searchValue.value,
    typeIds: '3,4',
    advertiserName: searchValue.value,
    page: 1,
    limit: 10
  }
  const carInfo = await searchBrand(params)
  const list = []
  if (carInfo.data.code === 0) {
    const carResult = carInfo.data?.data?.listData || []
    carResult.forEach((item) => {
      list.push({
        title: item.brandName || item.advertiserName,
        name: item.brandId || item.id
      })
    })
  }
  brandList.value = list
  loadingBrand.value = false
}

const setBrandNameSecond = (item) => {
  ruleForm.brandId = item.name
}

const getList = () => {
  tableDetails.value = {}
  getOfficialAccountProjectPage({
    page: page.value,
    limit: limit.value,
    ...ruleForm
  }).then((res) => {
    if (res.data.code === 0) {
      const data = res.data.data || {}
      tableData.value = data.listData || []
      total.value = data.total || 0
    }
  })
}

const currentChange = (num) => {
  page.value = num
  getList()
}

const expandChange = (row, expandedRows) => {
  const index = expandedRows.findIndex((v) => v.id === row.id)
  if (index >= 0) {
    getOfficialAccountContentPage({
      orderProjectId: row.id,
      page: 1,
      limit: 20
    }).then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data.listData || []
        tableDetails.value[row.id] = data
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.cooperation-list {
  padding: 20px;
}
</style>
