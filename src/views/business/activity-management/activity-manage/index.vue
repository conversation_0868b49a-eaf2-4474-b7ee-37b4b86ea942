<template>
  <div v-loading="loading" class="flex padding-content">
    <div class="flex1 mr-5">
      <p>
        <el-button
          type="primary"
          style="margin-right: 10px"
          @click="
            () => {
              current = {}
              dialogVisible = true
            }
          "
          >新增</el-button
        >
      </p>
      <el-row class="item header">
        <el-col :span="2">ID</el-col>
        <el-col :span="4">标题</el-col>
        <el-col :span="3">展示样式</el-col>
        <el-col :span="3">操作</el-col>
        <el-col :span="6">展示开始时间</el-col>
        <el-col :span="6">展示结束时间</el-col>
      </el-row>
      <draggable
        v-if="validList.length"
        ref="validList"
        item-key="actId"
        v-model="validList"
        @end="dragEnd"
      >
        <template #item="{ element }">
          <el-row class="item">
            <el-col :span="2">
              <div class="box">{{ element.actId }}</div>
            </el-col>
            <el-col :span="4">
              <div class="box">{{ element.title || '&nbsp;' }}</div>
            </el-col>
            <el-col :span="3">
              <div class="box">
                {{ element.operateSize === 1 ? '大图' : '小图' }}
              </div>
            </el-col>
            <el-col :span="3">
              <div class="box">
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="
                    () => {
                      current = { ...element }
                      dialogVisible = true
                    }
                  "
                  >配置</el-button
                >
              </div>
            </el-col>
            <el-col :span="6">
              <div class="box">
                {{ $filter.format(element.beginTime, 'YYYY') }}
              </div>
            </el-col>
            <el-col :span="6">
              <div class="box">{{ element.endTime }}</div>
            </el-col>
          </el-row>
        </template>
      </draggable>
      <p v-else v-show="!validList.length" class="noData" style="color: #666">
        暂无数据
      </p>
    </div>
    <div class="flex1">
      <p style="height: 38px">
        <el-button
          type="primary"
          style="margin-right: 10px"
          @click="showHistory = true"
          >查看过往专题</el-button
        >
      </p>
      <div v-show="showHistory">
        <el-row class="item header">
          <el-col :span="2">ID</el-col>
          <el-col :span="4">标题</el-col>
          <el-col :span="3">展示样式</el-col>
          <el-col :span="3">操作</el-col>
          <el-col :span="6">展示开始时间</el-col>
          <el-col :span="6">展示结束时间</el-col>
        </el-row>
        <div v-if="invalidList.length">
          <el-row
            v-for="(item, index) in invalidList"
            :key="index"
            class="item"
          >
            <el-col :span="2">
              <div class="box">{{ item.actId }}</div>
            </el-col>
            <el-col :span="4">
              <div class="box">{{ item.title || '&nbsp;' }}</div>
            </el-col>
            <el-col :span="3">
              <div class="box">
                {{ item.operateSize === 1 ? '大图' : '小图' }}
              </div>
            </el-col>
            <el-col :span="3">
              <div class="box">
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="
                    () => {
                      current = { ...item }
                      dialogVisible = true
                    }
                  "
                  >配置</el-button
                >
              </div>
            </el-col>
            <el-col :span="6">
              <div class="box">{{ item.beginTime }}</div>
            </el-col>
            <el-col :span="6">
              <div class="box">{{ item.endTime }}</div>
            </el-col>
          </el-row>
        </div>
        <p
          v-else
          v-show="!invalidList.length"
          class="noData"
          style="color: #666"
        >
          暂无数据
        </p>
        <el-pagination
          v-model:current-page="page"
          :page-size="20"
          :page-sizes="[20, 40, 60]"
          :total="total"
          background
          layout="total, prev, pager, next, jumper"
          style="text-align: center; margin-top: 10px"
          @current-change="getActivityList(page)"
        />
      </div>
    </div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="close"
      center
      width="500px"
    >
      <el-form ref="current" :model="current" status-icon label-width="100px">
        <el-form-item label="专题ID" required>
          <el-input
            v-focus
            v-model.trim="current.actId"
            type="number"
            placeholder="请输入专题ID"
          />
        </el-form-item>
        <el-form-item label="封面样式" required>
          <el-radio-group v-model="current.operateSize" class="radio-content">
            <el-radio :label="1">大图</el-radio>
            <el-radio :label="2">小图</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="current.operateSize === 2"
          label="封面标题"
          required
        >
          <el-input
            v-focus
            v-model.trim="current.title"
            type="text"
            maxlength="30"
            placeholder="请填写封面标题"
          />
        </el-form-item>
        <el-form-item label="封面图片" required>
          <img
            v-if="current.cover"
            :src="current.cover"
            style="width: 200px"
            alt
          />
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccess"
            :on-error="onError"
            name="upfile"
            style="display: inline-block"
            class="avatar-uploader"
            multiple
            action
          >
            <el-button class="button" type="primary" link>
              <el-button>添加图片</el-button>
            </el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="展示时间" required>
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="effectDateRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span>
          <el-button type="primary" @click="addOrUpdateActivity"
            >确 定</el-button
          >
          <el-button @click="close">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import draggable from 'vuedraggable'
import { forwardPickerOptions } from '@/utils/configData'
import {
  getActivityList,
  addOrUpdateActivity,
  sortActivity
} from '@/api/activeConfiguration'
export default {
  data() {
    return {
      // 是否显示编辑弹框，默认不显示
      dialogVisible: false,
      loading: false,
      changed: false,
      showHistory: false,
      page: 1,
      total: 0,
      pickerOptions: forwardPickerOptions,
      current: {
        actId: '', // 活动id
        title: '',
        operateSize: '', // 封面样式 1=大图 2=小图
        beginTime: '', // 展示开始时间如
        endTime: '', // 展示结束时间
        cover: ''
      },
      validList: [],
      invalidList: [],
      dayjs
    }
  },
  name: 'ActivityManage',
  components: {
    draggable
  },
  computed: {
    effectDateRange: {
      get() {
        if (this.current.beginTime && this.current.endTime) {
          return [this.current.beginTime, this.current.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.current['beginTime'] = value[0]
          this.current['endTime'] = value[1]
        } else {
          this.current.beginTime = ''
          this.current.endTime = ''
        }
      }
    }
  },
  activated() {
    this.getActivityList()
    console.log(this)
  },
  methods: {
    getActivityList(page) {
      this.loading = true
      this.page = page
      getActivityList({
        page: this.page,
        limit: 20
      })
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            this.validList =
              (data.valid &&
                data.valid.map((_) => {
                  _.beginTime = this.$date.format(_.beginTime)
                  _.endTime = this.$date.format(_.endTime)
                  delete _.updateTime
                  delete _.createTime
                  return _
                })) ||
              []
            this.invalidList =
              (data.invalid &&
                data.invalid.map((_) => {
                  _.beginTime = this.$date.format(_.beginTime)
                  _.endTime = this.$date.format(_.endTime)
                  delete _.updateTime
                  delete _.createTime
                  return _
                })) ||
              []
            this.total = data.total || 0
          }
        })
        .finally((_) => {
          this.loading = false
        })
    },
    addOrUpdateActivity() {
      if (!this.current.actId) {
        return this.$message.error('请输入专题ID')
      }
      if (!this.current.operateSize) {
        return this.$message.error('请选择封面样式')
      }
      if (this.current.operateSize === 2 && !this.current.title) {
        return this.$message.error('请填写封面标题')
      }
      // if (this.current.operateSize === 2 && this.current.title.length > 30) {
      //   return this.$message.error('封面标题不能大于30个字')
      // }
      if (!this.current.cover) {
        return this.$message.error('请添加封面图片')
      }
      if (!this.current.beginTime || !this.current.endTime) {
        return this.$message.error('请选择展示时间')
      }
      this.loading = true
      addOrUpdateActivity({
        ...this.current,
        beginTime: this.$date.format(
          this.current.beginTime,
          'YYYY-MM-DD HH:mm:ss'
        ),
        endTime: this.$date.format(this.current.endTime, 'YYYY-MM-DD HH:mm:ss')
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.close()
            this.$message.success('操作成功')
            this.getActivityList()
          } else {
            this.$message.error('操作失败')
          }
        })
        .finally((_) => {
          this.loading = false
        })
    },
    close() {
      this.dialogVisible = false
      this.current = {}
      // this.getActivityList();
    },
    // 拖拽完成请求接口
    dragEnd({ newIndex, oldIndex }) {
      // 拖动不改变位置不调接口
      if (newIndex === oldIndex) return
      this.loading = true
      sortActivity({
        ids: this.validList.map((_) => _.id).join(',')
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.$message.success('操作成功')
            this.getActivityList()
          } else {
            this.$message.error('操作失败')
          }
        })
        .finally((_) => {
          this.loading = false
        })
    },
    async httpRequest(option) {
      option.imageType = 'forum'
      this.$oss.ossUploadImage(option)
    },
    onError(res, file) {
      this.$message.error(res)
      console.log(res)
    },
    onSuccess(res, file, fileList) {
      // 假性成功，直接return
      if (!res) return
      console.log(res)

      if (res.name) {
        this.current.cover = res.imgOrgUrl
        this.$message.success('上传成功')
      } else {
        this.$notify.error({
          title: '上传错误',
          message: file.name,
          duration: 0
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.item {
  color: #909399;
  font-size: 14px;
  text-align: center;
  line-height: 40px;
  border: 1px solid #ebeef5;
  border-width: 0 0px 1px 1px;
  .el-col {
    word-break: break-all;
    border-right: 1px solid #ebeef5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .el-input__inner {
    width: 90%;
    height: 30px !important;
    line-height: 30px !important;
  }
  .el-input__suffix {
    right: 15px;
  }
  &.header {
    border-width: 1px 0 1px 1px;
    overflow-y: scroll;
  }
  &.active {
    background: #dcdcdc;
  }
}
.noData {
  text-align: center;
  line-height: 50px;
}
</style>
