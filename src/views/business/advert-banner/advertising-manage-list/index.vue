<template>
  <div class="advertising-manage-list">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="广告方ID">
        <el-input
          v-model="ruleForm.advertiserId"
          type="text"
          placeholder="请输入广告方ID"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="广告方名称">
        <el-input
          v-model="ruleForm.advertiserName"
          type="text"
          placeholder="请输入广告名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="广告方类型">
        <el-select v-model="ruleForm.typeId" clearable>
          <el-option
            v-for="(value, index) in advertisingTypeWithAll"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关联ID">
        <el-input
          v-model="ruleForm.relationId"
          type="text"
          placeholder="请输入关联ID"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="是否有效">
        <el-select v-model="ruleForm.status">
          <el-option
            v-for="(value, index) in effective"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button type="primary" @click="resetData">重置</el-button>
        <el-button type="primary" @click="openEdit">创建广告方</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="inquiryRecordList" highlight-current-row max-height="72vh">
      <el-table-column prop="advertiserId" label="广告方ID" align="center" />
      <el-table-column
        prop="advertiserName"
        label="广告方名称（广告方）"
        align="center"
      />
      <el-table-column prop="typeName" label="所属广告方类型" align="center" />
      <el-table-column prop="campaignCount" label="计划个数" align="center" />
      <el-table-column prop="adCount" label=" 广告个数" align="center" />
      <el-table-column prop="valid" label="是否有效" align="center">
        <template v-slot="scope">
          <el-switch
            v-model="scope.row.valid"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="changeStatus(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="210">
        <template v-slot="scope">
          <el-button
            type="primary"
            link
            size="small"
            @click="goToDetail(scope.row)"
            >查看详情</el-button
          >
          <el-button
            type="primary"
            link
            size="small"
            @click="openEdit(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-if="scope.row.typeId === 3"
            type="primary"
            link
            size="small"
            @click="billingInfoManage(scope.row)"
            >开票信息管理</el-button
          >
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template v-slot="scope">{{ scope.row.createTime }}</template>
      </el-table-column>
      <el-table-column label="操作日志" align="center">
        <template v-slot="scope">
          <el-button type="primary" link size="small" @click="seeLog(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="20"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      style="justify-content: center; margin-top: 15px"
      @current-change="currentChange"
    />
    <el-dialog v-model="dialogFormVisible" :title="title" width="30%">
      <el-form :model="otherForm" label-position="right" label-width="200px">
        <el-form-item label="所属广告方类型">
          <el-select
            style="width: 200px"
            v-model="otherForm.type"
            clearable
            @change="change"
            :disabled="!!otherForm.advertiserId"
          >
            <el-option
              v-for="(value, index) in advertisingType"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="otherForm.type === 1"
          label="关联品牌（广告方名称）"
        >
          <el-input
            v-model="shopName"
            :disabled="!!otherForm.advertiserId && !!shopName"
            :model-value="shopName"
            placeholder="单选品牌"
            style="width: 200px"
            @click="chooseBrand()"
          />
        </el-form-item>
        <el-form-item v-if="otherADVisible" label="第三方广告类型">
          <el-select
            v-model="otherForm.thirdLinkType"
            clearable
            style="width: 200px"
            :disabled="!!otherForm.advertiserId"
          >
            <el-option
              v-for="(value, index) in otherAdvertisingTypeList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告方名称（第三方）">
          <el-input
            v-model="otherForm.advertiserName"
            type="text"
            placeholder="请输入广告方名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item
          v-if="otherForm.type === 2"
          label="关联经销商（广告方名称）"
        >
          <el-input
            v-model="shopName"
            :model-value="shopName"
            placeholder="单选经销商"
            style="width: 200px"
            @click="chooseShop()"
            :disabled="!!otherForm.advertiserId && !!shopName"
          />
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="add">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="billingDialogVisible" title="开票信息管理" width="80%">
      <div class="billing-dialog">
        <el-button @click="$refs.invoicingManagement.addData({})"
          >新增</el-button
        >
      </div>
      <InvoicingManagement ref="invoicingManagement" />
    </el-dialog>
    <ChooseDialogBrand ref="ChooseDialogBrand" @sendData="getBrandData" />
    <ChooseDialogShop ref="ChooseDialogShop" @sendData="sendShopData" />
    <SeeLog2 ref="SeeLog" />
  </div>
</template>

<script>
import { effective, advertisingType } from '@/utils/enum/adConfigEnum.js'
import {
  getAdvertisingManageList,
  getOtherAdvertisingTypeList,
  postAdvertisingTypeAdd,
  postAdvertisingTypeUpdate,
  postAdvertisingStatueClose,
  postAdvertisingStatueOpen
} from '@/api/advertModuleNew'
import { GetBrandAllList } from '@/api/garage'
import { platformList, platformList2 } from '@/utils/enum'
import ChooseDialogShop from '@/components/Dialog/ChooseDialogShop.vue'
import ChooseDialogBrand from '@/components/Dialog/ChooseDialogBrand.vue'
import SeeLog2 from '../components/see-log.vue'
import InvoicingManagement from '../../../garage/factotyManagment/factoryManagement/components/invoicingManagement.vue'

export default {
  name: 'AdvertisingManageList',
  components: {
    ChooseDialogShop,
    ChooseDialogBrand,
    SeeLog2,
    InvoicingManagement
  },
  data() {
    return {
      effective: effective, // 是否有效
      dialogFormVisible: false, //创建广告dialog
      otherADVisible: false, //三方广告类型 展示
      otherAdvertisingTypeList: [], //第三方广告类型列表
      editing: false, //编辑
      title: '创建广告方',
      otherForm: {
        advertiserId: '',
        type: '',
        thirdLinkType: '',
        typeName: '',
        advertiserName: ''
      },
      ruleForm: {
        status: '', // 是否有效1有效0无效
        advertiserName: '', // 广告名称
        typeId: '', // 广告类型
        limit: 20, // 数量
        advertiserId: '', // 广告id
        relationId: ''
      },
      advertisingType,
      advertisingTypeWithAll: { 全部: '', ...advertisingType }, // 广告计划类型
      page: 1, // 页码
      total: 0, // 总数
      shopName: '',
      inquiryRecordList: [], // 配置内容数据
      platformList,
      platformList2,
      billingDialogVisible: false
    }
  },
  computed: {},
  activated() {},
  mounted() {
    this.search()
  },
  methods: {
    // 改变是否有效
    changeStatus(item) {
      const me = this
      const postAvertising = !item.valid
        ? postAdvertisingStatueClose
        : postAdvertisingStatueOpen
      postAvertising({
        id: item.advertiserId
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('修改成功')
          } else {
            item.valid = !item.valid
            me.$message.error('修改失败')
          }
        })
        .catch(() => {
          item.valid = !item.valid
        })
    },
    // 更新页码
    currentChange(page) {
      const me = this
      me.page = page
      me.inquiryRecordList = []
      me.getList({
        page: page
      })
    },
    // 查询
    search() {
      const me = this
      me.page = 1
      me.inquiryRecordList = []
      me.getList({ page: 1 })
    },
    // 获取列表数据
    getList(paramsObj) {
      const me = this
      const requestParams = {
        ...me.ruleForm,
        ...paramsObj
      }
      getAdvertisingManageList(requestParams)
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            me.inquiryRecordList = data.list
            me.page = requestParams.page
            me.total = data.total
          }
        })
        .catch(() => {})
    },
    // 创建广告方
    add() {
      const me = this
      const { type, advertiserName, thirdLinkType, relationId } = this.otherForm
      if (!type) {
        return this.$message.error('请选择所属广告方类型')
      }
      if (!thirdLinkType && type == 4) {
        return this.$message.error('请选择第三方广告类型')
      }
      if (!advertiserName) {
        return this.$message.error('请输入广告方名称（第三方）')
      }
      if (type === 2 && !relationId) {
        return this.$message.error('请选择经销商')
      }
      if (this.editing) {
        //修改
        postAdvertisingTypeUpdate({
          id: this.otherForm.advertiserId,
          name: this.otherForm.advertiserName,
          type: this.otherForm.type,
          thirdLinkType: this.otherForm.thirdLinkType,
          relationId: me.otherForm.relationId
        }).then((response) => {
          if (response.data.code === 0) {
            me.dialogFormVisible = false
            this.search()
          }
        })
      } else {
        //创建
        postAdvertisingTypeAdd({
          type: this.otherForm.type,
          thirdLinkType: this.otherForm.thirdLinkType,
          name: this.otherForm.advertiserName,
          relationId: me.otherForm.relationId
        }).then((response) => {
          if (response.data.code === 0) {
            this.dialogFormVisible = false
            this.search()
          }
        })
      }
    },
    //重置
    resetData() {
      this.ruleForm = {
        status: '', // 是否有效1有效0无效
        advertiserName: '', // 广告名称
        typeId: '', // 广告类型
        advertiserId: '', // 广告id
        relationId: ''
      }
      this.search()
    },
    // 查看配置详情
    goToDetail(row) {
      const me = this
      sessionStorage.setItem('AdvertisingPlanListNew', true)
      me.$router.push({
        name: 'AdvertisingPlanList',
        query: {
          advertiserId: row.advertiserId,
          advertiserName: row.advertiserName,
          typeId: row.typeId
        }
      })
    },

    //获取第三方广告类型列表
    getOtherAdvertisingTypeList() {
      getOtherAdvertisingTypeList()
        .then((response) => {
          if (response.data.code === 0) {
            this.otherAdvertisingTypeList = response.data.data
          }
        })
        .catch(() => {})
    },
    //编辑
    openEdit(row) {
      const me = this
      me.shopName = row.shopName || ''
      this.change(row.typeId ? row.typeId : '')
      this.otherForm = {
        advertiserId: row.advertiserId || '',
        type: row.typeId || '',
        advertiserName: row.advertiserName || '',
        thirdLinkType: row.thirdLinkType || '',
        typeName: row.typeName || '',
        relationId: row.relationId || ''
      }
      //编辑
      if (row.typeId) {
        this.editing = true
        me.title = '编辑广告方'
        me.shopName = row.relationName || ''
      } else {
        me.editing = false
        me.title = '创建广告方'
      }
      me.dialogFormVisible = true
    },
    change(item) {
      if (item == 4) {
        this.getOtherAdvertisingTypeList()
        this.otherADVisible = true
      } else {
        this.otherADVisible = false
      }
      this.shopName = ''
      this.otherForm.relationId = ''
      this.otherForm.advertiserName = ''
    },
    // 选择经销商
    chooseShop() {
      this.$refs.ChooseDialogShop.init({
        title: '选择经销商',
        labels: [],
        getListUrl: '',
        selectLimit: 1,
        canChoose: true,
        needSelfSearchShop: true
      })
    },
    sendShopData(result) {
      const target = result && result[0]
      this.otherForm.relationId = target.shopId || ''
      this.shopName = target.shopName || ''
      this.otherForm.advertiserName = target.shopName
    },
    // 选择品牌
    chooseBrand() {
      this.$refs.ChooseDialogBrand &&
        this.$refs.ChooseDialogBrand.init({
          title: '选择品牌',
          multiple: false,
          getListUrl: GetBrandAllList,
          canChoose: true
        })
    },
    // 获取返回的品牌信息
    getBrandData(data) {
      const target = (data.labels && data.labels[0]) || {}
      this.otherForm.relationId = target.brandId || ''
      this.shopName = target.brandName || ''
      this.otherForm.advertiserName = target.brandName
    },
    // 查看日志
    seeLog(data) {
      this.$refs.SeeLog.init({
        id: data.advertiserId,
        urlType: 1
      })
    },
    billingInfoManage(data) {
      this.billingDialogVisible = true
      this.$nextTick(() => {
        if (this.$refs.invoicingManagement) {
          this.$refs.invoicingManagement.search(data.advertiserId, 2)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.advertising-manage-list {
  padding: 10px 20px;
  .inquiryRecordList {
    width: 100%;
    height: 75vh;
    overflow-y: auto;

    .provinceAddress {
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .cityAddress {
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .dialog-footer {
    text-align: center;
  }

  .billing-dialog {
    text-align: right;
    margin-bottom: 15px;
  }
}
</style>
