<template>
  <div v-loading="loading" style="margin: 20px 20px 0">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="展示位置" required>
        <el-select v-model="ruleForm.location">
          <el-option
            v-for="(value, index) in locationList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="平台" prop="platformId" required>
        <el-select v-model="ruleForm.platformId">
          <el-option
            v-for="(value, index) in platformList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="封面图片" required>
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccess"
          name="upfile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <img
            v-if="ruleForm.picture"
            :src="ruleForm.picture"
            style="max-width: 400px; max-height: 400px"
            alt
          />
          <el-button type="primary" style="margin-left: 20px"
            >上传图片</el-button
          >
        </el-upload>
      </el-form-item>
      <el-form-item label="名称" required>
        <el-input
          v-model="ruleForm.name"
          placeholder="前台不展示"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="链接">
        <el-input
          v-model="ruleForm.link"
          placeholder="请输入链接地址"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="ruleForm.type">
          <el-option
            v-for="(value, index) in typeList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="有效期开始时间" required>
        <el-date-picker
          v-model="ruleForm.beginDateStr"
          type="datetime"
          value-format="x"
          placeholder="选择日期时间"
        />
      </el-form-item>

      <el-form-item label="有效期结束时间" required>
        <el-date-picker
          v-model="ruleForm.endDateStr"
          type="datetime"
          value-format="x"
          placeholder="选择日期时间"
        />
      </el-form-item>
      <el-form-item label="用户">
        <el-select v-model="ruleForm.userType">
          <el-option
            v-for="(value, index) in userTypeList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="打开频次">
        <el-select v-model="ruleForm.frequency">
          <el-option
            v-for="(value, index) in frequencyList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="打开时刻">
        第
        <el-input
          v-model="ruleForm.showTime"
          :min="1"
          :max="99999"
          type="number"
          style="width: 100px"
          @change="updataShowTime(ruleForm.showTime)"
        />次进入页面时展示
        <el-button type="primary" @click="setShowTime('up', ruleForm)"
          >+</el-button
        >
        <el-button type="primary" @click="setShowTime('down', ruleForm)"
          >-</el-button
        >
      </el-form-item>
      <el-form-item label="按照城市投放">
        <el-form :inline="true" class="demo-form-inline">
          <div
            v-for="(city, index) in cityList"
            :key="index"
            class="city-name-content"
          >
            <div
              :class="{ 'city-name-tip': !city.name }"
              class="city-name"
              @click="addCity(city)"
            >
              {{
                city.name || '选择单省(可选部分地级市)，选择多省(含全部地级市)'
              }}
            </div>
            <el-button
              v-if="cityList.length > 1"
              type="primary"
              @click="delList(index)"
              >-</el-button
            >
            <el-button v-if="!id" type="primary" @click="addList()"
              >+</el-button
            >
          </div>
        </el-form>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm()">保存</el-button>
        <el-button type="danger" @click="goBack()">取消</el-button>
      </el-form-item>
    </el-form>
    <add-area ref="addArea" @backArea="backArea" />
  </div>
</template>

<script>
import { deepCopy } from '@/utils'
import { timeFullS } from '@/filters'
import { getBannerDetailById, saveBanner } from '@/api/advertModule'
import { bannerLocation, frequencyList } from '@/utils/enum'
import { pickerOptions } from '@/utils/configData'
import { convertKeyValueEnum } from '@/utils/convert'
import { mapGetters } from 'vuex'
import AddArea from './comment/add-area.vue'
import { batchRecordBeforeAlter } from '@/utils/enum/logData'
export default {
  name: 'PageBulletDetail',
  components: {
    AddArea
  },
  data() {
    return {
      id: '',
      oldShowTime: '1', // 记录历史的
      operation: '1', // 数值变更，默认加
      loading: false,
      pickerOptions: pickerOptions,
      ruleForm: {}, // 筛选列表
      typeList: {
        链接: 'link',
        签到页: 'my_energy',
        活动: 'activity',
        短话题: 'short_topic',
        文章详情: 'essay_detail',
        能量商城: 'energy_mall',
        有赞商城: 'yz_shop'
      },
      platformList: {
        全部: 'ANDROID,IOS',
        ios: 'IOS',
        android: 'ANDROID'
      },
      locationList: {
        首页: '11',
        选车: '18',
        用车: '19',
        我的: '22'
      },
      userTypeList: {
        全部: '',
        新用户: '1',
        老用户: '0'
      },
      convertUserTypeList: {},
      frequencyList: frequencyList,
      convertBannerLocation: convertKeyValueEnum(bannerLocation),
      convertFrequencyList: convertKeyValueEnum(frequencyList),
      cityList: [],
      rules: {}
    }
  },
  computed: {
    ...mapGetters(['uid'])
  },
  activated() {
    this.convertUserTypeList = convertKeyValueEnum(this.userTypeList)
    this.$route.query && this.$route.query.id ? this.getData() : this.initData()
    this.id = this.$route.query && this.$route.query.id
    sessionStorage.setItem('menu', 'S10403')
  },
  methods: {
    initData() {
      this.ruleForm = {
        location: '', // 展示位置
        platform: '0', // 来源
        platformId: 'ANDROID,IOS',
        picture: '', // 图片
        name: '', // 名称
        link: '', // 链接
        type: '', // 类型
        beginDateStr: '', // 开始时间
        endDateStr: '', // 结束时间
        userType: '', // 用户分类
        frequency: '2', // 打开频次
        showTime: '1' // 打开时刻
      }
      this.cityList = []
      this.addList()
    },
    getData() {
      const me = this
      me.loading = true
      me.cityList = []
      getBannerDetailById({
        bannerid: me.$route.query.id
      })
        .then((response) => {
          me.loading = false
          if (response.status === 200) {
            me.ruleForm = response.data.data
            batchRecordBeforeAlter(me.ruleForm, me.$route.query.id)
            Object.values(me.locationList).map(function (value, index) {
              me.ruleForm.location =
                me.ruleForm.location === parseInt(value)
                  ? Object.keys(me.locationList)[index]
                  : me.ruleForm.location
            })
            Object.values(me.frequencyList).map(function (value, index) {
              me.ruleForm.frequency =
                me.ruleForm.frequency === parseInt(value)
                  ? Object.keys(me.frequencyList)[index]
                  : me.ruleForm.frequency
            })
            me.cityList.push({
              name:
                me.ruleForm.province || me.ruleForm.city
                  ? `${me.ruleForm.province}  ${me.ruleForm.city}`
                  : '',
              city: me.ruleForm.city,
              province: me.ruleForm.province
            })
            if (
              me.ruleForm.platformId === '1' ||
              me.ruleForm.platformId === '2'
            ) {
              me.ruleForm.platformId =
                me.ruleForm.platformId === '1' ? 'ANDROID' : 'IOS'
            } else {
              me.ruleForm.platformId = 'ANDROID,IOS'
            }
            me.ruleForm.beginDateStr = me.ruleForm.beginDateStr
              ? new Date(me.ruleForm.beginDateStr)
              : ''
            me.ruleForm.endDateStr = me.ruleForm.endDateStr
              ? new Date(me.ruleForm.endDateStr)
              : ''
            me.oldShowTime = me.ruleForm.showTime
          } else {
            me.loading = false
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.loading = false
          me.$message.error(err.message || '获取列表失败')
        })
    },
    // 改变打开失效
    setShowTime(type, data) {
      const me = this
      if (type === 'down' && data.showTime - 1 < 0) {
        return me.$message.error('最小为1')
      }
      me.operation = type === 'down' ? '0' : '1'
      type === 'down' ? data.showTime-- : data.showTime++
    },
    updataShowTime(value) {
      if (value === '0') {
        this.ruleForm.showTime = '1'
        this.$message.error('最小为1')
        return
      }
      this.operation = value >= this.oldShowTime ? '1' : '0'
      console.log(this.operation)
      this.oldShowTime = value
    },
    // 上传图片
    // updateImg(res) {
    //   if (res.code !== 0) {
    //     this.$message.error(res.msg)
    //     return
    //   }
    //   this.$set(this.ruleForm, 'picture', res.data)
    // },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      this.$oss.ossUploadImage(option)
    },
    onSuccess(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        this.ruleForm['picture'] = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 增加列表
    addList() {
      this.cityList.push({
        name: '',
        city: '',
        province: ''
      })
    },
    // 删除列表
    delList(index) {
      this.cityList.splice(index, 1)
    },
    // 增加城市
    addCity(data) {
      let cityList = []
      if (this.cityList[0] && this.cityList[0].province) {
        this.cityList.map((_) => {
          cityList = _.province ? cityList.concat(deepCopy(_)) : cityList
        })
      }
      const province = data.province ? deepCopy(data) : {}
      this.$refs.addArea.init(cityList, province, !!data.province)
    },
    // 返回地址
    backArea(data) {
      console.log(data)
      this.cityList = []
      this.cityList = data
      if (!(this.$route.query && this.$route.query.id)) {
        this.addList()
      }
    },
    // 提交
    submitForm() {
      const me = this
      if (!me.ruleForm.location) {
        return me.$message.error('请选择展示位置')
      }
      if (!me.ruleForm.platformId) {
        return me.$message.error('请选择展示平台')
      }
      if (!me.ruleForm.name) {
        return me.$message.error('请填写名称')
      }
      if (!me.ruleForm.picture) {
        return me.$message.error('请上传封面')
      }
      if (!me.ruleForm.showTime || me.ruleForm.showTime === '0') {
        return me.$message.error('请选择次数或次数不能为0')
      }
      if (!me.ruleForm.beginDateStr) {
        return me.$message.error('请填写开始时间')
      }
      if (!me.ruleForm.endDateStr) {
        return me.$message.error('请填写结束时间')
      }
      if (me.ruleForm.beginDateStr > me.ruleForm.endDateStr) {
        return me.$message.error('开始时间不得大于结束时间')
      }
      me.loading = true
      const task = [] // 临时存储数组
      let currentIndex = 0 // 当前上传张数
      let isEnd = false // 是否是最后一张判定
      me.cityList.map(function (value, index) {
        task.push(sendPostPromise(me.ruleForm, value)) // 添加数据
      })
      // 添加多段
      Promise.all(task).then(
        (responseAll) => {
          responseAll &&
            responseAll.map(function (response, index) {
              if (response && response.data.code === 1001) {
                me.$message.error(`可使用${response.data.data}`)
                me.loading = false
                return
              } else if (response && response.data.code !== 0) {
                me.$message.error(response.data.msg)
                me.loading = false
                return
              }
              currentIndex++
              isEnd = currentIndex === me.cityList.length
              if (isEnd) {
                const tip =
                  me.$route.query && me.$route.query.id ? '编辑' : '添加'
                me.$message.success(`${tip}成功`)
                me.loading = false
                setTimeout(() => {
                  me.$router.go(-1)
                }, 2000)
              }
            })
        },
        (fail) => {
          me.loading = false
          currentIndex++
          return me.$message.error(fail.message)
        }
      )
      function sendPostPromise(data, value) {
        const postData = deepCopy(data)
        delete postData.createtime
        delete postData.updatetime
        delete postData.platformAndroid
        delete postData.platformIOS
        delete postData.platformPC
        delete postData.platformWAP
        postData.status = postData.status || '1'
        me.operation = postData.bannerid ? me.operation : '1'
        postData.operation = me.operation
        postData.beginDateStr = timeFullS(
          Math.floor(new Date(data.beginDateStr))
        )
        postData.endDateStr = timeFullS(Math.floor(new Date(data.endDateStr)))
        postData.province = value.province
        postData.city = value.city
        return saveBanner(postData)
      }
    },
    // 取消
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.city-name-content {
  margin-bottom: 10px;
}
.city-name-tip {
  color: #999 !important;
}
.city-name {
  display: inline-block;
  margin-bottom: 5px;
  width: 400px;
  height: 36px;
  line-height: 36px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  color: #606266;
  padding: 0 15px;
}
</style>
