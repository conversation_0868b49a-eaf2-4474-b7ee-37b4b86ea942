<template>
  <div>
    <el-dialog
      v-model="visibleStatus"
      title="选择省市区位置"
      class="dialog-content"
      append-to-body
    >
      <div v-if="provinceAllData.length" class="show-content">
        当前已选择:
        <div
          v-for="(province, index) in provinceAllData"
          :key="index"
          class="province-list"
        >
          {{ province.province }}&ensp;&ensp;&ensp;{{
            province.city
          }}&ensp;&ensp;&ensp;
        </div>
      </div>
      <div class="content">
        <el-select
          v-if="!isEdit"
          v-model="provinceName"
          placeholder="请选择省市"
          clearable
          multiple
          @change="setChangeData()"
          @clear="clearDataName('province')"
        >
          <el-option
            v-for="(value, index) in provinceList"
            :key="index"
            :label="value.name"
            :value="value.name"
          />
        </el-select>
        <el-select
          v-else
          v-model="provinceName"
          placeholder="请选择省市"
          clearable
          @change="setChangeData()"
          @clear="clearDataName('province')"
        >
          <el-option
            v-for="(value, index) in provinceList"
            :key="index"
            :label="value.name"
            :value="value.name"
          />
        </el-select>
        <el-select
          v-model="cityName"
          :disabled="cityStatus"
          placeholder="请选择城市"
          clearable
          multiple
          @clear="clearDataName('city')"
        >
          <el-option
            v-for="(value, index) in cityList"
            :key="index"
            :label="value.name"
            :value="value.name"
          />
        </el-select>
      </div>
      <div class="food">
        <el-button type="success" @click="confirm(true)">确认</el-button>
        <el-button type="danger" @click="confirm(false)">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { GetArea } from '@/api/searchMap'
export default {
  name: 'AddArea',
  data() {
    return {
      type: '', // 变更或新增
      backCityName: '', // 返回的市名称
      index: undefined, // 所引致，替换所引值
      provinceName: undefined, // 省级名
      provinceCode: undefined, // 省份代码
      provinceAllData: [], // 已选数据
      cityName: [], // 城市位置
      provinceList: [], // 省列表
      cityList: [], // 城市列表
      isEdit: false, // 是否是编辑
      visibleStatus: false, // 是否显示
      cityStatus: false, // 城市选择是否禁用
    }
  },
  activated() {
    if (this.provinceList.length === 0) {
      this.getProvice()
    }
  },
  methods: {
    init(allData, data, type) {
      this.isEdit = type
      this.visibleStatus = true
      this.cityStatus = false
      this.provinceName = undefined
      this.provinceCode = undefined
      this.provinceAllData = allData
      this.cityName = []
      this.cityList = []
      this.backCityName = data.city || ''
      this.index =
        allData.findIndex((_) => {
          return _.province === data.province
        }) > -1
          ? allData.findIndex((_) => {
              return _.province === data.province
            })
          : undefined
      if (data.province) {
        this.provinceName = this.isEdit
          ? data.province
          : this.provinceName.concat([data.province])
        this.readyCityList()
      }
    },
    // 获取地理位置
    getProvice() {
      const me = this
      GetArea().then((res) => {
        const list = res.data.data && res.data.data.list
        if (!list.length) {
          return
        }
        me.provinceList = list
      })
    },
    // 获取地理位置 城市
    getCity() {
      const me = this
      const provinceCode =
        typeof me.provinceCode === 'string'
          ? me.provinceCode
          : me.provinceCode[0]
      const params = {
        provinceCode: provinceCode,
      }
      GetArea(params).then((res) => {
        const list = res.data.data && res.data.data.list
        if (!list.length) {
          return
        }
        me.cityList = list
        if (me.backCityName) {
          me.cityName = me.backCityName.split(',')
          me.backCityName = ''
        }
      })
    },
    // 设置省份数据
    setChangeData(type) {
      const me = this
      me.cityStatus = false
      const isString = typeof me.provinceName === 'string'
      me.cityName = []
      me.cityList = []
      if (
        !me.isEdit &&
        !isString &&
        me.provinceName.length &&
        me.provinceName.length > 1
      ) {
        return (me.cityStatus = true)
      }
      me.readyCityList()
    },
    // 准备请求城市数据
    readyCityList() {
      const me = this

      const isString = typeof me.provinceName === 'string'
      const filterData = me.provinceList.filter(
        (_) => _.name === (isString ? me.provinceName : me.provinceName[0])
      )
      me.provinceCode = filterData[0].provinceCode
      me.getCity()
    },
    // 清除数据
    clearDataName(type) {
      if (type === 'city') {
        this.cityName = []
      } else {
        this.provinceName = []
        this.provinceCode = []
      }
    },
    // 设置发送数据
    setPostData(province, city) {
      const data = {
        province: typeof province === 'string' ? province : province[0],
        city: city.length ? city.join() : '',
      }
      data.name = `${data.province} ${data.city}`
      return data
    },
    // 确认位置
    confirm(type) {
      const me = this
      if (!type) {
        me.visibleStatus = false
        return
      }
      if (!(me.provinceName && me.provinceName.length) && !me.cityName.length) {
        me.$message.error('请选择省市地址')
        return
      }
      let data
      if (typeof me.provinceName === 'string' || me.provinceName.length === 1) {
        data = me.setPostData(me.provinceName, me.cityName)
      } else {
        data = []
        me.provinceName.map((_) => {
          data.push(me.setPostData(_, ''))
        })
      }
      me.index !== undefined
        ? me.provinceAllData.splice(me.index, 1, data)
        : (me.provinceAllData = me.provinceAllData.concat(data))
      const backData = []
      me.provinceAllData.map((v) => {
        const index = backData.findIndex((_) => {
          return _.province === v.province
        })
        index > -1 ? backData.splice(index, 1, v) : backData.push(v)
      })
      me.visibleStatus = false
      $emit(me, 'backArea', backData)
    },
  },
  emits: ['backArea'],
}
</script>

<style scoped>
.el-dialog__body {
  padding: 0 20 30px 20;
}
.show-content {
  padding-bottom: 10px;
  font-size: 16px;
  line-height: 18px;
}
.province-list {
  font-size: 14px;
  line-height: 16px;
  margin-bottom: 5px;
}
.content {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 10px;
}
</style>
