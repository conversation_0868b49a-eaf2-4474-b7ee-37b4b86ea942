<template>
  <div class="advertisingDealerList">
    <header style="margin-bottom: 10px">
      <el-form :model="ruleForm" :inline="true">
        <el-form-item label="广告ID">
          <el-input
            v-model="ruleForm.adUnitId"
            type="text"
            placeholder="请输入广告ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="一级页面">
          <el-select
            v-model="ruleForm.siteSetFirstId"
            @change="changeLevelPage(1)"
            clearable
          >
            <el-option
              v-for="(value, index) in siteSetFirsetList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="二级页面">
          <el-select
            v-model="ruleForm.siteSetSecondId"
            @change="changeLevelPage(2)"
            clearable
          >
            <el-option
              v-for="(value, index) in siteSetSecondList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="三级页面">
          <el-select v-model="ruleForm.siteSetThirdId" clearable>
            <el-option
              v-for="(value, index) in siteSetThirdList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告类型">
          <el-select v-model="ruleForm.adTypeId" clearable>
            <el-option
              v-for="(value, index) in typeList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属广告方ID">
          <el-input
            v-model="ruleForm.orgId"
            type="text"
            placeholder="请输入所属广告方ID"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="所属广告方">
          <el-input
            v-model="ruleForm.orgName"
            type="text"
            placeholder="请输入所属广告方"
            clearable
            style="width: 180x"
          />
        </el-form-item>
        <el-form-item label="所属广告方类型">
          <el-select v-model="ruleForm.orgType" clearable>
            <el-option
              v-for="(value, index) in advertisingType"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="计划ID">
          <el-input
            v-model="ruleForm.campaignId"
            type="text"
            placeholder="请输入计划ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="计划名称">
          <el-input
            v-model="ruleForm.campaignName"
            type="text"
            placeholder="请输入计划名称"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="广告名称">
          <el-input
            v-model="ruleForm.adName"
            type="text"
            placeholder="请输入广告名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="广告位置">
          <el-select v-model="ruleForm.position" clearable>
            <el-option
              v-for="(value, index) in advertisingPositionRange"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告刷数">
          <el-select v-model="ruleForm.refreshCount" clearable>
            <el-option
              v-for="(value, index) in advertisingCountRange"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="ruleForm.statusType">
            <el-option
              v-for="(value, index) in statusTypeList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否有效">
          <el-select v-model="ruleForm.status">
            <el-option
              v-for="(value, index) in effective"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="投放区域(省)">
          <el-input
            v-model="ruleForm.province"
            type="text"
            placeholder="投放区域(省)"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="投放区域(市)">
          <el-input
            v-model="ruleForm.city"
            type="text"
            placeholder="投放区域(市)"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="有效时间段" label-width="100px">
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="daterange"
            style="width: 400px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="至"
            start-placeholder="发布开始日期"
            end-placeholder="发布结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="是否独占">
          <el-select v-model="ruleForm.exclusiveFlag">
            <el-option
              v-for="(value, index) in exclusiveFlagList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="投放品牌">
          <el-select
            v-model="ruleForm.brandName"
            :remote-method="remoteMethodBrandName"
            :loading="loading"
            placeholder="请输入品牌名"
            filterable
            remote
            clearable
            style="width: 200px"
            @clear="clearBrandName()"
            @change="setBrandName"
          >
            <el-option
              v-for="item in brandList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="投放车型">
          <el-select
            v-model="ruleForm.carName"
            :remote-method="remoteMethodCarName"
            :loading="loading"
            placeholder="请输入车型名"
            filterable
            remote
            clearable
            style="width: 200px"
            @clear="clearCarName()"
            @change="setCarName"
          >
            <el-option
              v-for="item in carList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="primary" @click="resetData">重置</el-button>
          <el-button type="primary" @click="openEdit">创建广告</el-button>
          <!-- <aliUpload></aliUpload> -->
        </el-form-item>
      </el-form>
    </header>
    <div style="min-height: 80vh">
      <el-table
        ref="inquiryRecordList"
        :data="inquiryRecordList"
        class="inquiryRecordList"
        highlight-current-row
        row-key="inquiryRecordList"
        max-height="650"
        @row-dblclick="openEdit"
      >
        <el-table-column prop="adUnitId" label="广告ID" align="center" />
        <el-table-column prop="siteSetFirst" label="一级页面" align="center" />
        <el-table-column prop="siteSetSecond" label="二级页面" align="center" />
        <el-table-column prop="siteSetThird" label="三级页面" align="center" />
        <el-table-column prop="adTypeName" label="广告类型" align="center" />
        <el-table-column
          prop="orgId"
          label="所属广告方ID"
          align="center"
          width="120px"
        />
        <el-table-column
          prop="orgName"
          label="所属广告方"
          align="center"
          width="100px"
        />
        <el-table-column
          prop="advertiserTypeName"
          label="所属广告方类型"
          align="center"
          width="150px"
        />
        <el-table-column prop="campaignId" label="计划ID" align="center" />
        <el-table-column prop="campaignName" label="计划名称" align="center" />
        <el-table-column prop="adName" label="广告名称" align="center" />
        <el-table-column prop="position" label="广告位置" align="center" />
        <el-table-column prop="refreshCount" label="广告刷数" align="center" />
        <el-table-column prop="exclusiveFlag" label="是否独占" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.exclusiveFlag === 1 ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="region" label="投放区域" align="center" />
        <el-table-column
          prop="showBrandNames"
          label="投放品牌"
          align="center"
        />
        <el-table-column
          prop="showGoodsNames"
          label="投放车型"
          align="center"
        />
        <el-table-column prop="exposurePv" label="曝光PV" align="center" />
        <el-table-column prop="clickPv" label="点击PV" align="center" />
        <el-table-column
          prop="region"
          label="广告期限"
          align="center"
          width="200px"
        >
          <template v-slot="scope">
            <span
              >{{ scope.row.beginTime }}&ensp;&ensp;{{
                scope.row.endTime
              }}</span
            >
          </template>
        </el-table-column>
        <el-table-column prop="statusDesc" label="状态" align="center" />
        <el-table-column align="center" label="操作" width="150px">
          <template v-slot:header>
            <span>操作</span>
            <el-tooltip placement="bottom">
              <template v-slot:content>
                <div>
                  说明：<br />
                  复制：支持复制广告；<br />
                  暂停：暂停广告后，剩余投放量仍保留；<br />
                  恢复：恢复广告后，继续投放广告；
                </div>
              </template>
              <el-button type="primary" link>?</el-button>
            </el-tooltip>
          </template>
          <template v-slot="scope">
            <el-button size="small" type="primary" @click="copyRow(scope.row)"
              >复制</el-button
            >
            <el-button
              v-if="['已暂停'].includes(scope.row.statusDesc)"
              size="small"
              type="primary"
              @click="moreIOperation(scope.row, 1)"
              >恢复</el-button
            >
            <el-button
              v-if="['未开始', '进行中'].includes(scope.row.statusDesc)"
              size="small"
              type="primary"
              @click="moreIOperation(scope.row, 0)"
              >暂停</el-button
            >
            <el-button
              v-if="['未启动'].includes(scope.row.statusDesc)"
              size="small"
              type="primary"
              @click="moreIOperation(scope.row, 1)"
              >启动</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="operatorName" label="创建人" align="center" />
        <el-table-column
          prop="createTime"
          label="创建时间"
          align="center"
          width="200px"
        />
        <el-table-column prop="showStatus" label="是否有效" align="center">
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.showStatus"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="changeStatus(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作日志" align="center">
          <template v-slot="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click="seeLog(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="page"
        :page-size="20"
        :page-sizes="[10, 20, 40, 60]"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        style="text-align: center; margin-top: 10px"
        @size-change="currentChange"
        @current-change="currentChange"
      />
      <el-dialog v-model="dialogFormVisible" title="提示" width="30%">
        <div>
          {{ `本次广告与${repeatData.repeatId}(广告ID)重复，是否继续配置` }}
        </div>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button @click="goToCheck">去查看</el-button>
            <el-button
              v-if="!repeatData.shopFlag"
              type="primary"
              @click="continueSet"
              >继续配置</el-button
            >
          </div>
        </template>
      </el-dialog>
      <SeeLog2 ref="SeeLog" />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  effective,
  advertisingType,
  advertisingRange
} from '@/utils/enum/adConfigEnum.js'
import { pickerOptions } from '@/utils/configData'
import {
  getAdvertisingList,
  postAdvertisingStateUpdate,
  getAdvertiserTypeAll,
  getAdvertisingNextLevelPage
} from '@/api/advertModuleNew'
import { platformList, platformList2 } from '@/utils/enum'
import { searchBrandList } from '@/api/brand'
import { searchCarList } from '@/api/garage'
import SeeLog2 from '../components/see-log.vue'
export default {
  data() {
    return {
      pickerOptions,
      // 是否有效
      effective: effective,
      //创建广告dialog
      dialogFormVisible: false,
      // 加载状态
      loading: false,
      //一级页面list
      siteSetFirsetList: [],
      //二级页面list
      siteSetSecondList: [],
      //
      siteSetThirdList: [],
      //已存在广告
      existAD: '',
      //广告位置位置
      advertisingPositionRange: advertisingRange,
      //广告刷数
      advertisingCountRange: advertisingRange,
      exclusiveFlagList: {
        全部: '',
        是: 1,
        否: 0
      },
      otherForm: {
        type: '',
        thirdLinkType: '',
        typeName: '',
        advertiserName: ''
      },
      ruleForm: {
        status: '', // 是否有效1有效0无效
        adUnitId: '', // 广告id
        siteSetFirstId: '', //一级页面id
        siteSetSecondId: '', //二级页面id
        siteSetThirdId: '', //三级页面id
        adTypeId: '', //广告类型id
        orgId: '', //所属广告方id
        orgName: '', //所属广告方名称
        orgType: '', //所属广告方类型
        campaignId: '', //计划id
        campaignName: '', //计划名称
        adName: '', //广告名称
        refreshCount: '', //第几刷
        position: '', //广告位置
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        province: '', //省
        city: '', //城市
        exclusiveFlag: '', // 是否独占
        showBrandId: '', // 投放品牌id
        showGoodsId: '', // 投放车型id
        statusType: '' // 状态
      },
      statusTypeList: {
        全部: '',
        未启动: 1,
        未开始: 2,
        进行中: 3,
        已暂停: 4,
        到期结束: 5,
        到量结束: 6,
        已无效: 7
      },
      //广告方类型
      advertisingType: { 全部: '', ...advertisingType },
      //广告类型
      advertiserType: [],
      //重复广告数据
      repeatData: { repeatId: '' },
      //所改变一行的数据
      changeRowData: {},
      // 页码
      page: 1,
      // 总数
      total: 0,
      // 配置内容数据
      inquiryRecordList: [],
      brandList: [],
      carList: [],
      platformList,
      platformList2,
      dayjs
    }
  },
  name: 'AdvertisingList',
  components: {
    // aliUpload
    SeeLog2
  },
  computed: {
    daterange: {
      get() {
        if (this.ruleForm.beginTime && this.ruleForm.endTime) {
          return [this.ruleForm.beginTime, this.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginTime = value[0]
          this.ruleForm.endTime = value[1]
        } else {
          this.ruleForm.beginTime = ''
          this.ruleForm.endTime = ''
        }
      }
    },
    // 广告类型列表
    typeList() {
      let typeList = [...this.advertiserType]
      if (this.ruleForm.siteSetFirstId) {
        this.siteSetFirsetList.map((_) => {
          if (_.id === this.ruleForm.siteSetFirstId) {
            typeList = _.typeList || []
          }
        })
        if (this.ruleForm.siteSetSecondId) {
          this.siteSetSecondList.map((_) => {
            if (_.id === this.ruleForm.siteSetSecondId) {
              typeList = _.typeList || []
            }
          })
          if (this.ruleForm.siteSetThirdId) {
            this.siteSetThirdList.map((_) => {
              if (_.id === this.ruleForm.siteSetThirdId) {
                typeList = _.typeList || []
              }
            })
          }
        }
      }
      return typeList
    }
  },
  async mounted() {
    this.getAdvertisingLevelPage(0)
    this.getAdvertiserTypeAll() //获取全部广告类型列表
    await this.setParams()
  },
  activated() {
    this.search()
  },
  methods: {
    setParams() {
      if (this.$route.query.repeatData) {
        this.repeatData = JSON.parse(
          decodeURIComponent(this.$route.query.repeatData)
        )
        return this.setSearchData()
      }
      const query = this.$route.query
      this.ruleForm.orgId = query.orgId
      this.ruleForm.orgName = query.orgName
      this.ruleForm.orgType = query.orgType
      this.ruleForm.campaignId = query.campaignId
      this.ruleForm.campaignName = query.campaignName
    },

    //获取全部广告类型
    getAdvertiserTypeAll() {
      getAdvertiserTypeAll({
        shopType: 1
      })
        .then((response) => {
          this.advertiserType = response.data.data || []
        })
        .catch((_) => {
          console.log(_)
        })
    },
    // 改变是否有效
    changeStatus(item) {
      const me = this
      if (item.showStatus) {
        item.showStatus = !item.showStatus
        return me.$message.error('广告无效后不可再开启，请重新创建')
      }
      me.$confirm(
        '无效后，广告不可再开启，且剩余投放量将释放，是否确认无效？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          me.changeRowData = item
          // console.log(item)
          // return
          me.checkStateUpdate(item.showStatus, false, -1)
        })
        .catch(() => {
          item.showStatus = !item.showStatus
        })
    },
    // 恢复/暂停，启动 操作
    moreIOperation(item, status) {
      this.changeRowData = item
      this.checkStateUpdate(status ? true : false, false, status)
    },
    //改变状态
    checkStateUpdate(isCheck, isContinue, status) {
      const item = this.changeRowData
      const me = this
      // console.log({
      postAdvertisingStateUpdate({
        adUnitId: item.adUnitId,
        status:
          status !== null ? status : item.showStatus || isContinue ? 1 : -1,
        checkConflict: isCheck ? 1 : 0 //校验是否有重复
      })
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            if (data && data.repeatId) {
              // item.showStatus = !item.showStatus
              this.repeatData = data
              this.dialogFormVisible = true
            } else {
              if (isContinue) {
                item.showStatus = !item.showStatus
              }
              me.$message.success('修改成功')
              me.getList({
                limit: 20,
                page: me.page
              })
            }
          } else {
            item.showStatus = false
          }
        })
        .catch((_) => {
          const err = _.response.data.code || ''
          if ([74, 75].includes(err)) return
          item.showStatus = !item.showStatus
          item.statusOld = !item.statusOld
          console.log(_)
        })
    },
    // 广告层级获取
    getAdvertisingLevelPage(level) {
      let list = ''
      let parentId = ''
      switch (level) {
        case 0:
          list = 'siteSetFirsetList'
          parentId = 0
          break
        case 1:
          list = 'siteSetSecondList'
          parentId = this.ruleForm.siteSetFirstId
          this.ruleForm.siteSetSecondId = ''
          this.ruleForm.siteSetThirdId = ''
          this.siteSetSecondList = []
          this.siteSetThirdList = []
          break
        case 2:
          list = 'siteSetThirdList'
          parentId = this.ruleForm.siteSetSecondId
          this.ruleForm.siteSetThirdId = ''
          this.siteSetThirdList = []
          break
      }
      // this.typeList = []
      this.ruleForm.adTypeId = ''
      if (!parentId && level) return
      this.getAdvertisingNextLevelPage(list, parentId)
    },
    getAdvertisingNextLevelPage(list, parentId) {
      getAdvertisingNextLevelPage({ parentId: parentId, shopType: 1 })
        .then((response) => {
          this[list] = response.data.data
        })
        .catch((_) => {
          console.log(_)
        })
    },
    changeLevelPage(level) {
      this.getAdvertisingLevelPage(level)
    },
    // 更新页码
    currentChange(page) {
      const me = this
      me.page = page
      me.inquiryRecordList = []
      me.getList({
        limit: 20,
        page: page
      })
    },
    // 查询
    search() {
      const me = this
      me.page = 1
      me.inquiryRecordList = []
      me.getList({
        limit: 20,
        page: 1
      })
    },
    // 获取列表数据
    getList(paramsObj) {
      const me = this
      const requestParams = {
        ...me.ruleForm,
        ...paramsObj,
        shopType: 1 // 仅经销商的数据
      }

      getAdvertisingList(requestParams)
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            data.listData.map((v) => {
              v.showStatus = v.status !== -1
              v.isRepeat = v.isRepeat == 1 ? '是' : '否'
              v.region = v.region ? v.region : '全国'
              v.showGoodsNames = me.splitBackData(v.showGoodsNames, 3, '个车型')
              v.region = me.splitBackData(v.region, 3, '个区域')
              v.showBrandNames = me.splitBackData(v.showBrandNames, 3, '个品牌')
              if (v.platform) {
                v.platform = v.platform
                  .split(',')
                  .map((_) => (_ = platformList[parseInt(_)]))
                  .join(' ')
              }
              return v
            })

            me.inquiryRecordList = data.listData
            me.page = requestParams.page
            me.total = data.total
          }
        })
        .catch((e) => {
          console.log(e)
        })
    },
    //去查看
    async goToCheck() {
      // this.resetDataOnly()
      // await this.setSearchData()
      // this.search()
      sessionStorage.setItem('AdvertisingListNew', true)
      const toTaskLog = this.$router.resolve({
        name: 'AdvertisingList',
        query: {
          repeatData: encodeURIComponent(JSON.stringify(this.repeatData))
        }
      })
      window.open(toTaskLog.href, '_blank')
      this.dialogFormVisible = false
    },
    //继续配置
    continueSet() {
      this.dialogFormVisible = false
      this.checkStateUpdate(false, true, 1)
    },
    async setSearchData() {
      // this.ruleForm = { ...this.ruleForm, ...this.repeatData }
      // delete this.ruleForm.repeatId
      // delete this.ruleForm.shopFlag
      await this.getAdvertisingNextLevelPage(
        'siteSetSecondList',
        this.repeatData.siteSetFirstId
      )
      await this.getAdvertisingNextLevelPage(
        'siteSetThirdList',
        this.repeatData.siteSetSecondId
      )
      this.ruleForm.adTypeId = this.repeatData.adTypeId
      this.ruleForm.refreshCount = this.repeatData.refreshCount
      this.ruleForm.position = this.repeatData.position
      this.ruleForm.beginTime = this.repeatData.beginTime
      this.ruleForm.endTime = this.repeatData.endTime
      this.ruleForm.province = this.repeatData.province
      this.ruleForm.city = this.repeatData.city
      this.ruleForm.siteSetFirstId = this.repeatData.siteSetFirstId
      this.ruleForm.siteSetSecondId = this.repeatData.siteSetSecondId
      this.ruleForm.siteSetThirdId = this.repeatData.siteSetThirdId
    },
    //初始化
    resetDataOnly() {
      this.ruleForm = {
        status: '', // 是否有效1有效0无效
        adUnitId: '', // 广告id
        siteSetFirstId: '', //一级页面id
        siteSetSecondId: '', //二级页面id
        siteSetThirdId: '', //三级页面id
        adTypeId: '', //广告类型id
        orgId: '', //对象id
        orgName: '', //对象名称
        orgType: '', //对象类型
        campaignId: '', //计划id
        campaignName: '', //计划名称
        adName: '', //广告名称
        refreshCount: '', //第几刷
        position: '', //广告位置
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        province: '', //省
        city: '', //城市
        exclusiveFlag: '', // 是否独占
        showBrandId: '', // 投放品牌id
        showGoodsId: '' // 投放车型id
      }
    },
    //重置
    resetData() {
      this.resetDataOnly()
      this.search()
    },
    // 查看配置详情
    goToDetail(row, copy) {
      const me = this
      // console.log(row)
      const params = {
        shopType: true
      }
      if (row.adUnitId) {
        params.id = row.adUnitId
      }
      if (copy) {
        params.copy = copy
      }
      me.$router.push({
        name: 'AdDetailsConfig',
        query: params
      })
    },

    copyRow(row) {
      this.goToDetail(row, true)
    },
    //进入广告配置
    openEdit(row) {
      this.goToDetail(row)
    },
    // 查询用户名称索引
    remoteMethodBrandName(query) {
      this.loading = true
      this.$tools.debounce(() => this.getBrandList(query), 300)()
    },
    getBrandList(query = '') {
      const me = this
      searchBrandList({
        brandName: query || me.ruleForm.brandName,
        page: 1,
        limit: 20
      })
        .then((response) => {
          me.brandList = []
          const result = response.data.data.list
          result.map(function (value) {
            const newObj = {
              name: value.brandName,
              id: value.brandId
            }
            me.brandList.push(newObj)
          })
        })
        .finally((_) => {
          me.loading = false
        })
    },
    // 展示用户名称,实际取userId
    setBrandName(id) {
      this.ruleForm.showBrandId = id
      this.brandList = []
    },
    clearBrandName() {
      this.ruleForm.brandName = ''
      this.ruleForm.showBrandId = ''
    },
    // 查询用户名称索引
    remoteMethodCarName(query) {
      this.loading = true
      this.$tools.debounce(() => this.getCarList(query), 300)()
    },
    getCarList(query = '') {
      const me = this
      searchCarList({
        name: query || me.ruleForm.carName,
        page: 1,
        limit: 20
      })
        .then((response) => {
          me.carList = []
          const result = response.data.data.list
          result.map(function (value) {
            const newObj = {
              name: value.goodName,
              id: value.goodId
            }
            me.carList.push(newObj)
          })
        })
        .finally((_) => {
          me.loading = false
        })
    },
    // 展示用户名称,实际取userId
    setCarName(id) {
      this.ruleForm.showGoodsId = id
      this.carList = []
    },
    clearCarName() {
      this.ruleForm.carName = ''
      this.ruleForm.showGoodsId = ''
    },
    // 查看日志
    seeLog(data) {
      this.$refs.SeeLog.init({
        id: data.adUnitId,
        urlType: 3
      })
    },
    // 仅展示部分数据
    splitBackData(data, postion, endTip) {
      let newData = data ? data.replace(/,/g, '、') : ''
      const showGoodsNamesLength = (newData && newData.split('、')) || 0
      if (showGoodsNamesLength.length > postion) {
        const showGoodsNamesList = newData.split('、')
        newData =
          newData.slice(0, newData.indexOf(showGoodsNamesList[postion]) - 1) +
          '等' +
          showGoodsNamesList.length +
          endTip
      }
      return newData
    }
  }
}
</script>

<style lang="scss" scoped>
.advertisingDealerList {
  padding: 10px 20px;
  .inquiryRecordList {
    width: 100%;
    height: 75vh;
    overflow-y: auto;

    .provinceAddress {
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .cityAddress {
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
