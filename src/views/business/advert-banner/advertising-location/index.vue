/** * 广告列表 */
<template>
  <div class="advertising-list" v-loading="loading">
    <header style="margin-bottom: 10px">
      <el-button type="primary" @click="addData">新增</el-button>
      <el-button type="primary" @click="goSeeLog">操作日志</el-button>
    </header>
    <p>不配置，默认随机展示</p>
    <div style="min-height: 80vh">
      <el-table
        ref="inquiryRecordList"
        :data="dataList"
        class="inquiryRecordList"
        highlight-current-row
        row-key="inquiryRecordList"
        max-height="650"
      >
        <el-table-column
          prop="siteSetFirst"
          label="一级页面"
          align="center"
          fixed
        />
        <el-table-column
          prop="siteSetSecond"
          label="二级页面"
          align="center"
          fixed
        />
        <el-table-column
          prop="siteSetThird"
          label="三级页面"
          align="center"
          fixed
        />
        <el-table-column
          prop="position"
          label="位置"
          align="center"
          width="110px"
        />
        <el-table-column prop="statusType" label="展示方式" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.showType === 1 ? '随机' : '轮巡' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="150px">
          <template v-slot="scope">
            <el-button size="small" type="primary" @click="copyRow(scope.row)"
              >编辑</el-button
            >
            <el-button
              size="small"
              type="danger"
              @click="beforeDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="page"
        :page-size="20"
        :page-sizes="[10, 20, 40, 60]"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        style="text-align: center; margin-top: 10px"
        @size-change="currentChange"
        @current-change="currentChange"
      />
      <el-dialog v-model="dialogFormVisible" title="新增配置" width="800px">
        <el-form-item label="选择广告位置">
          <el-select
            v-model="ruleForm.siteSetFirstId"
            placeholder="请选择一级页面"
            @change="getSecondAd"
          >
            <el-option
              v-for="(value, index) in stairAdList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
          <el-select
            v-if="secondAdList.length"
            v-model="ruleForm.siteSetSecondId"
            clearable
            placeholder="请选择二级页面"
            @change="getThirdAd"
          >
            <el-option
              v-for="(value, index) in secondAdList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
          <el-select
            v-if="thirdAdList.length"
            v-model="ruleForm.siteSetThirdId"
            clearable
            placeholder="请选择三级页面"
            @change="getClientPage"
          >
            <el-option
              v-for="(value, index) in thirdAdList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="showPosition" label="广告位置" required>
          <el-select
            v-model="ruleForm.position"
            placeholder="请选择"
            style="width: 120px; margin-right: 10px"
          >
            <el-option
              v-for="(value, index) in positionMaxNum"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="展示方式" required>
          <el-select
            v-model="ruleForm.showType"
            placeholder="请选择"
            style="width: 120px; margin-right: 10px"
          >
            <el-option
              v-for="(value, index) in showTypeList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
        </el-form-item>
        <el-button type="primary" @click="requiredItemA">提交</el-button>
      </el-dialog>
    </div>
    <seeLog ref="seeLog" />
  </div>
</template>

<script>
import {
  getAdSiteSetPositionList,
  AddAdSiteSetPositionList,
  EditAdSiteSetPositionList,
  RemoveAdSiteSetPositionList,
  adSiteSetGetNextLevel
} from '@/api/advertModule'
import seeLog from '@/components/seeLog/SeeLog.vue'
import { deepCopy } from '@/utils'
import { postLog } from '@/components/seeLog/SaveLog.js'

export default {
  name: 'advertisingLocation',
  components: {
    // aliUpload
    seeLog
  },
  data() {
    return {
      dialogFormVisible: false, //创建广告dialog
      loading: false,
      dataList: [],
      otherForm: {
        type: '',
        thirdLinkType: '',
        typeName: '',
        advertiserName: ''
      },
      limit: 20,
      page: 1,
      total: 0,
      ruleForm: {
        siteSetFirstId: '', //一级页面id
        siteSetSecondId: '', //二级页面id
        siteSetThirdId: '', //三级页面id
        position: '' //广告位置
      },
      positionEnum: 0,
      positionMaxNum: [],
      stairAdList: [],
      secondAdList: [],
      thirdAdList: [],
      showTypeList: [
        {
          name: '随机',
          id: 1
        },
        {
          name: '轮巡',
          id: 2
        }
      ]
    }
  },
  computed: {
    positionMax() {
      let num = 0
      if (this.ruleForm.siteSetFirstId) {
        const item_1 = this.stairAdList.find(
          (v1) => v1.id === this.ruleForm.siteSetFirstId
        )
        num = (item_1 && item_1.positionMax) || 0
      }
      if (this.ruleForm.siteSetSecondId) {
        const item_2 = this.secondAdList.find(
          (v2) => v2.id === this.ruleForm.siteSetSecondId
        )
        num = (item_2 && item_2.positionMax) || 0
      }
      if (this.ruleForm.siteSetThirdId) {
        const item_3 = this.thirdAdList.find(
          (v3) => v3.id === this.ruleForm.siteSetThirdId
        )
        num = (item_3 && item_3.positionMax) || 0
      }
      return num
    },
    showPosition() {
      return !!this.positionMax
    }
  },
  async activated() {
    this.getList({
      limit: this.limit || 20,
      page: this.page || 1
    })
    this.loading = true
  },
  methods: {
    // 更新页码
    currentChange(page) {
      const me = this
      me.page = page
      me.inquiryRecordList = []
      me.getList({
        limit: 20,
        page: page
      })
    },
    // 查询
    search() {
      const me = this
      me.page = 1
      me.getList({
        limit: 20,
        page: 1
      })
      me.loading = true
    },
    // 获取列表数据
    getList(paramsObj) {
      const me = this
      getAdSiteSetPositionList({
        ...paramsObj
      })
        .then((response) => {
          if (response.status === 200) {
            const data = response.data.data
            me.dataList = data.listData
            me.total = data.total
            me.page = paramsObj.page
            me.limit = paramsObj.limit
            me.loading = false
          } else {
            me.loading = false
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.loading = false
          me.$message.error(err.message || '获取列表失败')
        })
    },
    // 新增
    addData() {
      this.ruleForm = {
        siteSetFirstId: '', //一级页面id
        siteSetSecondId: '', //二级页面id
        siteSetThirdId: '', //三级页面id
        position: '', //广告位置
        showType: 1
      }
      this.getLevelThreePage(0, 1)

      sessionStorage.setItem('advertisingLocation', '{}')
      this.dialogFormVisible = true
    },
    copyRow(item) {
      this.ruleForm = deepCopy(item)
      sessionStorage.setItem(
        'advertisingLocation',
        JSON.stringify(deepCopy(item))
      )
      this.getLevelThreePage(0, 1)
      this.dialogFormVisible = true
      this.positionEnum = item.positionMax || ''
      this.setPostionNumber()
      if (this.ruleForm.siteSetSecondId) {
        this.getLevelThreePage(this.ruleForm.siteSetFirstId, 2)
        if (this.ruleForm.siteSetThirdId) {
          this.getLevelThreePage(this.ruleForm.siteSetSecondId, 3)
        }
      }
    },
    // 获取广告页面
    getLevelThreePage(parentId, level) {
      adSiteSetGetNextLevel({
        parentId: parentId || '',
        shopType: '',
        advertPlatform: 1,
        campaignType: '',
        level
      }).then((res) => {
        if (res.data.code === 0) {
          let list = res.data.data || []
          // console.log(`list`, list)
          if ([1, 3].includes(level)) {
            return level === 1
              ? (this.stairAdList = list)
              : (this.thirdAdList = list)
          }
          // 第二级别类型需要删除
          // 18 推送 19 直播 80 最新
          const delLevelList = [18, 19, 80]
          delLevelList.map((levelId) => {
            const findIndex = list.findIndex((item) => item.id === levelId)
            list.splice(findIndex, findIndex !== -1 ? 1 : 0)
          })
          this.secondAdList = list
        }
      })
    },
    // 获取二级广告页面
    getSecondAd(e) {
      this.ruleForm.siteSetSecondId = ''
      this.ruleForm.siteSetThirdId = ''
      this.secondAdList = []
      this.thirdAdList = []
      this.stairAdList.map((_) => {
        if (_.id === e) {
          this.ruleForm.clientPage = _.clientPage || ''
        }
      })
      this.getLevelThreePage(e, 2)
    },

    // 获取三级广告页面
    getThirdAd(e) {
      this.ruleForm.siteSetThirdId = ''
      this.thirdAdList = []
      const arrList = e ? this.secondAdList : this.stairAdList
      e = e ? e : this.ruleForm.siteSetFirstId
      arrList.map((_) => {
        if (_.id === e) {
          this.ruleForm.clientPage = _.clientPage || ''
          this.positionEnum = _.positionMax || ''
          this.setPostionNumber()
        }
      })
      if (!e) return
      this.getLevelThreePage(e, 3)
    },
    // 获取广告位&广告类型
    getClientPage(e) {
      this.ruleForm.adTypeId = ''
      const arrList = e ? this.thirdAdList : this.secondAdList
      e = e ? e : this.ruleForm.siteSetSecondId
      arrList.map((_) => {
        if (_.id === e) {
          this.ruleForm.clientPage = _.clientPage || ''
          this.positionEnum = _.positionMax || ''
          this.setPostionNumber()
        }
      })
    },
    setPostionNumber() {
      if (this.positionEnum) {
        const getArr = (nums) => {
          return Array.from(new Array(nums), (_, i) => ++i)
        }
        this.positionMaxNum = getArr(this.positionEnum)
      }
    },
    // 提交
    requiredItemA() {
      const me = this
      if (!me.ruleForm.siteSetFirstId)
        return me.$message.error('请选择一级页面')
      if (!me.ruleForm.siteSetSecondId)
        return me.$message.error('请选择二级页面')
      if (!me.ruleForm.siteSetThirdId)
        return me.$message.error('请选择三级页面')
      if (!me.ruleForm.position && me.showPosition) {
        return me.$message.error('请选择广告位置')
      }
      if (!me.showPosition) {
        me.ruleForm.position = 1
      }
      const url = me.ruleForm.id
        ? EditAdSiteSetPositionList
        : AddAdSiteSetPositionList
      url({
        ...me.ruleForm
      }).then((res) => {
        if (res.data.code === 0) {
          me.dialogFormVisible = false
          me.$message.success('操作成功')
          me.saveLog(
            me.ruleForm,
            me.ruleForm.id ? '编辑' : '新增',
            me.ruleForm.id ? me.ruleForm.id : res.data.data
          )
          me.search()
        } else {
          me.$message.error('操作失败')
        }
      })
    },
    beforeDelete(item) {
      this.$confirm('是否确认删除', '提示', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteData(item)
        })
        .catch(() => {})
    },
    // 删除
    deleteData(item) {
      const me = this
      RemoveAdSiteSetPositionList({
        id: item.id
      }).then((res) => {
        if (res.data.code === 0) {
          me.$message.success('删除成功')
          me.search()
          postLog(98, item.id, '删除', `删除列表数据: ${item.id}`, '{}', '{}')
        } else {
          me.$message.error('删除失败')
        }
      })
    },

    // 广告日志
    saveLog(detailsForm, operateType, response) {
      const me = this
      const afterData = {
        ...deepCopy(detailsForm),
        id: detailsForm.id || response
      }
      const beforeData = sessionStorage.getItem('advertisingLocation')
      postLog(
        98,
        me.ruleForm.id || response || '',
        operateType,
        '',
        response ? beforeData : '{}',
        JSON.stringify(afterData)
      )
    },
    // 日志
    goSeeLog() {
      this.$refs.seeLog.init('98')
    }
  }
}
</script>

<style lang="scss" scoped>
.advertising-list {
  padding: 10px 20px;

  .inquiryRecordList {
    width: 100%;
    height: 75vh;
    overflow-y: auto;

    .provinceAddress {
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .cityAddress {
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
