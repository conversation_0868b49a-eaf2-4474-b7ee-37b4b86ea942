<template>
  <div style="margin: 10px">
    <!-- <el-dialog v-model="dialogFormVisible" :title="title" width="700px"> -->
    <el-form :model="planForm" label-position="right" label-width="110px">
      <el-form-item label="所属广告方类型">
        <el-select
          v-model="planForm.typeId"
          :disabled="!!(planForm.campaignId && planForm.adCount)"
          @change="change"
        >
          <el-option
            v-for="(value, index) in advertisingType"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="所属广告方">
        <el-select
          ref="selectAdvertiser"
          v-model="advertiserId"
          @change="changeAdvertiser"
          filterable
          remote
          placeholder="请输入所属广告方"
          :disabled="!!(planForm.campaignId && planForm.adCount)"
          :remote-method="remoteMethod"
          :loading="loading"
        >
          <el-option
            v-for="(value, index) in advertiserList"
            :key="index"
            :label="value.advertiserName"
            :value="value.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="计划类型" v-if="[1, 3].includes(planForm.typeId)">
        <el-select
          v-model="planFormCampaignType"
          clearable
          :disabled="!!(planForm.campaignId && planForm.adCount)"
          @change="changePlanFormCampaignType"
        >
          <el-option
            v-for="(value, index) in showCampaignTypeAll"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合作项目" v-if="[1, 3].includes(planForm.typeId)">
        <el-button
          @click="
            $refs.cooperateSelected.init(
              brandId,
              (planFormCampaignType || '0') === '12'
                ? '9'
                : planFormCampaignType,
              planForm.typeId === 1 ? 1 : 2
            )
          "
          >选择项目</el-button
        >
        <el-button type="primary" @click="updateSelectedData({}, true)"
          >取消关联</el-button
        >
        <CooperateList
          ref="CooperateList"
          :isPlan="true"
          :showType="true"
          :setHeight="'100px'"
        />
      </el-form-item>
      <el-form-item label="计划名称" required>
        <el-input
          v-model="planForm.campaignName"
          type="text"
          placeholder="请输入计划名称"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item
        label="计划天数"
        v-if="['13', '14', '15'].includes(planFormCampaignType)"
        required
      >
        <el-input
          v-model="planForm.controlHoursPredictDay"
          type="text"
          placeholder="请输入计划天数"
          clearable
          style="width: 200px"
        >
          <template v-slot:append>
            <span class="flex-item-company">天</span>
          </template>
        </el-input>
        <!-- <el-input
          v-model="planForm.controlHoursPredictHour"
          type="number"
          min="0"
          max="23"
          placeholder="请输入计划小时"
          clearable
          style="width: 200px"
        >
          <template v-slot:append>
            <span class="flex-item-company">小时</span>
          </template>
        </el-input> -->
      </el-form-item>
      <el-form-item label="总量" v-else>
        <el-input
          v-model="controlNumberPredict"
          type="number"
          placeholder="请输入预计曝光次数"
          clearable
          style="width: 200px"
        />
        次
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model="planForm.remark"
          type="textarea"
          :rows="2"
          placeholder="请输入备注"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="提醒配置">
        <el-checkbox v-model="planForm.isRemind"></el-checkbox>
      </el-form-item>
      <el-form-item label="提醒人" v-if="planForm.isRemind">
        <el-autocomplete
          v-model="remindUserName"
          clearable
          :fetch-suggestions="querySearchAsync"
          @select="updatedRemindUser"
        />
      </el-form-item>
      <!-- <el-select v-model="planForm.remindUserName" clearable :disabled="showOtherEdit && !!planForm.campaignId">
                        <el-option v-for="(value, index) in auditNameList" :key="index" :label="value.ossName" :value="value.uid" />
                      </el-select>
                    </el-form-item> -->
    </el-form>
    <div class="dialog-footer">
      <el-button @click="cancel()">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </div>
    <CooperateSelected
      ref="cooperateSelected"
      :isProjectType="false"
      :isPlan="true"
      @updateSelectedData="updateSelectedData"
    />
    <!-- </el-dialog> -->
  </div>
</template>

<script>
import { $emit } from '../../../../../utils/gogocodeTransfer'
import { advertisingType, campaignType } from '@/utils/enum/adConfigEnum.js'
import { getAdvertiserList, getOssAuditName } from '@/api/advertModule'
import {
  postAdvertisingPlanAdd,
  postAdvertisingPlanUpdate
} from '@/api/advertModuleNew'
// import { filterDay } from '@/utils'
import { convertKeyValueEnum } from '@/utils/convert'
import { getOrderProjectListByParam } from '@/api/garage'
import { mapGetters } from 'vuex'
import CooperateSelected from '@/views/business/contentTreasure/managementActivity/commponents/CooperateSelected.vue'
import CooperateList from '@/views/business/contentTreasure/managementActivity/commponents/CooperateList.vue'
export default {
  name: 'editPlan',
  components: {
    CooperateList,
    CooperateSelected
  },
  computed: {
    ...mapGetters(['uid', 'name'])
  },
  props: {},
  data() {
    return {
      dialogFormVisible: false,
      editing: false, //编辑
      loading: false,
      showOtherEdit: false, // 是否是其他编辑，非其他编辑就是厂家编辑
      auditNameList: [],
      advertiserList: [],
      campaignType,
      campaignTypeAll: { ...convertKeyValueEnum(campaignType), 请选择: '' },
      showCampaignTypeAll: {}, // 展示的计划类型
      selectedList: {}, // 合同项目列表
      advertiserId: '', // 广告id
      title: '创建计划',
      advertisingType,
      planForm: {
        typeId: '',
        campaignName: '',
        advertiserName: '',
        advertiserId: '',
        campaignId: '',
        campaignType: ''
      },
      controlNumberPredict: '',
      contractNameId: '',
      brandId: '',
      remindUserName: '',
      planFormCampaignType: '',
      value: ''
    }
  },
  methods: {
    querySearchAsync(queryString, cb) {
      if (!queryString) return cb([])
      getOssAuditName({
        nameOrCode: queryString
      })
        .then((res) => {
          if (res.data.code === 0) {
            const listData = res.data.data || []
            const cbData = listData.map((item) => {
              return {
                ...item,
                value: item.userName
              }
            })
            return cb(cbData)
          } else {
            this.$message.error(res.data.msg)
          }
        })
        .catch((e) => {
          this.$message.error(e.message)
        })
    },
    change() {
      this.planForm.advertiserName = ''
      this.advertiserId = ''
      this.planForm.advertiserId = ''
      this.planFormCampaignType = ''
      this.controlNumberPredict = ''
      this.planForm.controlHoursPredictDay = ''
      this.planForm.controlHoursPredictHour = ''
      this.remoteMethod('')
      this.$refs.selectAdvertiser && this.$refs.selectAdvertiser.focus()
      this.showCampaignTypeAll = this.campaignTypeAll
      this.planForm.isRemind = [1, 3].includes(this.planForm.typeId)
        ? true
        : this.planForm.isRemind
    },
    changeAdvertiser(query) {
      const me = this
      me.planForm.advertiserId = query
      me.contractNameId = ''
      me.planForm.factoryProjectId = ''
      me.advertiserList.map((item) => {
        if (item.id === query) {
          me.planForm.advertiserName = item.advertiserName
          me.brandId = item.relationId || item.id || ''
        }
      })
    },
    remoteMethod(query) {
      this.loading = true
      getAdvertiserList({
        typeId: this.planForm.typeId,
        advertiserName: query
        // limit: 10
      })
        .then((res) => {
          this.loading = false
          if (res.data.code == 0) {
            this.advertiserList = res.data.data
          }
        })
        .catch((e) => console.log(e))
    },
    remoteMethodList(query, id) {
      const me = this
      me.loading = true
      getOrderProjectListByParam({
        id,
        contractName: query,
        brandType: me.planForm.typeId === 1 ? 1 : 2,
        brandId: me.brandId,
        availableCntZero: 1,
        extraIncludeId: id
      })
        .then((res) => {
          me.loading = false
          if (res.data.code == 0) {
            const list = res.data.data || []
            const findProjectData =
              list.find((_) => {
                return _.id === me.planForm.factoryProjectId
              }) || {}
            me.$refs.CooperateList.init([findProjectData], query)
          }
        })
        .catch((e) => console.log(e))
    },
    // 新增
    addOpen(ruleForm) {
      const me = this
      me.planForm = {
        typeId: ruleForm.typeId || '',
        advertiserId: ruleForm.advertiserId || '',
        advertiserName: ruleForm.advertiserName || '',
        campaignId: '',
        isRemind: false,
        campaignName: ruleForm.campaignName || '',
        remindUserId: me.uid || '',
        remindUserName: ruleForm.remindUserName || me.name || ''
      }
      me.title = '创建计划'
      me.editing = false
      me.resetData({})
      if (me.planForm.typeId) {
        me.remoteMethod(me.planForm.advertiserName || '')
      }
      me.planForm.isRemind = [1, 3].includes(me.planForm.typeId)
        ? true
        : me.planForm.isRemind
    },
    //编辑
    openEdit(row) {
      const me = this
      me.planForm = {
        typeId: row.typeId || '',
        advertiserId: row.advertiserId || '',
        advertiserName: row.advertiserName || '',
        campaignId: row.campaignId || '',
        campaignName: row.campaignName || '',
        isRemind: !!row.isRemind,
        remindUserId: row.remindUserId || '',
        remindUserName: row.remindUserName || '',
        remark: row.remark || '',
        campaignType: row.campaignType.toString() || '',
        factoryProjectId: row.factoryProjectId || '',
        adCount: row.adCount || ''
      }

      me.planFormCampaignType = row.campaignType.toString() || ''
      me.planFormCampaignType =
        me.planFormCampaignType === '' ? '' : me.planFormCampaignType
      me.showOtherEdit = row.typeId !== 1
      me.resetData(row)
      if (
        row.factoryContractName ||
        row.factoryProjectName ||
        row.relationId ||
        (row.typeId === 3 && row.advertiserId)
      ) {
        // 非车企类型，广告方id ，赋值给brandId
        me.brandId = row.typeId === 3 ? row.advertiserId : row.relationId || ''
        row.factoryContractName || row.factoryProjectName
          ? me.remoteMethodList(
              row.factoryContractName || row.factoryProjectName,
              row.factoryProjectId || row.factoryProjectId
            )
          : me.$refs.CooperateList && me.$refs.CooperateList.init([], '')
      } else {
        me.$refs.CooperateList && me.$refs.CooperateList.init([], '')
      }
      if (
        [13, 14, '13', '14'].includes(row.campaignType) &&
        row.controlHoursPredict
      ) {
        me.planForm.controlHoursPredictDay = row.controlHoursPredict.includes(
          '天'
        )
          ? row.controlHoursPredict.split('天')[0]
          : ''
        const controlHoursPredictHour = me.planForm.controlHoursPredictDay
          ? row.controlHoursPredict.split('天')[1].split('小时')[0]
          : row.controlHoursPredict.split('小时')[0]
        me.planForm.controlHoursPredictHour = controlHoursPredictHour || ''
      }
      //编辑
      me.editing = row.typeId ? true : false
      me.title = `${row.typeId ? '编辑' : '创建'}计划`
      row.typeId ? me.remoteMethod('') : null
    },
    // 重置数据
    resetData(row) {
      const me = this
      me.remindUserName = row.remindUserName || me.name || ''
      me.advertiserId = row.advertiserId || ''
      me.controlNumberPredict = row.controlNumberPredict || ''
      me.advertiserList = []
      me.contractNameId = ''
      me.showCampaignTypeAll = me.campaignTypeAll
    },
    // 创建计划
    confirm() {
      const me = this
      const {
        typeId,
        advertiserId,
        campaignName,
        controlHoursPredictDay,
        controlHoursPredictHour
      } = this.planForm
      console.log(advertiserId, typeId)
      if (!typeId) {
        return this.$message.error('请选择所属广告方类型')
      }
      if (!advertiserId) {
        return this.$message.error('请选择所属广告方')
      }
      if (!campaignName) {
        return this.$message.error('请输入计划名称')
      }
      if (controlHoursPredictHour > 23)
        return this.$message.error('计划小时不得大于23')
      let controlHoursPredict =
        (controlHoursPredictDay ? controlHoursPredictDay * 24 : 0) +
        (controlHoursPredictHour
          ? Number(controlHoursPredictHour.split(':')[0])
          : 0)
      if (
        !controlHoursPredict &&
        ['13', '14', '15'].includes(me.planFormCampaignType)
      ) {
        return this.$message.error('请输入计划天数')
      }
      if (
        me.planForm.factoryProjectId &&
        !me.controlNumberPredict &&
        !['13', '14', '15'].includes(me.planFormCampaignType)
      ) {
        return me.$message.error('总量必填')
      }
      if (
        !!me.controlNumberPredict &&
        me.controlNumberPredict < 0 &&
        !['13', '14', '15'].includes(me.planFormCampaignType)
      ) {
        return me.$message.error('总量需要大于0')
      }
      if (
        me.planForm.factoryProjectId &&
        Number(me.controlNumberPredict) >
          me.selectedList.availableCount * 10000 &&
        !['13', '14', '15'].includes(me.planFormCampaignType)
      ) {
        return me.$message.error('总量不可超过可用量')
      }
      const url = me.editing
        ? postAdvertisingPlanUpdate
        : postAdvertisingPlanAdd
      const tip = me.editing ? '修改成功' : '创建成功'
      url({
        ...me.planForm,
        id: me.planForm.campaignId || '',
        name: me.planForm.campaignName,
        advertiserId: me.planForm.advertiserId,
        controlNumberPredict: me.controlNumberPredict,
        isRemind: me.planForm.isRemind ? 1 : 0,
        remindUserName: me.remindUserName || me.name || '',
        campaignType: me.planFormCampaignType || 0,
        controlHoursPredict:
          controlHoursPredict &&
          ['13', '14', '15'].includes(me.planFormCampaignType)
            ? controlHoursPredict
            : ''
      })
        .then((response) => {
          if (response.data.code === 0) {
            console.log(response)
            me.$message.success(tip)
            $emit(me, 'success')
          }
        })
        .catch((_) => {
          console.log(_)
        })
    },
    // 更新提醒人
    updatedRemindUser(val) {
      this.planForm.remindUserId = val.userId || ''
    },
    // 修改页面展示的列表
    updateSelectedData(val, status) {
      this.$refs.CooperateList.init(
        val?.id ? [val] : [],
        val.contractName || ''
      )
      this.planForm.campaignName = !status
        ? val.projectName || ''
        : this.planForm.campaignName || ''
      this.selectedList = val || {}
      this.planForm.factoryProjectId = val.id || ''
      // this.planFormCampaignType = val.projectType ? val.projectType.toString() : ''
    },
    // 取消
    cancel() {
      $emit(this, 'success')
    },
    // 更新计划类型
    changePlanFormCampaignType() {
      this.updateSelectedData({})
      this.planForm.controlHoursPredictDay = ''
      this.planForm.controlHoursPredictHour = ''
      this.controlNumberPredict = ''
    }
  },
  emits: ['success']
}
</script>
