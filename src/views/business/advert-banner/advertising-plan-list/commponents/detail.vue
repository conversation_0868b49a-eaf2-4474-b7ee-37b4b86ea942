<template>
    <editPlan ref="editPlan" @success="success" />
</template>
<script>
import editPlan from './editPlan.vue'
export default {
  name: 'advertising-plan-detail',
  components: {
    editPlan
  },
  activated() {
    const row = JSON.parse(sessionStorage.getItem('AdvertisingPlanDetail'))
    const query = this.$route.query || {}
    query.id ? this.$refs.editPlan.openEdit(row) : this.$refs.editPlan.addOpen(row)
  },
  methods: {
    success() {
      this.$router.go(-1)
    }
  }
}
</script>