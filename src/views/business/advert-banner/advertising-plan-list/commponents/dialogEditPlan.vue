<template>
  <el-dialog v-model="dialogFormVisible" :title="title" width="1000px">
    <editPlan ref="editPlan" @success="success" />
  </el-dialog>
</template>
<script>
import editPlan from './editPlan.vue'
export default {
  name: 'advertising-plan-detail',
  components: {
    editPlan
  },
  data() {
    return {
        dialogFormVisible: false,
        title: ''
    }
  },
  mounted() {
    
  },
  methods: {
    openDialog(row) {
      const me = this
      me.dialogFormVisible = true
      me.title = '创建计划'
      setTimeout(() => {
        me.$refs.editPlan.addOpen(row)
      }, 10);
    },
    success() {
      this.dialogFormVisible = false
    }
  }
}
</script>
