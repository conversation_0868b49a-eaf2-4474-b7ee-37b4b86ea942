<template>
  <div class="advertising-plan-list">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="计划ID">
        <el-input
          v-model="ruleForm.campaignId"
          type="text"
          placeholder="请输入计划ID"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="计划名称">
        <el-input
          v-model="ruleForm.campaignName"
          type="text"
          placeholder="请输入计划名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="所属广告方ID">
        <el-input
          v-model="ruleForm.advertiserId"
          type="text"
          placeholder="请输入所属广告方ID"
          clearable
          style="width: 200px"
          @change="(e) => changeAdvertiserId(e, true)"
        />
      </el-form-item>
      <el-form-item label="所属广告方名称">
        <!-- <el-input
          v-model="ruleForm.advertiserName"
          type="text"
          placeholder="请输入所属广告方名称"
          clearable
          style="width: 200px"
        /> -->
        <el-select
          v-model="advertiserForm.id"
          filterable
          remote
          clearable
          :remote-method="advertiserNameSearch"
          style="width: 200px"
          @change="changeAdvertiserId"
        >
          <el-option
            v-for="item in advertiserNameList"
            :key="item.advertiserId"
            :label="item.advertiserName"
            :value="item.advertiserId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属广告方类型">
        <el-select v-model="ruleForm.typeId" clearable>
          <el-option
            v-for="(value, index) in advertisingTypeWithAll"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="当前状态">
        <el-select v-model="ruleForm.campaignStatus" clearable>
          <el-option
            v-for="(value, index) in currentStatus"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否有效" clearable>
        <el-select v-model="ruleForm.isValid">
          <el-option
            v-for="(value, index) in effective"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="计划类型">
        <el-select v-model="ruleForm.campaignTypes" clearable multiple>
          <el-option
            v-for="(value, index) in campaignTypeAll"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单名称">
        <el-select v-model="ruleForm.factoryProjectId" clearable filterable>
          <el-option
            v-for="(value, index) in factoryProjectIdList"
            :key="index"
            :label="value.name"
            :value="value.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button type="primary" @click="resetDataSearch">重置</el-button>
        <el-button type="primary" @click="addOpen">创建计划</el-button>
        <el-button type="primary" @click="exportExcel()">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="inquiryRecordList"
      :data="inquiryRecordList"
      max-height="72vh"
      border
      @sort-change="sortSearchList"
      @row-dblclick="openEdit"
    >
      <el-table-column prop="campaignId" label="计划ID" align="center" />
      <el-table-column
        prop="advertiserId"
        label="所属广告方ID"
        align="center"
        sortable="custom"
        width="150"
      />
      <el-table-column
        prop="advertiserName"
        label="所属广告方"
        align="center"
        width="130"
      />
      <el-table-column
        prop="typeName"
        label="所属广告方类型"
        align="center"
        width="130"
      />
      <el-table-column
        prop="campaignName"
        label="计划名称"
        align="center"
        width="170"
      />
      <el-table-column
        prop="orderName"
        label="订单名称"
        align="center"
        width="170"
      />
      <el-table-column
        prop="factoryProjectName"
        label="合作项目"
        align="center"
        width="170"
      />
      <el-table-column
        prop="campaignType"
        label="计划类型"
        align="center"
        width="90"
      >
        <template v-slot="scope">{{
          campaignType[scope.row.campaignType]
        }}</template>
      </el-table-column>
      <el-table-column
        prop="servingStartTime"
        label="投放开始时间"
        align="center"
        width="130"
      >
        <template v-slot="scope">{{ scope.row.servingStartTime }}</template>
      </el-table-column>
      <el-table-column
        prop="servingEndTime"
        label="投放结束时间"
        align="center"
        width="130"
      >
        <template v-slot="scope">{{ scope.row.servingEndTime }}</template>
      </el-table-column>
      <el-table-column
        prop="controlNumberPredict"
        label="总量"
        align="center"
        sortable="custom"
      >
        <template v-slot="scope">{{
          scope.row.controlNumberPredict || '不限'
        }}</template>
      </el-table-column>
      <el-table-column
        prop="servingNum"
        label="已投放量"
        align="center"
        sortable="custom"
        width="120"
      />
      <el-table-column
        prop="servingLeftNum"
        label="可用余量"
        align="center"
        sortable="custom"
        width="120"
      >
        <template v-slot="scope">{{
          !scope.row.controlNumberPredict ? '不限' : scope.row.servingLeftNum
        }}</template>
      </el-table-column>
      <el-table-column
        prop="exposureNum"
        label="实际曝光"
        align="center"
        sortable="custom"
        width="120"
      />
      <el-table-column
        prop="exposureLeftNum"
        label="实际余量"
        align="center"
        sortable="custom"
        width="120"
      >
        <template v-slot="scope">{{
          !scope.row.controlNumberPredict ? '不限' : scope.row.exposureLeftNum
        }}</template>
      </el-table-column>
      <el-table-column
        prop="controlHoursPredict"
        label="总天数"
        align="center"
        width="170"
      />
      <!-- <el-table-column
          prop="servingLeftHours"
          label="可用天数"
          align="center"
          width="170"
        /> -->
      <el-table-column
        prop="servingHours"
        label="已用天数"
        align="center"
        width="170"
      />
      <el-table-column
        prop="adCount"
        label="广告个数"
        align="center"
        width="90"
      />
      <el-table-column prop="status" label="当前状态" align="center" width="90">
        <template v-slot="scope">
          <span>{{ reserveCurrentState[scope.row.status] || '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="valid" label="是否有效" align="center" width="90">
        <template v-slot="scope">
          <el-switch
            v-model="scope.row.valid"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="changeStatus(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template v-slot="scope">
          <el-button
            type="primary"
            link
            size="small"
            @click="goToDetail(scope.row)"
            >{{
              scope.row.campaignType !== 12 ? '查看广告' : '查看内容池'
            }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="160"
      >
        <template v-slot="scope">{{ scope.row.createTime }}</template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" />
      <el-table-column label="操作日志" align="center" width="90">
        <template v-slot="scope">
          <el-button type="primary" link size="small" @click="seeLog(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="20"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      style="justify-content: center; margin-top: 10px"
      @current-change="currentChange"
    />
    <SeeLog2 ref="SeeLog" />
  </div>
</template>

<script>
import {
  effective,
  advertisingType,
  currentStatus,
  campaignType
} from '@/utils/enum/adConfigEnum.js'
import {
  //计划列表查询
  getAdvertisingPlanList,
  postAdvertisingPlanClose,
  postAdvertisingPlanOpen,
  getAdvertisingManageList
} from '@/api/advertModuleNew'
import { deepCopy, filterDay } from '@/utils'
import { getSelectAllOrderWithProjectId } from '@/api/garage'
import { convertKeyValueEnum } from '@/utils/convert'
import { platformList, platformList2 } from '@/utils/enum'
import { timeFullS } from '@/filters'
import SeeLog2 from '../components/see-log.vue'

export default {
  name: 'AdvertisingPlanList',
  components: {
    SeeLog2
  },
  data() {
    return {
      effective: effective, // 是否有效
      isPostStatus: false, // 是否是有过参数传递
      currentStatus,
      reserveCurrentState: convertKeyValueEnum(currentStatus),
      campaignType: {},
      campaignTypeAll: {},
      loading: false,
      ruleForm: {
        campaignId: '',
        campaignName: '',
        isValid: '', // 是否有效1有效0无效
        advertiserName: '', // 广告名称
        typeId: '', // 广告类型
        campaignStatus: '', // 计划状态
        limit: 20, // 数量
        advertiserId: '', // 广告id
        campaignTypes: [], // 广告计划类型
        sortCode: '1',
        sortType: '2',
        factoryProjectId: '' // 订单名称
      },
      advertisingTypeWithAll: { 全部: '', ...advertisingType },
      sortTypeList: {
        advertiserId: '2',
        controlNumberPredict: '3',
        servingNum: '4',
        servingLeftNum: '5',
        exposureNum: '6',
        exposureLeftNum: '7'
      },
      advertisingType,
      page: 1, // 页码
      total: 0, // 总数
      inquiryRecordList: [], // 配置内容数据
      factoryProjectIdList: [], // 合同名称列表
      platformList,
      platformList2,
      advertiserNameList: [],
      advertiserForm: {
        id: '',
        brandId: ''
      }
    }
  },
  activated() {
    this.campaignType = deepCopy(campaignType)
    this.campaignTypeAll = convertKeyValueEnum(this.campaignType)
    const oldQuery = sessionStorage.getItem('AdvertisingPlanList') || '{}'
    const getStatus = sessionStorage.getItem('AdvertisingPlanListNew') || false
    if (getStatus) {
      sessionStorage.removeItem('AdvertisingPlanListNew')
      this.resetData()
      this.setParams()
      const me = this
      setTimeout(() => {
        me.search()
      }, 100)
      return
    }
    this.getFactoryOrderList()
    const oldQueryInit = JSON.parse(oldQuery)
    Object.keys(this.ruleForm).forEach((key) => {
      if (key === 'campaignTypes') {
        this.ruleForm[key] = oldQueryInit[key]
          ? oldQueryInit[key].split(',')
          : []
      } else {
        this.ruleForm[key] = oldQueryInit[key]
      }
    })
    this.search()
  },
  methods: {
    advertiserNameSearch(query, flag) {
      if (query) {
        getAdvertisingManageList({
          typeId: this.ruleForm.typeId,
          advertiserName: query,
          page: 1,
          limit: 200
        })
          .then((res) => {
            if (res.data.code === 0) {
              const data = res.data.data || {}
              this.advertiserNameList = data.list || []
              if (this.ruleForm.advertiserId && flag) {
                this.changeAdvertiserId(this.ruleForm.advertiserId)
              }
            } else {
              this.advertiserNameList = []
            }
          })
          .catch(() => {
            this.advertiserNameList = []
          })
      } else {
        this.advertiserNameList = []
      }
    },
    changeAdvertiserId(e, flag) {
      this.ruleForm.advertiserId = e
      this.advertiserForm.brandId = ''
      if (flag) {
        this.advertiserForm.id = ''
      }
      if (e) {
        const item = this.advertiserNameList.find(
          (v) => v.advertiserId === Number(e)
        )
        if (item && item.typeId === 1) {
          const brandId = item.relationId
          if (brandId) {
            this.advertiserForm.brandId = brandId
          }
        }
      }
      this.getFactoryOrderList()
    },
    setParams() {
      const query = this.$route.query
      this.ruleForm.advertiserName = query.advertiserName
      this.ruleForm.typeId = query.typeId ? Number(query.typeId) : ''
      this.ruleForm.advertiserId = query.advertiserId
      if (query.advertiserName) {
        this.advertiserNameSearch(query.advertiserName, true)
      }
      if (
        query.campaignTypeName &&
        (query.campaignType || query.factoryProjectId)
      ) {
        const list = query.campaignTypeName.split(',')
        const arr = []
        list.forEach((v) => {
          const type = this.campaignTypeAll[v]
          if (type) {
            arr.push(type)
          }
        })
        this.ruleForm.campaignTypes = arr
        this.ruleForm.advertiserId = query.brandId || ''
        this.ruleForm.factoryProjectId = query.factoryProjectId || ''
      }
    },
    // 改变是否有效
    changeStatus(item) {
      const me = this
      const postAdversing = item.valid
        ? postAdvertisingPlanOpen
        : postAdvertisingPlanClose
      postAdversing({
        id: item.campaignId
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('修改成功')
          }
          // else {
          //   item.valid = !item.valid
          //   me.$message.error('修改失败')
          // }
        })
        .catch(() => {
          item.valid = !item.valid
        })
    },
    // 更新页码
    currentChange(page) {
      const me = this
      me.page = page
      me.inquiryRecordList = []
      me.getList({
        page: page
      })
    },
    // 查询
    search() {
      const me = this
      me.page = 1
      me.getList({ page: 1 })
    },
    // 筛选
    sortSearchList(item) {
      if (item.order) {
        this.ruleForm.sortType = item.order === 'ascending' ? '1' : '2'
        this.ruleForm.sortCode = this.sortTypeList[item.prop]
          ? this.sortTypeList[item.prop]
          : '1'
      } else {
        this.ruleForm.sortCode = '1'
        this.ruleForm.sortType = '2'
      }
      this.getList({
        page: this.page
      })
    },
    // 获取列表数据
    getList(paramsObj) {
      const me = this
      const requestParams = {
        ...me.ruleForm,
        ...paramsObj,
        campaignTypes: me.ruleForm.campaignTypes.join(',')
      }
      sessionStorage.setItem(
        'AdvertisingPlanList',
        JSON.stringify(requestParams)
      )
      me.isPostStatus = !!(
        requestParams.advertiserName ||
        requestParams.advertiserId ||
        requestParams.typeId
      )
      me.inquiryRecordList = []
      //计划列表查询
      getAdvertisingPlanList(requestParams)
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            // data.list.map(v => {
            //   v.status = v.valid
            //   return v
            // })
            me.inquiryRecordList = (data.list || []).map((item) => {
              item.controlHoursPredict = item.controlHoursPredict
                ? filterDay(item.controlHoursPredict)
                : '/'
              item.servingHours = item.servingHours
                ? filterDay(item.servingHours)
                : '/'
              item.servingLeftHours = item.servingLeftHours
                ? filterDay(item.servingLeftHours)
                : '/'
              return item
            })
            me.page = requestParams.page
            me.total = data.total
          }
        })
        .catch(() => {})
    },
    //重置
    resetData() {
      this.ruleForm = {
        isValid: '', // 是否有效1有效0无效
        advertiserName: '', // 广告名称
        typeId: '', // 广告类型
        advertiserId: '', // 广告id
        campaignId: '', //计划id
        campaignName: '', //计划名称
        campaignStatus: '', //当前状态
        campaignTypes: [], // 广告计划类型
        sortCode: '1',
        sortType: '2',
        factoryProjectId: '' // 订单名称
      }
      this.advertiserForm = {
        id: '',
        brandId: ''
      }
      this.$refs.inquiryRecordList.clearSort()
      this.getFactoryOrderList()
    },
    // 重置列表
    resetDataSearch() {
      this.resetData()
      this.search()
    },
    // 查看配置详情
    goToDetail(row) {
      const me = this
      if (row.campaignType === 12) {
        return me.$router.push({
          name: 'contentPoolManagement'
        })
      }
      sessionStorage.setItem('AdvertisingListNew', true)
      me.$router.push({
        name: 'AdvertisingList',
        query: {
          orgName: row.advertiserName,
          orgType: row.typeId,
          orgId: row.advertiserId,
          campaignId: row.campaignId,
          campaignName: row.campaignName
        }
      })
    },
    addOpen() {
      const postData =
        this.isPostStatus && this.inquiryRecordList.length ? this.ruleForm : {}
      sessionStorage.setItem('AdvertisingPlanDetail', JSON.stringify(postData))
      this.$router.push({
        name: 'AdvertisingPlanDetail'
      })
    },
    openEdit(row) {
      sessionStorage.setItem('AdvertisingPlanDetail', JSON.stringify(row))
      this.$router.push({
        name: 'AdvertisingPlanDetail',
        query: {
          id: row.advertiserId
        }
      })
    },
    getFactoryOrderList() {
      const me = this
      const data = {
        page: 1,
        pageSize: 9999
      }
      if (me.advertiserForm.brandId) {
        data.brandId = me.advertiserForm.brandId
      }
      getSelectAllOrderWithProjectId(data).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data || []
          me.factoryProjectIdList = [
            { name: '全部', id: '' },
            { name: '未关联', id: '0' }
          ]
          let flag = true
          data.forEach((item) => {
            if (item.projectIds) {
              if (me.ruleForm.factoryProjectId === item.projectIds) {
                flag = false
              }
              me.factoryProjectIdList.push({
                name: item.orderName,
                id: item.projectIds
              })
            }
          })
          if (flag) {
            me.ruleForm.factoryProjectId = ''
          }
        }
      })
    },
    // 导出线索赠送表格
    exportExcel() {
      const me = this
      me.$confirm('你确认导出到Excel么', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '正在导出，请稍等......',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          getAdvertisingPlanList({
            ...me.ruleForm,
            campaignTypes: me.ruleForm.campaignTypes.join(','),
            page: me.page,
            limit: 9999,
            sortCode: me.ruleForm.sort ? me.ruleForm.sort.split(', ')[0] : '1',
            sortType: me.ruleForm.sort ? me.ruleForm.sort.split(', ')[1] : '1'
          }).then(async (res) => {
            const data = res.data.data.list || []
            if (!data || data.length === 0) {
              loading.close()
              me.$message.success('暂无数据可以导出')
              return
            }
            data &&
              data.map((item) => {
                item.statusName = me.reserveCurrentState[item.status]
                item.campaignTypeName = me.campaignType[item.campaignType]
                item.validName = `${item.valid ? '有' : '无'}效`
              })
            const { export_json_to_excel } = await import(
              '@/vendor/Export2Excel'
            )
            // 导出的表头
            const tHeader = [
              '计划ID',
              '所属广告方ID',
              '所属广告方',
              '所属广告方类型',
              '计划名称',
              '订单名称',
              '合作项目',
              '计划类型',
              '投放开始时间',
              '投放结束时间',
              '总量',
              '已投放量',
              '可用余量',
              '实际曝光',
              '实际余量',
              '总天数',
              // '可用天数',
              '已用天数',
              '广告个数',
              '当前状态',
              '是否有效',
              '创建时间',
              '备注'
            ]
            // 导出表头要对应的数据
            const filterVal = [
              'campaignId',
              'advertiserId',
              'advertiserName',
              'typeName',
              'campaignName',
              'orderName',
              'factoryProjectName',
              'campaignTypeName',
              'servingStartTime',
              'servingEndTime',
              'controlNumberPredict',
              'servingNum',
              'servingLeftNum',
              'exposureNum',
              'exposureLeftNum',
              'controlHoursPredict',
              'servingHours',
              // 'servingLeftHours',
              'adCount',
              'statusName',
              'validName',
              'createTime',
              'remark'
            ]
            const exportData = me.formatJsonUser(filterVal, data)
            export_json_to_excel(tHeader, exportData, '计划管理列表')
            me.$message.success('导出成功')
            loading.close()
          })
        })
        .catch()
    },
    formatJsonUser(filterVal, jsonData) {
      return jsonData.map((v, i) =>
        filterVal.map((j) => {
          if (j === 'num') {
            return i + 1
          }
          if (j === 'createTime') {
            return timeFullS(v[j])
          }
          if (j === 'free') {
            if (v[j] === 5) {
              return '否'
            } else {
              return '是'
            }
          }
          if (
            [
              'controlHoursPredict',
              'servingHours',
              'servingLeftHours'
            ].includes(j)
          ) {
            return filterDay(v[j])
          }
          return v[j]
        })
      )
    },
    // 查看日志
    seeLog(data) {
      this.$refs.SeeLog.init({
        id: data.campaignId,
        urlType: 2
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.advertising-plan-list {
  padding: 20px;
}
</style>
