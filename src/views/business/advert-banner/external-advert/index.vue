<template>
  <div>
    <el-row style="min-width: 1530px">
      <el-col :span="18" style="border-right: 1px solid #ddd">
        <advert-table @changeValue="changeValue" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import AdvertTable from './components/advert-table.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'AdvertBanner',
  components: {
    AdvertTable,
  },
  data() {
    return {
      confirmText: '(外部广告)已有修改内容，但未保存，是否离开？',
    }
  },
  computed: {
    ...mapGetters(['pageChangedStatus']),
  },
  methods: {
    changeValue() {
      this.$store.commit('CHANGE_PAGE_CHANGED_STATUS', {
        name: this.$route.name,
        text: this.confirmText,
      })
      // this.$store.commit('CHANGE_PAGE_CHANGED_CALLBACK', this.$refs['CarClassList'].effective)
    },
  },
}
</script>

<style lang="scss" scoped>
.el-row {
  padding: 20px;
}
</style>
