<template>
  <div>
    <p>
      <el-button type="success" @click="save()">保存</el-button>
    </p>
    <el-row class="item header">
      <el-col :span="3"> 页面 </el-col>
      <el-col :span="2"> 平台 </el-col>
      <el-col :span="2"> 是否显示 </el-col>
      <el-col :span="4"> 广告厂商 </el-col>
      <el-col :span="3"> 样式 </el-col>
      <el-col :span="2"> 广告ID </el-col>
      <el-col :span="2"> 每页展示 </el-col>
      <el-col :span="2"> 位置 </el-col>
      <el-col :span="4"> 修改时间 </el-col>
    </el-row>
    <div class="content">
      <transition-group tag="span" v-if="advertList && advertList.length">
        <el-row
          v-for="(item, index) in advertList"
          :key="`0` + index"
          class="item"
        >
          <!-- 页面 -->
          <el-col :span="3">
            {{ item.name }}
          </el-col>
          <!-- 平台 -->
          <el-col :span="2">
            <span v-if="item.platform === '1'">Android</span>
            <span v-if="item.platform === '2'">iOS</span>
          </el-col>
          <!-- 是否显示(1：有效；0：无效) -->
          <el-col :span="2">
            <el-switch
              v-model="item.status"
              active-text=""
              inactive-text=""
              @change="changeStatus(item)"
            />
          </el-col>
          <!-- 广告厂商 -->
          <el-col :span="4">
            <el-select
              v-model="item.advertPlat"
              placeholder=""
              @change="changeAdvertPlat(item)"
            >
              <el-option
                v-for="advertPlat in item.advertPlatList"
                :key="advertPlat.value"
                :label="advertPlat.label"
                :value="advertPlat.value"
              />
            </el-select>
          </el-col>
          <!-- 样式（左图、右图、启动屏） -->
          <el-col :span="3">
            <el-select
              v-model="item.advertStyle"
              placeholder=""
              @change="changeStyle(item)"
            >
              <el-option
                v-for="advertStyle in item.advertStyleList"
                :key="advertStyle.value"
                :label="advertStyle.label"
                :value="advertStyle.value"
              />
            </el-select>
          </el-col>
          <!-- 广告ID -->
          <el-col :span="2">
            <el-input
              v-model="item.extAdvertId"
              placeholder=""
              @change="changeAdvertId(item)"
            />
          </el-col>
          <!-- 每页显示（是、否） -->
          <el-col :span="2">
            <el-select
              v-model="item.pageDisplay"
              placeholder=""
              @change="changePageShow(item)"
            >
              <el-option
                v-for="pageDisplay in item.pageDisplayList"
                :key="pageDisplay.value"
                :label="pageDisplay.label"
                :value="pageDisplay.value"
              />
            </el-select>
          </el-col>
          <!-- 位置 -->
          <el-col :span="2">
            <el-input
              v-model="item.position"
              :min="0"
              :max="20"
              :maxlength="2"
              placeholder=""
              @change="changePosition(item)"
            />
          </el-col>
          <!-- 修改时间 -->
          <el-col :span="4">
            {{ item.updateTime }}
          </el-col>
        </el-row>
      </transition-group>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { advertRequest, advertSave } from '@/api/advertModule'
import { baseCodelistByView } from '@/api/commonModule'
import { batchRecordBeforeAlter } from '@/utils/enum/logData'
export default {
  name: 'AdvertTable',
  components: {},
  data() {
    return {
      basecodeList: [], // 字典表
      advertList: [], // 广告列表
      disableSubmitPosition: false, // 广告位置为空，是否禁用发送请求
      disableSubmitId: false, // 广告id错误，是否禁用发送请求
      disableSubmitPositionNum: false, // 广告位置数据错误，是否禁用发送请求
    }
  },
  computed: {},
  activated() {
    this.getadvertList()
  },
  methods: {
    // 获取广告列表
    getadvertList() {
      advertRequest({
        page: 1,
        limit: 100,
      }).then((response) => {
        const result = response.data && response.data.list
        baseCodelistByView({
          view: 'v_external_adv_display',
        }).then((response) => {
          if (response.data.code === 0) {
            this.basecodeList = response.data.data
            batchRecordBeforeAlter(this.basecodeList)
            this.basecodeList &&
              this.basecodeList.map(function (value) {
                result &&
                  result.map(function (v) {
                    if (v.status === '1') {
                      v.status = true
                    } else if (v.status === '0') {
                      v.status = false
                    }
                    v.advertPlatList = [
                      {
                        value: '1',
                        label: 'AD mobile',
                      },
                      {
                        value: '2',
                        label: 'Mintegral',
                      },
                    ]
                    v.pageDisplayList = [
                      {
                        value: '1',
                        label: '是',
                      },
                      {
                        value: '0',
                        label: '否',
                      },
                    ]
                    if (v.advertPlat === '1') {
                      v.advertStyleList = [
                        {
                          value: '1',
                          label: '左图',
                        },
                        {
                          value: '2',
                          label: '右图',
                        },
                        {
                          value: '3',
                          label: '大图',
                        },
                      ]
                    } else if (v.advertPlat === '2') {
                      v.advertStyle = '3'
                      v.advertStyleList = [
                        {
                          value: '3',
                          label: '大图',
                        },
                      ]
                    }
                    if (value.id === v.advertType) {
                      v.name = value.name
                    }
                  })
              })
            this.advertList = result
          }
        })
      })
    },
    // 改变是否显示
    changeStatus(item) {
      if (item.status === '1') {
        item.status = true
      } else if (item.status === '0') {
        item.status = false
      }
      this.changeAdvert(item)
    },
    // 改变广告厂商
    changeAdvertPlat(item) {
      if (item.advertPlat === '1') {
        item.advertStyleList = [
          {
            value: '1',
            label: '左图',
          },
          {
            value: '2',
            label: '右图',
          },
          {
            value: '3',
            label: '大图',
          },
        ]
      } else if (item.advertPlat === '2') {
        item.advertStyle = '3'
        item.advertStyleList = [
          {
            value: '3',
            label: '大图',
          },
        ]
      }
      this.changeAdvert(item)
    },
    // 改变样式
    changeStyle(item) {
      this.changeAdvert(item)
    },
    // 改变广告Id
    changeAdvertId(item) {
      if (item.extAdvertId) {
        if (!/^\d+$/.test(item.extAdvertId)) {
          return this.$message.error('广告ID请输入数字')
        }
        this.changeAdvert(item)
      }
    },
    // 改变每页展示
    changePageShow(item) {
      this.changeAdvert(item)
    },
    // 修改位置
    changePosition(item) {
      if (item.position) {
        if (/^\d+(\.\d+)?$/.test(item.position)) {
          if (parseInt(item.position) > 20) {
            return this.$message.error('位置数值应<=20')
          }
        } else {
          return this.$message.error('位置请输入数字或非负值')
        }
      }
      this.changeAdvert(item)
    },
    changeAdvert(item) {
      $emit(this, 'changeValue')
    },
    // 保存
    save() {
      const me = this
      if (me.advertList && me.advertList.length > 0) {
        me.disableSubmitPosition = false
        me.disableSubmitId = false
        me.disableSubmitPositionNum = false
        me.advertList &&
          me.advertList.map(function (value) {
            if (!value.position) {
              me.disableSubmitPosition = true
            }
            if (value.position) {
              if (/^\d+(\.\d+)?$/.test(value.position)) {
                if (parseInt(value.position) > 20) {
                  me.disableSubmitPositionNum = true
                }
              } else {
                me.disableSubmitPositionNum = true
              }
            }
            if (value.extAdvertId) {
              if (!/^\d+$/.test(value.extAdvertId)) {
                me.disableSubmitId = true
              }
            }
          })
      }
      if (me.disableSubmitPosition) {
        return me.$message.error('请完善位置信息')
      }
      if (me.disableSubmitPositionNum) {
        return me.$message.error('请修改位置数据在0-20之间')
      }
      if (me.disableSubmitId) {
        return me.$message.error('广告ID请输入数字')
      }
      me.advertList &&
        me.advertList.map(function (value) {
          advertSave({
            advertType: value.advertType,
            position: value.position,
            platform: value.platform,
            advertStyle: value.advertStyle,
            advertPlat: value.advertPlat,
            pageDisplay: value.pageDisplay,
            extAdvertId: value.extAdvertId || '',
            id: value.id || '',
            status: value.status === true ? '1' : '0',
          }).then((response) => {
            if (response.data.code === 0) {
              me.$message.success('成功')
              me.$store.commit('CHANGE_PAGE_CHANGED_STATUS', {
                name: me.$route.name,
                text: '',
              })
            } else {
              me.$message.error('失败')
            }
          })
        })
    },
  },
  emits: ['changeValue'],
}
</script>

<style lang="scss">
.item {
  color: #909399;
  font-size: 14px;
  text-align: center;
  line-height: 40px;
  border: 1px solid #ebeef5;
  border-width: 0 0px 1px 1px;
  .el-col {
    word-break: break-all;
    border-right: 1px solid #ebeef5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .el-input__inner {
    width: 80%;
    height: 30px !important;
    line-height: 30px !important;
  }
  .el-input__suffix {
    right: 15px;
  }
  &.header {
    border-width: 1px 0 1px 1px;
    overflow-y: scroll;
  }
  &.active {
    background: #dcdcdc;
  }
}
.content {
  height: calc(100vh - 240px);
  overflow-y: scroll;
}
.noData {
  text-align: center;
  line-height: 50px;
}
</style>
