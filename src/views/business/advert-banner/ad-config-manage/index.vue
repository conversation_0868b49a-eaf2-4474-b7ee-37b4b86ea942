<template>
  <div class="ad-config-manage">
    <el-form :model="ruleForm">
      <el-form-item label="插屏热启间隔时间">
        <el-input
          v-model="ruleForm.value"
          v-int-zero
          clearable
          style="width: 200px"
        />
        <span class="ml5">秒</span>
      </el-form-item>
      <el-form-item>
        <el-button @click="saveData">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import {
  getAppConfigExtendByName,
  setAppConfigExtendByName
} from '@/api/advertModule'

const { proxy } = getCurrentInstance()

const ruleForm = reactive({
  name: 'slot_ad_pull_frequency_interval_second',
  value: ''
})

onActivated(() => {
  getData()
})

const getData = () => {
  getAppConfigExtendByName({
    name: 'slot_ad_pull_frequency_interval_second'
  }).then((res) => {
    if (res.data.code === 0) {
      const data = res.data.data || {}
      ruleForm.value = data.value || ''
    }
  })
}

const saveData = () => {
  if (!ruleForm.value && ruleForm.value !== 0) {
    return proxy.$message.error('请输入插屏热启间隔时间')
  }
  setAppConfigExtendByName({
    ...ruleForm
  }).then((res) => {
    if (res.data.code === 0) {
      getData()
      proxy.$message.success('操作成功')
    } else {
      proxy.$message.error(res.data.msg || '操作失败')
    }
  })
}
</script>

<style lang="scss" scoped>
.ad-config-manage {
  padding: 20px;
}
</style>
