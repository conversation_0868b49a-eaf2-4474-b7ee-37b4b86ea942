<template>
  <div v-loading="loading" style="margin: 20px 20px 0">
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <el-form-item label="标题">
        <el-input
          v-model="ruleForm.name"
          type="text"
          placeholder="请选择标题"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="ruleForm.status" clearable>
          <el-option
            v-for="(value, index) in statusList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="展示位置">
        <el-select v-model="ruleForm.location" clearable>
          <el-option
            v-for="(value, index) in locationList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户分类">
        <el-select v-model="ruleForm.userType" clearable>
          <el-option
            v-for="(value, index) in userTypeList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="省份/直辖市/自治区">
        <el-input
          v-model="ruleForm.province"
          type="text"
          placeholder="请输入省份/直辖市/自治区"
          clearable
          style="width: 160px"
          @change="ruleForm.city = ''"
        />
      </el-form-item>
      <el-form-item label="城市">
        <el-input
          v-model="ruleForm.city"
          type="text"
          placeholder="请输入城市"
          clearable
          style="width: 160px"
          @change="ruleForm.province = ''"
        />
      </el-form-item>
      <el-form-item label="有效期起止时间" label-width="160px">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          :disabled-date="pickerOptions && pickerOptions.disabledDate"
          :cell-class-name="pickerOptions && pickerOptions.cellClassName"
          v-model="daterange"
          style="width: 400px"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="至"
          start-placeholder="有效期开始日期"
          end-placeholder="有效期结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search(1)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
        <el-button type="primary" @click="add()">新增</el-button>
        <el-button :loading="refreshLoading" type="warning" @click="refresh()"
          >刷新缓存</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      ref="articleList"
      :data="dataList"
      row-key="articleList"
      border
      style="width: 100%; overflow-y: auto; height: 65vh"
      @row-dblclick="rowDoubleClick"
    >
      <el-table-column prop="bannerid" label="ID" align="center" width="80" />
      <el-table-column prop="name" label="名称" align="center" />
      <el-table-column prop="userType" label="用户分类" align="center">
        <template v-slot="scope">
          <span>{{ convertUserTypeList[scope.row.userType] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="region" label="投放区域" align="center">
        <template v-slot="scope">
          <span v-if="scope.row.region">{{ scope.row.region }}</span>
          <span v-else>全国</span>
        </template>
      </el-table-column>
      <el-table-column prop="location" label="展示位置" align="center">
        <template v-slot="scope">
          <span>{{ convertBannerLocation[scope.row.location] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="frequency" label="打开频次" align="center">
        <template v-slot="scope">
          <span>{{ convertFrequencyList[scope.row.frequency] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="showTime" label="打开时刻" align="center">
        <template v-slot="scope">
          <span>第{{ scope.row.showTime || '0' }}次进入页面</span><br />
          <el-button type="primary" @click="setShowTime('up', scope.row)"
            >+</el-button
          >
          <el-button type="primary" @click="setShowTime('down', scope.row)"
            >-</el-button
          >
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template v-slot="scope">
          <el-switch
            v-model="scope.row.status"
            @change="changeSwitch(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="begindate" align="center" label="开始时间">
        <template v-slot="scope">{{
          scope.row.begindate ? $filters.timeFullS(scope.row.begindate) : ''
        }}</template>
      </el-table-column>
      <el-table-column prop="enddate" align="center" label="结束时间">
        <template v-slot="scope">{{
          scope.row.enddate ? $filters.timeFullS(scope.row.enddate) : ''
        }}</template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-sizes="[10, 20, 50]"
      :total="total"
      background
      layout="total, sizes, prev, pager, next, jumper"
      class="el-pagination-center"
      @size-change="handleSizeChange"
      @current-change="currentChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { deepCopy } from '@/utils'
import { timeFullS } from '@/filters'
import { getBannerList, saveBanner, clearShopCache } from '@/api/advertModule'
import { frequencyList } from '@/utils/enum'
import { pickerOptions } from '@/utils/configData'
import { convertKeyValueEnum } from '@/utils/convert'
import { recordOldData, recordBeforeAlter } from '@/utils/enum/logData'
export default {
  data() {
    return {
      loading: false,
      refreshLoading: false,
      pickerOptions: pickerOptions,
      page: 1,
      total: 0,
      limit: 10,
      // 筛选列表
      ruleForm: {
        name: '', // 名称
        status: '', // 状态
        location: '', // 展示位置
        userType: '', // 用户分类
        province: '', // 省份/直辖市/自治区
        city: '', // 城市
        beginDate: '', // 开始时间
        endDate: '' // 结束时间
      },
      statusList: {
        有效: '1',
        无效: '0'
      },
      locationList: {
        首页: '11',
        选车: '18',
        用车: '19',
        我的: '22'
      },
      userTypeList: {
        全部: '',
        新用户: '1',
        老用户: '0'
      },
      convertUserTypeList: {},
      convertBannerLocation: {},
      convertFrequencyList: convertKeyValueEnum(frequencyList),
      dataList: [],
      dayjs
    }
  },
  name: 'BulletWindow',
  components: {},
  computed: {
    daterange: {
      get() {
        if (this.ruleForm.beginDate && this.ruleForm.endDate) {
          return [this.ruleForm.beginDate, this.ruleForm.endDate]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginDate = value[0]
          this.ruleForm.endDate = value[1]
        } else {
          this.ruleForm.beginDate = ''
          this.ruleForm.endDate = ''
        }
      }
    }
  },
  activated() {
    this.convertUserTypeList = convertKeyValueEnum(this.userTypeList)
    this.convertBannerLocation = convertKeyValueEnum(this.locationList)
    this.search()
    sessionStorage.setItem('menu', 'S10403')
  },
  methods: {
    search() {
      const me = this
      me.loading = true
      const postData = {
        page: me.page,
        limit: me.limit,
        ...me.ruleForm
      }
      postData.location = postData.location || '11, 18, 19, 22'
      getBannerList(postData)
        .then((response) => {
          if (response.status === 200) {
            const data = response.data
            recordOldData(data.list)
            data.list.map((_) => {
              _.status = _.status !== '0'
              if (_.province || _.city) {
                _.region = `${_.province} ${_.city}`
                _.region =
                  _.region.length > 30
                    ? `${_.region.slice(0, 30)}...`
                    : _.region
              }
            })
            me.dataList = data.list
            me.total = data.total
            me.loading = false
          } else {
            me.loading = false
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.loading = false
          me.$message.error(err.message || '获取列表失败')
        })
    },
    add() {
      this.$router.push({
        name: 'PageBulletDetail'
      })
    },
    rowDoubleClick(data) {
      this.$router.push({
        name: 'PageBulletDetail',
        query: {
          id: data && data.bannerid
        }
      })
    },
    // 变更查询个数
    handleSizeChange(limit) {
      this.limit = limit
      this.search(this.page)
    },
    // 变更页签
    currentChange(page) {
      this.search(page)
    },
    // 改变打开失效
    setShowTime(type, data) {
      const me = this
      if (type === 'down' && data.showTime - 1 < 1) {
        return me.$message.error('最小为1')
      }
      me.loading = true
      type === 'down' ? data.showTime-- : data.showTime++
      const postData = deepCopy(data)
      delete postData.createtime
      delete postData.updatetime
      postData.beginDateStr = timeFullS(Math.floor(new Date(data.begindate)))
      postData.endDateStr = timeFullS(Math.floor(new Date(data.enddate)))
      postData.status = postData.status ? '1' : '0'
      postData.operation = type === 'down' ? '0' : '1'
      recordBeforeAlter(postData, 'id')
      saveBanner(postData).then((response) => {
        me.loading = false
        if (response.data.code === 0) {
          me.$message.success(`修改成功`)
        } else if (response.data.code === 1001) {
          const tip =
            type === 'down'
              ? `已存在第${response.data.data}条数据`
              : `可使用${response.data.data}`
          type === 'down' ? data.showTime++ : data.showTime--
          me.$message.error(tip)
        } else {
          me.$message.error(response.data.msg)
          type === 'down' ? data.showTime++ : data.showTime--
        }
      })
    },
    // 切换状态
    changeSwitch(data) {
      const me = this
      const postData = deepCopy(data)
      postData.status = postData.status ? '1' : '0'
      delete postData.createtime
      delete postData.updatetime
      postData.beginDateStr = timeFullS(Math.floor(new Date(data.begindate)))
      postData.endDateStr = timeFullS(Math.floor(new Date(data.enddate)))
      recordBeforeAlter(postData, 'id')
      saveBanner(postData).then((response) => {
        me.loading = false
        if (response.data.code === 0) {
          me.$message.success(`修改成功`)
        } else {
          me.$message.error(response.data.msg)
          postData.status = !postData.status
        }
      })
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        name: '', // 名称
        status: '', // 状态
        location: '', // 展示位置
        userType: '', // 用户分类
        province: '', // 省份/直辖市/自治区
        city: '', // 城市
        beginDate: '', // 开始时间
        endDate: '' // 结束时间
      }
      this.search()
    },
    // 刷新缓存
    refresh() {
      const me = this
      this.$confirm('此操作将刷新banner缓存, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          me.refreshLoading = true
          clearShopCache({
            type: 'homebanner',
            _dc: Math.floor(new Date())
          })
            .then((response) => {
              if (response.data.code === 0) {
                this.$message.success('刷新成功')
              }
            })
            .finally((_) => {
              me.refreshLoading = false
            })
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }
}
</script>
