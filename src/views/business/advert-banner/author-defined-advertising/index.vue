<template>
  <div v-loading="loading" style="margin: 20px 20px 0">
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <el-form-item label="广告ID">
        <el-input
          v-model="ruleForm.ids"
          placeholder="请输入广告ID"
          clearable
          style="width: 130px"
        />
      </el-form-item>
      <seleted-user
        ref="selectUser"
        :name="'用户名'"
        :placeholder="'请输入关键词'"
        @sendData="setUid"
      />
      <el-form-item label="广告状态">
        <el-select v-model="ruleForm.status">
          <el-option
            v-for="(value, index) in statusList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search(1)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="articleList"
      :data="dataList"
      row-key="articleList"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="广告ID" align="center" width="80" />
      <el-table-column prop="username" label="用户名" align="center" />
      <el-table-column prop="title" label="广告标题" align="center" />
      <el-table-column prop="cover" label="图片" align="center">
        <template v-slot="scope">
          <el-image
            v-if="scope.row.cover"
            :src="scope.row.cover"
            class="img-content"
            @click="seeBigImg(scope.row.cover)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="link" label="链接" align="center">
        <template v-slot="scope">
          <span style="color: green" @click="seeLink(scope.row.link)">{{
            scope.row.link
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="广告状态" align="center">
        <template v-slot="scope">
          <span>{{ convertStatusList[scope.row.status] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" align="center" label="提交日期">
        <template v-slot="scope">{{
          scope.row.updateTime ? $filters.timeFullS(scope.row.updateTime) : ''
        }}</template>
      </el-table-column>
      <el-table-column prop="location" label="操作" align="center">
        <template v-slot="scope">
          <el-button
            v-if="scope.row.status !== 1"
            type="primary"
            size="small"
            @click="updataStatus('1', scope.row.id, '')"
            >通过</el-button
          >
          <el-button
            v-if="scope.row.status !== 2"
            type="danger"
            size="small"
            @click="colseJurisdiction(scope.row.id)"
            >不通过</el-button
          >
        </template>
      </el-table-column>
      <el-table-column prop="reason" label="审核不通过原因" align="center" />
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-sizes="[10, 20, 50]"
      :total="total"
      background
      layout="total, sizes, prev, pager, next, jumper"
      class="el-pagination-center"
      @size-change="handleSizeChange"
      @current-change="currentChange"
    />
    <reject-notice
      ref="rejectNotice"
      :title="'拒绝原因'"
      :placeholder="'填写审核不通过原因(必填)'"
      @confirmRejection="confirmRejection"
    />
    <choose-show-image ref="showImage" />
  </div>
</template>

<script>
import { getCustomAdList, adUpdateStatus } from '@/api/advertModule'
import { convertKeyValueEnum } from '@/utils/convert'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import SeletedUser from '@/components/SeletedUser/SeletedUser.vue'
import RejectNotice from '@/components/Notice/rejectNotice.vue'
import { recordOldData, recordBeforeAlter } from '@/utils/enum/logData'
export default {
  name: 'BottomPrompt',
  components: {
    RejectNotice,
    ChooseShowImage,
    SeletedUser
  },
  data() {
    return {
      loading: false,
      page: 1,
      total: 0,
      limit: 10,
      updataId: '', // 更新id
      ruleForm: {
        ids: '', // 广告id
        autherid: '', // 用户id
        status: '' // 状态
      }, // 筛选列表
      statusList: {
        全部: '',
        待审核: '0',
        有效: '1',
        审核不通过: '2'
      },
      convertStatusList: {},
      dataList: []
    }
  },
  computed: {},
  activated() {
    this.convertStatusList = convertKeyValueEnum(this.statusList)
    this.search()
  },
  methods: {
    search() {
      const me = this
      me.loading = true
      const postData = {
        page: me.page,
        limit: me.limit,
        ...me.ruleForm
      }
      // 获取数据
      getCustomAdList(postData)
        .then((response) => {
          if (response.status === 200) {
            const data = response.data.data
            me.dataList = data.listData
            me.total = data.total
            me.loading = false
            recordOldData(me.dataList)
          } else {
            me.loading = false
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.loading = false
          me.$message.error(err.message || '获取列表失败')
        })
    },
    // 变更查询个数
    handleSizeChange(limit) {
      this.limit = limit
      this.search(this.page)
    },
    // 变更页签
    currentChange(page) {
      this.search(page)
    },
    // 大图
    seeBigImg(img) {
      this.$refs.showImage.init(img)
    },
    // 链接
    seeLink(seeLink) {
      window.open(seeLink)
    },
    // 设置返回uid
    setUid(id) {
      this.ruleForm.autherid = id
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        ids: '', // 广告id
        autherid: '', // 用户id
        status: '' // 状态
      }
      this.$refs.selectUser.clearData()
      this.search()
    },
    // 关闭权限
    colseJurisdiction(id) {
      this.updataId = id
      this.$refs.rejectNotice.init()
    },
    // 确认或取消
    confirmRejection(mes) {
      this.updataStatus('2', this.updataId, mes)
    },
    updataStatus(type, id, rejectData) {
      const me = this
      recordBeforeAlter({ id: id }, 'id')
      adUpdateStatus({
        id: id,
        status: type,
        reason: rejectData || ''
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('操作成功')
          me.rejectShowStatus = false
          me.search()
        } else {
          me.$message.error(response.data.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 10px;
  display: block;
  width: 100%;
  resize: none;
}
</style>
