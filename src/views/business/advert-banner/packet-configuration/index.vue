<template>
  <div v-loading="loading" style="margin: 20px 20px 0">
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <el-form-item label="包名">
        <el-select v-model="ruleForm.packageName" @change="search()">
          <el-option
            v-for="(value, index) in packageNameList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <el-form ref="ruleForm" :model="dataContent" label-width="100px">
      <el-form-item label="弹窗图片" prop="title" class="pd10">
        <img
          v-if="dataContent.img"
          :src="dataContent.img"
          class="data-img"
          alt
          @click="seeBigImg(dataContent.img)"
        />
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccess"
          name="upfile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <el-button>选择图片</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="弹窗开关">
        <el-switch v-model="dataContent.flag" active-text="" inactive-text="" />
      </el-form-item>
      <el-button @click="add()">保存</el-button>
    </el-form>
    <choose-show-image ref="showImage" />
  </div>
</template>

<script>
import { getPacketInfo, savePacketInfo } from '@/api/advertModule'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
export default {
  name: 'PacketConfiguration',
  components: {
    ChooseShowImage,
  },
  data() {
    return {
      loading: false,
      ruleForm: {
        packageName: 'com.halo.getprice', // 小包包名
      }, // 筛选列表
      packageNameList: {
        报价大全: 'com.halo.getprice',
        摩托车库: 'com.jdd.motorcheku',
        探索版: 'com.jdd.wanmt',
        二手车: 'com.halo.secondhand',
      },
      dataContent: {},
    }
  },
  computed: {},
  activated() {
    this.search()
  },
  methods: {
    search() {
      const me = this
      me.loading = true
      const postData = {
        ...me.ruleForm,
      }
      // 获取数据
      getPacketInfo(postData)
        .then((response) => {
          if (response.status === 200) {
            me.dataContent = response.data.data
            me.dataContent.flag = !!(me.dataContent.flag === 1)
            me.loading = false
          } else {
            me.loading = false
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.loading = false
          me.$message.error(err.message || '获取列表失败')
        })
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      this.$oss.ossUploadImage(option)
    },
    // 上传成功
    onSuccess(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        this.dataContent.img = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误',
        })
      }
    },
    // 大图
    seeBigImg(img) {
      this.$refs.showImage.init(img)
    },
    // 上传数据
    add() {
      const me = this
      if (!me.dataContent.img) {
        return me.$message.error('请上传图片')
      }
      const postData = {
        packageName: me.ruleForm.packageName,
        ...me.dataContent,
      }
      postData.flag = postData.flag ? 1 : 0
      savePacketInfo(postData).then((response) => {
        if (response.data.code === 0) {
          me.$message.success(`变更成功`)
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.data-img {
  display: inline-block;
  max-height: 300px;
  max-width: 300px;
}
</style>
