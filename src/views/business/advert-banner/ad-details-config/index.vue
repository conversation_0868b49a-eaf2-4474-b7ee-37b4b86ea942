<template>
  <div class="ad-details-config">
    <SelectAdPlan ref="SelectAdPlan" @next="updateNext" />
    <div class="title" v-if="nextStatus">
      <span>选择广告位置</span>
      <div class="line"></div>
    </div>
    <SelectAdPlacement
      v-if="nextStatus"
      ref="SelectAdPlacement"
      :isShopType="isShopType"
    />
    <div v-if="$store.state.adConfig.adDetailShow && isActive">
      <div class="title">
        <span>配置广告内容</span>
        <div class="line"></div>
      </div>
      <AdConfigDifferentA
        ref="AdConfigDifferentA"
        @requiredItem="requiredItem"
      />
      <div class="line margin-bottom"></div>
      <AdConfigIdentical
        ref="AdConfigIdentical"
        @requiredItem="requiredItemB"
      />
      <div class="line margin-bottom"></div>
      <AdConfigDifferentB ref="AdConfigDifferentB" @requiredItem="submitAd" />
      <div class="submit-config" v-if="showStatus">
        <el-button type="primary" @click="requiredItemA">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import SelectAdPlan from './components/SelectAdPlan.vue'
import SelectAdPlacement from './components/SelectAdPlacement.vue'
import AdConfigDifferentA from './components/AdConfigDifferentA.vue'
import AdConfigIdentical from './components/AdConfigIdentical.vue'
import AdConfigDifferentB from './components/AdConfigDifferentB.vue'
import {
  adAdvertManageDetail,
  adAdvertManageAdd,
  adAdvertManageUpdate
} from '@/api/advertModule'
import { timeFullS } from '@/filters'

import { deepCopy } from '@/utils'
import { mapGetters } from 'vuex'
import { postLog } from '@/components/seeLog/SaveLog.js'
export default {
  name: 'AdDetailsConfig',
  components: {
    SelectAdPlan,
    SelectAdPlacement,
    AdConfigDifferentA,
    AdConfigIdentical,
    AdConfigDifferentB
  },
  data() {
    return {
      id: '', // 广告id
      copy: '', // 是否是复制
      isActive: false, // 是否活动
      flag: true,
      isShopType: false, // 是否是经销商广告
      nextStatus: false, // 下一步
      showStatus: false, // 是否展示提交
      adIdOld: ''
    }
  },
  activated() {
    const query = this.$route.query || {}
    this.id = query.id || ''
    this.copy = query.copy || ''
    this.adIdOld = sessionStorage.getItem('adIdOld') || ''
    let idNew = query.id || ''
    this.isShopType = !!query.shopType
    idNew = idNew && idNew.toString()
    if (idNew !== this.adIdOld) {
      sessionStorage.setItem('adIdOld', idNew)
      this.flag = true
      this.$store.dispatch('resetAdDetail')
    }
    sessionStorage.setItem('AdDetailsConfig', '{}') // 保存日志使用
    const adDetailData = this.$store.state.adConfig.adDetailData || {}
    this.showStatus = true
    if (this.flag) {
      this.flag = false
      if (this.id) {
        this.adAdvertManageDetail(this.id)
      }
    }
    if (
      (idNew !== this.adIdOld && !this.id) ||
      (idNew === this.adIdOld && !adDetailData.campaignId)
    ) {
      this.$refs.SelectAdPlan.initData({
        campaignId: query.campaignId || '',
        orgId: query.orgId || '',
        orgType: query.orgType || ''
      })
      this.$refs.SelectAdPlacement && this.$refs.SelectAdPlacement.init()
    } else if (idNew === this.adIdOld) {
      this.$store.dispatch('changeAdDetailShow', true)
    }
    this.isActive = true
    // if (query.orgId && query.orgType) {
    //   this.$refs.SelectAdPlan.initData({
    //     campaignId: query.campaignId || '',
    //     orgId: query.orgId || '',
    //     orgType: query.orgType
    //   })
    // }
    this.$store.dispatch('getCityList')
    // this.nextStatus = !!this.id
  },
  computed: {
    ...mapGetters(['uid', 'name', 'cachedViews']),
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    }
  },
  watch: {
    cachedViews: {
      deep: true,
      handler(newVal) {
        if (!newVal.includes('AdDetailsConfig')) {
          this.flag = true
        }
      }
    }
  },
  methods: {
    // 获取广告详情
    adAdvertManageDetail(id) {
      const me = this
      adAdvertManageDetail({ id }).then((res) => {
        if (res.data.code === 0) {
          me.nextStatus = true
          const data = res.data.data
          sessionStorage.setItem('AdDetailsConfig', JSON.stringify(data)) // 保存日志使用
          data.platform = data.platform.split(',')
          // data.timeSeries = data.timeSeries ? data.timeSeries.split(',') : []
          data.timeSeries = data.timeSeries || []
          data.isRepeat = data.isRepeat ? true : false
          data.exclusiveFlag = data.exclusiveFlag ? true : false
          data.regionListStr = data.regionList
          data.relationListStr = data.relationList
          data.uvControl = data.uvControl ? data.uvControl.toString() : data.uvControl
          data.isRemind = data.isRemind === 1
          data.pageSelect = ''
          if (data.controlType === 3) {
            data.controlHoursPredictDay =
              Math.floor(data.controlHours / 24) || 0
            data.controlHoursPredictHour =
              data.controlHours - 24 * data.controlHoursPredictDay
          } else {
            data.controlHours = data.controlHours ? data.controlHours / 24 : ''
          }
          delete data.regionList
          delete data.relationList
          me.$refs.SelectAdPlan.initData(data)
          me.$store.dispatch('changeAdDetail', data)
          me.showStatus = me.copy || data.status !== -1
          setTimeout(() => {
            me.$refs.SelectAdPlacement && me.$refs.SelectAdPlacement.init()
          }, 10)
        }
      })
    },
    requiredItemA() {
      const flag = sessionStorage.getItem('adMerchantStatus')
      if (flag) {
        return this.$message.error('该店铺状态异常')
      }
      if (this.copy) {
        const detailsForm = deepCopy(this.$store.state.adConfig.adDetailData)
        if (
          (detailsForm.adProjectType === 2 || detailsForm.orgType === 2) &&
          !detailsForm.orderNum
        ) {
          return this.$message.error('请选择关联广告服务订单')
        }
      }
      this.$refs.AdConfigDifferentA &&
        this.$refs.AdConfigDifferentA.requiredItem()
      this.isShopType = this.isShopType
        ? this.isShopType
        : this.$refs.SelectAdPlacement.updateShopType
    },
    requiredItem() {
      this.$refs.AdConfigIdentical &&
        this.$refs.AdConfigIdentical.requiredItem()
    },
    requiredItemB() {
      this.$refs.AdConfigDifferentB &&
        this.$refs.AdConfigDifferentB.requiredItem()
    },
    // 创建广告
    submitAd(checkConflict) {
      const isShopType = this.isShopType
        ? this.isShopType
        : this.$refs.SelectAdPlacement.updateShopType
      const detailsForm = deepCopy(this.$store.state.adConfig.adDetailData) // 广告数据
      detailsForm.platform = detailsForm.platform.join(',')
      detailsForm.isRepeat = detailsForm.isRepeat ? 1 : 0
      detailsForm.exclusiveFlag = detailsForm.exclusiveFlag ? 1 : 0
      detailsForm.regionListStr =
        JSON.stringify(detailsForm.regionListStr) === '[]'
          ? ''
          : JSON.stringify(detailsForm.regionListStr)
      detailsForm.relationListStr =
        JSON.stringify(detailsForm.relationListStr) === '[]'
          ? ''
          : JSON.stringify(detailsForm.relationListStr)
      detailsForm.linkType = detailsForm.linkType
        ? detailsForm.linkType
        : detailsForm.pageSelect
      detailsForm.advertPlatform = 1
      detailsForm.operatorId = this.uid // 操作人ID
      detailsForm.operatorName = this.name // 操作人姓名
      detailsForm.shopType = isShopType ? 1 : ''
      detailsForm.isRemind = detailsForm.isRemind ? 1 : 0
      detailsForm.remindUserName = detailsForm.remindUserName || this.name
      detailsForm.userLable = !detailsForm.userLableFilter
        ? ''
        : detailsForm.userLable ||
          JSON.stringify({
            type: '',
            price: '',
            volume: '',
            source: '',
            energy: '',
            brand: '',
            car: ''
          })
      detailsForm.controlHours = detailsForm.controlHours
        ? detailsForm.controlHours * 24
        : ''
      if (['3', 3].includes(detailsForm.controlType)) {
        detailsForm.controlHours =
          (detailsForm.controlHoursPredictDay
            ? detailsForm.controlHoursPredictDay * 24
            : 0) +
          (detailsForm.controlHoursPredictHour
            ? Number(detailsForm.controlHoursPredictHour)
            : 0)
      }
      delete detailsForm.displayRelationList
      delete detailsForm.pageSelect
      if (detailsForm.linkUrl) {
        detailsForm.linkUrl = detailsForm.linkUrl.toString().trim()
      }
      console.log(`object`, detailsForm)
      // if (checkConflict === 0) {
      detailsForm.checkConflict = checkConflict
      // }
      detailsForm.position =
        detailsForm.clientPage === 121 ? 1 : detailsForm.position || ''
      console.log(detailsForm)
      if (this.id && !this.copy) {
        return this.adAdvertManageUpdate(detailsForm)
      }
      this.adAdvertManageAdd(detailsForm)
    },
    // 创建
    adAdvertManageAdd(obj) {
      const currentTime = timeFullS(Math.floor(new Date())).split(' ')[0]
      const beginTime = timeFullS(obj.beginTime).split(' ')[0]
      const postData = {
        ...obj,
        beginTime:
          beginTime === currentTime
            ? timeFullS(Math.floor(new Date()) + 60000)
            : obj.beginTime // 跟pc时间日期一致，传递时间增加一分钟
      }
      adAdvertManageAdd(postData)
        .then((res) => {
          if (res.data.code === 0) {
            this.id = res.data.data
            this.saveLog(postData, '新增')
            this.$message.success('创建成功')
            this.flag = true
            this.goBack()
          } else {
            this.$message.error(res.data.msg || '创建失败')
          }
        })
        .catch((err) => {
          this.repetition(err)
        })
    },
    // 更新
    adAdvertManageUpdate(obj) {
      adAdvertManageUpdate({
        adUnitId: this.id,
        ...obj
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.saveLog(obj, '编辑')
            this.$message.success('更新成功')
            this.flag = true
            this.goBack()
          } else {
            this.$message.error(res.data.msg || '更新失败')
          }
        })
        .catch((err) => {
          this.repetition(err)
        })
    },
    // 重复广告
    repetition(err) {
      const data = err.response.data
      const detailsForm = deepCopy(this.$store.state.adConfig.adDetailData) // 广告数据
      if (data?.data?.repeatId) {
        this.$confirm(`${err.message}`, {
          showConfirmButton:
            detailsForm.adProjectType === 2 || detailsForm.orgType === 2
              ? false
              : true,
          cancelButtonText: '去查看',
          confirmButtonText: '继续配置',
          showClose: true,
          distinguishCancelAndClose: true
        })
          .then((_) => {
            this.submitAd(0)
          })
          .catch((_) => {
            console.log(_)
            if (_ === 'close') {
              return
            }
            sessionStorage.setItem('AdvertisingListNew', true)
            const toTaskLog = this.$router.resolve({
              name: 'AdvertisingList',
              query: {
                repeatData: encodeURIComponent(JSON.stringify(data.data || {}))
              }
            })
            window.open(toTaskLog.href, '_blank')
          })
      }
    },
    // 返回
    goBack() {
      sessionStorage.setItem('adIdOld', '')
      this.$store.dispatch('resetAdDetail')
      this.$refs.SelectAdPlan.initData({})
      this.nextStatus = false
      this.delView(this.$route)
      if (this.isShopType) {
        return this.$router.go(-1)
      }
      this.$router.push({
        name: 'AdvertisingList'
      })
    },
    // 新增或者编辑，完成后关闭tag
    delView(view) {
      this.$store.dispatch('updateDelTagData', view)
    },
    // 广告方及计划 下一步
    updateNext(status) {
      if (this.nextStatus && status)
        return this.$refs.SelectAdPlacement.resetData()
      this.nextStatus = status
      if (!status) {
        this.$store.dispatch('changeAdDetailShow', false)
      }
    },
    // 广告日志
    saveLog(detailsForm, operateType) {
      const afterData = {
        ...deepCopy(detailsForm),
        shopType: 0
      }
      // 兼容布尔值、数组、null、数字
      ;['exclusiveFlag', 'isRemind', 'isRepeat'].map((item) => {
        afterData[item] = afterData[item] ? 1 : 0
      }) // 转换数据将，true 改成1
      ;['channel'].map((item) => {
        afterData[item] = afterData[item].length ? afterData[item] : ''
      })
      ;['timeSeries'].map((item) => {
        afterData[item] =
          afterData[item] && afterData[item].length ? afterData[item] : null
      })
      ;['displayRelationList', 'relationListStr'].map((item) => {
        afterData[item] = afterData[item] || []
      })
      ;[
        ['relationList', 'relationListStr'],
        ['regionList', 'regionListStr']
      ].map((item) => {
        afterData[item[0]] = afterData[item[1]] || []
      })
      ;['relationList', 'regionList', 'timeSeries'].map((item) => {
        afterData[item] =
          afterData[item] && afterData[item].length
            ? JSON.parse(afterData[item])
            : afterData[item]
      })
      ;['userLable', 'linkType'].map((item) => {
        afterData[item] = afterData[item] || null
      })
      ;['controlType', 'uvControl'].map((item) => {
        afterData[item] = afterData[item]
          ? Number(afterData[item])
          : afterData[item]
      })
      ;[
        'regionListStr',
        'relationListStr',
        'controlHoursPredictDay',
        'controlHoursPredictHour'
      ].map((item) => {
        delete afterData[item]
      })
      const beforeData = sessionStorage.getItem('AdDetailsConfig')
      const isNew = this.id && !this.copy
      postLog(
        48,
        this.id || '',
        operateType,
        '',
        isNew ? beforeData : '{}',
        JSON.stringify(afterData)
      )
    }
  },
  beforeRouteLeave(to, from, next) {
    this.isActive = false
    this.$store.dispatch('changeAdDetailShow', false)
    next()
  }
}
</script>

<style lang="scss">
.ad-details-config {
  padding: 0 20px 0 10px;
  .title {
    margin: 20px 0;
    span {
      font-size: 21px;
      font-weight: 600;
    }
    .line {
      background-color: #c7c7c7;
      width: calc(100% - 140px);
      position: relative;
      left: 140px;
      bottom: 10px;
    }
  }
  .submit-config {
    margin: 10px 0 50px 50px;
  }
  .line {
    height: 1px;
    background-color: #dedede;
  }
  .margin-bottom {
    margin-bottom: 22px;
    width: 75%;
  }
}
</style>
