<template>
  <div class="newArea">
    <el-dialog
      v-model="dialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handlerClose"
      :title="dialogTitle"
      width="850px"
    >
      <div class="content">
        <div v-if="showSearchCity">
          城市选择:
          <el-select
            v-model="selectCity"
            filterable
            remote
            reserve-keyword
            placeholder="请输入关键词"
            :remote-method="remoteMethodCity"
            @change="changeCity()"
          >
            <el-option
              v-for="item in selectCityList"
              :key="item.value"
              :label="item.cityName"
              :value="item.cityName"
            >
            </el-option>
          </el-select>
        </div>
        <div class="content-select">
          <p v-if="selectedProvince.length">
            已选省份：<span v-for="(p, index) in selectedProvince" :key="index"
              >{{ p }}&ensp;
            </span>
          </p>
          <p v-if="selectedCitys.length">
            已选城市：<span v-for="(c, index) in selectedCitys" :key="index"
              >{{ c }}
            </span>
          </p>
          <p>
            <el-button style="height: 40px" type="info" plain @click="reset()"
              >重置</el-button
            >
          </p>
        </div>
        <el-tree
          :data="treeData"
          :props="defaultProps"
          :load="loadNode"
          lazy
          @check="check"
          @node-expand="nodeExpand"
          show-checkbox
          node-key="name"
          accordion
          ref="tree"
        >
        </el-tree>
      </div>
      <div class="fooder">
        <el-button type="success" @click="confirm">确认</el-button>
        <el-button type="danger" @click="handlerClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { GetArea } from '@/api/searchMap'
import { settings } from 'nprogress'
import { mapGetters } from 'vuex'

export default {
  name: 'NewMultiArea',
  data() {
    return {
      dialogVisible: false,
      treeData: [], // 树形图数据
      defaultProps: {
        children: 'Citys',
        label: 'name'
      },
      checkAll: true, // 是否选中全国
      defaultCheckedKeys: [], // 默认勾选项，只需要传递城市ID, 如果传入的是省份ID会默认勾选下面所有市
      selectedData: {}, // 选中的数据列
      selectedProvince: [], // 选中的省份
      selectedCitys: [], // 选中的城市
      loadCityData: [], // 需要加载城市列表的省份名
      selectCityList: [], // 筛选后的城市
      selectCity: ''
    }
  },
  props: {
    dialogTitle: {
      typeof: String,
      default: '城市选择'
    },
    cityDataStatus: {
      typeof: Boolean,
      default: true
    },
    showSearchCity: {
      typeof: Boolean,
      default: true
    }
  },
  computed: {
    ...mapGetters(['cityList'])
  },
  methods: {
    // 加载数据
    loadNode(node, resolve) {
      const me = this
      if (node.level === 0 && !me.treeData.length) {
        return me.getProvice({})
      }
      const levelData = node.data
      if (levelData.name === '全国' || node.level > 1) return resolve([])
      if (node.level === 1) {
        me.cityDataStatus ? me.getProvice(levelData, resolve) : resolve([])
      }
    },
    // 获取地理位置 省份
    getProvice(data, resolve, loadStatus) {
      const me = this
      GetArea({
        provinceName: data.name,
        provinceCode: data.provinceCode
      }).then((res) => {
        const provinceObjectList = res.data.data && res.data.data.list
        if (!provinceObjectList.length) {
          return
        }
        if (loadStatus) {
          const loadData = me.$refs.tree.getNode(data.name)
          provinceObjectList.map((item) => {
            me.$refs.tree.append(item, loadData)
          })
          me.$refs.tree.store.nodesMap[data.name].indeterminate = true
          me.$refs.tree.store.nodesMap[data.name].loaded = true
        } else if (data.name) {
          setTimeout(() => {
            resolve(provinceObjectList)
          }, 100)
        } else {
          me.treeData = provinceObjectList
          me.treeData.unshift({
            name: '全国',
            Citys: []
          })
          setTimeout(() => {
            me.loadCityList()
          }, 100)
        }
      })
    },
    // 需要加载数据
    loadCityList() {
      console.log(this.loadCityData)
      if (!(this.loadCityData && this.loadCityData.length)) return
      if (!this.cityDataStatus) return
      const me = this
      Promise.all(
        me.loadCityData.map(async (item) => {
          const findData = me.treeData.find((tree) => {
            return tree.name === item
          })
          await me.getProvice(findData, '', true)
        })
      ).then((response) => {
        setTimeout(() => {
          me.$refs.tree.setCheckedKeys(me.defaultCheckedKeys)
          me.showData()
        }, 200)
      })
    },
    // 当复选框被点击的时候触发
    check(data) {
      const me = this
      if (data.name !== '全国' && me.checkAll) {
        me.checkAll = false
        me.$refs.tree.setChecked('全国', false, true)
      }
      if (data.name === '全国' && !me.checkAll) {
        me.checkAll = true
        me.reset()
        me.$refs.tree.setCheckedKeys(['全国'])
      }
      me.showData()
    },
    // 节点展开时设置, 重新设置了选中值
    nodeExpand(data) {
      const me = this
      // if (me.loadCityData && me.loadCityData.length && me.loadCityData.length !== 1) return // 多个城市筛选，不管
      // if (data.name !== me.loadCityData[0]) return // 当前选择省份和传值过来的数据不一致
      const defaultCheckedKeys = [
        ...this.selectedCitys,
        ...this.selectedProvince
      ]
      setTimeout(() => {
        me.$refs.tree.setCheckedKeys(defaultCheckedKeys)
      }, 200)
    },
    // 展示数据
    showData() {
      const me = this
      const treeList = this.$refs.tree.getCheckedNodes(false, true)
      let findData = new Set()
      treeList.map((item) => {
        if (findData[item.provinceCode] === undefined) {
          findData[item.provinceCode] = []
        }
        findData[item.provinceCode].push(item.name)
      })
      me.resetShowData()
      Object.values(findData).map((item) => {
        if (!item.length) return
        // 仅省一级时
        if (item.length === 1) return me.setProvinceShowData(item[0])
        // 省下有选中数据
        const findNode = me.$refs.tree.getNode(item[0]) // 省下的子节点
        const isEqual =
          findNode.childNodes.length === item.length - 1 ||
          findNode.childNodes.length === 0 // item 数组中自带省名所以要减 1,直辖市
        // 选中数据和查询到的子节点数据完全相等时,走增加省份逻辑，否则走增加城市逻辑
        isEqual ? me.setProvinceShowData(item[0]) : me.setCitysShowData(item)
      })
    },
    // 提交数据
    confirm() {
      $emit(this, 'backData', {
        checkAll: this.checkAll,
        selectedDataNew: JSON.stringify(this.selectedData)
      })
      this.reset()
      this.dialogVisible = false
    },
    // 初始化打开弹窗
    init(info) {
      const me = this
      me.dialogVisible = true
      me.defaultCheckedKeys = info.defaultCheckedKeys || []
      if (info.loadCityList && info.loadCityList.length)
        me.loadCityData = info.loadCityList.filter((item) => {
          if (!item.includes('市')) return item
        })
      // 仅省的拉省下数据，直辖市不拉
      me.$nextTick(() => {
        // 单选关闭所有展开项
        const treeAllNodes = me.$refs.tree.store._getAllNodes()
        if (treeAllNodes && treeAllNodes.length > 0) {
          treeAllNodes.map((item) => (item.expanded = false))
        }
        if (me.defaultCheckedKeys.length > 0) {
          me.$refs.tree.setCheckedKeys(me.defaultCheckedKeys)
        } else {
          // 勾选全国
          me.reset()
          me.$refs.tree.setCheckedKeys(['全国'])
        }
        // if (!(info.loadCityList && info.loadCityList.length)) me.showData()
        me.showData()
      })
    },
    // 关闭弹窗
    handlerClose() {
      this.reset()
      this.dialogVisible = false
    },
    // 重置数据
    reset() {
      this.$refs.tree && this.$refs.tree.setCheckedKeys([])
      this.showData()
    },
    // 重置显示数据
    resetShowData() {
      this.selectedData = {} // 选中的数据列
      this.selectedProvince = [] // 选中的城市
      this.selectedCitys = [] // 选中的城市
    },
    // 设置展示省份名
    setProvinceShowData(name) {
      if (name === '全国') return
      this.selectedProvince.push(name)
      this.selectedData[name] = []
    },
    // 设置展示城市
    setCitysShowData(item) {
      const provinceName = item.splice(0, 1)
      this.selectedCitys = this.selectedCitys.concat(item)
      this.selectedData[provinceName] = item
    },
    // 筛选城市
    remoteMethodCity(query) {
      if (query !== '') {
        this.selectCityList = this.cityList.filter((item) => {
          return item.cityName.toLowerCase().indexOf(query.toLowerCase()) > -1
        })
      } else {
        this.selectCityList = []
      }
    },
    // 选中城市
    changeCity() {
      const me = this
      if (!me.selectCity) return
      const findData =
        me.selectCityList.find((item) => {
          return item.cityName === me.selectCity
        }) || {}
      const findTreeData =
        me.treeData.find((item) => {
          return item.name === findData.provinceName
        }) || {}
      if (!(findTreeData.Citys && findTreeData.Citys.length)) {
        // 没找到省下的城市数据
        Promise.all(
          [findTreeData].map(async (item) => {
            await me.getProvice(item, '', true)
          })
        ).then((response) => {
          setTimeout(() => {
            me.updateChangeCity(findData)
          }, 100)
        })
      } else {
        me.updateChangeCity(findData)
      }
    },
    // 选中城市后更新
    updateChangeCity(findData) {
      const me = this
      me.selectedData[findData.provinceName] =
        me.selectedData[findData.provinceName] &&
        me.selectedData[findData.provinceName].length
          ? me.selectedData[findData.provinceName].includes(findData.cityName)
            ? me.selectedData[findData.provinceName]
            : me.selectedData[findData.provinceName].concat(
                findData.provinceName
              )
          : [findData.provinceName]
      me.selectedCitys = me.selectedCitys.includes(findData.cityName)
        ? me.selectedCitys
        : me.selectedCitys.concat(findData.cityName)
      setTimeout(() => {
        const defaultCheckedKeys = [...me.selectedCitys, ...me.selectedProvince]
        me.$refs.tree.setCheckedKeys(defaultCheckedKeys)
        me.showData()
        me.checkAll = false
      }, 200)
      setTimeout(() => {
        Object.keys(me.$refs.tree.store.nodesMap).map((item) => {
          me.$refs.tree.store.nodesMap[item].expanded = false
        })
        me.$refs.tree.store.nodesMap[findData.provinceName].expanded = true
      }, 1000)
      setTimeout(() => {
        me.selectCity = ''
        me.selectCityList = []
      }, 1)
    }
  },
  emits: ['backData']
}
</script>

<style lang="scss">
.newArea {
  .content {
    .el-tree {
      width: 800px;
      display: flex;
      flex-wrap: wrap;
      .el-tree-node {
        width: 200px;
      }
    }
  }
  .content-select {
    border-bottom: 1px solid rgb(228, 227, 223);
    margin-bottom: 10px;
    justify-content: space-between;
    .content-select-input {
      width: 200px;
      margin-bottom: 10px;
      margin-left: 20px;
    }
  }
  .fooder {
    margin-top: 20px;
    text-align: center;
  }
}
</style>
