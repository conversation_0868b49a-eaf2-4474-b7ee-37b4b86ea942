/** * 选择广告位置 */
<template>
  <div class="select-ad-placement">
    <el-form ref="form" :model="adForm" label-width="130px">
      <el-form-item label="选择广告位置">
        <el-select
          v-model="adForm.siteSetFirstId"
          placeholder="请选择一级页面"
          @change="getSecondAd"
        >
          <el-option
            v-for="(value, index) in stairAdList"
            :key="index"
            :label="value.name"
            :value="value.id"
          />
        </el-select>
        <el-select
          v-if="secondAdList.length"
          v-model="adForm.siteSetSecondId"
          clearable
          placeholder="请选择二级页面"
          @change="getThirdAd"
        >
          <el-option
            v-for="(value, index) in secondAdList"
            :key="index"
            :label="value.name"
            :value="value.id"
          />
        </el-select>
        <el-select
          v-if="thirdAdList.length"
          v-model="adForm.siteSetThirdId"
          clearable
          placeholder="请选择三级页面"
          @change="getClientPage"
        >
          <el-option
            v-for="(value, index) in thirdAdList"
            :key="index"
            :label="value.name"
            :value="value.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="广告类型">
        <el-select
          v-model="adForm.adTypeId"
          placeholder="请选择广告类型"
          @change="getOutside"
        >
          <el-option
            v-for="(value, index) in typeList"
            :key="index"
            :label="value.name"
            :value="value.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </el-form-item>
    </el-form>
    <div
      class="example"
      v-if="exampleImg && $store.state.adConfig.adDetailShow"
    >
      <img :src="exampleImg" alt="" />
    </div>
  </div>
</template>

<script>
import { adSiteSetGetNextLevel } from '@/api/advertModule'
export default {
  name: 'SelectAdPlacement',
  props: {
    isShopType: {
      type: Boolean,
      default: false
    },
    isMerchant: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      updateShopType: false, // 更新的shopType
      adForm: {
        siteSetFirstId: '', // 广告页面-一级
        siteSetSecondId: '', // 广告页面-二级
        siteSetThirdId: '', // 广告页面-三级
        clientPage: '', // 广告位
        adTypeId: '', // 广告类型ID
        outside: '', // 内外部
        positionMax: '' // 最大多少广告刷数
      },
      exampleImg: '', // 示例图片
      stairAdList: [], // 一级广告页面列表
      secondAdList: [], // 二级广告页面列表
      thirdAdList: [] // 三级广告页面列表
      // typeList: [] // 广告类型列表
    }
  },
  computed: {
    // 广告类型列表
    typeList() {
      let typeList = []
      if (this.adForm.siteSetFirstId) {
        this.stairAdList.map((_) => {
          if (_.id === this.adForm.siteSetFirstId) {
            typeList = _.typeList || []
          }
        })
        if (this.adForm.siteSetSecondId) {
          this.secondAdList.map((_) => {
            if (_.id === this.adForm.siteSetSecondId) {
              typeList = _.typeList || []
            }
          })
          if (this.adForm.siteSetThirdId) {
            this.thirdAdList.map((_) => {
              if (_.id === this.adForm.siteSetThirdId) {
                typeList = _.typeList || []
              }
            })
          }
        }
      }
      return typeList
    }
  },
  mounted() {
    this.resetData()
  },
  methods: {
    resetData() {
      const me = this
      setTimeout(() => {
        me.adForm = me.$store.state.adConfig.adDetailData
        me.getStairAd()
      }, 1)
    },
    init() {
      this.adForm = this.$store.state.adConfig.adDetailData
      this.updateShopType = !!this.adForm.shopType
      if (this.adForm.siteSetSecondId) {
        this.getLevelThreePage(this.adForm.siteSetFirstId, 2)
        if (this.adForm.siteSetThirdId) {
          this.getLevelThreePage(this.adForm.siteSetSecondId, 3)
        }
      }
      if (this.adForm.adTypeId) {
        setTimeout(() => {
          this.typeList.map((_) => {
            if (_.id === this.adForm.adTypeId) {
              this.exampleImg = _.exampleImg || ''
            }
          })
        }, 200)
        this.$store.dispatch('changeAdDetailShow', true)
      }
    },
    // 获取一级广告页面
    getStairAd() {
      this.getLevelThreePage(0, 1)
    },
    // 获取二级广告页面
    getSecondAd(e) {
      this.adForm.siteSetSecondId = ''
      this.adForm.adTypeId = ''
      this.secondAdList = []
      this.thirdAdList = []
      this.$store.dispatch('changeAdDetailShow', false)
      this.stairAdList.map((_) => {
        if (_.id === e) {
          this.adForm.clientPage = _.clientPage || ''
          this.updateShopType = !!_.shopType
          // this.typeList = _.typeList || []
        }
      })
      this.getLevelThreePage(e, 2)
    },
    // 获取三级广告页面
    getThirdAd(e) {
      this.adForm.siteSetThirdId = ''
      this.adForm.adTypeId = ''
      this.thirdAdList = []
      this.$store.dispatch('changeAdDetailShow', false)
      const arrList = e ? this.secondAdList : this.stairAdList
      e = e ? e : this.adForm.siteSetFirstId
      arrList.map((_) => {
        if (_.id === e) {
          this.adForm.clientPage = _.clientPage || ''
          this.updateShopType = !!_.shopType
          this.adForm.positionMax = _.positionMax || ''
          // this.typeList = _.typeList || []
        }
      })
      if (!e) return
      this.getLevelThreePage(e, 3)
    },
    // 获取广告页面
    getLevelThreePage(parentId, level) {
      adSiteSetGetNextLevel({
        parentId: parentId || '',
        shopType: this.isShopType ? 1 : '',
        advertPlatform: this.isMerchant ? 2 : 1,
        // typeId: this.adForm.campaignType || '',
        campaignType: this.adForm.campaignType || '',
        level
      }).then((res) => {
        if (res.data.code === 0) {
          let list = res.data.data || []
          // console.log(`list`, list)
          if ([1, 3].includes(level)) {
            return level === 1
              ? (this.stairAdList = list)
              : (this.thirdAdList = list)
          }
          // 第二级别类型需要删除
          // 18 推送 19 直播 80 最新
          const delLevelList = [18, 19, 80]
          delLevelList.map((levelId) => {
            const findIndex = list.findIndex((item) => item.id === levelId)
            list.splice(findIndex, findIndex !== -1 ? 1 : 0)
          })
          this.secondAdList = list
        }
      })
    },
    // 获取广告位&广告类型
    getClientPage(e) {
      this.adForm.adTypeId = ''
      this.$store.dispatch('changeAdDetailShow', false)
      const arrList = e ? this.thirdAdList : this.secondAdList
      e = e ? e : this.adForm.siteSetSecondId
      arrList.map((_) => {
        if (_.id === e) {
          this.adForm.clientPage = _.clientPage || ''
          this.updateShopType = !!_.shopType
          this.adForm.positionMax = _.positionMax || ''
          // this.typeList = _.typeList || []
        }
      })
    },
    // 获取内外部&示例图片
    getOutside(e) {
      this.$store.dispatch('changeAdDetailShow', false)
      this.typeList.map((_) => {
        if (_.id === e) {
          this.adForm.outside = _.outside || ''
          this.exampleImg = _.exampleImg || ''
        }
      })
    },
    nextStep() {
      if (!this.adForm.siteSetFirstId) {
        return this.$message.error('请选择一级页面')
      }
      // if (this.secondAdList.length && !this.adForm.siteSetSecondId) {
      //   return this.$message.error('请选择二级页面')
      // }
      // if (this.thirdAdList.length && !this.adForm.siteSetThirdId) {
      //   return this.$message.error('请选择三级页面')
      // }
      if (!this.adForm.adTypeId) {
        return this.$message.error('请选择广告类型')
      }
      const data = {
        siteSetFirstId: this.adForm.siteSetFirstId, // 广告页面-一级
        siteSetSecondId: this.adForm.siteSetSecondId, // 广告页面-二级
        siteSetThirdId: this.adForm.siteSetThirdId, // 广告页面-三级
        clientPage: this.adForm.clientPage, // 广告位
        adTypeId: this.adForm.adTypeId, // 广告类型ID
        outside: this.adForm.outside, // 内外部
        positionMax: this.adForm.positionMax, // 最大多少广告刷数，
        orgId: this.adForm.orgId, // 对象id
        campaignId: this.adForm.campaignId, // 计划id
        orgType: this.adForm.orgType, // 广告类型
        adProjectType: this.adForm.adProjectType
      }
      this.$store.dispatch('resetAdDetailList') // 先清空数据(部分，非全部)
      this.$store.dispatch('changeAdDetail', data)
      this.$store.dispatch('changeAdDetailShow', true)
      console.log(`this.adForm`, this.adForm)
    }
  }
}
</script>

<style lang="scss">
.select-ad-placement {
  position: relative;
  .example {
    position: absolute;
    z-index: 100;
    width: 18%;
    right: 50px;
    top: 30vh;
    img {
      width: 100%;
    }
  }
}
</style>
