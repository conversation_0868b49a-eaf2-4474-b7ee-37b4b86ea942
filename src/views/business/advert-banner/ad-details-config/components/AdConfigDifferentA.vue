<template>
  <div class="ad-config-different_A">
    <el-form ref="form" :model="detailsForm" label-width="130px">
      <el-form-item label="广告名称" required>
        <el-input v-model="detailsForm.adName" clearable style="width: 200px" />
      </el-form-item>
      <el-form-item
        v-if="detailsForm.adTypeId === 2 && adProjectType !== 4"
        label="信息流标题"
        required
      >
        <el-input
          v-model="detailsForm.adAliasName"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item v-if="thirdParty" label="第三方广告ID" required>
        <el-input
          v-model="detailsForm.linkUrl"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <div class="inarow">
        <el-form-item v-if="selectAdPartybrand" required label="选择品牌">
          <el-input
            v-model="selectBrandName"
            :model-value="selectBrandName"
            placeholder="请选择"
            style="width: 290px"
            @click="chooseBrand({ multi: false })"
          />
        </el-form-item>
        <el-form-item v-if="selectAdPartybrand" required label="标签文案">
          <el-input
            v-model="detailsForm.badge"
            clearable
            style="width: 200px"
          />
        </el-form-item>
      </div>
      <SelectType
        v-if="linkTypeShow || isLinkUrl || linkUrlButtonShow"
        ref="SelectType"
        @requiredItemType="requiredItemData"
        @updateAdName="updateAdName"
      >
        <template v-slot:center>
          <el-form-item v-if="this.typeId === 16" label="主标题">
            <el-input
              v-model="detailsForm.title"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item v-if="this.typeId === 16" label="副标题">
            <el-input
              v-model="detailsForm.subTitle"
              clearable
              style="width: 250px"
            />
          </el-form-item>
        </template>
      </SelectType>
      <el-form-item v-if="picture" label="图片" required>
        <img :src="detailsForm.coverImage" alt="" class="imgsize" />
        <div style="align-self: flex-end">
          <uploadImg @getImgData="getImgData" />
          <span>图片规格{{ ImgSize }}</span>
        </div>
      </el-form-item>
      <el-form-item v-if="showDealer" label="选择经销商" required>
        <el-input
          v-model="detailsForm.linkUrlName"
          :model-value="detailsForm.linkUrlName"
          placeholder="单选经销商"
          style="width: 200px"
          @click="chooseShop()"
        />
      </el-form-item>
    </el-form>
    <ChooseDialogBrand ref="ChooseDialogBrand" @sendData="getBrandData" />
    <createNewPlan ref="createNewPlan" />
    <ChooseDialogShop
      :memberType="memberType"
      ref="ChooseDialogShop"
      @sendData="sendShopData"
    />
  </div>
</template>

<script>
import { $emit } from '../../../../../utils/gogocodeTransfer'
import createNewPlan from './createNewPlan.vue'
import uploadImg from './uploadImg.vue'
import { getLinkTypeList } from '@/api/advertModule'
import ChooseDialogBrand from '@/components/Dialog/ChooseDialogBrand.vue'
import ChooseDialogShop from '@/components/Dialog/ChooseDialogShop.vue'
import SelectType from './selectType.vue'
import { GetBrandAllList } from '@/api/garage'
export default {
  name: 'AdConfigDifferentA',
  components: {
    createNewPlan,
    uploadImg,
    ChooseDialogBrand,
    SelectType,
    ChooseDialogShop
  },
  data() {
    return {
      adProjectType: '', // 广告方类型
      adProjectTypeEnum: {},
      linkTypeEnum: [],
      adProjectList: [], // 广告方列表
      campaignList: [], // 计划列表
      typeId: '',
      clientPage: '',
      selectBrandName: '',
      memberType: ''
    }
  },
  computed: {
    detailsForm() {
      return this.$store.state.adConfig.adDetailData || {}
    },
    /**
     * 广告类型
     * 1-启动屏(开屏) 2-信息流 3-焦点图 4-弹窗 5-热搜标题 6-经销商 7-品牌 8-车辆
     * 9-文章原样式 10-运营位新 11-金刚位 12-小视频 13-贴片视频 14-激励视频 15-驾校 16-悬浮图
     * 26-公告栏
     *
     * 广告clientPage
     * 30-摩友圈-首页-摩友圈tab 31-摩友圈-摩友圈详情-信息流 41-选车-新能源
     * 40-选车-选车首页 55-选车-车型详情页-底层banner 77-我的-能量兑换
     * 253-内容详情-视频类-小视频
     */
    isLinkUrl() {
      const linkType = this.detailsForm.linkType
      const pageSelect = this.detailsForm.pageSelect
      let isLink = ''
      this.linkTypeEnum.map((_) => {
        if (_.linkType === linkType) {
          isLink = _.isLink
        }
      })
      if (pageSelect) {
        this.pageSelect.map((_) => {
          if (_.linkType === pageSelect) {
            isLink = _.isLink
          }
        })
      }
      return isLink
    },
    // 控制显示-第三方广告ID
    thirdParty() {
      const pageArr = [253]
      const falg = Boolean(
        pageArr.includes(this.clientPage) || this.adProjectType === 4
      )
      return falg
    },
    // 控制显示-类型
    linkTypeShow() {
      const idArr = [6, 7, 8, 9, 15, 17, 23, 26] // 23 车型小组件
      const clientPages = [199, 210, 253] // 199 搜索·搜索结果页-综合·搜索站 210 搜索-默认搜索页-轮播词
      const falg = Boolean(
        this.adProjectType !== 4 &&
          !idArr.includes(this.typeId) &&
          !clientPages.includes(this.clientPage)
      )
      return falg
    },
    // 控制显示-图片
    picture() {
      const idArr = [1, 3, 4, 11]
      const pageArr = [31, 165]
      const noPageArr = [196] // 196 搜索·搜索结果页-综合·搜索站
      const falg = Boolean(
        this.adProjectType !== 4 &&
          (idArr.includes(this.typeId) || pageArr.includes(this.clientPage)) &&
          !noPageArr.includes(this.clientPage)
      )
      return falg
    },
    // 图片大小
    ImgSize() {
      // 主要根据 typeId 判定，再type 为3 焦点图时 辅助 clentPage 判定
      // 166 选车-品牌详情页
      const flag = [30, 41, 55, 77, 166].includes(this.clientPage)
      // 单独根据 clientPage 判定
      const clientPageAll = [40, 200, 201, 202].includes(this.clientPage)
      // 根据 clientPage 和 typeId 联合判定的
      // 31_2 摩友圈-详情页-焦点图、31_3 摩友圈-详情页-信息流
      const clientPageTypeId = ['31_2', '31_3'].includes(
        `${this.clientPage}_${this.typeId}`
      )
      // 主要根据 typeId 判定 的图片枚举
      const ImgSizeEnum = {
        1: '1242*2268',
        2: '750*1126',
        3: flag ? '750*150' : '750*200',
        4: '468*832',
        11: '70*70',
        16: '100*100'
      }
      // 单独根据 clientPage 的图片枚举
      // 200 二手车首页-我的发布、201二手车首页-我的订单、202我的-我的发布-二手车
      const ImgSizeEnumClientPage = {
        40: '750*120',
        200: '388*200',
        201: '750*150',
        202: '750*150'
      }
      // 根据 clientPage 和 typeId 联合判定的图片枚举
      const ImgSizeEnumClientPageTypeId = {
        '31_2': '大图750*388；小图750*170',
        '31_3': '750*150'
      }
      return clientPageTypeId
        ? ImgSizeEnumClientPageTypeId[`${this.clientPage}_${this.typeId}`]
        : clientPageAll
        ? ImgSizeEnumClientPage[this.clientPage]
        : ImgSizeEnum[this.typeId]
    },
    // 控制显示-选择经销商
    showDealer() {
      const idArr = [6, 15]
      const falg = Boolean(idArr.includes(this.typeId))
      return falg
    },
    // 控制显示-选择品牌
    selectAdPartybrand() {
      const pageArr = [185, 186]
      const falg = Boolean(pageArr.includes(this.clientPage))
      return falg
    },
    // 控制显示-选择页面
    pageSelect() {
      const linkType = this.detailsForm.linkType
      let child = []
      this.linkTypeEnum.map((_) => {
        if (_.linkType === linkType) {
          child = _.child || []
        }
      })
      return child
    },
    // 控制显示-选择id按钮
    linkUrlButtonShow() {
      const idArr = [9]
      const noShow = [20]
      const me = this
      const falg =
        Boolean(
          idArr.includes(me.typeId) || me.detailsForm.pageSelect === 13
        ) && !noShow.includes(me.typeId)
      return falg
    }
  },
  mounted() {
    // this.detailsForm = this.$store.state.adConfig.adDetailData || {}
    this.typeId = this.detailsForm.adTypeId || ''
    this.clientPage = this.detailsForm.clientPage || ''
    const brandList =
      this.detailsForm.relationListStr.find((item) => {
        return item.businessType === 3
      }) || {}
    if (
      this.selectAdPartybrand &&
      brandList.relationIdList &&
      brandList.relationIdList.length
    ) {
      this.getGetBrandAllList(brandList.relationIdList)
    }
    // this.getLinkTypeList()
    this.getAdType()
  },
  methods: {
    // 必选
    requiredItem() {
      if (this.linkTypeShow) {
        this.$refs.SelectType && this.$refs.SelectType.requiredItem()
      } else {
        this.requiredItemData()
      }
    },
    requiredItemData() {
      if (
        this.detailsForm.adTypeId === 2 &&
        this.adProjectType !== 4 &&
        !this.detailsForm.adAliasName
      ) {
        return this.$message.error('请输入信息流标题')
      }
      if (!this.detailsForm.adName) {
        return this.$message.error('请填写广告名称')
      }
      if (!this.detailsForm.linkUrl && this.showDealer) {
        return this.$message.error('请选择经销商')
      }
      if (!this.detailsForm.linkUrl && this.thirdParty) {
        return this.$message.error('请填写第三方广告ID')
      }
      if (this.selectAdPartybrand && !this.selectBrandName) {
        return this.$message.error('请选择品牌')
      }
      if (this.selectAdPartybrand && !this.detailsForm.badge) {
        return this.$message.error('请输入标签文案')
      }
      if (!this.detailsForm.coverImage && this.picture) {
        return this.$message.error('请上传图片')
      }
      $emit(this, 'requiredItem')
    },
    // 获取广告方类型
    getAdType() {
      // outside 内外部 1:内部 2:外部 3:内外部
      const outside = this.detailsForm.outside || 3
      if (outside === 1) {
        this.adProjectTypeEnum = { 厂商: 1, 经销商: 2, 非车企: 3, 摩托范: 5 }
      }
      if (outside === 2) {
        this.adProjectTypeEnum = { 第三方: 4 }
      }
      if (outside === 3) {
        this.adProjectTypeEnum = {
          厂商: 1,
          经销商: 2,
          非车企: 3,
          第三方: 4,
          摩托范: 5
        }
      }
      this.adProjectType =
        this.detailsForm.orgType || this.detailsForm.adProjectType
      this.$store.dispatch('changeAdType', this.adProjectType || '')
    },
    // 获取广告跳转类型列表
    getLinkTypeList() {
      getLinkTypeList({
        typeId: this.detailsForm.adTypeId || ''
      }).then((res) => {
        if (res.data.code === 0) {
          this.linkTypeEnum = res.data.data || []
          if (this.detailsForm.linkType) {
            this.setPageSelect(this.linkTypeEnum)
          }
        }
      })
    },
    // 获取所有品牌
    getGetBrandAllList(list) {
      const me = this
      GetBrandAllList().then((res) => {
        if (res.data.code === 0) {
          const data = res.data.data
          const findData = data.find((item) => {
            return item.brandId === list[0]
          })
          me.selectBrandName = findData.brandName || ''
          me.detailsForm.relationListStr = [
            { businessType: 3, relationIdList: [findData.brandId] }
          ]
        }
      })
    },
    setPageSelect(data) {
      if (data) {
        const linkTypeArr = data.map((_) => {
          return _.linkType
        })
        if (!linkTypeArr.includes(this.detailsForm.linkType)) {
          this.detailsForm.pageSelect = this.detailsForm.linkType
          this.detailsForm.linkType = 0
        }
      }
    },
    // 返回图片数据
    getImgData(imgOrgUrl) {
      console.log(imgOrgUrl)
      this.detailsForm.coverImage = imgOrgUrl
    },
    changeLinkType() {
      this.detailsForm.pageSelect = ''
      this.detailsForm.linkUrl = ''
    },
    // 选择品牌
    chooseBrand(data = { multi: false }) {
      this.$refs.ChooseDialogBrand &&
        this.$refs.ChooseDialogBrand.init({
          title: '选择品牌',
          multiple: data.multi,
          // labels: brandList,
          getListUrl: GetBrandAllList,
          canChoose: true
        })
    },
    // 获取返回的品牌信息
    getBrandData(data) {
      const obj = data && data.labels && data.labels[0]
      this.selectBrandName = obj.brandName || ''
      this.detailsForm.relationListStr = [
        { businessType: 3, relationIdList: [obj.brandId] }
      ]
    },
    // 选择经销商
    chooseShop() {
      this.memberType = [26, 27].includes(this.detailsForm.siteSetSecondId)
        ? 3
        : ''
      this.$refs.ChooseDialogShop.init({
        title: '选择经销商',
        labels: [],
        getListUrl: '',
        selectLimit: 1,
        canChoose: true,
        needSelfSearchShop: true
      })
    },
    sendShopData(result) {
      const target = result && result[0]
      this.detailsForm.linkUrl = target.shopId || ''
      this.detailsForm.linkUrlName = target.shopName || ''
    },
    updateAdName(name) {
      this.detailsForm.adName = this.detailsForm.adName || name || ''
    }
  },
  emits: ['requiredItem']
}
</script>

<style lang="scss">
.ad-config-different_A {
  .inarow {
    display: flex;
  }

  .imgsize {
    max-width: 500px;
  }
}
</style>
