<template>
  <!-- <div v-loading="loading" style="margin: 20px 20px 0"> -->
  <el-dialog
    v-model="dialogVisible"
    :title="'选择计划'"
    center
    class="choose-dialog"
    width="1000px"
    append-to-body
  >
    <div v-loading="loading" class="select-plan-list">
      <el-form
        ref="ruleForm"
        :inline="true"
        label-width="80px"
        class="selected-brand-form-list"
      >
        <el-form-item label="计划名称">
          <el-input
            v-model="planForm.campaignName"
            type="text"
            placeholder="请输入计划名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="计划类型">
          <el-select
            v-model="planForm.campaignType"
            clearable
            :disabled="!!planForm.campaignId"
          >
            <el-option
              v-for="(value, index) in campaignTypeAll"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="selectProject()">查询</el-button>
          <el-button type="primary" @click="resetData()">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        ref="multipleTable"
        :data="listData"
        row-key="multipleTable"
        highlight-current-row
        @current-change="handleCurrentChange"
        border
        class="select-plan-list-table"
        :row-class-name="rowClassName"
      >
        <el-table-column prop="campaignName" label="计划名称" align="center" />
        <el-table-column prop="campaignType" label="计划类型" align="center">
          <template v-slot="scope">
            <span>{{ campaignType[scope.row.campaignType] || '' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="controlHoursPredict"
          label="总天数"
          align="center"
        />
        <!-- <el-table-column
          prop="servingLeftHours"
          label="可用天数"
          align="center"
        /> -->
        <el-table-column prop="servingHours" label="已用天数" align="center" />
        <el-table-column
          prop="controlNumberPredict"
          label="总量"
          align="center"
        />
        <el-table-column prop="servingNum" label="已投放量" align="center" />
        <el-table-column
          prop="servingLeftNum"
          label="可用余额"
          align="center"
        />
        <el-table-column prop="exposureNum" label="实际曝光" align="center" />
        <el-table-column
          prop="exposureLeftNum"
          label="实际余量"
          align="center"
        />
        <el-table-column prop="remark" label="备注" align="center" />
      </el-table>
      <div class="dialog-content center footer el-pagination-center">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { filterDay } from '@/utils'
import { $emit } from '../../../../../utils/gogocodeTransfer'
import { campaignType } from '@/utils/enum/adConfigEnum.js'
import { convertKeyValueEnum } from '@/utils/convert'
import { campaignManageList, campaignManageAdCount } from '@/api/advertModule'
export default {
  name: 'selectPlanList',
  components: {},
  props: {},
  data() {
    return {
      dialogVisible: false,
      loading: false,
      campaignType,
      campaignTypeAll: convertKeyValueEnum(campaignType),
      listData: [],
      planForm: {
        campaignName: '',
        campaignType: ''
      },
      currentRowIndex: null,
      currentRow: {}
    }
  },
  methods: {
    init(typeId, advertiserId) {
      this.dialogVisible = true
      this.planForm = {
        typeId: typeId,
        advertiserId: advertiserId,
        campaignName: '',
        campaignType: ''
      }
      this.listData = []
      this.selectProject()
    },
    // 获取计划
    selectProject() {
      campaignManageList({
        typeId: this.planForm.typeId || '',
        advertiserId: this.planForm.advertiserId || '',
        isValid: 1,
        campaignType: this.planForm.campaignType || '',
        campaignName: this.planForm.campaignName || '',
        limit: 999,
        launchType: 1,
        statusFilter: 3,
        filterCampaignType: 15 // 过滤15
      }).then((res) => {
        if (res.data.code === 0) {
          this.listData = (res.data.data && res.data.data.list) || []
          this.listData = this.listData.map((item) => {
            item.controlHoursPredict = item.controlHoursPredict
              ? filterDay(item.controlHoursPredict)
              : '/'
            item.servingHours = item.servingHours
              ? filterDay(item.servingHours)
              : '/'
            item.servingLeftHours = item.servingLeftHours
              ? filterDay(item.servingLeftHours)
              : '/'
            return item
          })
        }
      })
    },
    resetData() {
      this.planForm = {
        ...this.planForm,
        campaignName: '',
        campaignType: ''
      }
      this.selectProject()
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleCurrentChange(val) {
      const findIndex = this.listData.findIndex((item) => {
        return item.campaignId === val.campaignId
      })
      this.currentRowIndex = findIndex
      this.currentRow = val
    },
    // 改变class
    rowClassName(row) {
      if (row.rowIndex === this.currentRowIndex) {
        return 'selected-list'
      }
    },
    confirm() {
      const campaignType =
        (this.currentRow && this.currentRow.campaignType) || ''
      const flag = [13, '13'].includes(campaignType)
      if (flag) {
        campaignManageAdCount({
          campaignId: this.currentRow.campaignId
        }).then((res) => {
          if (res.data.code === 0) {
            $emit(this, 'showList', this.currentRow)
            this.dialogVisible = false
          } else if (res.data.code === -1) {
            this.$message.error('操作失败，该计划已有两条生效中的广告')
          } else {
            this.$message.error(res.data.msg)
          }
        })
      } else {
        $emit(this, 'showList', this.currentRow)
        this.dialogVisible = false
      }
    }
  },
  emits: ['showList']
}
</script>

<style lang="scss" scoped>
.select-plan-list {
  .select-plan-list-table {
    width: 100%;
    overflow-y: auto;
    height: 55vh;
    :deep(.selected-list td) {
      background-color: #add8e6 !important;
    }
    :deep(.el-table__row:hover > td) {
      background-color: #ecf5ff !important;
    }
  }
}
</style>
