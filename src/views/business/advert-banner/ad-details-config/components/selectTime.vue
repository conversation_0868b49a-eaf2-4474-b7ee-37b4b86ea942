<template>
  <div class="byted-weektime">
    <div class="calendar">
      <div class="text-right">
        <i class="text-right-icon text-right-sel"></i>已选
        <i class="text-right-icon text-right-no"></i>可选
      </div>
      <table class="calendar-table" style="width: 1000px">
        <thead class="calendar-head">
          <tr>
            <th rowspan="6" class="week-td">星期/时间</th>
            <th colspan="24">00:00 - 12:00</th>
            <th colspan="24">12:00 - 24:00</th>
          </tr>
          <tr>
            <td colspan="2" v-for="(item, index) in tableHeader" :key="index">
              {{ item }}
            </td>
          </tr>
        </thead>
        <tbody id="tableBody">
          <div
            id="kuang"
            :style="{
              width: kuangObj.width + 'px',
              height: kuangObj.height + 'px',
              top: kuangObj.top + 'px',
              left: kuangObj.left + 'px',
              bottom: kuangObj.bottom + 'px',
              right: kuangObj.right + 'px',
            }"
          ></div>
          <tr>
            <td>星期一</td>
            <el-tooltip
              :show-after="1000"
              v-for="(item, i) in rowUnit[0]"
              :key="i"
              effect="dark"
              :content="setTip('星期一', i)"
              placement="bottom-start"
            >
              <td
                class="calendar-atom-time"
                :class="item.class"
                @mousedown.prevent="handleMouseDown(i, 0)"
                @mouseup.prevent="handleMouseUp(i, 0)"
              ></td>
            </el-tooltip>
          </tr>
          <tr>
            <td>星期二</td>
            <el-tooltip
              :show-after="1000"
              v-for="(item, i) in rowUnit[1]"
              :key="i"
              effect="dark"
              :content="setTip('星期二', i)"
              placement="bottom-start"
            >
              <td
                @mousedown.prevent="handleMouseDown(i, 1)"
                @mouseup.prevent="handleMouseUp(i, 1)"
                class="calendar-atom-time"
                :class="item.class"
              ></td>
            </el-tooltip>
          </tr>
          <tr>
            <td>星期三</td>
            <el-tooltip
              :show-after="1000"
              v-for="(item, i) in rowUnit[2]"
              :key="i"
              effect="dark"
              :content="setTip('星期三', i)"
              placement="bottom-start"
            >
              <td
                @mousedown.prevent="handleMouseDown(i, 2)"
                @mouseup.prevent="handleMouseUp(i, 2)"
                class="calendar-atom-time"
                :class="item.class"
              ></td>
            </el-tooltip>
          </tr>
          <tr>
            <td>星期四</td>
            <el-tooltip
              :show-after="1000"
              v-for="(item, i) in rowUnit[3]"
              :key="i"
              effect="dark"
              :content="setTip('星期四', i)"
              placement="bottom-start"
            >
              <td
                @mousedown.prevent="handleMouseDown(i, 3)"
                @mouseup.prevent="handleMouseUp(i, 3)"
                class="calendar-atom-time"
                :class="item.class"
              ></td>
            </el-tooltip>
          </tr>
          <tr>
            <td>星期五</td>
            <el-tooltip
              :show-after="1000"
              v-for="(item, i) in rowUnit[4]"
              :key="i"
              effect="dark"
              :content="setTip('星期五', i)"
              placement="bottom-start"
            >
              <td
                @mousedown.prevent="handleMouseDown(i, 4)"
                @mouseup.prevent="handleMouseUp(i, 4)"
                class="calendar-atom-time"
                :class="item.class"
              ></td>
            </el-tooltip>
          </tr>
          <tr>
            <td>星期六</td>
            <el-tooltip
              :show-after="1000"
              v-for="(item, i) in rowUnit[5]"
              :key="i"
              effect="dark"
              :content="setTip('星期六', i)"
              placement="bottom-start"
            >
              <td
                @mousedown.prevent="handleMouseDown(i, 5)"
                @mouseup.prevent="handleMouseUp(i, 5)"
                class="calendar-atom-time"
                :class="item.class"
              ></td>
            </el-tooltip>
          </tr>
          <tr>
            <td>星期日</td>
            <el-tooltip
              :show-after="1000"
              v-for="(item, i) in rowUnit[6]"
              :key="i"
              effect="dark"
              :content="setTip('星期日', i)"
              placement="bottom-start"
            >
              <td
                @mousedown.prevent="handleMouseDown(i, 6)"
                @mouseup.prevent="handleMouseUp(i, 6)"
                class="calendar-atom-time"
                :class="item.class"
              ></td>
            </el-tooltip>
          </tr>
          <tr>
            <td colspan="49" class="timeContent">
              <div>
                已选择时间段<span class="fl-right" @click="clear()">清除</span>
              </div>
              <div
                v-for="(item, index) in timeStr"
                :key="index"
                v-show="item.length"
              >
                <span>{{ weekDate[index + 1] }}:</span>
                <strong>
                  <span>{{ item }}</span>
                </strong>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
export default {
  name: 'timeSelect',
  data() {
    return {
      tableHeader: [
        '00',
        '01',
        '02',
        '03',
        '04',
        '05',
        '06',
        '07',
        '08',
        '09',
        '10',
        '11',
        '12',
        '13',
        '14',
        '15',
        '16',
        '17',
        '18',
        '19',
        '20',
        '21',
        '22',
        '23',
      ],
      weekDate: {
        1: '星期一',
        2: '星期二',
        3: '星期三',
        4: '星期四',
        5: '星期五',
        6: '星期六',
        7: '星期日',
      },
      rowUnit: [], //每一个单元格
      timeContent: [], //选中的时间段原始数据
      timeSection: [], //时间段，可以返回给后台的数据
      timeStr: [], //时间段，前端显示的数据
      beginDay: 0,

      beginTime: 0,
      downEvent: false,

      kuangObj: {
        width: 0,
        height: 0,
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        oldLeft: 0,
        oldTop: 0,
        flag: false,
      },
    }
  },
  created() {
    this.init()
  },
  mounted() {},
  methods: {
    init() {
      for (let i = 0; i < 7; i++) {
        let arr = []
        for (let j = 0; j < 48; j++) {
          arr.push({ class: null, timeData: j })
        }
        this.rowUnit.push(arr)
        this.timeContent.push({ arr: [] })
        this.timeSection.push([])
        this.timeStr.push('')
      }
    },
    initData(data) {
      const me = this
      data.map((item, index) => {
        let list = []
        item.map((i) => {
          for (let e = i[0]; e < i[1]; e++) {
            list.push(e)
            me.rowUnit[index][e].class = 'ui-selected'
          }
        })
        me.timeContent[index].arr = list
      })
      me.filterTime(0, 6)
    },
    handleMouseDown(i, day) {
      this.downEvent = true //按下时鼠标不在范围内则不算
      this.beginDay = day
      this.beginTime = i
    },
    handleMouseUp(i, day) {
      let _this = this
      let begin = this.beginTime
      let start = begin <= i ? begin : i //x轴 起点
      let length = Math.abs(begin - i)
      let end = start + length //x轴 终点
      let dayStart = this.beginDay <= day ? this.beginDay : day //y轴 起点
      let dayLength = Math.abs(this.beginDay - day)
      let dayEnd = dayStart + dayLength //y轴 终点      //当框选范围内所有块都是选中状态时,执行反选
      function isAdd() {
        for (let x = dayStart; x < dayEnd + 1; x++) {
          for (let y = start; y < end + 1; y++) {
            if (_this.rowUnit[x][y].class == null) return true
          }
        }
        return false
      }
      //当点击事件是在table内才触发选取数据操作
      if (this.downEvent) {
        //选时间段
        if (isAdd()) {
          //没选中的全都选上
          for (let x = dayStart; x < dayEnd + 1; x++) {
            for (let y = start; y < end + 1; y++) {
              if (this.rowUnit[x][y].class == null) {
                this.rowUnit[x][y].class = 'ui-selected'
                this.timeContent[x].arr.push(this.rowUnit[x][y].timeData)
              }
            }
          }
        } else {
          //反选
          for (let x = dayStart; x < dayEnd + 1; x++) {
            for (let y = start; y < end + 1; y++) {
              if (this.rowUnit[x][y].class == 'ui-selected') {
                this.rowUnit[x][y].class = null
                var c = this.rowUnit[x][y].timeData
                var kong = ''
                for (let i = 0; i < this.timeContent[x].arr.length; i++) {
                  if (c == this.timeContent[x].arr[i]) {
                    kong = i
                  }
                }
                this.timeContent[x].arr.splice(kong, 1)
              }
            }
          }
        }
        //过滤时间段,将临近的时间段合并
        this.filterTime(dayStart, dayEnd)
      }
      this.downEvent = false
    },
    filterTime(start, end) {
      console.log(start, end, this.timeContent, '12312')
      //选中的x,y坐标信息 x:0-47  y:0-6
      function sortCut(arr) {
        //提取连续的数字
        var result = []
        arr.forEach(function (v, i) {
          var temp = result[result.length - 1]
          if (!i) {
            result.push([v])
          } else if (v % 1 === 0 && v - temp[temp.length - 1] == 1) {
            temp.push(v)
          } else {
            result.push([v])
          }
        })
        return result
      }
      function toStr(num) {
        if (Number.isInteger(num)) {
          let str = num < 10 ? '0' + num : num.toString()
          return str + ':00'
        } else {
          let str =
            Math.floor(num) < 10
              ? '0' + Math.floor(num)
              : Math.floor(num).toString()
          return str + ':30'
        }
      }
      function timeToStr(arr) {
        //把数组转成方便人看到字符串
        let str = ''
        arr.forEach((arr, index) => {
          let str1 = ''
          if (index == 0) {
            str1 = toStr(arr[0]) + '~' + toStr(arr[1])
          } else {
            str1 = ' , ' + toStr(arr[0]) + '~' + toStr(arr[1])
          }
          str += str1
        })
        return str
      }
      //排序,分割成
      for (let i = start; i < end + 1; i++) {
        let arr1 = sortCut(this.timeContent[i].arr.sort((a, b) => a - b))
        let arr2 = []
        arr1.forEach((arr) => {
          //转成带小数点的时间段,以及供前端显示的字符串
          let arr3 = []
          arr3.push(arr[0] / 2)
          arr3.push(arr[arr.length - 1] / 2 + 0.5)
          arr2.push(arr3)
        })
        this.timeStr[i] = timeToStr(arr2)
        this.timeSection[i] = arr2
      }
      const backData = this.timeSection.map((item) => {
        return item.map((i) => {
          return i.map((e) => {
            return e * 2
          })
        })
      })
      console.log(this.timeSection, '22222')
      $emit(this, 'updateTime', backData)
    },
    clear() {
      this.rowUnit.forEach((item) => {
        item.forEach((item1) => {
          item1.class = null
        })
      })
      this.timeContent.forEach((item) => {
        item.arr = []
      })
      this.timeSection.forEach((item) => {
        //赋值成空数组[]出问题
        item.length = 0
      })
      //遍历赋值成'',不管用
      this.timeStr.length = 0
      for (let i = 0; i < 7; i++) {
        this.timeStr.push('')
      }
      this.filterTime(0, 6)
    },
    setTip(name, index) {
      // if (!index) return ''
      function setVal(val) {
        const splitData = (val / 2).toString().split('.')
        return `${splitData[0]}:${splitData.length > 1 ? '30' : '00'}`
      }
      return `${name} ${setVal(index)}-${setVal(index + 1)}`
    },
  },
  emits: ['updateTime'],
}
</script>

<style lang="scss" scoped>
.byted-weektime {
  width: 1000px;
  margin-bottom: 10px;
  margin-left: 30px;
}
.text-right {
  height: 40px;
  line-height: 40px;
  border: 1px solid #ccc;
  border-bottom: 0;
  padding: 0 6px;
}
.text-right-icon {
  display: inline-block;
  width: 20px;
  height: 4px;
  border-radius: 4px;
  position: relative;
  top: -4px;
  margin: 0 3px;
}
.text-right-sel {
  background-color: #2f88ff;
}
.text-right-no {
  border: 1px solid #ccc;
}
.byted-weektime .calendar {
  -webkit-user-select: none;
  position: relative;
  display: inline-block;
  width: 1000px;
}
.byted-weektime .calendar .calendar-table {
  border-collapse: collapse;
  border-radius: 4px;
  width: 1000px;
}
.byted-weektime .calendar .calendar-table tr .calendar-atom-time:hover {
  background: #ccc;
}
.byted-weektime .calendar .calendar-table tr .ui-selected {
  background: #2f88ff;
}
.byted-weektime .calendar .calendar-table tr .ui-selected:hover {
  background: #2f88ff;
}
.byted-weektime .calendar .calendar-table tr,
.byted-weektime .calendar .calendar-table td,
.byted-weektime .calendar .calendar-table th {
  border: 1px solid #ccc;
  font-size: 12px;
  text-align: center;
  line-height: 1.8em;
  -webkit-transition: background 200ms ease;
  -moz-transition: background 200ms ease;
  -ms-transition: background 200ms ease;
  transition: background 200ms ease;
}
.byted-weektime .calendar .calendar-table tbody tr {
  height: 30px;
}
.byted-weektime .week-td {
  width: 75px;
  padding: 20px 0;
}
.byted-weektime a {
  cursor: pointer;
  color: #2f88ff;
  font-size: 14px;
}
#kuang {
  position: absolute;
  background-color: blue;
  opacity: 0.3;
}
.byted-weektime .calendar .calendar-table .timeContent {
  text-align: left;
  padding: 3px 10px;
}
.byted-weektime .calendar .calendar-table .timeContent .fl-right {
  color: #2f88ff;
}
</style>
