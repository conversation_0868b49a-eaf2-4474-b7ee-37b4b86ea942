<template>
  <!-- <div v-loading="loading" style="margin: 20px 20px 0"> -->
  <el-dialog
    v-model="dialogVisible"
    :title="'圈子查询'"
    center
    class="choose-dialog"
    width="600px"
    append-to-body
  >
    <div v-loading="loading">
      <el-form
        ref="ruleForm"
        :inline="true"
        label-width="130px"
        class="selected-brand-form-list"
      >
        <el-form-item :label="'圈子名称'" class="brand-list-content">
          <el-input
            v-model="searchValue"
            style="width: 200px"
            placeholder="查询圈子名称"
            clearable
            @change="remoteMethod()"
            @clear="clear()"
          >
            <template v-slot:append>
              <el-button v-if="searchValue" :icon="IconSearch"></el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <p style="margin: 0px 0px 10px 0">双击选中</p>
      <el-table
        ref="multipleTable"
        :data="circleList"
        row-key="multipleTable"
        border
        style="width: 100%; overflow-y: auto; height: 55vh"
        @row-dblclick="checkAction"
      >
        <el-table-column prop="id" label="圈子id" align="center" />
        <el-table-column prop="name" label="圈子名称" align="center" />
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import { Search as IconSearch } from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { getCircleList } from '@/api/circle'
export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      // 搜索
      searchValue: '',
      circleList: [],
      IconSearch: markRaw(IconSearch)
    }
  },
  name: 'selectCircle',
  components: {},
  props: {},
  methods: {
    init() {
      this.dialogVisible = true
      this.searchValue = ''
      this.listData = []
    },
    setData(data) {
      console.log(`data`, data)
      this.circleId = []
      data &&
        data.map((_) => {
          _.name = _.hoopName
          _.id = _.hoopId
          this.circleId.push(_.hoopId)
        })
      this.circleList = data || []
      this.changeCircle(this.circleId)
    },
    remoteMethod() {
      this.loading = true
      getCircleList({
        hoopName: this.searchValue,
        page: 1,
        limit: 100
      })
        .then((res) => {
          if (res.data.code === 0) {
            const data = res.data.data
            this.circleList = (data && data.list) || []
          }
        })
        .finally((_) => {
          this.loading = false
        })
    },
    checkAction(data) {
      console.log(data)
      $emit(this, 'updateCircleData', {
        id: data.id,
        name: data.name
      })
      this.dialogVisible = false
    },
    clear() {
      this.circleList = []
    }
  },
  emits: ['updateCircleData']
}
</script>
