<template>
  <div class="create-new-plan">
    <el-dialog title="创建计划" v-model="dialogFormVisible" width="25%">
      <el-form :model="planForm" label-width="120px">
        <el-form-item label="所属广告方类型">
          <span>{{ planForm.adProjectType }}</span>
        </el-form-item>
        <el-form-item label="所属广告方">
          <span>{{ planForm.adProjectName }}</span>
        </el-form-item>
        <el-form-item label="计划名称">
          <el-input v-model="name" clearable style="width: 80%" />
        </el-form-item>
        <el-form-item label="预计曝光">
          <el-input
            v-model="controlNumberPredict"
            type="text"
            placeholder="请输入预计曝光次数"
            clearable
            style="width: 200px"
          />次
        </el-form-item>
      </el-form>
      <div class="button-box">
        <el-button @click="dialogFormVisible = false"
          >&#x3000;取 消&#x3000;</el-button
        >
        <el-button type="primary" @click="createNewPlan"
          >&#x3000;确 定&#x3000;</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { campaignManageAdd } from '@/api/advertModule'
export default {
  name: 'createNewPlan',
  data() {
    return {
      dialogFormVisible: false,
      planForm: {
        adProjectType: '',
        adProjectName: '',
        advertiserId: '',
      },
      name: '',
      controlNumberPredict: '',
    }
  },
  mounted() {},
  methods: {
    init(obj) {
      this.planForm = obj
      this.controlNumberPredict = ''
      this.dialogFormVisible = true
    },
    createNewPlan() {
      const advertiserId = this.planForm.advertiserId || ''
      campaignManageAdd({
        advertiserId,
        name: this.name,
        controlNumberPredict: this.controlNumberPredict,
      }).then((res) => {
        // console.log(`res`, res)
        if (res.data.code === 0) {
          $emit(this, 'updatePlan', advertiserId)
          this.$message.success('创建成功')
          this.dialogFormVisible = false
        } else {
          this.$message.error(res.data.msg || '创建失败')
        }
      })
    },
  },
  emits: ['updatePlan'],
}
</script>

<style lang="scss">
.create-new-plan {
  .button-box {
    padding: 0 20%;
    display: flex;
    justify-content: space-between;
  }
}
</style>
