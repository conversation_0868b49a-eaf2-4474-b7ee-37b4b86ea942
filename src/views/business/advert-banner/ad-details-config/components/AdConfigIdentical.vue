/** * 配置广告内容-相同 */
<template>
  <div class="ad-config-identical">
    <el-form ref="form" :model="detailsForm" label-width="130px">
      <el-form-item label="投放渠道" required>
        <!-- <el-select v-model="detailsForm.platform" multiple placeholder="请选择" style="width: 120px" @change="handlePlatformChange">
          <el-option v-for="(value, index) in channelEnum" :key="index" :label="index" :value="value" />
        </el-select> -->
        <el-cascader
          class="ad-cascader-selected"
          :options="channelEnum"
          :props="{ multiple: true }"
          v-model="selectedPlatform"
          @change="handlePlatformChange"
          @clearAndroid="clearAndroid"
          ref="cascader"
          clearable
        ></el-cascader>
        <span>&#x3000;从&#x3000;</span>
        <el-input
          v-model="detailsForm.beginVersion"
          clearable
          placeholder="例：3.1.0"
          style="width: 150px"
        />
        <span>&#x3000;版本开始适用&#x3000;</span>
        <el-input
          v-model="detailsForm.versionEnd"
          clearable
          placeholder="例：3.1.0"
          style="width: 150px"
        />
        <span>&#x3000;版本截止</span>
      </el-form-item>

      <div class="inarow" v-if="!isMerchant">
        <el-form-item label="分用户">
          <div style="align-self: flex-end">
            <el-select
              v-model="detailsForm.newUser"
              placeholder="请选择"
              style="width: 120px"
            >
              <el-option
                v-for="(value, index) in newUserEnum"
                :key="index"
                :label="index"
                :value="value"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="用户画像" label-width="110px">
          <div>
            <el-select
              v-model="userSelectedId"
              filterable
              placeholder="请输入画像"
              style="width: 240px"
              @change="userSelectedLabel"
            >
              <el-option
                v-for="item in userProfileIdList"
                :key="item.labelId"
                :label="item.labelName"
                :value="item.labelName"
              />
            </el-select>
            <br />
            <el-tooltip
              v-for="(item, index) in userSelectedList"
              :key="index"
              :content="item.labelName"
              :disabled="true"
              effect="dark"
              placement="top-start"
            >
              <el-tag
                closable
                @close="deleteLable(index)"
                style="margin-right: 5px"
                >{{ $filters.subString(item.labelName, 20) }}</el-tag
              >
            </el-tooltip>
          </div>
        </el-form-item>
      </div>
      <el-form-item label="用户标签" label-width="110px">
        <el-checkbox v-model="detailsForm.userLableFilter"></el-checkbox>
      </el-form-item>
      <selectedUserTag
        :show-tag="detailsForm.userLableFilter"
        @backData="updateTage"
        ref="selectedUserTag"
      />
      <el-form-item label="投放区域">
        <el-input
          v-if="!isMerchant"
          v-model="location"
          :value="location"
          placeholder="请选择投放区域"
          style="width: 670px"
          @click="placementArea()"
        />
        <el-input
          v-else
          v-model="location"
          :value="location"
          placeholder="请选择投放区域"
          style="width: 670px"
          @click="placementAreaNew()"
        />
      </el-form-item>

      <el-form-item label="提醒配置">
        <el-checkbox v-model="detailsForm.isRemind"></el-checkbox>
      </el-form-item>
      <el-form-item label="提醒人" v-if="detailsForm.isRemind">
        <el-autocomplete
          v-model="remindUserName"
          clearable
          :fetch-suggestions="querySearchAsync"
          @select="updateRemindId"
        />
      </el-form-item>
      <template v-if="showAllocation">
        <div class="title">
          <span>配置控量</span>
          <div class="line"></div>
        </div>
        <el-form-item label="整体控量方式">
          <el-radio
            v-model="detailsForm.controlType"
            label="1"
            @change="clearControlType()"
            >曝光量</el-radio
          >
          <el-radio
            v-model="detailsForm.controlType"
            label="2"
            @change="clearControlType()"
            >点击量</el-radio
          >
          <!-- <el-radio
            v-model="detailsForm.controlType"
            label="3"
            @change="clearControlType()"
            >天数</el-radio
          > -->
        </el-form-item>
        <!-- <el-form-item label="投放天数" v-if="detailsForm.controlType === '3'">
          <div>
            <el-input
              v-model="detailsForm.controlHoursPredictDay"
              type="text"
              placeholder="请输入计划天数"
              clearable
              style="width: 200px"
            >
              <template v-slot:append>
                <span class="flex-item-company">天</span>
              </template>
            </el-input>
            <el-input
              v-model="detailsForm.controlHoursPredictHour"
              type="number"
              min="0"
              max="23"
              placeholder="请输入计划小时"
              clearable
              style="width: 220px"
            >
              <template v-slot:append>
                <span class="flex-item-company">小时</span>
              </template>
            </el-input>
          </div>
        </el-form-item> -->
        <template
          v-if="detailsForm.controlType && detailsForm.controlType !== '3'"
        >
          <el-form-item
            :label="detailsForm.controlType === '1' ? '投放总量' : '点击总量'"
            label-width="100px"
            :required="detailsForm.adTypeId === 9"
          >
            <el-input
              v-model="detailsForm.controlNumber"
              clearable
              placeholder="请输入"
              style="width: 440px"
              type="number"
            />次
          </el-form-item>
          <el-form-item
            :label="detailsForm.controlType === '1' ? '曝光/天' : '点击/天'"
            label-width="100px"
          >
            <el-input
              v-model="detailsForm.controlDayNumber"
              clearable
              placeholder="请输入"
              style="width: 440px"
              type="number"
            />次
          </el-form-item>
        </template>
        <el-form-item label="用户控量方式">
          <el-radio
            v-model="detailsForm.uvControl"
            label="1"
            @change="clearUvControl()"
            >曝光量</el-radio
          >
          <el-radio
            v-model="detailsForm.uvControl"
            label="2"
            @change="clearUvControl()"
            >点击量</el-radio
          >
        </el-form-item>
        <template v-if="detailsForm.uvControl">
          <el-form-item
            :label="detailsForm.uvControl === '1' ? '单人总曝光' : '单人总点击'"
            label-width="100px"
          >
            <el-input
              v-model="detailsForm.uvControlNum"
              clearable
              placeholder="请输入"
              style="width: 440px"
              type="number"
            />
            次
          </el-form-item>
          <el-form-item
            :label="
              detailsForm.uvControl === '1' ? '单人曝光/天' : '单人点击/天'
            "
            label-width="100px"
          >
            <el-input
              v-model="detailsForm.uvControlDayNum"
              clearable
              placeholder="请输入"
              style="width: 440px"
              type="number"
            />次
          </el-form-item>
        </template>
        <el-form-item label="点击量系数">
          <el-input
            v-model="detailsForm.clickRatio"
            disabled
            clearable
            placeholder="读取服务端点击量系数"
            style="width: 440px"
            type="number"
          />
        </el-form-item>
      </template>
      <div class="title">
        <span>配置时段</span>
        <div class="line"></div>
      </div>
      <el-form-item label="投放日期">
        <div>
          <el-date-picker
            v-model="detailsForm.beginTime"
            type="date"
            :default-value="$dayjs('00:00:00', 'hh:mm:ss').toDate()"
            placeholder="开始日期"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
          <el-date-picker
            v-model="detailsForm.endTime"
            type="date"
            :default-value="$dayjs('23:59:59', 'hh:mm:ss').toDate()"
            placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDateFun"
          />
          <!-- <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            v-model="createDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker> -->
        </div>
      </el-form-item>
      <el-form-item label="投放时段">
        <el-radio-group
          v-model="checkboxGroup1"
          @change="updateCheckboxGroup1()"
        >
          <el-radio-button label="不限"></el-radio-button>
          <el-radio-button label="指定时间段"></el-radio-button>
        </el-radio-group>
      </el-form-item>
      <SelectTime
        ref="SelectTime"
        v-if="checkboxGroup1 === '指定时间段'"
        @updateTime="updateTime"
      />
    </el-form>
    <!-- <MultiArea ref="MultiArea" @backData="backData" /> -->
    <selectedCity ref="selectedCity" @backData="backData" />
    <SelectProvince ref="selectProvinceRef" @backData="backData" />
  </div>
</template>

<script>
import { channelEnum, newUserEnum } from '@/utils/enum/adConfigEnum'
// import MultiArea from '@/components/area/MultiArea.vue'
import selectedCity from './selectedCity.vue'
import SelectTime from './selectTime.vue'
import { getLabelTypeIdFuzzy } from '@/api/advertModule'
import { getOssAuditName } from '@/api/advertModule'
import selectedUserTag from './selectedUserTag.vue'
// import forkElCascader from '@/components/forkElCascader/index.vue';
// import { timeFullS } from '@/filters'
import { mapGetters } from 'vuex'
import SelectProvince from './SelectProvince.vue'

export default {
  name: 'AdConfigIdentical',
  components: {
    // MultiArea,
    selectedCity,
    SelectTime,
    selectedUserTag,
    // forkElCascader
    SelectProvince
  },
  props: {
    isMerchant: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      channelEnum, // 投放渠道枚举
      newUserEnum, // 是否是新用户枚举
      startTime: '', // 生效时间段开始
      endTime: '', // 生效时间段结束
      userSelectedId: '', // 选中的画像id
      userSelectedList: [], // 选中的画像数据
      userProfileIdList: [], // 用户画像id
      selectedPlatform: [], // 选中的投放渠道枚举
      location: '', // 投放区域
      remindUserName: '', // 提醒人
      remindUserId: '', // 提醒人id
      checkboxGroup1: '不限'
    }
  },
  computed: {
    ...mapGetters(['name', 'uid']),
    detailsForm() {
      return this.$store.state.adConfig.adDetailData || {}
    },
    placeholderA() {
      const type = this.detailsForm.controlType
      if (type === 3) {
        return '例：值30000，代表当这个广告每天曝光/点击30,000次后无效'
      }
      return '例：值30000，代表当这个广告曝光/点击30,000次后无效'
    },
    placeholderB() {
      const type = Number(this.detailsForm.uvControl)
      if (type === 1) {
        return '例：值2，代表每人最多可看到该广告2次'
      }
      return '例：值2，代表每人每天最多可看到该广告2次'
    },
    createDateRange: {
      get() {
        if (this.detailsForm.beginTime && this.detailsForm.endTime) {
          return [this.detailsForm.beginTime, this.detailsForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          // this.detailsForm.beginTime = timeFullS(Math.floor(new Date(value[0])))
          // this.detailsForm.endTime = timeFullS(Math.floor(new Date(value[1])))
          this.detailsForm.beginTime = value[0]
          this.detailsForm.endTime = value[1]
        } else {
          this.detailsForm.beginTime = ''
          this.detailsForm.endTime = ''
        }
      }
    },
    showAllocation() {
      const campaignType = this.$store.state.adConfig.campaignType || ''
      return ![13, 14, '13', '14'].includes(campaignType)
    }
  },
  watch: {},
  mounted() {
    const me = this
    // me.detailsForm = me.$store.state.adConfig.adDetailData || {}
    me.detailsForm.controlType = me.detailsForm.controlType
      ? me.detailsForm.controlType.toString()
      : me.detailsForm.controlType
    me.detailsForm.uvControl = me.detailsForm.uvControl
      ? me.detailsForm.uvControl.toString()
      : me.detailsForm.uvControl
    me.userSelectedList = me.detailsForm.userProfileId
      ? JSON.parse(me.detailsForm.userProfileId)
      : []
    me.remindUserName = me.detailsForm.remindUserName || me.name || ''
    me.remindUserId = me.detailsForm.remindUserId || me.uid || ''
    if (me.detailsForm.regionListStr && me.detailsForm.regionListStr.length) {
      const arr = []
      me.detailsForm.regionListStr.map((_) => {
        arr.push(
          `${_.province}${_.cityList && _.cityList.length ? ':' : ''}${
            _.cityList && _.cityList.join(',')
          }`
        )
      })
      me.location = arr.join('；') ? arr.join('；') : '全国'
    } else {
      me.location = '全国'
    }
    me.checkboxGroup1 = !me.detailsForm.timeSeries.length
      ? '不限'
      : '指定时间段'
    if (me.checkboxGroup1 !== '不限') {
      setTimeout(() => {
        me.$refs.SelectTime &&
          me.$refs.SelectTime.initData(
            Array.isArray(me.detailsForm.timeSeries)
              ? me.detailsForm.timeSeries
              : JSON.parse(me.detailsForm.timeSeries)
          )
      }, 1000)
    }
    // 以下代码之所以这么做，由于开发环境数据正常回显，线上无法回显，暂时这么做
    let arr = []
    arr = (me.detailsForm.platform || []).map((item) => {
      return [Number(item)]
    })
    if (
      me.detailsForm.channel &&
      me.detailsForm.channel.length &&
      !Array.isArray(me.detailsForm.channel)
    ) {
      const channel = me.detailsForm.channel
      me.detailsForm.channel =
        channel === 'all' &&
        (me.detailsForm.platform.includes('1') ||
          me.detailsForm.platform.includes(1))
          ? 'xiaomi,oppo,vivo,meizu,baidu,huawei,qihu360,wandoujia,yingyongbao,lenovo,samsung,rongyao,0'
          : channel
      me.detailsForm.channel = me.detailsForm.channel.split(',') || []
      let selectedPlatform = []
      me.detailsForm.channel.map((item) => {
        selectedPlatform.push(['1', item])
      })
      arr = arr.concat(selectedPlatform || [])
    } else if (
      me.detailsForm.channel &&
      me.detailsForm.channel.length &&
      Array.isArray(me.detailsForm.channel)
    ) {
      let selectedPlatform = []
      me.detailsForm.channel.map((item) => {
        selectedPlatform.push(['1', item])
      })
      arr = arr.concat(selectedPlatform || [])
    }
    me.selectedPlatform = arr
    this.$nextTick(() => {
      let arr2 = []
      arr2 = (me.detailsForm.platform || []).map((item) => {
        return [Number(item)]
      })
      arr2 = arr2.filter((item) => item[0] !== 1)
      me.selectedPlatform = [...me.selectedPlatform, ...arr2]
      me.handlePlatformChange(me.selectedPlatform)
    })
    if (me.detailsForm.userLable) {
      me.$refs.selectedUserTag.init(JSON.parse(me.detailsForm.userLable))
    }
    // const adTypeId = me.detailsForm.adTypeId
    // me.detailsForm.clickRatio = [8, 22].includes(adTypeId) ? 2.33 : [9].includes(adTypeId) ? 3.17 : me.detailsForm.clickRatio ? me.detailsForm.clickRatio :  1
    if (!me.isMerchant) {
      me.getLabelTypeIdFuzzy()
    }
  },
  methods: {
    disabledDateFun(time) {
      if (this.detailsForm.adProjectType === 2 && this.detailsForm.orderNum) {
        const item = JSON.parse(
          sessionStorage.getItem('adOrderNumData') || '{}'
        )
        if (item.orderNumber) {
          return time.getTime() > item.deadlineTime
        } else {
          return false
        }
      }
      return false
    },
    // 必选
    requiredItem() {
      this.detailsForm.remindUserName = this.remindUserName || ''
      this.detailsForm.remindUserId = this.remindUserId || ''
      if (
        !this.detailsForm.platform.length &&
        !this.detailsForm.channel.length
      ) {
        return this.$message.error('请选择投放渠道')
      }
      if (!this.detailsForm.beginTime) {
        return this.$message.error('请选择有效开始时间')
      }
      if (
        !(
          Number(this.detailsForm.controlType) === 1 &&
          this.detailsForm.controlNumber
        ) &&
        !this.detailsForm.endTime
      ) {
        return this.$message.error('请选择有效结束时间')
      }
      if (this.detailsForm.endTime) {
        // 特殊处理结束时间
        this.detailsForm.endTime =
          this.$dayjs(this.detailsForm.endTime).format('YYYY-MM-DD') +
          ' 23:59:59'
      }
      if (this.showAllocation) {
        if (
          Number(this.detailsForm.controlDayNumber) >
            Number(this.detailsForm.controlNumber) &&
          this.detailsForm.controlNumber &&
          Number(this.detailsForm.controlType) === 1
        ) {
          return this.$message.error('控量不可超过该广告的投放总量')
        }
        if (
          this.detailsForm.controlType === 3 &&
          !this.detailsForm.controlHoursPredictDay &&
          !this.detailsForm.controlHoursPredictHour
        ) {
          return this.$message.error('请输入投放天数')
        }
        if (this.detailsForm.controlHoursPredictHour > 23) {
          return this.$message.error('投放小时不得大于23')
        }
        // if (Number(this.detailsForm.uvControlDayNum) > Number(this.detailsForm.uvControlNum) && this.detailsForm.uvControlNum) {
        //   return this.$message.error(Number(this.detailsForm.uvControl) === 1 ? '控量不可超过该广告的投放总量' : '点击量不可超过该广告的点击总量')
        // }
        if (
          this.detailsForm.uvControlDayNum > 10 &&
          this.detailsForm.uvControl === '1'
        ) {
          return this.$message.error('单人曝光/天不可超过10次')
        }
        if (
          this.detailsForm.adTypeId === 9 &&
          !this.detailsForm.controlNumber
        ) {
          return this.$message.error('内容推荐类型，投放总量必填')
        }
      } else {
        this.detailsForm.controlType = 3
        this.detailsForm.controlHoursPredictHour = ''
        const beginTime = this.$dayjs(this.detailsForm.beginTime).format(
          'YYYY-MM-DD'
        )
        const endTime = this.$dayjs(this.detailsForm.endTime).format(
          'YYYY-MM-DD'
        )
        const day = (new Date(endTime) - new Date(beginTime)) / 86400000 + 1
        this.detailsForm.controlHoursPredictDay = day
      }
      this.$emit('requiredItem')
    },
    querySearchAsync(queryString, cb) {
      if (!queryString) return cb([])
      getOssAuditName({
        nameOrCode: queryString
      })
        .then((res) => {
          if (res.data.code === 0) {
            const listData = res.data.data || []
            const cbData = listData.map((item) => {
              return {
                ...item,
                value: item.userName
              }
            })
            return cb(cbData)
          } else {
            this.$message.error(res.data.msg)
          }
        })
        .catch((e) => {
          this.$message.error(e.message)
        })
    },
    placementAreaNew() {
      const locationArr = this.detailsForm.regionListStr || []
      const province = []
      let city = []
      locationArr.forEach((v) => {
        if (!v.cityList.length) {
          province.push(v.province)
        } else {
          city = [...city, ...v.cityList]
        }
      })
      this.$refs.selectProvinceRef.init({ province, city })
    },
    placementArea() {
      const locationArr = this.detailsForm.regionListStr || []
      let defaultCheckedKeys = []
      let loadCityList = []
      locationArr.map((item) => {
        defaultCheckedKeys = defaultCheckedKeys.concat(
          item.cityList && item.cityList.length ? item.cityList : item.province
        )
        loadCityList =
          item.cityList && item.cityList.length
            ? loadCityList.concat(item.province)
            : loadCityList
      })
      this.$refs['selectedCity'] &&
        this.$refs['selectedCity'].init({
          defaultCheckedKeys: defaultCheckedKeys,
          loadCityList: loadCityList
        })
    },
    getLabelTypeIdFuzzy() {
      const me = this
      getLabelTypeIdFuzzy({
        brandCode: 'moto',
        groupId: '15',
        labelName: ''
      }).then((response) => {
        if (response.data.code === 0) {
          me.userProfileIdList = response.data.data
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    // 选中的数据
    userSelectedLabel(name) {
      const me = this
      const findIndex = me.userSelectedList.findIndex((item) => {
        return item.labelName === name
      })
      me.userSelectedId = ''
      if (findIndex !== -1) return me.$message.error('已有该数据')
      if (me.userSelectedList.length > 4)
        return me.$message.error('最多5条数据')
      const findName = me.userProfileIdList.find((item) => {
        return item.labelName === name
      })
      me.userSelectedList.push(findName)
      me.setUserProfileId()
    },
    deleteLable(index) {
      this.userSelectedList.splice(index, 1)
      this.setUserProfileId()
    },
    setUserProfileId() {
      const userProfileId =
        this.userSelectedList.map((item) => {
          return {
            labelType: item.labelType,
            labelId: item.labelId,
            labelName: item.labelName
          }
        }) || []
      this.detailsForm.userProfileId = JSON.stringify(userProfileId)
    },
    backData(data) {
      this.detailsForm.regionListStr = []
      let obj = JSON.parse(data.selectedDataNew)
      if (data.checkAll) {
        obj = {} // 全选
      }
      const location = []
      Object.keys(obj).forEach((key) => {
        this.detailsForm.regionListStr.push({
          province: key,
          cityList: obj[key]
        })
        location.push(`${key}${obj[key].join() ? ':' : ''}${obj[key].join()}`)
      })
      this.location = location.join('；') ? location.join('；') : '全国'
    },
    updateTime(data) {
      this.detailsForm.timeSeries = JSON.stringify(data)
    },
    updateCheckboxGroup1() {
      if (this.checkboxGroup1 === '不限') {
        this.detailsForm.timeSeries = ''
      }
    },
    // 点击&曝光 切换时清除数据
    clearControlType() {
      this.detailsForm.controlNumber = ''
      this.detailsForm.controlDayNumber = ''
      this.detailsForm.controlHours = ''
      this.detailsForm.controlHoursPredictDay = ''
      this.detailsForm.controlHoursPredictHour = ''
    },
    // 点击&曝光 切换时清除数据
    clearUvControl() {
      this.detailsForm.uvControlNum = ''
      this.detailsForm.uvControlDayNum = ''
    },
    // 更新提醒人id
    updateRemindId(val) {
      this.remindUserId = val.userId || ''
    },
    // 更新选中投放渠道
    handlePlatformChange(value) {
      const me = this
      me.detailsForm.channel = []
      me.detailsForm.platform = []
      value.map((item) => {
        me.detailsForm.platform.push(Number(item[0]))
        item.length === 2 ? me.detailsForm.channel.push(item[1]) : null
      })
      me.detailsForm.channel =
        me.detailsForm.channel.length === 13 ? ['all'] : me.detailsForm.channel
      me.detailsForm.platform = Array.from(new Set(me.detailsForm.platform))
      me.detailsForm.channel = Array.isArray(me.detailsForm.channel)
        ? me.detailsForm.channel.join(',')
        : me.detailsForm.channel
    },
    // 清除安卓所有
    clearAndroid() {
      const selectedPlatform = this.selectedPlatform
      this.selectedPlatform = selectedPlatform.filter((item) => {
        if (item.length === 1) return item
      })
      this.handlePlatformChange(this.selectedPlatform)
    },
    // 更新tag
    updateTage(data) {
      this.detailsForm.userLable = data
    }
  }
}
</script>

<style lang="scss">
.ad-config-identical {
  .inarow {
    display: flex;
  }
  .ad-cascader-selected {
    width: 400px;
  }
}
</style>
