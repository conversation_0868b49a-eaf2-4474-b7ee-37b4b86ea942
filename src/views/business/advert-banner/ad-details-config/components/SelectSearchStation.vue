<template>
  <div class="select-search-station">
    <el-form ref="form" :model="detailsForm" label-width="130px">
      <el-form-item
        v-if="[20, 22].includes(typeId)"
        label="广告标题"
        :required="typeId === 21"
      >
        <el-input
          v-model="detailsForm.title"
          placeholder="请输入标题，最多50个字"
          maxlength="50"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item v-if="typeId === 22" label="选择车型" required>
        <el-input
          v-model="linkCarShow"
          @click="selectVehicle"
          placeholder="请选择车型"
          clearable
          style="width: 350px"
          @clear="displanCarList = []"
        />
      </el-form-item>
      <el-form-item
        :label="
          [20, 22].includes(typeId)
            ? `${typeId === 20 ? '跳转' : ''}类型`
            : '图片跳转'
        "
        required
      >
        <el-select
          v-model="detailsForm.linkType"
          clearable
          placeholder="请选择跳转类型"
        >
          <el-option
            v-for="(value, index) in linkTypeEnum"
            :key="index"
            :label="value.name"
            :value="value.linkType"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="pageSelect.length"
        label="选择页面"
        required
        label-width="110px"
      >
        <el-select
          v-model="detailsForm.pageSelect"
          clearable
          placeholder="请选择页面"
          @change="changeLinkType"
        >
          <el-option
            v-for="(value, index) in pageSelect"
            :key="index"
            :label="value.name"
            :value="value.linkType"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="isLinkUrl"
        v-show="![13, 24, 25].includes(detailsForm.pageSelect)"
        :label="
          [1, 2, 3, 4, 44].includes(detailsForm.linkType)
            ? '填写链接'
            : '填写ID'
        "
      >
        <el-input
          v-model="detailsForm.linkUrl"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item
        v-if="isLinkUrl && detailsForm.pageSelect === 13"
        label="配置ID"
      >
        <el-button type="primary" link @click="$refs.SelectEassay.clearData()"
          >选择ID</el-button
        >
      </el-form-item>
      <el-form-item
        v-if="isLinkUrl && detailsForm.pageSelect === 13"
        label="配置内容"
      >
        <ShowEassayList ref="ShowEassayList" />
      </el-form-item>
      <el-form-item
        v-if="detailsForm.linkType === 44"
        v-show="![13, 24, 25].includes(detailsForm.pageSelect)"
        label="外挂JS"
      >
        <el-input
          v-model.trim="detailsForm.linkUrlExt"
          placeholder="填写文档链接"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item v-if="typeId === 21" required label="选择品牌">
        <el-input
          v-model="selectBrandName"
          :model-value="selectBrandName"
          placeholder="请选择"
          style="width: 290px"
          @click="chooseBrand({ multi: false })"
        />
      </el-form-item>
      <el-form-item v-if="[21, 22].includes(typeId)" label="图片" required>
        <img :src="detailsForm.coverImage" alt="" class="imgsize" />
        <div style="align-self: flex-end">
          <uploadImg @getImgData="getImgData" />
          <span class="ml10">图片规格{{ ImgSizeEnum[typeId] }}</span>
        </div>
      </el-form-item>
      <el-form-item v-if="typeId === 22" label="按钮名称" required>
        <el-input
          v-model="detailsForm.buttonName"
          placeholder="请输入按钮名称"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item v-if="typeId === 21" label="配置车型&链接" required>
        <el-table
          ref="multipleTable"
          :data="dataList"
          row-key="multipleTable"
          border
          style="width: 40%; overflow-y: auto"
        >
          <el-table-column
            label="选择车型"
            class-name="check-all"
            align="center"
          >
            <template v-slot="scope">
              <el-autocomplete
                popper-class="my-autocomplete"
                v-model="scope.row.relationName"
                :fetch-suggestions="querySearchGoods"
                placeholder="请输入内容"
                @select="handleSelectGoods"
                @click="selectedGoodsIndex = scope.$index"
              >
                <template v-slot:suffix>
                  <el-icon class="el-input__icon"><IconEdit /></el-icon>
                </template>
                <template v-slot="{ item }">
                  <div class="name">{{ item.goodName }}</div>
                </template>
              </el-autocomplete>
            </template>
          </el-table-column>
          <el-table-column
            prop="relationLinkUrl"
            label="配置链接"
            align="center"
          >
            <template v-slot="scope">
              {{ scope.row.relationLinkUrl || '' }}
              <el-button
                size="small"
                type="primary"
                link
                @click="updateLink(scope.$index)"
                >修改</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item v-if="typeId === 21">
        <el-checkbox v-model="checked">引导文案</el-checkbox>
        <el-form-item v-if="checked" label="文案" required label-width="50px">
          <el-input
            v-model="detailsForm.title"
            placeholder="请输入文案，最多50个字"
            maxlength="50"
            clearable
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item v-if="checked" label="角标" required label-width="50px">
          <el-select v-model="badges" clearable placeholder="请选择页面">
            <el-option
              v-for="(value, index) in badgeList"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
          <el-input
            v-model="badgeName"
            placeholder="请输入自定义内容"
            maxlength="1"
            clearable
            style="width: 200px"
          />
          （最多支持1个字）
        </el-form-item>
      </el-form-item>
      <el-form-item v-if="typeId === 20" label="图片" required>
        <img :src="detailsForm.coverImage" alt="" class="imgsize" />
        <div style="align-self: flex-end">
          <uploadImg @getImgData="getImgData" />
          <span>图片规格{{ ImgSizeEnum[typeId] }}</span>
        </div>
      </el-form-item>
      <el-form-item label="展示广告标签">
        <el-checkbox
          v-model="advertTag"
          @change="detailsForm.advertTag = advertTag ? 1 : 0"
        />
      </el-form-item>
      <!-- 关键词 -->
      <el-form-item label="关键词">
        <el-input
          v-model="detailsForm.searchKeyword"
          type="textarea"
          placeholder="按英文,分开"
          clearable
          style="width: 300px"
        />
      </el-form-item>
    </el-form>
    <ChooseDialogCar ref="ChooseDialogCar" @sendCarData="sendCarData" />
    <ChooseDialogBrand ref="ChooseDialogBrand" @sendData="getBrandData" />
    <SelectEassay ref="SelectEassay" @setBackData="setLinkData" />
    <el-dialog
      v-model="dialogVisible"
      :title="'修改'"
      center
      class="choose-dialog"
      width="800px"
      append-to-body
    >
      <el-form ref="form" :model="updateDetailsForm" label-width="130px">
        <el-form-item label="车型">
          <span>{{ updateDetailsForm.relationName }}</span>
        </el-form-item>
        <el-form-item label="配置链接">
          <el-input
            v-model="updateDetailsForm.relationLinkUrl"
            placeholder="请输入链接"
            clearable
            style="width: 350px"
          />
        </el-form-item>
      </el-form>
      <div class="footer-content">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="updateLinkData()">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Edit as IconEdit } from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import uploadImg from './uploadImg.vue'
import ChooseDialogCar from '@/components/Dialog/ChooseDialogCar.vue'
import ChooseDialogBrand from '@/components/Dialog/ChooseDialogBrand.vue'
import { getLinkTypeList } from '@/api/advertModule'
import { GetBrandAllList, searchCarList } from '@/api/garage'
import ShowEassayList from './ShowEassayList.vue'
import SelectEassay from './SelectEassay.vue'
import { deepCopy } from '@/utils'
export default {
  components: {
    uploadImg,
    ChooseDialogCar,
    ChooseDialogBrand,
    ShowEassayList,
    SelectEassay,
    IconEdit
  },
  name: 'SelectSearchStation',
  data() {
    return {
      typeId: '',
      selectBrandName: '',
      linkCarShow: '', // 展示选中的车型
      badges: '', // 角标
      badgeName: '', // 角标
      selectedGoodsIndex: 0,
      ImgSizeEnum: {
        20: '690*388', // 内容站
        21: '690*200', // 品牌站
        22: '320*180' // 车型站
      },
      checked: false, // 引导文案按钮状态
      advertTag: false, // 展示广告标签
      dialogVisible: false, // 修改link数据
      updateDetailsForm: {}, // link数据修改
      detailsForm: {},
      linkTypeEnum: [],
      goodsDataList: [],
      dataList: [
        { relationName: '' },
        { relationName: '' },
        { relationName: '' }
      ], // 列表数据
      badgeList: ['无', '荐', '热', '爆', '惠', '自定义'],
      displayRelationList: [], // 品牌数据
      displanCarList: [] // 车型数据
    }
  },
  computed: {
    isLinkUrl() {
      const linkType = this.detailsForm.linkType
      const pageSelect = this.detailsForm.pageSelect
      let isLink = ''
      this.linkTypeEnum.map((_) => {
        if (_.linkType === linkType) {
          isLink = _.isLink
        }
      })
      if (pageSelect) {
        this.pageSelect.map((_) => {
          if (_.linkType === pageSelect) {
            isLink = _.isLink
          }
        })
      }
      const me = this
      if (me.detailsForm.linkUrl && me.typeId === 20) {
        setTimeout(() => {
          me.$refs.ShowEassayList &&
            me.$refs.ShowEassayList.getList(me.detailsForm.linkUrl)
        }, 10)
      }
      return isLink
    },
    // 控制显示-选择页面
    pageSelect() {
      const linkType = this.detailsForm.linkType
      let child = []
      this.linkTypeEnum.map((_) => {
        if (_.linkType === linkType) {
          child = _.child || []
        }
      })
      return child
    }
  },
  mounted() {
    const me = this
    me.detailsForm = me.$store.state.adConfig.adDetailData || {}
    me.typeId = me.detailsForm.adTypeId || ''
    me.advertTag = !!this.detailsForm.advertTag
    if (me.detailsForm.linkUrl && me.typeId === 20) {
      setTimeout(() => {
        me.$refs.ShowEassayList &&
          me.$refs.ShowEassayList.getList(this.detailsForm.linkUrl)
      }, 100)
    }
    if (me.typeId === 22) {
      me.displanCarList = me.detailsForm.displayRelationList || []
      me.linkCarShow = me.displanCarList.length
        ? me.displanCarList[0].relationName || ''
        : ''
    }
    if (me.typeId === 21) {
      me.displanCarList =
        me.detailsForm.displayRelationList &&
        me.detailsForm.displayRelationList.length
          ? [me.detailsForm.displayRelationList[0]]
          : []
      me.selectBrandName = me.displanCarList.length
        ? me.displanCarList[0].relationName || ''
        : ''
      me.dataList =
        me.detailsForm.displayRelationList &&
        me.detailsForm.displayRelationList.length
          ? me.detailsForm.displayRelationList.slice(1, 5)
          : [{ relationName: '' }, { relationName: '' }, { relationName: '' }]
      me.badges =
        me.detailsForm.badge && !me.badgeList.includes(me.detailsForm.badge)
          ? '自定义'
          : me.detailsForm.badge || ''
      me.badgeName =
        me.detailsForm.badge && !me.badgeList.includes(me.detailsForm.badge)
          ? me.detailsForm.badge || ''
          : ''
      me.checked = !!(me.detailsForm.title && me.detailsForm.badge)
      me.displanCarList.length
        ? me.getGoodsDataList(
            me.displanCarList[0].unitId,
            me.displanCarList[0].relationName
          )
        : null
    }
    me.getLinkTypeList()
  },
  methods: {
    // 获取广告跳转类型列表
    getLinkTypeList() {
      getLinkTypeList({
        typeId: this.detailsForm.adTypeId || ''
      }).then((res) => {
        if (res.data.code === 0) {
          this.linkTypeEnum = res.data.data || []
        }
      })
    },
    // 返回图片数据
    getImgData(imgOrgUrl) {
      console.log(imgOrgUrl)
      this.detailsForm.coverImage = imgOrgUrl
    },
    // 选择车型
    selectVehicle() {
      this.$refs.ChooseDialogCar &&
        this.$refs.ChooseDialogCar.init({
          selectLimit: 1
        })
    },
    // 获取返回的车型信息
    sendCarData(data) {
      console.log(`data`, data)
      if (!data.length) return
      this.displanCarList = [
        {
          businessType: 4,
          relationId: data[0].goodId,
          relationLinkUrl: '',
          relationName: data[0].goodName
        }
      ]
      this.linkCarShow = data[0].goodName || ''
      this.detailsForm.linkUrl =
        this.detailsForm.pageSelect === 14
          ? data[0].goodId || ''
          : this.detailsForm.linkUrl || ''
      this.detailsForm.coverImage = data[0].goodPic || ''
    },
    // 选择品牌
    chooseBrand(data = { multi: false }) {
      this.$refs.ChooseDialogBrand &&
        this.$refs.ChooseDialogBrand.init({
          title: '选择品牌',
          multiple: data.multi,
          // labels: brandList,
          getListUrl: GetBrandAllList,
          canChoose: true
        })
    },
    // 获取返回的品牌信息
    getBrandData(data) {
      const obj = data && data.labels && data.labels[0]
      this.selectBrandName = obj.brandName || ''
      this.displayRelationList = [
        {
          businessType: 3,
          relationId: obj.brandId,
          relationName: obj.brandName
        }
      ]
      this.getGoodsDataList(obj.brandId, obj.brandName)
    },
    // 获取该品牌下数据
    getGoodsDataList(brandId, brand) {
      searchCarList({
        brand,
        brandId,
        page: 1,
        limit: 1000,
        saleStatus: 1,
        isOnStatus: 1
      }).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data || {}
          this.goodsDataList = data.list || []
        }
      })
    },
    // 筛选品牌下车型数据
    querySearchGoods(queryString, cb) {
      if (!this.goodsDataList.length) this.$message.error('品牌下没有车型数据')
      let restaurants = this.goodsDataList
      var results = queryString
        ? restaurants.filter(this.createFilterGoods(queryString))
        : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilterGoods(queryString) {
      return (restaurant) => {
        return (
          restaurant.goodName.toLowerCase().indexOf(queryString.toLowerCase()) >
          -1
        )
      }
    },
    // 选择对应数据
    handleSelectGoods(data) {
      const newData = {
        relationName: data.goodName,
        businessType: 4,
        relationId: data.goodId,
        relationLinkUrl: `http://wap.58moto.com/garage/detail/${data.goodId}`
      }
      this.dataList.splice(this.selectedGoodsIndex, 1, newData)
    },
    // 更新link
    updateLink(index) {
      this.selectedGoodsIndex = index
      if (!this.dataList[index].relationName)
        return this.$message.error('没有车型数据')
      this.updateDetailsForm = deepCopy(this.dataList[index])
      this.dialogVisible = true
    },
    // 更新link 数据
    updateLinkData() {
      this.dialogVisible = false
      this.dataList.splice(this.selectedGoodsIndex, 1, this.updateDetailsForm)
    },
    // 设置返回的文章数据
    setLinkData(data) {
      this.detailsForm.linkUrl = data.id
      this.detailsForm.coverImage =
        this.detailsForm.coverImage ||
        (data.mediaInfo && data.mediaInfo.length
          ? data.mediaInfo[0].img || ''
          : '')
      this.detailsForm.title = this.detailsForm.title || data.title
      this.$refs.ShowEassayList.setListData([data])
    },
    postData() {
      const me = this
      if (me.typeId === 21 && !me.selectBrandName)
        return me.$message.error('请选择品牌')
      if (!me.detailsForm.pageSelect && me.pageSelect.length)
        return me.$message.error('请选择页面')
      if (!me.detailsForm.coverImage) return me.$message.error('请上传图片')
      if (me.typeId === 22 && !me.displanCarList.length)
        return me.$message.error('请选择车型')
      if (me.typeId === 22 && !me.detailsForm.buttonName)
        return me.$message.error('请输入按钮名称')
      const newCarData = this.dataList.filter((item) => {
        if (item.relationName) return item
      })
      if (me.typeId === 21 && !newCarData.length)
        return me.$message.error('请配置车型')
      if (me.typeId === 21 && newCarData.length !== 3)
        return me.$message.error('请配置车型必须配置3个')
      if (me.checked && !me.detailsForm.title)
        return me.$message.error('请配置文案')
      me.detailsForm.badge =
        me.badges === '自定义' ? me.badgeName : me.badges || ''
      if (me.checked && !me.detailsForm.badge)
        return me.$message.error('请配置角标')
      const backData =
        [...me.displayRelationList, ...me.displanCarList, ...newCarData] || []
      me.detailsForm.displayRelationListStr = backData.length
        ? JSON.stringify(backData)
        : ''
      $emit(me, 'updateSearchData')
    },
    changeLinkType() {
      this.detailsForm.linkUrl = ''
      if (this.detailsForm.pageSelect === 14) {
        this.detailsForm.linkUrl =
          this.detailsForm.linkUrl || this.displanCarList[0].relationId || ''
      }
    }
  },
  emits: ['updateSearchData']
}
</script>
