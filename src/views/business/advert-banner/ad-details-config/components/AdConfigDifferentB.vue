/** * 配置广告内容-不同-2 */
<template>
  <div class="ad-config-different-B">
    <el-form ref="form" :model="detailsForm" label-width="130px">
      <el-form-item label="投放类型" v-if="clientPage === 210">
        <el-radio v-model="releaseType" label="1">品牌</el-radio>
        <el-radio v-model="releaseType" label="2">车型</el-radio>
      </el-form-item>
      <div class="inarow">
        <el-form-item v-if="selectAdParty" label="选择品牌" required>
          <el-input
            v-model="detailsForm.linkUrlName"
            :value="detailsForm.linkUrlName"
            placeholder="请选择"
            style="width: 290px"
            @click="chooseBrand()"
          />
        </el-form-item>
        <el-form-item
          v-if="selectCar"
          label="选择车型"
          :required="clientPage === 210"
        >
          <el-input
            v-model="getCarName"
            :value="getCarName"
            placeholder="请选择"
            style="width: 290px"
            @click="selectVehicle()"
          />
        </el-form-item>
      </div>
      <!-- 投放品牌 投放车型 -->
      <el-form-item
        v-if="launchBrand"
        label="投放品牌/车型"
        :required="![55, 251].includes(clientPage)"
      >
        <SelectBrandCar ref="selectedBrand1" @selectData="updateBrand" />
      </el-form-item>
      <!-- 选车-车型详情页-相关车型/同级新车 投放品牌/车型  目标品牌 目标车型 -->
      <el-form-item v-if="targetBrand" required label="投放品牌/车型">
        <el-input
          v-model="detailsForm.linkUrlName"
          :value="detailsForm.linkUrlName"
          placeholder="请选择"
          style="width: 670px"
          @click="getTargetBrand()"
        />
        <el-form-item v-if="showCarName" label="投放款型">
          <el-select v-model="linkUrlExt.carId" placeholder="请选择" clearable>
            <el-option
              v-for="(value, index) in carList"
              :key="index"
              :label="value.goodsCarName"
              :value="value.carId"
            />
          </el-select>
        </el-form-item>
      </el-form-item>
      <el-form-item v-if="showCarName" required label="卡片标题">
        <el-input
          v-model="detailsForm.cardTitle"
          placeholder="请输入卡片标题"
          style="width: 670px"
        />
      </el-form-item>
      <!-- 首页-车辆推荐、搜索站-车型站、选车、搜索、首页-推荐-车型小组件、竞品车型、品牌详情页-车辆-->
      <SelectType
        v-if="showTypes"
        ref="SelectType"
        :linkTypeShowDefault="true"
        @requiredItemType="requiredItemData"
      />
      <el-form-item v-if="targetCar" label="目标品牌/车型" required>
        <SelectBrandCar ref="selectedBrand2" @selectData="updateBrand" />
      </el-form-item>
      <!-- --------------------------------- -->
      <el-form-item v-if="imageStyle" label="图片样式" required>
        <el-select v-model="detailsForm.pictureStyle" placeholder="请选择">
          <el-option
            v-for="(value, index) in pictureStyleEnum"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="imageUpload" label="图片上传" required>
        <img :src="detailsForm.coverImage" alt="" class="imgsize" />
        <div style="align-self: flex-end">
          <uploadImg @getImgData="getImgData" />
          <span>图片规格：大图750*388、小图750*170</span>
        </div>
      </el-form-item>
      <template v-if="showBulletinBoardTag">
        <el-form-item label="标签" required>
          <el-input
            v-model="detailsForm.badge"
            placeholder="最多两个字"
            maxlength="2"
            clearable
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item label="文案" required>
          <el-input
            v-model="detailsForm.title"
            clearable
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item label="跳转文案">
          <el-input
            v-model="detailsForm.buttonName"
            placeholder="最多四个字"
            maxlength="4"
            clearable
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item label="跳转链接">
          <el-input
            v-model="detailsForm.linkUrl"
            clearable
            style="width: 450px"
          />
        </el-form-item>
      </template>
      <div class="inarow">
        <el-form-item v-if="adLocation" label="广告位置" required>
          <el-select
            v-model="detailsForm.position"
            placeholder="请选择"
            style="width: 120px; margin-right: 10px"
          >
            <el-option
              v-for="(value, index) in positionMaxNum"
              :key="index"
              :label="value.showLabel"
              :value="value.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="selectAdPartybrand" label="选择品牌">
          <el-input
            v-model="selectBrandName"
            :value="selectBrandName"
            placeholder="请选择"
            style="width: 290px"
            @click="chooseBrand({ multi: true })"
          />
        </el-form-item>
        <el-form-item v-if="refreshCount" label="广告刷数" label-width="90px">
          <el-select
            v-model="detailsForm.refreshCount"
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option
              v-for="(value, index) in refreshCountEnum"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="showAdvertTag" label="展示广告标签">
          <el-checkbox
            v-model="advertTag"
            @change="detailsForm.advertTag = advertTag ? 1 : 0"
          />
        </el-form-item>
      </div>
      <el-form-item v-if="monopolize" label="是否独占" required>
        <el-switch
          v-model="detailsForm.exclusiveFlag"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
      </el-form-item>
      <el-form-item v-if="btnPosition" label="按钮位置">
        <el-select
          v-model="detailsForm.buttonPosition"
          placeholder="请选择"
          style="width: 120px"
        >
          <el-option
            v-for="(value, index) in buttonPositionEnum"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="buttonSize" label="按钮大小">
        <el-select
          v-model="detailsForm.buttonSize"
          placeholder="请选择"
          style="width: 120px"
        >
          <el-option
            v-for="(value, index) in buttonSizeEnum"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="polling" label="是否轮巡">
        <el-switch
          v-model="detailsForm.isRepeat"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
      </el-form-item>
      <el-form-item v-if="badge" label="配置角标">
        <el-select v-model="badgeName" @change="changeBadge">
          <el-option
            v-for="(value, index) in badgeEnum"
            :key="index"
            :label="index"
            :value="value"
          />
          <el-option label="自定义" value="自定义" />
        </el-select>
        <el-input
          v-model="badgeCustom"
          :disabled="badgeName !== '自定义'"
          placeholder="自定义文本内容"
          style="width: 210px; margin: 0 10px"
          :maxlength="typeId === 11 ? 3 : 2"
          @input="changeBadge"
        />
        <span>(最多支持{{ typeId === 11 ? '三' : '两' }}个字)</span>
        <div v-if="badgeName !== ''" style="display: flex; align-items: center">
          <span style="margin-left: 20px; margin-right: 10px">角标颜色</span>
          <span
            v-for="(color, index) in badgeColorList"
            :key="index"
            :style="{
              width: '60px',
              height: '30px',
              backgroundColor: color,
              marginRight: '10px',
              display: 'inline-block',
              border: `${
                detailsForm.badgeColor === color ? '2px solid #3370ff' : ''
              }`
            }"
            @click="changeBadgeColor(color)"
          ></span>
        </div>
      </el-form-item>

      <div class="inarow" v-if="badgeSignTime">
        <el-form-item label="角标标记开始时间" label-width="134px">
          <el-date-picker
            v-model="detailsForm.badgeBeginTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择日期时间"
            style="width: 230px; margin-right: 30px"
          />
        </el-form-item>
        <el-form-item label="角标标记结束时间" label-width="134px">
          <el-date-picker
            v-model="detailsForm.badgeEndTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择日期时间"
            :default-time="$dayjs('23:59:59', 'hh:mm:ss').toDate()"
            style="width: 230px"
          />
        </el-form-item>
      </div>
      <el-form-item v-if="buttonName" label="按钮名称" required>
        <el-input
          v-model="detailsForm.buttonName"
          clearable
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item v-if="hoopIds" label="摩友圈ID">
        <el-input
          v-model="motoCircleId"
          clearable
          style="width: 450px"
          @change="setMotoCircleId"
        />
      </el-form-item>
      <!-- 摩友圈-品牌/车型 -->
      <el-form-item v-if="hoopBrandCar" label="摩友圈-品牌/车型">
        <SelectBrandCar ref="selectedBrand3" @selectData="updateBrand" />
      </el-form-item>
      <!-- 摩友圈-->
      <el-form-item
        v-if="selectCircle"
        label="选择摩友圈"
        class="set-padding"
        required
      >
        <el-input
          v-model="showCircleName"
          :value="showCircleName"
          placeholder="请选择"
          style="width: 290px"
          @click="selectCircleDialog()"
        />
      </el-form-item>
      <!-- 关键词 -->
      <el-form-item v-if="showSearchKeyword" label="关键词">
        <el-input
          v-model="detailsForm.searchKeyword"
          type="textarea"
          placeholder="按英文,分开"
          clearable
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="权重/顺序" required>
        <el-input
          v-model="detailsForm.positionSortNum"
          type="number"
          clearable
          style="width: 200px"
        />
      </el-form-item>
    </el-form>
    <ChooseDialogBrand ref="ChooseDialogBrand" @sendData="getBrandData" />
    <ChooseDialogCar ref="ChooseDialogCar" @sendCarData="sendCarData" />
    <ChooseDialogCar
      ref="ChooseDialogTargetCar"
      @sendCarData="sendTargetCarData"
    />
    <SelectCircle ref="selectCircleItem" @updateCircleData="circleData" />
    <!-- 综合搜索单独展示 -->
    <SelectSearchStation
      v-if="clientPage === 199"
      ref="SelectSearchStation"
      @updateSearchData="selectSearchStation"
    />
  </div>
</template>

<script>
import { $emit } from '../../../../../utils/gogocodeTransfer'
import {
  pictureStyleEnum,
  positionEnum,
  refreshCountEnum,
  buttonPositionEnum,
  buttonSizeEnum,
  badgeEnum
} from '@/utils/enum/adConfigEnum'

import { getAdSiteSetPositionList } from '@/api/advertModule'
import { laterPickerOptions } from '@/utils/configData'
import ChooseDialogBrand from '@/components/Dialog/ChooseDialogBrand.vue'
import ChooseDialogCar from '@/components/Dialog/ChooseDialogCar.vue'
import SelectCircle from './SelectCircle.vue'
import uploadImg from './uploadImg.vue'
import SelectSearchStation from './SelectSearchStation.vue'
import SelectType from './selectType.vue'
import { GetBrandAllList, getAgeList } from '@/api/garage'
import SelectBrandCar from './selectBrandCar.vue'
export default {
  name: 'AdConfigDifferentB',
  components: {
    ChooseDialogBrand,
    ChooseDialogCar,
    uploadImg,
    SelectCircle,
    SelectSearchStation,
    SelectType,
    SelectBrandCar
  },
  data() {
    return {
      pickerOptions: laterPickerOptions,
      // detailsForm: {},
      pictureStyleEnum, // 图片样式枚举
      positionEnum, // 广告位置枚举
      refreshCountEnum, // 广告刷数枚举
      buttonPositionEnum, // 按钮位置枚举
      buttonSizeEnum, // 按钮大小枚举
      badgeEnum, // 配置角标枚举
      badgeName: '', // 角标假值
      badgeCustom: '', // 角标自定义
      badgeTime: [], // 角标标记时间
      positionList: [], // 位置
      motoCircleId: '', // 摩友圈ID
      getCarName: '',
      typeId: '',
      clientPage: '',
      selectBrandName: '',
      brandNull: '', // 判断品牌是否为空
      showCircleName: '', // 展示的圈子id
      advertTag: false, // 广告标识
      releaseType: '1', // 品牌 210 搜索-默认搜索页-轮播词使用
      displayRelationList: [], // 210 搜索-默认搜索页-轮播词 使用的列表
      carList: [], // 投放款型
      linkUrlExt: {
        goodsId: '',
        carId: ''
      },
      badgeColorList: ['#f43530', '#5288f6', '#feaf38']
    }
  },
  computed: {
    detailsForm() {
      return this.$store.state.adConfig.adDetailData || {}
    },
    /**
     * 广告类型
     * 1-启动屏(开屏) 2-信息流 3-焦点图 4-弹窗 5-热搜标题 6-经销商 7-品牌 8-车辆
     * 9-文章原样式 10-运营位新 11-金刚位 12-小视频 13-贴片视频 14-激励视频 15-驾校
     * 26-公告栏
     *
     * 广告clientPage
     * 24-首页/发现/车辆推荐 25-经销商/经销商列表 123-经销商/附近tab-优质经销商
     * 83-经销商/车型详情页/优质经销商 121-经销商/车型详情页/车型图片-经销商 232 233摩友圈/摩友圈详情（原31）
     * 32摩友圈/首页-摩友圈tab/热门动态 33摩友圈/首页-摩友圈tab/我的圈子动态 43-选车/品牌详情页/车型
     * 44-选车/选车-首页/推荐车型 48-选车/车型详情页/相关车型 49-选车/车型详情页/同级新车
     * 55-选车/车型详情页/底部banner 69-商城/商城首页/运营位1 73-商城/商城首页/活动专区
     * 76-搜索/默认搜索页/推荐车型 79-选车/选车-首页/条件选车 154-驾校报名/驾校列表/驾校
     * 166-选车/品牌详情页/焦点图banner 253-内容详情-视频类-小视频
     */

    // 控制显示-选择品牌
    selectAdParty() {
      const idArr = [7]
      const pageArr = [210] // 210 搜索-默认搜索页-轮播词使用
      // const idArr = [7]
      // const pageArr = [24, 44, 76]
      // const pageArr = [166]
      // const falg = Boolean(idArr.includes(this.typeId) || pageArr.includes(this.clientPage))
      const falg =
        Boolean(idArr.includes(this.typeId)) ||
        (pageArr.includes(this.clientPage) && this.releaseType === '1')
      return falg
    },
    // 控制显示-选择品牌
    selectAdPartybrand() {
      const pageArr = [166]
      const falg = Boolean(pageArr.includes(this.clientPage))
      return falg
    },
    // 控制显示-选择车型
    selectCar() {
      const pageArr = [210] // 210 搜索-默认搜索页-轮播词使用
      // const pageArr = [24, 44, 76]
      const falg =
        Boolean(pageArr.includes(this.clientPage)) && this.releaseType === '2'
      return falg
    },
    // 控制显示-投放品牌/车型
    launchBrand() {
      const pageArr = [83, 121, 55, 251]
      const falg = Boolean(pageArr.includes(this.clientPage))
      return falg
    },
    // 控制显示-投放品牌/车型
    targetBrand() {
      // const pageArr = [48, 49, 79]
      // const pageArr = [24, 44, 48, 49, 76, 79]
      const idArr = [8]
      const falg = Boolean(idArr.includes(this.typeId))
      return falg
    },
    // 控制显示-目标品牌/车型
    targetCar() {
      const pageArr = [43, 48, 49]
      const falg = Boolean(pageArr.includes(this.clientPage))
      return falg
    },
    // 控制显示-图片样式
    imageStyle() {
      const adProjectType = this.$store.state.adConfig.adProjectType || ''
      const idArr = [2]
      const pageArr = []
      // const pageArr = [232, 233]
      const falg = Boolean(
        adProjectType !== 4 &&
          idArr.includes(this.typeId) &&
          !pageArr.includes(this.clientPage)
      )
      return falg
    },
    // 控制显示-图片上传
    imageUpload() {
      const adProjectType = this.$store.state.adConfig.adProjectType || ''
      const idArr = [2]
      const pageArr = []
      // const pageArr = [232, 233]
      const falg = Boolean(
        adProjectType !== 4 &&
          idArr.includes(this.typeId) &&
          !pageArr.includes(this.clientPage)
      )
      return falg
    },
    showBulletinBoardTag() {
      const idArr = [26]
      const flag = idArr.includes(this.typeId)
      return flag
    },
    // 控制显示-广告位置
    adLocation() {
      const idArr = [2, 3, 5, 6, 7, 8, 9, 11, 12, 15, 16, 17, 23] // 23 车型小组件
      const pageArr = [
        24, 121, 197, 198, 199, 203, 213, 214, 215, 250, 254, 259
      ] // 197 选车首页-车型推荐  198 搜索页-车型推荐 // 199 搜索·搜索结果页-综合·搜索站 // 信息流-车型小组件下车型 // 213 选车-新能源-推荐车型 // 214 内容详情-文章详情页-底部信息流  215 内容详情-视频详情页-底部信息流 250 二手车-二手车详情页-底部信息流 254 全局-全局信息流 259 全局-全局小视频
      const falg = Boolean(
        idArr.includes(this.typeId) && !pageArr.includes(this.clientPage)
      )
      return falg
    },
    positionMaxNum() {
      const pageArr = [253]
      const falg = Boolean(pageArr.includes(this.clientPage))
      let maxNum = 20
      if (this.detailsForm.positionMax) {
        maxNum = this.detailsForm.positionMax
      }
      if (falg) {
        maxNum = 5
      }
      const listInit = Array.from(new Array(maxNum), (_, i) => ++i)
      let list = []
      listInit.forEach((num) => {
        const index = this.positionList.findIndex(
          (v) => v.position === num && v.showType === 2
        )
        const obj = {
          value: num,
          showLabel: index >= 0 ? `${num}(轮巡)` : `${num}(随机)`
        }
        list.push(obj)
      })
      return list
    },
    // 控制显示-广告刷数
    refreshCount() {
      const idArr = [2, 9, 12, 23] // 23 车型小组件
      const pageArr = [25, 79, 154]
      const noPageArr = [214, 215, 250, 254, 259] // 250 二手车-二手车详情页-底部信息流 254 全局-全局信息流 259 全局-全局小视频
      const falg = Boolean(
        (idArr.includes(this.typeId) || pageArr.includes(this.clientPage)) &&
          !noPageArr.includes(this.clientPage)
      )
      return falg
    },
    // 控制显示-是否独占
    monopolize() {
      const pageArr = [123, 83]
      const falg = Boolean(pageArr.includes(this.clientPage))
      return falg
    },
    // 控制显示-按钮位置
    btnPosition() {
      const adProjectType = this.$store.state.adConfig.adProjectType || ''
      const idArr = [4]
      const falg = Boolean(adProjectType !== 4 && idArr.includes(this.typeId))
      return falg
    },
    // 控制显示-按钮大小
    buttonSize() {
      const adProjectType = this.$store.state.adConfig.adProjectType || ''
      const idArr = [4]
      const falg = Boolean(adProjectType !== 4 && idArr.includes(this.typeId))
      return falg
    },
    // 控制显示-是否轮巡
    polling() {
      const idArr = [2]
      const noPageArr = [214, 215, 250, 254] // 250 二手车-二手车详情页-底部信息流 254 全局-全局信息流
      const pageArr = [253]
      const falg = Boolean(
        (idArr.includes(this.typeId) || pageArr.includes(this.clientPage)) &&
          !noPageArr.includes(this.clientPage)
      )
      return falg
    },
    // 控制显示-配置角标
    badge() {
      const idArr = [5, 8, 11]
      const pageArr = [79]
      const falg = Boolean(
        idArr.includes(this.typeId) && !pageArr.includes(this.clientPage)
      )
      return falg
    },
    // 控制显示-角标标记时间
    badgeSignTime() {
      const idArr = [5, 8, 11]
      const pageArr = [79]
      const falg = Boolean(
        idArr.includes(this.typeId) && !pageArr.includes(this.clientPage)
      )
      return falg
    },
    // 控制显示-选品库/品牌库地址
    // jumpAddress() {
    //   const pageArr = [69, 73]
    //   const falg = Boolean(pageArr.includes(this.clientPage))
    //   return falg
    // },
    // 控制显示-按钮名称
    buttonName() {
      const pageArr = [24]
      const falg = Boolean(pageArr.includes(this.clientPage))
      return falg
    },
    // 控制显示-按钮跳转链接
    // buttonJumpLink() {
    //   const pageArr = [24]
    //   const falg = Boolean(pageArr.includes(this.clientPage))
    //   return falg
    // },
    // 控制显示-摩友圈ID
    hoopIds() {
      const adProjectType = this.$store.state.adConfig.adProjectType || ''
      const idArr = [3]
      const pageArr = [232, 233]
      const falg = Boolean(
        (adProjectType === 4 && idArr.includes(this.typeId)) ||
          pageArr.includes(this.clientPage)
      )
      return falg
    },
    // 控制显示-摩友圈-品牌/车型
    hoopBrandCar() {
      const adProjectType = this.$store.state.adConfig.adProjectType || ''
      const idArr = [3]
      const pageArr = [232, 233]
      const falg = Boolean(
        (adProjectType === 4 && idArr.includes(this.typeId)) ||
          pageArr.includes(this.clientPage)
      )
      return falg
    },
    // 控制显示-选择摩友圈
    selectCircle() {
      const pageArr = [181, 182] // 车型/品牌-热门圈子
      const falg = Boolean(pageArr.includes(this.clientPage))
      return falg
    },
    // 控制显示-是否展示 展示广告标签
    showAdvertTag() {
      const typeArr = [1, 2, 3, 4, 9, 26]
      const noPageArr = [199, 250] // 199 搜索·搜索结果页-综合·搜索站 250 二手车-二手车详情页-底部信息流
      const pageArr = [253]
      const falg = Boolean(
        (typeArr.includes(this.typeId) || pageArr.includes(this.clientPage)) &&
          !noPageArr.includes(this.clientPage)
      )
      return falg
    },
    // 控制显示-关键词
    showSearchKeyword() {
      const pageArr = [193, 194, 195] // 搜索结果页-文章、搜索结果页-动态、搜索结果页-视频
      const falg = Boolean(pageArr.includes(this.clientPage))
      return falg
    },
    // 控制显示-类型
    showTypes() {
      const pageArr = [24, 197, 198, 48, 43, 203] // 首页-车辆推荐、选车、搜索、首页-推荐-车型小组件、竞品车型、品牌详情页-车辆
      const falg = Boolean(pageArr.includes(this.clientPage))
      return falg
    },
    // 控制显示-款型和卡片标题
    showCarName() {
      const pageArr = [24] // 首页-推荐-车辆推荐
      const falg = Boolean(pageArr.includes(this.clientPage))
      return falg
    }
  },
  mounted() {
    // this.detailsForm = this.$store.state.adConfig.adDetailData || {}
    // if (this.detailsForm.positionMax) {
    //   const getArr = (nums) => {
    //     return Array.from(new Array(nums), (_, i) => {
    //       ++i
    //       return { value: i, showLabel: i }
    //     })
    //   }
    //   this.positionEnum = getArr(this.detailsForm.positionMax)
    // }
    this.typeId = this.detailsForm.adTypeId || ''
    this.clientPage = this.detailsForm.clientPage || ''
    this.displayRelationList = []
    if (
      this.detailsForm.displayRelationList &&
      this.detailsForm.displayRelationList.length &&
      this.clientPage === 210
    ) {
      this.displayRelationList = this.detailsForm.displayRelationList
      const findStatus =
        this.displayRelationList.findIndex((item) => {
          return item.businessType === 3
        }) !== -1
      this.releaseType = findStatus ? '1' : '2'
      this.getCarName =
        this.releaseType === '2'
          ? this.displayRelationList[0].relationName || ''
          : ''
    }
    if (this.clientPage === 24) {
      this.linkUrlExt =
        this.detailsForm.linkUrlExt &&
        JSON.parse(this.detailsForm.linkUrlExt) &&
        typeof JSON.parse(this.detailsForm.linkUrlExt) === 'object'
          ? JSON.parse(this.detailsForm.linkUrlExt)
          : {}
      this.linkUrlExt.goodsId ? this.getCarList() : null
    }
    this.pictureStyleEnum = [214, 215].includes(this.clientPage)
      ? {
          大图: 1
        }
      : this.pictureStyleEnum
    this.advertTag = !!this.detailsForm.advertTag
    this.renderDetailData()
    this.getPosition()
  },
  methods: {
    changeBadgeColor(color) {
      this.detailsForm.badgeColor = color
    },
    // 渲染详情数据
    renderDetailData() {
      let arrBrand = []
      let arrCar = []
      this.detailsForm.relationListStr &&
        this.detailsForm.relationListStr.map((_) => {
          if (_.businessType === 3) {
            arrBrand = _.relationIdList
          }
          if (_.businessType === 4) {
            this.getCarName = _.relationIdList.join(',')
            arrCar =
              (_.relationIdList &&
                _.relationIdList.map((item) => {
                  return Number(item)
                })) ||
              []
          }
          if (_.businessType === 5) {
            this.motoCircleId = _.relationIdList.join(',')
          }
        })
      if (this.launchBrand || this.targetCar || this.hoopBrandCar) {
        const num = this.launchBrand ? '1' : this.targetCar ? '2' : '3'
        this.$nextTick(() => {
          this.$refs[`selectedBrand${num}`] &&
            this.$refs[`selectedBrand${num}`].init(
              arrBrand,
              arrCar,
              this.detailsForm.brandGoodsList
            )
        })
      }
      if (this.selectAdPartybrand) {
        GetBrandAllList()
          .then((res) => {
            if (res.data.code === 0) {
              const data = res.data.data || []
              data.map((_) => {
                if (arrBrand.includes(_.brandId)) {
                  this.selectBrandName += _.brandName + ','
                }
              })
            } else {
              this.selectBrandName = ''
            }
          })
          .catch(() => {
            this.selectBrandName = ''
          })
      }

      if (this.detailsForm.badge) {
        const arr = ['上新', '热门', '推荐', '广告', '降价', 'hot']
        this.badgeName = this.detailsForm.badge
        if (!arr.includes(this.detailsForm.badge)) {
          this.badgeName = '自定义'
          this.badgeCustom = this.detailsForm.badge
        }
      }

      if (this.detailsForm.badgeBeginTime && this.detailsForm.badgeEndTime) {
        this.badgeTime = [
          this.detailsForm.badgeBeginTime,
          this.detailsForm.badgeEndTime
        ]
      }
      // 选择摩友圈数据
      const pageArr = [181, 182] // 车型/品牌-热门圈子
      if (
        Boolean(pageArr.includes(this.clientPage)) &&
        this.detailsForm.linkUrl
      ) {
        this.showCircleName = this.detailsForm.linkUrlName
      }
    },
    // 必选
    requiredItem() {
      if (this.showTypes) {
        this.$refs.SelectType && this.$refs.SelectType.requiredItem()
      } else {
        this.requiredItemData()
      }
    },
    requiredItemData() {
      if (
        !this.detailsForm.linkUrl &&
        this.selectAdParty &&
        this.clientPage !== 210
      ) {
        return this.$message.error('请选择品牌')
      }
      if (
        !this.brandNull &&
        this.launchBrand &&
        ![55, 251].includes(this.clientPage)
      ) {
        return this.$message.error('请选择投放品牌')
      }
      if (
        !this.brandNull &&
        this.launchBrand &&
        [55, 251].includes(this.clientPage)
      ) {
        this.detailsForm.relationListStr = [
          {
            businessType: 8, // 8 全部品牌
            relationIdList: [0]
          }
        ]
      }
      if (!this.detailsForm.linkUrl && this.targetBrand) {
        return this.$message.error('请选择投放品牌/车型')
      }
      if (!this.detailsForm.cardTitle && this.showCarName) {
        return this.$message.error('请输入卡片标题')
      }
      if (!this.detailsForm.pictureStyle && this.imageStyle) {
        return this.$message.error('请选择图片样式')
      }
      if (!this.detailsForm.coverImage && this.imageUpload) {
        return this.$message.error('请上传图片')
      }
      if (this.showBulletinBoardTag) {
        if (!this.detailsForm.badge) {
          return this.$message.error('请填写标签')
        }
        if (!this.detailsForm.title) {
          return this.$message.error('请填写文案')
        }
        // this.detailsForm.relationListStr = [
        //   {
        //     businessType: 8, // 8 全部品牌
        //     relationIdList: [0]
        //   }
        // ]
      }
      if (!this.detailsForm.position && this.adLocation) {
        return this.$message.error('请选择广告位置')
      }
      if (!this.detailsForm.positionSortNum) {
        return this.$message.error(`请填写权重/顺序`)
      }
      if (!this.refreshCount) {
        this.detailsForm.refreshCount = ''
      }
      if (!this.detailsForm.buttonName && this.buttonName) {
        return this.$message.error('请填写按钮名称')
      }
      if (!this.detailsForm.buttonUrl && this.buttonJumpLink) {
        return this.$message.error('请填写按钮跳转链接')
      }
      if (!this.detailsForm.linkUrl && this.selectCircle) {
        return this.$message.error('请选择摩友圈')
      }
      if (!this.displayRelationList.length && this.clientPage === 210) {
        return this.$message.error('请选择车型或品牌')
      }
      if (this.detailsForm.searchKeyword) {
        const splitData = this.detailsForm.searchKeyword.split(',')
        if (splitData.length > 10) return this.$message.error('最多10个词')
        let errStatus = false
        splitData.map((item) => {
          if (item.length > 10) errStatus = true
        })
        if (errStatus) return this.$message.error('每个词最多10个字')
      }
      if (this.clientPage === 199) {
        return this.$refs.SelectSearchStation.postData()
      }
      this.detailsForm.displayRelationListStr =
        this.clientPage === 210
          ? JSON.stringify(this.displayRelationList)
          : this.detailsForm.displayRelationListStr
      this.detailsForm.linkUrlExt =
        this.clientPage === 24
          ? JSON.stringify(this.linkUrlExt)
          : this.detailsForm.linkUrlExt
      $emit(this, 'requiredItem', 1)
    },
    changeBadge() {
      if (this.badgeName === '自定义') {
        this.detailsForm.badge = this.badgeCustom
      } else {
        this.detailsForm.badge = this.badgeName
        this.badgeCustom = ''
      }
    },
    // 选择品牌
    chooseBrand(data = { multi: false }) {
      this.$refs.ChooseDialogBrand &&
        this.$refs.ChooseDialogBrand.init({
          title: '选择品牌',
          multiple: data.multi,
          // labels: brandList,
          getListUrl: GetBrandAllList,
          canChoose: true
        })
    },
    // 获取返回的品牌信息
    getBrandData(data) {
      if (this.clientPage === 210) {
        const obj = data.labels[0]
        this.displayRelationList = [
          {
            businessType: 3,
            relationId: obj.brandId,
            relationName: obj.brandName
          }
        ]
        this.detailsForm.displayRelationListStr = JSON.stringify(
          this.displayRelationList
        )
        this.detailsForm.linkUrlName = obj.brandName || ''
        return
      }
      if (this.selectAdPartybrand) {
        const ids = []
        this.selectBrandName = ''
        data.labels.map((_) => {
          ids.push(_.brandId)
          this.selectBrandName += _.brandName + ','
        })
        this.setRelationList(3, ids)
        return
      }
      const obj = data && data.labels && data.labels[0]
      this.detailsForm.linkUrl = obj.brandId || ''
      this.detailsForm.linkUrlName = obj.brandName || ''
    },
    // 选择车型
    selectVehicle() {
      const pageArr = [210]
      this.$refs.ChooseDialogCar &&
        this.$refs.ChooseDialogCar.init({
          selectLimit: pageArr.includes(this.clientPage) ? 1 : 99
        })
    },
    // 获取返回的车型信息
    sendCarData(data) {
      if (this.clientPage === 210) {
        const obj = data[0]
        this.displayRelationList = [
          {
            relationName: obj.goodName,
            businessType: 4,
            relationId: obj.goodId
          }
        ]
        this.detailsForm.displayRelationListStr = JSON.stringify(
          this.displayRelationList
        )
        this.getCarName = obj.goodName || ''
        return
      }
      const idArr = []
      const nameArr = []
      data &&
        data.map((_) => {
          idArr.push(_.goodId)
          nameArr.push(_.goodName)
        })
      this.getCarName = nameArr.join(',')
      this.setRelationList(4, idArr)
    },
    // 投放品牌/车型
    getTargetBrand() {
      this.$refs.ChooseDialogTargetCar &&
        this.$refs.ChooseDialogTargetCar.init({
          selectLimit: 1
        })
    },
    sendTargetCarData(data) {
      const obj = data && data[0]
      this.detailsForm.linkUrl = obj.goodId || ''
      if (obj.goodId) {
        this.$refs.SelectType && this.$refs.SelectType.init(obj.goodId)
      }
      this.detailsForm.linkUrlName = `${obj.brandName}/${obj.goodName}` || ''
      this.detailsForm.cardTitle = this.showCarName
        ? !this.detailsForm.cardTitle
          ? this.detailsForm.linkUrlName
          : this.detailsForm.cardTitle
        : ''
      if (this.clientPage === 24) {
        this.detailsForm.buttonUrl = `https://m.58moto.com/garage/detail/${obj.goodId}`
        this.linkUrlExt.goodsId = obj.goodId
      }
      this.getCarList()
    },
    // 投放品牌-车型/目标品牌-车型
    updateBrand(data) {
      const brand = data.brand === 'all' ? '' : data.brand
      const good = data.good
      if (good) {
        this.$refs.SelectType && this.$refs.SelectType.init(good)
      }
      this.setRelationList(3, brand ? brand.split(',') : [])
      this.setRelationList(4, good ? good.split(',') : [])
      this.brandNull = brand || good
    },
    // 摩友圈ID
    setMotoCircleId(e) {
      this.setRelationList(5, e ? e.split(',') : [])
    },
    setRelationList(num, arr) {
      let falg = true
      const findAllStatus = this.detailsForm.relationListStr.findIndex(
        (item) => {
          return item.businessType === 8
        }
      )
      findAllStatus !== -1
        ? this.detailsForm.relationListStr.splice(findAllStatus, 1)
        : null
      this.detailsForm.relationListStr.map((_) => {
        if (_.businessType === num) {
          _.relationIdList = arr
          falg = false
        }
      })
      if (falg) {
        this.detailsForm.relationListStr.push({
          businessType: num, // 3品牌 4车型 5 摩友圈
          relationIdList: arr
        })
      }
    },
    // 返回图片数据
    getImgData(imgOrgUrl) {
      this.detailsForm.coverImage = imgOrgUrl
    },
    // 选择摩友圈id
    selectCircleDialog() {
      this.$refs.selectCircleItem.init()
    },
    // 摩友圈数据
    circleData(data) {
      this.detailsForm.linkUrl = data.id || ''
      this.detailsForm.linkUrlName = data.name || ''
      this.showCircleName = data.name || ''
    },
    // 更新综合搜索数据
    selectSearchStation() {
      $emit(this, 'requiredItem')
    },
    // 获取款型数据
    getCarList() {
      if (!this.linkUrlExt.goodsId) return
      const me = this
      getAgeList({
        page: 1,
        limit: 20,
        isOnStatus: 1,
        goodsId: me.linkUrlExt.goodsId
      })
        .then((res) => {
          if (res.data.code === 0) {
            const data = res.data.data || {}
            me.carList = data.list || []
          }
        })
        .catch(() => {})
    },
    // 获取位置
    getPosition() {
      getAdSiteSetPositionList({
        clientPage: this.clientPage
      })
        .then((res) => {
          if (res.data.code === 0) {
            const data = res.data.data || {}
            this.positionList = data.listData || []
          }
        })
        .catch(() => {})
    }
  },
  emits: ['requiredItem']
}
</script>

<style lang="scss">
.ad-config-different-B {
  .inarow {
    display: flex;
  }
  .imgsize {
    max-width: 500px;
  }
}
</style>
