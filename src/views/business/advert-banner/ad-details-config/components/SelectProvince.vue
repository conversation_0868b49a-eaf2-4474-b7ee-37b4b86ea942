<template>
  <el-dialog
    v-model="dialogVisible"
    title="城市选择"
    width="850"
    top="10vh"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="handlerClose"
  >
    <div class="select-province">
      <div class="box-top">
        <div class="select-city">
          <div>城市选择：</div>
          <el-select
            v-model="selectCity"
            filterable
            remote
            reserve-keyword
            placeholder="请输入关键词"
            :remote-method="remoteMethodCity"
            @change="changeCity"
          >
            <el-option
              v-for="item in selectCityList"
              :key="item.value"
              :label="item.cityName"
              :value="item.cityName"
            >
            </el-option>
          </el-select>
        </div>
        <div class="content-select">
          <div v-if="selectedProvince.length" class="content-item">
            <span class="title">已选省份：</span>
            <span v-for="(p, i) in selectedProvince" :key="i" class="item">
              {{ p }}
            </span>
          </div>
          <div v-if="selectedCitys.length" class="content-item">
            <span class="title">已选城市：</span>
            <span v-for="(c, i) in selectedCitys" :key="i" class="item">
              {{ c }}
            </span>
          </div>
        </div>
        <el-button type="info" plain @click="resetData">重置</el-button>
      </div>
      <div class="box-bottom">
        <div class="checkbox-list">
          <el-checkbox
            v-model="checkedValue"
            label="全国"
            class="checkbox-style"
            @change="changeCheckedValue"
            :disabled="loading"
          />
          <el-checkbox-group
            v-model="checkedGroupValue"
            @change="changeCheckedGroupValue"
            :disabled="loading"
          >
            <el-checkbox :label="1">一线城市</el-checkbox>
            <el-checkbox :label="2">二线城市</el-checkbox>
            <el-checkbox :label="3">三线城市</el-checkbox>
            <el-checkbox :label="4">四线城市</el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="tree-box">
          <el-tree
            :data="treeData"
            :props="defaultProps"
            @check="checkData"
            show-checkbox
            node-key="name"
            accordion
            ref="elTreeRef"
          />
        </div>
        <div class="fooder-style">
          <el-button type="success" @click="confirm">确认</el-button>
          <el-button type="danger" @click="handlerClose">取消</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { useStore } from 'vuex'

const store = useStore()

const emits = defineEmits(['backData'])

const cityList = computed(() => {
  return store.state.cityList.cityList
})

const defaultProps = {
  children: 'citys',
  label: 'name'
}
const dialogVisible = ref(false)
const elTreeRef = ref()
const treeData = ref([])

const selectCity = ref('')
const selectCityList = ref([])

const selectedData = ref({})
const selectedCityData = ref([])
const selectedProvince = computed(() => {
  const list = []
  Object.keys(selectedData.value).forEach((key) => {
    if (!selectedData.value[key].length) {
      list.push(key)
    }
  })
  return list
})
const selectedCitys = computed(() => {
  let list = []
  Object.keys(selectedData.value).forEach((key) => {
    if (selectedData.value[key].length) {
      list = [...list, ...selectedData.value[key]]
    }
  })
  return list
})

const checkedValue = ref(false)
const checkedGroupValue = ref([])
const checkedGroupValue_ = ref([])
const loading = ref(false)

const CQ_1 = [
  '巴南区',
  '北碚区',
  '大渡口区',
  '江北区',
  '九龙坡区',
  '南岸区',
  '沙坪坝区',
  '渝北区',
  '渝中区'
]
const CQ_3 = [
  '璧山区',
  '城口县',
  '大足区',
  '垫江县',
  '丰都县',
  '奉节县',
  '涪陵区',
  '合川区',
  '江津区',
  '开州区',
  '梁平区',
  '南川区',
  '彭水苗族土家族自治县',
  '綦江区',
  '黔江区',
  '荣昌区',
  '石柱土家族自治县',
  '铜梁区',
  '潼南区',
  '万州区',
  '巫山县',
  '巫溪县',
  '武隆区',
  '秀山土家族苗族自治县',
  '永川区',
  '酉阳土家族苗族自治县',
  '云阳县',
  '长寿区',
  '忠县'
]
const cityList_1_super = ['北京市', '广州市', '深圳市', '上海市']
const cityList_1 = [
  '贵阳市',
  '郑州市',
  '武汉市',
  '长沙市',
  '南京市',
  '苏州市',
  '成都市',
  '济南市',
  '西安市',
  '昆明市',
  '杭州市',
  ...CQ_1 // 重庆市 一线区
]
const cityList_2 = [
  '合肥市',
  '福州市',
  '泉州市',
  '厦门市',
  '佛山市',
  '惠州市',
  '江门市',
  '中山市',
  '南宁市',
  '石家庄市',
  '洛阳市',
  '无锡市',
  '南昌市',
  '青岛市',
  '潍坊市',
  '烟台市',
  '太原市',
  '天津市',
  '金华市',
  '宁波市',
  '温州市'
]
const cityList_3 = [
  '安庆市',
  '蚌埠市',
  '阜阳市',
  '六安市',
  '马鞍山市',
  '芜湖市',
  '龙岩市',
  '南平市',
  '宁德市',
  '东莞市',
  '揭阳市',
  '茂名市',
  '汕头市',
  '肇庆市',
  '桂林市',
  '柳州市',
  '兰州市',
  '遵义市',
  '保定市',
  '沧州市',
  '廊坊市',
  '秦皇岛市',
  '唐山市',
  '邢台市',
  '开封市',
  '南阳市',
  '平顶山市',
  '信阳市',
  '新乡市',
  '许昌市',
  '周口市',
  '黄冈市',
  '荆州市',
  '襄阳市',
  '宜昌市',
  '常德市',
  '衡阳市',
  '湘潭市',
  '岳阳市',
  '张家界市',
  '株洲市',
  '长春市',
  '常州市',
  '淮安市',
  '连云港市',
  '南通市',
  '宿迁市',
  '泰州市',
  '徐州市',
  '扬州市',
  '盐城市',
  '镇江市',
  '赣州市',
  '九江市',
  '景德镇市',
  '萍乡市',
  '宜春市',
  '大连市',
  '沈阳市',
  '包头市',
  '赤峰市',
  '呼和浩特市',
  '银川市',
  '德阳市',
  '广安市',
  '乐山市',
  '绵阳市',
  '南充市',
  '宜宾市',
  '滨州市',
  '东营市',
  '德州市',
  '菏泽市',
  '济宁市',
  '临沂市',
  '聊城市',
  '日照市',
  '泰安市',
  '威海市',
  '枣庄市',
  '淄博市',
  '大同市',
  '临汾市',
  '运城市',
  '咸阳市',
  '乌鲁木齐市',
  '大理白族自治州',
  '玉溪市',
  '湖州市',
  '嘉兴市',
  '丽水市',
  '衢州市',
  '绍兴市',
  '台州市',
  '舟山市',
  ...CQ_3 // 重庆市 三线区
]

const cityListEnum = {
  1: [...cityList_1_super, ...cityList_1],
  2: cityList_2,
  3: cityList_3,
  4: []
}

onMounted(() => {
  const data = JSON.parse(localStorage.getItem('cityMapList') || '[]')
  const item = data.find((v) => v.name === '重庆市')
  if (item) {
    const list = [...CQ_1, ...CQ_3]
    const citys = list.map((v) => {
      return {
        name: v,
        provinceId: item.id
      }
    })
    item.citys = citys
  }
  const list_ = [
    ...cityList_1_super,
    ...cityList_1,
    ...cityList_2,
    ...cityList_3
  ]
  data.forEach((v) => {
    if (v.citys && v.citys.length) {
      v.citys.forEach((_) => {
        if (!list_.includes(_.name)) {
          cityListEnum[4].push(_.name)
        }
      })
    }
  })
  treeData.value = data
})

const init = ({ province, city }) => {
  dialogVisible.value = true
  nextTick(() => {
    let cityList = city || []
    if (province && province.length) {
      province.forEach((v) => {
        const item = treeData.value.find((_) => _.name === v)
        if (item && item.citys && item.citys.length) {
          const list = item.citys.map((c) => c.name)
          cityList = [...cityList, ...list]
        }
      })
    }
    elTreeRef.value.setCheckedKeys(cityList)
    checkData()
  })
}

const remoteMethodCity = (query) => {
  if (query !== '') {
    selectCityList.value = cityList.value.filter((item) => {
      return item.cityName.toLowerCase().indexOf(query.toLowerCase()) > -1
    })
  } else {
    selectCityList.value = []
  }
}

const changeCity = (e) => {
  if (e) {
    if (!selectedCityData.value.includes(e)) {
      elTreeRef.value.setCheckedKeys([...selectedCityData.value, e])
      checkData()
    }
    const item = selectCityList.value.find((v) => v.cityName === e)
    if (item && item.provinceName) {
      Object.keys(elTreeRef.value.store.nodesMap).forEach((k) => {
        elTreeRef.value.store.nodesMap[k].expanded = false
      })
      elTreeRef.value.store.nodesMap[item.provinceName].expanded = true
    }
  }
  selectCity.value = ''
}

const changeCheckedValue = () => {
  loading.value = true
  elTreeRef.value.setCheckedKeys([])
  checkData()
}

const changeCheckedGroupValue = (e) => {
  if (e.length === 4) {
    changeCheckedValue()
  } else {
    loading.value = true
    const arr_1 = [] // 增加
    const arr_2 = [] // 删除
    e.forEach((v) => {
      if (!checkedGroupValue_.value.includes(v)) {
        arr_1.push(v)
      }
    })
    checkedGroupValue_.value.forEach((v) => {
      if (!e.includes(v)) {
        arr_2.push(v)
      }
    })
    let list_1 = JSON.parse(JSON.stringify(selectedCityData.value))
    arr_1.forEach((v) => {
      list_1 = [...list_1, ...cityListEnum[v]]
    })
    let list_2 = []
    arr_2.forEach((v) => {
      list_2 = [...list_2, ...cityListEnum[v]]
    })
    const list = []
    list_1.forEach((v) => {
      if (!list_2.includes(v)) {
        list.push(v)
      }
    })
    elTreeRef.value.setCheckedKeys(list)
    checkData()
  }
}

const checkData = () => {
  const treeList = elTreeRef.value.getCheckedNodes(false, true)
  const list = {}
  treeList.forEach((v) => {
    if (!v.provinceId) {
      if (list[v.id]) {
        list[v.id].name = list[v.id].name || v.name || ''
        list[v.id].num = list[v.id].num || (v.citys && v.citys.length) || 0
      } else {
        list[v.id] = {
          name: v.name || '',
          num: (v.citys && v.citys.length) || 0,
          list: []
        }
      }
    } else {
      if (list[v.provinceId]) {
        list[v.provinceId].list.push(v.name)
      } else {
        list[v.provinceId] = {
          name: '',
          num: 0,
          list: [v.name]
        }
      }
    }
  })
  const list_data = {}
  let listData = []
  Object.values(list).forEach((v) => {
    list_data[v.name] = v.list.length < v.num ? v.list : []
    listData = [...listData, ...v.list]
  })
  selectedData.value = list_data
  selectedCityData.value = listData
  checkedValue.value = !listData.length
  setCheckedGroupValue(listData)
}

const setCheckedGroupValue = (list) => {
  checkedGroupValue.value = []
  checkedGroupValue_.value = []
  const arr = []
  const flag = {
    1: true,
    2: true,
    3: true,
    4: true
  }
  Object.keys(cityListEnum).forEach((k) => {
    cityListEnum[k].forEach((v) => {
      if (!list.includes(v)) {
        flag[k] = false
      }
    })
  })
  Object.keys(flag).forEach((k) => {
    if (flag[k]) {
      arr.push(Number(k))
    }
  })
  checkedGroupValue.value = arr
  checkedGroupValue_.value = arr
  loading.value = false
}

const handlerClose = () => {
  checkedValue.value = false
  changeCheckedValue()
  dialogVisible.value = false
}

const resetData = () => {
  checkedValue.value = false
  changeCheckedValue()
}

const confirm = () => {
  emits('backData', {
    checkAll: checkedValue.value,
    selectedDataNew: JSON.stringify(selectedData.value)
  })
  handlerClose()
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.select-province {
  .box-top {
    padding-bottom: 15px;
    border-bottom: 1px solid rgb(228, 227, 223);
    .select-city {
      display: flex;
      align-items: center;
    }
    .content-select {
      max-height: 15vh;
      margin-bottom: 10px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 10px;
      }
      &::-webkit-scrollbar-thumb {
        background: #f1f1f1;
        border-radius: 5px;
      }
      .content-item {
        margin-top: 10px;
        .title {
          color: #000000;
        }
        .item {
          margin-right: 10px;
          line-height: 20px;
        }
      }
    }
  }
  .box-bottom {
    .checkbox-list {
      margin-top: 10px;
      padding-left: 24px;
      display: flex;
      align-items: center;
      .checkbox-style {
        margin-right: 30px;
      }
    }
    .tree-box {
      height: 40vh;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 10px;
      }
      &::-webkit-scrollbar-thumb {
        background: #f1f1f1;
        border-radius: 5px;
      }
      :deep(.el-tree) {
        width: 800px;
        display: flex;
        flex-wrap: wrap;
        .el-tree-node {
          width: 200px;
        }
      }
    }
    .fooder-style {
      text-align: center;
      margin-top: 20px;
    }
  }
}
</style>
