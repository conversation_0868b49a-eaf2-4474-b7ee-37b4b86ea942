<template>
  <div class="select-search-station">
    <el-form ref="form" :model="detailsForm" label-width="130px">
      <div class="inarow" v-if="linkTypeShow || linkTypeShowDefault">
        <el-form-item label="类型" required>
          <el-select
            v-model="detailsForm.linkType"
            clearable
            placeholder="请选择类型"
            @change="changeLinkType"
          >
            <el-option
              v-for="(value, index) in linkTypeEnum"
              :key="index"
              :label="value.name"
              :value="value.linkType"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="pageSelect.length"
          label="选择页面"
          required
          label-width="110px"
        >
          <el-select
            v-model="detailsForm.pageSelect"
            clearable
            placeholder="请选择页面"
            @change="changePageSelect"
          >
            <el-option
              v-for="(value, index) in pageSelect"
              :key="index"
              :label="value.name"
              :value="value.linkType"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="detailsForm.pageSelect === 48"
          label="直播配置"
          label-width="90px"
        >
          <el-select
            @change="setJumpUrl"
            v-model="chatId"
            :fit-input-width="true"
            style="width: 260px"
          >
            <el-option
              v-for="(value, index) in liveList"
              :key="index"
              :label="value.showName"
              :value="value.chatId"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="isLinkUrl && !showAppletLink"
          v-show="
            ![13, 24, 25].includes(detailsForm.pageSelect) &&
            ![199].includes(this.clientPage)
          "
          :label="
            [1, 2, 3, 4, 44].includes(detailsForm.linkType) ||
            detailsForm.pageSelect === 48
              ? '填写链接'
              : '填写ID'
          "
          label-width="90px"
        >
          <el-input
            v-model="detailsForm.linkUrl"
            clearable
            style="width: 350px"
          />
        </el-form-item>
        <template v-if="showAppletLink">
          <el-form-item label="原始ID">
            <el-input
              v-model="detailsForm.linkUrl"
              clearable
              style="width: 350px"
            />
          </el-form-item>
          <el-form-item label="页面路径">
            <el-input
              v-model="detailsForm.linkUrlExt"
              clearable
              style="width: 350px"
            />
          </el-form-item>
        </template>
      </div>
      <div class="inarow"><slot name="center"></slot></div>
      <el-form-item
        v-if="detailsForm.linkType === 44"
        v-show="
          ![24, 25].includes(detailsForm.pageSelect) &&
          ![199].includes(this.clientPage)
        "
        label="外挂JS"
      >
        <el-input
          v-model.trim="detailsForm.linkUrlExt"
          placeholder="填写文档链接"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item v-if="linkUrlButtonShow" label="配置ID">
        <el-button type="primary" link @click="$refs.SelectEassay.clearData()"
          >选择ID</el-button
        >
      </el-form-item>
      <el-form-item v-if="linkUrlButtonShow" label="配置内容">
        <ShowEassayList ref="ShowEassayList" />
      </el-form-item>
      <div v-if="isLinkUrl && detailsForm.pageSelect === 24" class="inarow">
        <el-form-item label="车型" required>
          <el-input
            v-model="linkCarShow"
            @click="selectVehicle"
            clearable
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item label="选择经销商">
          <el-select
            v-model="linkUrlExtShow"
            :disabled="!linkCarShow"
            filterable
            remote
            :remote-method="selectShopInList"
            placeholder="请选择"
            style="margin-right: 30px"
            @change="userSelectedLabel"
          >
            <el-option
              v-for="(value, index) in shopList"
              :key="index"
              :label="value.shopName"
              :value="value.shopId"
            />
          </el-select>
          <br />
          <el-tooltip
            v-for="(item, index) in linkUrlExtList"
            :key="index"
            :content="item.shopName"
            :disabled="true"
            effect="dark"
            placement="top-start"
          >
            <el-tag
              closable
              @close="deleteLable(index)"
              style="margin-right: 5px"
              >{{ $filters.subString(item.shopName, 20) }}</el-tag
            >
          </el-tooltip>
        </el-form-item>
      </div>
      <div v-if="isLinkUrl && detailsForm.pageSelect === 25" class="inarow">
        <el-form-item label="专题名称">
          <el-select
            v-model="linkUrlExtShow"
            filterable
            remote
            :remote-method="selectActList"
            placeholder="请选择"
            style="margin-right: 30px"
            @change="userAcitveLabel"
          >
            <el-option
              v-for="(value, index) in shopList"
              :key="index"
              :label="value.activityName"
              :value="value.activityId"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <ChooseDialogShop ref="ChooseDialogShop" @sendData="sendShopData" />
    <ChooseDialogCar
      ref="ChooseDialogCar"
      :isAlone="true"
      @sendCarData="sendCarData"
    />
    <SelectEassay ref="SelectEassay" @setBackData="setLinkData" />
  </div>
</template>

<script>
import { $emit } from '../../../../../utils/gogocodeTransfer'
import { getLinkTypeList, getQueryPriceShopList } from '@/api/advertModule'
import { activityGetListFactoryActs } from '@/api/activeConfiguration'
import ChooseDialogShop from '@/components/Dialog/ChooseDialogShop.vue'
import ShowEassayList from './ShowEassayList.vue'
import SelectEassay from './SelectEassay.vue'
import ChooseDialogCar from '@/components/Dialog/ChooseDialogCar.vue'
import { match } from '@haluo/util'
import { getLiveCountryList } from '@/api/user'
export default {
  components: {
    ChooseDialogShop,
    ShowEassayList,
    SelectEassay,
    ChooseDialogCar
  },
  props: {
    linkTypeShowDefault: {
      type: Boolean,
      default: false
    }
  },
  name: 'SelectType',
  data() {
    return {
      linkTypeEnum: [],
      clientPage: '',
      typeId: '',
      goodId: '',
      chatId: '', // 直播id
      linkCarShow: '', // 展示选中的车型
      adProjectType: '', // 广告方类型
      linkUrlExtShow: '', // 展示显示的经销商
      shopList: [], // 获取的经销商列表
      linkUrlExtList: [], // 经销商列表
      liveList: [], // 正在直播的直播信息列表
      // detailsForm: {}
    }
  },
  computed: {
    detailsForm() {
      return this.$store.state.adConfig.adDetailData || {}
    },
    /**
     * 广告类型
     * 1-启动屏(开屏) 2-信息流 3-焦点图 4-弹窗 5-热搜标题 6-经销商 7-品牌 8-车辆
     * 9-文章原样式 10-运营位新 11-金刚位 12-小视频 13-贴片视频 14-激励视频 15-驾校 16-悬浮图
     *
     * 广告clientPage
     * 30-摩友圈-首页-摩友圈tab 31-摩友圈-摩友圈详情-信息流 41-选车-新能源
     * 40-选车-选车首页 55-选车-车型详情页-底层banner 77-我的-能量兑换
     */
    isLinkUrl() {
      const linkType = this.detailsForm.linkType
      const pageSelect = this.detailsForm.pageSelect
      let isLink = ''
      this.linkTypeEnum.map((_) => {
        if (_.linkType === linkType) {
          isLink = _.isLink
        }
      })
      if (pageSelect) {
        this.pageSelect.map((_) => {
          if (_.linkType === pageSelect) {
            isLink = _.isLink
          }
        })
      }
      return isLink
    },
    // 控制显示-选择页面
    pageSelect() {
      const linkType = this.detailsForm.linkType
      let child = []
      this.linkTypeEnum.map((_) => {
        if (_.linkType === linkType) {
          child = _.child || []
        }
      })
      return child
    },
    // 控制显示-填写链接/ID
    // linkUrlShow() {
    //   const idArr = []
    //   const falg = Boolean(idArr.includes(this.typeId))
    //   return falg
    // },
    showAppletLink() {
      return [51, 52].includes(this.detailsForm.linkType)
    },
    // 控制显示-选择id按钮
    linkUrlButtonShow() {
      const idArr = [9]
      const noShow = [20]
      const me = this
      const falg =
        Boolean(
          idArr.includes(me.typeId) || me.detailsForm.pageSelect === 13
        ) && !noShow.includes(me.typeId)
      if (me.detailsForm.linkUrl) {
        // eslint-disable-next-line vue/no-async-in-computed-properties
        setTimeout(() => {
          me.$refs.ShowEassayList &&
            me.$refs.ShowEassayList.getList(me.detailsForm.linkUrl)
        }, 10)
      }
      return falg
    },
    // 控制显示-类型
    linkTypeShow() {
      const idArr = [6, 7, 8, 9, 15, 17, 23] // 23 车型小组件
      const clientPages = [199, 210] // 199 搜索·搜索结果页-综合·搜索站 210 搜索-默认搜索页-轮播词
      const falg = Boolean(
        this.adProjectType !== 4 &&
          !idArr.includes(this.typeId) &&
          !clientPages.includes(this.clientPage)
      )
      return falg
    }
  },
  watch: {
    // 车型数据空时
    linkCarShow(val) {
      if (!val) {
        this.detailsForm.linkUrl = ''
        this.detailsForm.linkUrlName = ''
        this.linkUrlExtShow = '' // 展示显示的经销商
        this.detailsForm.linkUrlExt = '' // 展示的经销商数据
        this.linkUrlExtList = []
      }
    }
  },
  mounted() {
    // this.detailsForm = this.$store.state.adConfig.adDetailData || {}
    this.clientPage = this.detailsForm.clientPage || ''
    this.typeId = this.detailsForm.adTypeId || ''
    this.chatId = ''
    this.adProjectType =
      this.detailsForm.orgType || this.detailsForm.adProjectType
    if (
      this.detailsForm.linkType === 24 ||
      this.detailsForm.pageSelect === 24
    ) {
      // 询价页面单独设置
      this.linkCarShow = this.detailsForm.linkUrlName || ''
      this.linkUrlExtList = this.detailsForm.linkUrlExt
        ? JSON.parse(this.detailsForm.linkUrlExt)
        : []
    } else {
      this.linkUrlExtShow = this.detailsForm.linkUrlName || ''
    }
    this.getLinkTypeList()
    this.getLiveCountryList()
  },
  methods: {
    // 获取直播信息
    getLiveCountryList() {
      getLiveCountryList({
        page: 1,
        limit: 100
      }).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data || {}
          this.liveList = (data.listData || []).map((item) => {
            item.showName = `${item.title} ${item.mobile}`
            return item
          })
          if (this.detailsForm.pageSelect === 48 && this.liveList.length) {
            const chatId = this.detailsForm.linkUrl.split('liveId=')[1]
            const findIndex = this.liveList.findIndex((item) => {
              return item.chatId === chatId
            })
            this.chatId = findIndex !== -1 ? chatId : ''
          }
        }
      })
    },
    init(good) {
      this.goodId = good
    },
    // 获取广告跳转类型列表
    getLinkTypeList() {
      getLinkTypeList({
        typeId: this.detailsForm.adTypeId || ''
      }).then((res) => {
        if (res.data.code === 0) {
          this.linkTypeEnum = res.data.data || []
          if (this.detailsForm.linkType) {
            this.setPageSelect(this.linkTypeEnum)
          }
        }
      })
    },
    setPageSelect(data) {
      if (data) {
        const linkTypeArr = data.map((_) => {
          return _.linkType
        })
        if (!linkTypeArr.includes(this.detailsForm.linkType)) {
          this.detailsForm.pageSelect = this.detailsForm.linkType
          this.detailsForm.linkType = 0
        }
      }
    },
    changeLinkType() {
      this.detailsForm.pageSelect = ''
      this.detailsForm.linkUrlExt = ''
      this.changePageSelect()
    },
    changePageSelect() {
      this.detailsForm.linkUrl = ''
      if (this.detailsForm.pageSelect === 14) {
        this.detailsForm.linkUrl = this.detailsForm.linkUrl || this.goodId || ''
      }
    },
    // 设置直播url
    setJumpUrl() {
      const me = this
      me.liveList.length > 0 &&
        me.liveList.map((item) => {
          if (item.chatId === me.chatId) {
            me.detailsForm.linkUrl = item.liveAddress
          }
        })
    },
    // 设置返回的link
    setLinkData(data) {
      this.detailsForm.linkUrl = data.id
      this.$refs.ShowEassayList.setListData([data])
      $emit(this, 'updateAdName', data.title)
    },

    // 选择经销商
    chooseShop() {
      this.$refs.ChooseDialogShop.init({
        title: '选择经销商',
        labels: [],
        getListUrl: '',
        selectLimit: 1,
        canChoose: true,
        needSelfSearchShop: true
      })
    },
    sendShopData(result) {
      const target = result && result[0]
      this.detailsForm.linkUrl = target.shopId || ''
      this.detailsForm.linkUrlName = target.shopName || ''
    },
    // 选择车型
    selectVehicle() {
      this.$refs.ChooseDialogCar &&
        this.$refs.ChooseDialogCar.init({
          selectLimit: 99
        })
    },
    // 获取返回的车型信息
    sendCarData(data) {
      console.log(`data`, data)
      this.linkCarShow = `${data[0].brandName}${data[0].goodName}`
      this.detailsForm.linkUrlName = `${data[0].goodName}`
      this.detailsForm.linkUrl = data[0].goodId
    },
    // 获取经销商列表
    selectShopInList(campaignName) {
      const me = this
      getQueryPriceShopList({
        goodsId: me.detailsForm.linkUrl || '',
        shopName: campaignName || '',
        cityName:
          me.linkUrlExtList && me.linkUrlExtList.length
            ? me.linkUrlExtList[0].cityName
            : '',
        page: 1,
        limit: 20
      }).then((res) => {
        if (res.data.code === 0) {
          console.log(`campaignManageList`, res)
          me.shopList = res.data.data || []
        }
      })
    },
    // 选中的数据
    userSelectedLabel(id) {
      const me = this
      const findIndex = me.linkUrlExtList.findIndex((item) => {
        return item.shopId === id
      })
      me.linkUrlExtShow = ''
      if (findIndex !== -1) return me.$message.error('已有该数据')
      if (me.linkUrlExtList.length > 4) return me.$message.error('最多5条数据')
      const findName = me.shopList.find((item) => {
        return item.shopId === id
      })
      me.shopList = []
      me.linkUrlExtList.push(findName)
      me.setlinkUrlExt()
    },
    deleteLable(index) {
      this.linkUrlExtList.splice(index, 1)
      this.setlinkUrlExt()
    },
    // 获取厂家活动列表
    selectActList(activityName) {
      activityGetListFactoryActs({
        activityName: activityName,
        status: '2',
        page: 1,
        limit: 10
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.shopList = response.data.data.list
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    userAcitveLabel(id) {
      console.log(id)
      const findData = this.shopList.find((item) => {
        return item.activityId === id
      })
      this.detailsForm.linkUrl = `https://wap.58moto.com/zt/2021/5/7/big-clientele?activityId=${id}&share=true`
      this.detailsForm.linkUrlName = findData.activityName
    },
    setlinkUrlExt() {
      this.detailsForm.linkUrlExt = JSON.stringify(this.linkUrlExtList || [])
    },
    // 必选
    requiredItem() {
      if (this.detailsForm.linkType === '') {
        return this.$message.error('请选择类型')
      }
      if (this.showAppletLink) {
        if (!this.detailsForm.linkUrl) {
          return this.$message.error('请输入原始ID')
        }
        if (!this.detailsForm.linkUrlExt) {
          return this.$message.error('请输入页面路径')
        }
      }
      if (this.detailsForm.linkType === 44 && this.detailsForm.linkUrlExt) {
        if (!match.checkType(this.detailsForm.linkUrlExt, 'url')) {
          return this.$message.error('请输入正确的外挂JS链接')
        }
      }
      if (!this.detailsForm.pageSelect && this.pageSelect.length) {
        return this.$message.error('请选择页面')
      }
      if (this.detailsForm.pageSelect === 24 && !this.linkCarShow) {
        return this.$message.error('请选择车型')
      }
      $emit(this, 'requiredItemType')
    }
  },
  emits: ['requiredItemType']
}
</script>
