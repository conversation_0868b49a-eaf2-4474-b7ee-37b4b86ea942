<script>
import { ElCascader } from 'element-plus'
export default {
  extends: ElCascader,
  render(h) {
    return h(ElCascader)
  },
  methods: {
    deleteTag(tag) {
      const { checkedValue, checkedNodes } = this
      if (tag.text === 'Android / 全部') return this.$emit('clearAndroid')
      const findData = checkedNodes.find((item) => {
        const pathLabelsText =
          item.pathLabels.length === 2
            ? item.pathLabels.join(' / ')
            : item.pathLabels.join('')
        if (pathLabelsText === tag.text) return item
      })
      this.checkedValue = checkedValue.filter((n) => {
        if (n.length !== findData.path.length) return n
        if (
          (n[0] === findData.path[0] && n[1] !== findData.path[1]) ||
          n[0] !== findData.path[0]
        )
          return n
      })
    }
  }
}
</script>
