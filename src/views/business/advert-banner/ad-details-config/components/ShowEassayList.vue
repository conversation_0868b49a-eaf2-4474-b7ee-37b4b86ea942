<template>
  <div
    v-loading="loading"
    v-show="listData && listData.length"
    class="eassay-list"
    :style="{ height: showAllWidth ? '60vh' : 'auto', width: '100%' }"
  >
    <el-table
      ref="multipleTable"
      :data="listData"
      row-key="multipleTable"
      border
      style="overflow-y: auto"
      :style="{
        width: showAllWidth ? '100%' : '40%',
        height: showSelected ? 550 : 'auto',
        maxHeight: 550
      }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="70" v-if="showSelected" />
      <el-table-column prop="id" label="内容ID" width="80" align="center" />
      <el-table-column
        prop="author"
        label="发布者"
        width="120"
        align="center"
      />
      <el-table-column prop="name" label="标题/内容" align="center">
        <template v-slot="scope">
          <el-tag
            v-if="scope.row.prime && showPrime"
            size="small"
            type="warning"
            effect="dark"
          >
            {{ '优质' }}
          </el-tag>
          <c-feedList :card="scope.row" />
        </template>
      </el-table-column>
      <el-table-column
        prop="author"
        label="异常状态"
        width="120"
        align="center"
      >
        <template #default="scope">
          {{
            scope.row.outDate && showExpire
              ? '标签已过期'
              : scope.row.prime
              ? '无异常'
              : '非优质'
          }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { searchArticleList } from '@/api/articleModule'
import CFeedList from '@/components/CFeedList/index.vue'
export default {
  name: 'ShowEassayList',
  components: {
    CFeedList
  },
  props: {
    showSelected: {
      type: Boolean,
      default: false
    },
    showAllWidth: {
      type: Boolean,
      default: false
    },
    showPrime: {
      type: Boolean,
      default: true
    },
    showExpire: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      listData: [],
      multipleSelection: [], // 展示数据
      loading: false
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    canSelect(row) {
      const index = this.multipleSelection.findIndex(
        (item) => item.id === row.id
      )
      if (this.multipleSelection.length >= 1) {
        if (index !== -1) {
          return true
        } else {
          return false
        }
      } else {
        return true
      }
    },
    init() {
      this.listData = []
      this.multipleSelection = []
    },
    getList(id) {
      const me = this
      me.loading = true
      const postData = {
        limit: 100, // 数量
        page: 1,
        id
      }
      searchArticleList(postData)
        .then((response) => {
          const res = response.data.data.listData
          me.listData = res
        })
        .finally((_) => {
          me.loading = false
        })
    },
    handleSelectionChange(val) {
      if (this.flag) return
      if (val.length >= 2) {
        this.flag = true
        this.$refs['multipleTable'].clearSelection()
        this.$refs['multipleTable'].toggleRowSelection(val[1], true)
        this.flag = false
        this.multipleSelection = [val[1]]
      } else {
        this.multipleSelection = val
      }
    },
    setListData(data) {
      this.listData = data
    }
  }
}
</script>

<style lang="scss">
.eassay-list {
  overflow-y: scroll;
  .el-table__header .el-checkbox {
    display: none;
  }
}
</style>
