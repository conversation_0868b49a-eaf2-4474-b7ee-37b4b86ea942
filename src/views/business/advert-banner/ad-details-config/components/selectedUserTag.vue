/** * 选择用户标签 */
<template>
  <div v-loading="loading" v-show="showTag" style="margin-left: 100px">
    <el-form ref="form" :model="detailsForm" label-width="90px">
      <el-form-item label="可选：">
        <div>
          <div v-for="(item, iIndex) in Object.keys(allData)" :key="iIndex">
            <div>{{ typeEnmu[item] }}</div>
            <el-tag
              v-for="tag in allData[item]"
              :key="tag"
              :type="selectedTagData[item].includes(tag) ? '' : 'info'"
              style="margin-right: 5px"
              @click="selecteTag(tag, item)"
            >
              {{ tag }}
            </el-tag>
          </div>
          <p>
            常看车型
            <el-input
              v-model="linkCarShow"
              placeholder="请选择车型"
              @click="selectVehicle"
              clearable
              style="width: 350px"
            />
          </p>
          <p>
            品牌厂商
            <el-input
              v-model="selectBrandName"
              placeholder="请选择品牌"
              style="width: 350px"
              @click="chooseBrand({ multi: false })"
            />
          </p>
        </div>
      </el-form-item>
      <el-form-item label="已选：">
        <!-- <span v-for="(item, iIndex) in Object.keys(selectedTagData)" :key="iIndex + 10">
          <el-tag
            class="tag-name"
            v-for="tag in selectedTagData[item]"
            :key="tag"
            :type="''"
            closable
            style="margin-right: 5px"
            @close="selecteTag(tag, item)"
          >
            {{ tag }}
          </el-tag>
        </span> -->
        <span v-for="(item, iIndex) in brandData" :key="iIndex + 100">
          <el-tag
            class="tag-name"
            :type="''"
            closable
            style="margin-right: 5px"
            @close="selecteOtherTag(item, 'brandData')"
          >
            {{ item.name }}
          </el-tag>
        </span>
        <span v-for="(item, iIndex) in carData" :key="iIndex + 1000">
          <el-tag
            class="tag-name"
            :type="''"
            closable
            style="margin-right: 5px"
            @close="selecteOtherTag(item, 'carData')"
          >
            {{ item.name }}
          </el-tag>
        </span>
      </el-form-item>
      <el-form-item label="预计人数：">
        <span
          >{{
            memberCnt && memberCntStatus ? memberCnt.toLocaleString() : '-'
          }}人</span
        >
      </el-form-item>
    </el-form>
    <ChooseDialogBrand ref="ChooseDialogBrand" @sendData="getBrandData" />
    <ChooseDialogCar ref="ChooseDialogCar" @sendCarData="sendCarData" />
  </div>
</template>

<script>
import ChooseDialogBrand from '@/components/Dialog/ChooseDialogBrand.vue'
import ChooseDialogCar from '@/components/Dialog/ChooseDialogCar.vue'
import { GetBrandAllList, searchCarList } from '@/api/garage'
export default {
  name: 'selectedUserTag',
  components: {
    ChooseDialogBrand,
    ChooseDialogCar
  },
  props: {
    showTag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      selectBrandName: '',
      linkCarShow: '',
      detailsForm: {},
      allData: {
        type: [
          '不限',
          'MINI',
          '三轮',
          '其他',
          '复古',
          '巡航太子',
          '弯梁',
          '拉力',
          '旅行',
          '越野',
          '街车',
          '跑车',
          '踏板'
        ],
        price: [
          '不限',
          '1万以下',
          '1万~2万',
          '2万~3万',
          '3万~5万',
          '5万~10万',
          '10万以上'
        ],
        volume: [
          '不限',
          '150cc以下',
          '151~250cc',
          '251~400cc',
          '401~600cc',
          '601~1000cc',
          '1000cc以上'
        ],
        source: ['不限', '国产', '合资', '进口'],
        energy: ['不限', '汽油', '纯电动', '柴油', '混动']
      },
      typeEnmu: {
        type: '车型类别',
        price: '价格区间',
        volume: '排量区间',
        source: '生产厂商',
        energy: '能源类型'
      },
      brandData: [],
      carData: [],
      selectedTagData: {
        type: ['不限'],
        price: ['不限'],
        volume: ['不限'],
        source: ['不限'],
        energy: ['不限']
      },
      memberCnt: 0,
      memberCntStatus: true
    }
  },
  methods: {
    init(data) {
      const me = this
      me.brandData = []
      me.carData = []
      Object.keys(data).map((item) => {
        if (['status', 'unitId'].includes(item)) return
        if (item === 'brand') {
          me.getGetBrandAllList(data[item].split(','))
        } else if (item === 'car') {
          me.getGetCarList(data[item].split(','))
        } else if (item === 'memberCnt') {
          me.memberCnt = data[item]
        } else {
          me.selectedTagData[item] =
            data[item] === '' ? ['不限'] : data[item].split(',')
        }
      })
    },
    // 获取所有品牌
    getGetBrandAllList(names) {
      const me = this
      if (names[0] === '') return
      GetBrandAllList().then((res) => {
        if (res.data.code === 0) {
          const data = res.data.data
          data.map((item) => {
            if (names.includes(item.brandName)) {
              me.brandData.push({
                id: item.brandId,
                name: item.brandName
              })
            }
          })
        }
      })
    },
    // 拿到所有车型轮训掉用
    getGetCarList(ids) {
      ids.map((id) => {
        this.getList(id)
      })
    },
    // 获取车型
    getList(goodsId) {
      const me = this
      if (goodsId === '') return
      searchCarList({
        goodsId: goodsId,
        isOnStatus: 1,
        page: 1,
        limit: 20
      })
        .then((response) => {
          if (response.data.code === 0) {
            const list = response.data.data.list || []
            if (list && list.length) {
              me.carData.push({
                id: list[0].goodId,
                name: list[0].goodName
              })
            }
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch(() => {})
        .finally(() => {})
    },
    // 选择品牌
    chooseBrand(data = { multi: false }) {
      this.$refs.ChooseDialogBrand &&
        this.$refs.ChooseDialogBrand.init({
          title: '选择品牌',
          getListUrl: GetBrandAllList,
          multiple: data.multi,
          canChoose: true
        })
    },
    // 获取返回的品牌信息
    getBrandData(data) {
      const me = this
      data.labels.map((brand) => {
        const findIndex = me.brandData.findIndex((item) => {
          return brand.brandId === item.id
        })
        findIndex !== -1
          ? null
          : me.brandData.push({
              id: brand.brandId,
              name: brand.brandName
            })
      })
      me.confirm()
    },
    // 选择车型
    selectVehicle() {
      this.$refs.ChooseDialogCar &&
        this.$refs.ChooseDialogCar.init({
          selectLimit: 99
        })
    },
    // 获取返回的车型信息
    sendCarData(data) {
      const me = this
      data.map((car) => {
        const findIndex = me.carData.findIndex((item) => {
          return car.goodId === item.id
        })
        findIndex !== -1
          ? null
          : me.carData.push({
              id: car.goodId,
              name: car.goodName
            })
      })
      me.confirm()
    },
    // 设置/删除 tag
    selecteTag(tag, tagType) {
      const findIndex = this.selectedTagData[tagType].findIndex((item) => {
        return item === tag
      })
      const isUnlimited = this.selectedTagData[tagType].includes('不限')
      if (tag !== '不限' && findIndex !== -1) {
        this.selectedTagData[tagType].splice(findIndex, 1)
        this.selectedTagData[tagType] = this.selectedTagData[tagType].length
          ? this.selectedTagData[tagType]
          : ['不限']
      } else if (findIndex !== -1 || (isUnlimited && tag === '不限')) {
        // 当是包含不限，tag 也是不限，不动
      } else if (isUnlimited || tag === '不限') {
        this.selectedTagData[tagType] =
          isUnlimited && tag !== '不限' ? [tag] : ['不限']
      } else {
        this.selectedTagData[tagType].push(tag)
      }
      this.selectedTagData.tagType = this.selectedTagData[tagType]
      this.confirm()
    },
    // 清除其他tag
    selecteOtherTag(tag, allTag) {
      const findIndex = this[allTag].findIndex((item) => {
        return tag.id === item.id
      })
      findIndex === -1 ? null : this[allTag].splice(findIndex, 1)
      this.confirm()
    },
    // 确认
    confirm() {
      const me = this
      let backData = {}
      Object.keys(me.selectedTagData).map((item) => {
        backData[item] = me.selectedTagData[item].length
          ? me.selectedTagData[item].join(',')
          : ''
        backData[item] = backData[item] === '不限' ? '' : backData[item]
      })
      backData.car = me.carData.length
        ? me.carData
            .map((item) => {
              return item.id
            })
            .join(',')
        : ''
      backData.brand = me.brandData.length
        ? me.brandData
            .map((item) => {
              return item.name
            })
            .join(',')
        : ''
      me.memberCntStatus = false
      me.$emit('backData', JSON.stringify(backData))
    }
  }
}
</script>

<style lang="scss" scoped></style>
