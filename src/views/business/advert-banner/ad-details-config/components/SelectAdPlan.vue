<template>
  <div class="select-ad-placement">
    <div class="title">
      <span>配置广告方及计划</span>
    </div>
    <el-form ref="form" :model="adForm" label-width="130px">
      <el-form-item label="广告方类型" required>
        <el-select
          v-model="adProjectType"
          placeholder="请选择"
          @change="getAdvertiserList"
        >
          <el-option
            v-for="(value, index) in advertisingType"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="adProjectType"
        label="选择广告方"
        required
        label-width="120px"
      >
        <el-select
          v-model="adForm.orgId"
          filterable
          remote
          :remote-method="selectAdvertiserList"
          placeholder="请选择"
          @change="campaignManageList"
        >
          <el-option
            v-for="(value, index) in adProjectList"
            :key="index"
            :label="value.advertiserName"
            :value="value.id"
          />
        </el-select>
        <el-form-item
          v-if="adProjectType === 2 && isCreate"
          label="关联广告服务订单"
          required
          label-width="140px"
          class="ml50"
        >
          <el-select
            v-model="adForm.orderNum"
            filterable
            placeholder="请选择"
            @change="changeOrderNum"
          >
            <el-option
              v-for="(value, index) in orderList"
              :key="index"
              :label="value.title"
              :value="value.orderNumber"
            />
          </el-select>
        </el-form-item>
      </el-form-item>

      <el-form-item label-width="10px" v-if="orgId">
        <el-button @click="selectedPlan">选择计划</el-button>
        <el-button type="primary" plain @click="createNewPlan"
          >创建新计划</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      ref="multipleTable"
      :data="listData"
      row-key="multipleTable"
      border
      style="width: 100%; overflow-y: auto"
    >
      <el-table-column prop="campaignName" label="计划名称" align="center" />
      <el-table-column prop="campaignType" label="计划类型" align="center">
        <template v-slot="scope">
          <span>{{ campaignType[scope.row.campaignType] || '' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="controlHoursPredict"
        label="总天数"
        align="center"
      />
      <!-- <el-table-column
        prop="servingLeftHours"
        label="可用天数"
        align="center"
      /> -->
      <el-table-column prop="servingHours" label="已用天数" align="center" />
      <el-table-column
        prop="controlNumberPredict"
        label="总量"
        align="center"
      />
      <el-table-column prop="servingNum" label="已投放量" align="center" />
      <el-table-column prop="servingLeftNum" label="可用余额" align="center" />
      <el-table-column prop="exposureNum" label="实际曝光" align="center" />
      <el-table-column prop="exposureLeftNum" label="实际余量" align="center" />
      <el-table-column prop="remark" label="备注" align="center" />
    </el-table>
    <p>
      <el-button v-if="orgId" type="primary" @click="nextStep"
        >下一步</el-button
      >
    </p>
    <editPlan ref="editPlan" />
    <selectPlanList ref="selectPlanList" @showList="setShowList" />
  </div>
</template>

<script>
import { filterDay } from '@/utils'
import { $emit } from '../../../../../utils/gogocodeTransfer'
import { advertisingType, campaignType } from '@/utils/enum/adConfigEnum.js'
import editPlan from '../../advertising-plan-list/commponents/dialogEditPlan.vue'
import selectPlanList from './selectPlanList.vue'
import {
  getAdvertiserList,
  campaignManageList,
  advertOrderPickOrder
} from '@/api/advertModule'
import { searchMerchantList } from '@/api/garage'

export default {
  name: 'SelectAdPlan',
  components: {
    editPlan,
    selectPlanList
  },
  props: {
    isShopType: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      campaignType,
      advertisingType,
      adProjectType: '', // 广告方类型
      adProjectList: [], // 广告方列表
      listData: [],
      adForm: {},
      orgId: '',
      orderList: [],
      isCreate: false
    }
  },
  watch: {
    listData: {
      deep: true,
      immediate: true,
      handler(newVal) {
        const item = newVal && newVal.length ? newVal[0] : {}
        this.$store.dispatch('changeCampaignType', item.campaignType || '')
      }
    }
  },
  methods: {
    initData(data) {
      const me = this
      me.isCreate = !me.$route.query?.id || me.$route.query?.copy
      me.adForm = {
        campaignId: data.campaignId || '',
        orgId: data.orgId || '',
        orderNum: ''
      }
      me.orgId = data.orgId || ''
      me.adProjectType = data.orgType || ''
      if (!me.orgId) {
        me.listData = []
        return
      }
      sessionStorage.removeItem('adOrderNumData')
      sessionStorage.removeItem('adMerchantStatus')
      const flag = me.adProjectType === 2 && me.isCreate
      const flag1 = me.adProjectType === 2
      me.selectAdvertiserList('', flag, flag1)
      me.selectProject()
      console.log(me.advertisingType, me.orgId, me.adForm, data, '1231231')
    },
    campaignManageList(e) {
      this.adForm.campaignId = ''
      this.adForm.orderNum = ''
      this.orderList = []
      this.orgId = e
      this.listData = []
      $emit(this, 'next', false)
      if (this.adProjectType === 4) {
        this.adProjectList &&
          this.adProjectList.map((_) => {
            if (_.id === e) {
              this.adForm.linkType = _.thirdLinkType || ''
            }
          })
      }
      sessionStorage.removeItem('adMerchantStatus')
      if (this.adProjectType === 2) {
        this.getMerchantStatus()
        this.getOrderList()
      }
    },
    getOrderList() {
      const item = this.adProjectList.find((v) => v.id === this.adForm.orgId)
      if (item) {
        advertOrderPickOrder({
          shopId: item.relationId
        }).then((res) => {
          if (res.data.code === 0) {
            this.orderList = res.data.data || []
          }
        })
      }
    },
    getAdvertiserList(typeId) {
      this.isCreate = !this.$route.query?.id || this.$route.query?.copy
      this.$store.dispatch('changeAdType', typeId)
      this.adForm.orgId = ''
      sessionStorage.removeItem('adMerchantStatus')
      this.orderList = []
      this.adForm.campaignId = ''
      this.adForm.orderNum = ''
      // this.detailsForm.pageSelect = ''
      this.selectAdvertiserList()
      this.listData = []
      $emit(this, 'next', false)
    },
    // 获取广告方
    selectAdvertiserList(advertiserName, flag, init) {
      getAdvertiserList({
        typeId: this.adProjectType,
        advertiserName,
        limit: 9999
      }).then((res) => {
        if (res.data.code === 0) {
          this.adProjectList = res.data.data || []
          if (flag) {
            this.getOrderList()
          }
          if (init) {
            this.getMerchantStatus()
          }
        }
      })
    },
    getMerchantStatus() {
      if (!this.adForm.orgId) return
      const item = this.adProjectList.find((v) => v.id === this.adForm.orgId)
      if (item && item.relationId) {
        searchMerchantList({
          shopId: item.relationId,
          page: 1,
          limit: 20
        }).then((res) => {
          if (res.data.code === 0) {
            const data = res.data.data || {}
            const list = data.list || []
            const item = list[0] || {}
            if (item && item.shopStatus !== 1) {
              this.$message.error('该店铺状态异常')
              sessionStorage.setItem('adMerchantStatus', true)
            }
          }
        })
      }
    },
    // 广告方列表
    selectedPlan() {
      this.$refs.selectPlanList.init(
        this.adProjectType || '',
        this.adForm.orgId || ''
      )
    },
    // 获取计划
    selectProject() {
      const me = this
      campaignManageList({
        typeId: this.adProjectType || '',
        advertiserId: this.adForm.orgId || '',
        isValid: 1,
        campaignType: '',
        campaignName: '',
        limit: 999,
        filterCampaignType: 15 // 过滤15
      }).then((res) => {
        if (res.data.code === 0) {
          const listData = (res.data.data && res.data.data.list) || []
          listData.map((item) => {
            item.controlHoursPredict = item.controlHoursPredict
              ? filterDay(item.controlHoursPredict)
              : '/'
            item.servingHours = item.servingHours
              ? filterDay(item.servingHours)
              : '/'
            item.servingLeftHours = item.servingLeftHours
              ? filterDay(item.servingLeftHours)
              : '/'
            if (item.campaignId === me.adForm.campaignId) {
              me.listData = [item]
            }
          })
        }
      })
    },
    setShowList(list) {
      this.listData = [list]
      this.adForm.campaignId = list.campaignId
      if (this.adProjectType === 2) {
        if (this.adForm.orderNum) {
          const flag = !!this.$route.query?.copy
          this.updateAdDetail(flag)
        }
      } else {
        this.updateAdDetail()
      }
    },
    changeOrderNum() {
      if (this.adForm.orderNum) {
        const item = this.orderList.find(
          (v) => v.orderNumber === this.adForm.orderNum
        )
        if (item) {
          sessionStorage.setItem('adOrderNumData', JSON.stringify(item))
        }
        if (
          (this.adForm.campaignId && this.adProjectList.length) ||
          !this.adProjectList.length
        ) {
          const flag = !!this.$route.query?.copy
          this.updateAdDetail(flag)
        }
      }
    },
    // 创建广告方
    createNewPlan() {
      this.$refs.editPlan.openDialog({
        typeId: this.adProjectType || '',
        advertiserId: this.adForm.orgId || ''
      })
    },
    // 下一步
    nextStep() {
      const flag = sessionStorage.getItem('adMerchantStatus')
      if (flag) {
        return this.$message.error('该店铺状态异常')
      }
      if (!this.adForm.orderNum && this.adProjectType === 2) {
        return this.$message.error('请选择关联广告服务订单')
      }
      if (!this.adForm.campaignId && this.adProjectList.length) {
        return this.$message.error('请选择计划')
      }
      this.updateAdDetail()
    },
    updateAdDetail(flag) {
      if (this.listData[0].servingLeftNum === 0) {
        this.$message.error('计划的可用余量需要大于0')
      }
      const flag_ = sessionStorage.getItem('adMerchantStatus')
      if (flag_) return
      let data = {
        ...this.adForm,
        adProjectType: this.adProjectType,
        orgType: this.adProjectType || '',
        campaignType:
          this.adProjectType === 1 && this.listData.length
            ? this.listData[0].campaignType
            : ''
      }
      if (!flag) {
        data = {
          ...data,
          siteSetFirstId: '',
          siteSetSecondId: '',
          siteSetThirdId: '',
          adTypeId: ''
        }
      }
      this.$store.dispatch('changeAdDetail', data)
      $emit(this, 'next', true)
    }
  },
  emits: ['next']
}
</script>

<style lang="scss">
.select-ad-placement {
  position: relative;
  .example {
    position: absolute;
    z-index: 100;
    width: 18%;
    right: 50px;
    top: 30vh;

    img {
      width: 100%;
    }
  }
}
</style>
