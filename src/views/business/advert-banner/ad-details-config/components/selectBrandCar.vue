<template>
  <div class="brand-car-box">
    <el-popover
      popper-class="popper-class-no-padding"
      trigger="click"
      placement="bottom-start"
      :width="props.popoverWidth"
      :hide-after="0"
    >
      <div class="box-list">
        <div class="vehicle-type-search">
          <el-select
            v-model="goodsSearch"
            filterable
            remote
            reserve-keyword
            placeholder="车型搜索"
            :remote-method="remoteMethod"
            :loading="searchLoading"
            :teleported="false"
            style="width: 100%"
            @change="changeGoodsSearch"
          >
            <el-option
              v-for="item in goodsList"
              :key="item.goodId"
              :label="`${item.brandName || ''} ${item.goodName || ''}`"
              :value="item.goodId"
            />
          </el-select>
        </div>
        <div class="box-item">
          <el-input v-model="search_1" placeholder="请输入品牌" clearable />
          <div class="scroll-box">
            <div
              v-for="item in showListData_1"
              :key="item.value_"
              :class="[
                'el-select-dropdown__item',
                'item',
                { selected: selectValue_1 === item.value_ }
              ]"
              @click="selectFun_1(item)"
            >
              <el-checkbox
                v-model="item.checked_"
                :indeterminate="item.indeterminate_"
                @click.stop
                @change="checkedChange1(item)"
              />
              {{ item.label_ }}
            </div>
          </div>
        </div>
        <div class="box-item item-right">
          <el-input v-model="search_2" placeholder="请输入车型" clearable />
          <div class="switch-box">
            <el-switch
              v-model="saleStatus"
              active-text="仅看在售"
              inactive-text="查看全部"
              @change="getList_2"
            />
          </div>
          <div class="scroll-box is-switch">
            <div
              v-show="!search_2 && showListData_2.length"
              class="el-select-dropdown__item item"
              @click="selectAllFun_2"
            >
              <el-checkbox :model-value="selectAllFlag_2" />
              全部
              <div class="shade"></div>
            </div>
            <div
              v-for="item in showListData_2"
              :key="item.value_"
              class="el-select-dropdown__item item"
              @click="selectFun_2(item)"
            >
              <el-checkbox :model-value="item.checked_" />
              {{ item.label_ }}
              <div class="shade"></div>
            </div>
            <div v-if="loading" class="loading-box">
              <el-icon class="is-loading">
                <IconLoading />
              </el-icon>
              <span>加载中...</span>
            </div>
          </div>
        </div>
      </div>
      <template #reference>
        <div
          class="reference-box"
          :style="{ width: props.width, minWidth: props.width }"
        >
          {{ showTitle }}
        </div>
      </template>
    </el-popover>
    <div class="select-brand-car">
      <div style="color: #bbbbbb">可单独选择多个品牌或车型</div>
      <div v-if="showSelect_1.length || showSelect_2.length">已选:</div>
      <div v-for="(item, key) in showCheckedList" :key="key">
        <template v-if="item.list_ && item.list_.length">
          <el-tag
            v-for="(car, index) in item.list_"
            :key="index"
            type="info"
            closable
            class="mr5"
            @close="selectFun_2(car)"
          >
            {{ car.brandName }} - {{ car.label_ }}
          </el-tag>
        </template>
        <el-tag
          v-else
          type="info"
          closable
          class="mr5"
          @close="deleteCheckedValue1(item)"
        >
          {{ item.label_ }} - 全部
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Loading as IconLoading } from '@element-plus/icons-vue'
import { searchBrandList } from '@/api/brand'
import { searchCarList } from '@/api/garage'

const props = defineProps({
  popoverWidth: {
    type: Number,
    default: 400
  },
  width: {
    type: String,
    default: '400px'
  }
})

const emit = defineEmits(['selectData'])

const search_1 = ref('')
const search_2 = ref('')
const select_1 = ref({})
const selectValue_1 = ref('')
const listData_1 = ref([])
const listData_2 = ref([])
const loading = ref(false)
const checkedValue_1 = ref([])
const checkedValue_2 = ref([])
const showSelect_1 = ref([])
const showSelect_2 = ref([])
const saleStatus = ref(false)
const goodsList = ref([])
const searchLoading = ref(false)
const goodsSearch = ref('')

const showListData_1 = computed(() => {
  if (!search_1.value) return listData_1.value
  return listData_1.value.filter(
    (item) =>
      item.label_.toUpperCase().includes(search_1.value.toUpperCase()) ||
      item.label_.toLowerCase().includes(search_1.value.toLowerCase())
  )
})
const showListData_2 = computed(() => {
  if (!search_2.value) return listData_2.value
  return listData_2.value.filter(
    (item) =>
      item.label_.toUpperCase().includes(search_2.value.toUpperCase()) ||
      item.label_.toLowerCase().includes(search_2.value.toLowerCase())
  )
})
const showCheckedList = computed(() => {
  const obj = {}
  showSelect_1.value.forEach((_) => {
    obj[_.value_] = _
  })
  showSelect_2.value.forEach((_) => {
    if (!obj[_.brandId]) {
      obj[_.brandId] = {}
    }
    if (obj[_.brandId].list_) {
      obj[_.brandId].list_.push(_)
    } else {
      obj[_.brandId].list_ = [_]
    }
  })
  return obj
})
const showTitle = computed(() => {
  const list = []
  Object.values(showCheckedList.value).forEach((item) => {
    if (item.list_ && item.list_.length) {
      item.list_.forEach((car) => {
        list.push(`${car.brandName} - ${car.label_}`)
      })
    } else {
      list.push(`${item.label_} - 全部`)
    }
  })
  return list.join('；')
})
const selectAllFlag_2 = computed(() => {
  let flag = true
  listData_2.value.forEach((_) => {
    if (!_.checked_) {
      flag = false
    }
  })
  return flag
})

const init = (arrBrand, arrCar, brandGoodsList) => {
  reset()
  checkedValue_1.value = arrBrand
  checkedValue_2.value = arrCar
  setShowData()
  const brandList = []
  if (brandGoodsList && brandGoodsList.length) {
    brandGoodsList.forEach((item) => {
      brandList.push(item.brandId)
    })
  }
  getList_1(brandList, brandGoodsList)
}

const reset = () => {
  search_1.value = ''
  search_2.value = ''
  select_1.value = {}
  selectValue_1.value = ''
  listData_1.value = []
  listData_2.value = []
  loading.value = false
  checkedValue_1.value = []
  checkedValue_2.value = []
  showSelect_1.value = []
  showSelect_2.value = []
  saleStatus.value = false
  goodsList.value = []
  searchLoading.value = false
  goodsSearch.value = ''
}

const remoteMethod = (query) => {
  if (query) {
    searchLoading.value = true
    searchCarList({
      limit: 9999,
      isOnStatus: 1,
      name: query
    })
      .then((res) => {
        if (res.data.code === 0) {
          const data = res.data.data || {}
          const list = data.list || []
          list.forEach((item) => {
            item.label_ = item.goodName
            item.value_ = item.goodId
            item.checked_ = false
          })
          goodsList.value = list
        }
      })
      .finally(() => {
        searchLoading.value = false
      })
  } else {
    goodsList.value = []
  }
}

const changeGoodsSearch = (e) => {
  if (e) {
    const item = goodsList.value.find((_) => _.goodId === e)
    if (
      !checkedValue_1.value.includes(item.brandId) &&
      !checkedValue_2.value.includes(item.goodId)
    ) {
      item.checked_ = true
      checkedValue_2.value.push(item.goodId)
      showSelect_2.value.push(item)
      setCheckedDataShow2()
      setCheckedData1()
      setShowData()
    }
    const data = listData_1.value.find((_) => _.value_ === item.brandId)
    if (data && data.value_) {
      select_1.value = data || {}
      selectValue_1.value = data.value_ || ''
      search_1.value = data.label_ || ''
      search_2.value = item.goodName || ''
      saleStatus.value = false
      getList_2()
    }
  }
  goodsSearch.value = ''
}

const getList_1 = (brand, arr) => {
  searchBrandList({
    isShow: 1
  }).then((res) => {
    if (res.data.code === 0) {
      const data = res.data.data || {}
      const list = data.list || []
      let firstData = {}
      list.forEach((item) => {
        item.label_ = item.brandName
        item.value_ = item.brandId
        item.checked_ = false
        item.indeterminate_ = false
        if (checkedValue_1.value.includes(item.value_)) {
          if (!firstData.value_) {
            firstData = item
          }
          item.checked_ = true
          showSelect_1.value.push(item)
        }
        if (brand.includes(item.value_)) {
          if (!firstData.value_) {
            firstData = item
          }
          item.indeterminate_ = true
          const brandItem = arr.find((_) => _.brandId === item.value_) || {}
          const goodsList = brandItem.goodsList || []
          goodsList.forEach((car) => {
            showSelect_2.value.push({
              ...car,
              brandName: item.label_,
              label_: car.goodsName,
              value_: car.goodsId,
              checked_: checkedValue_2.value.includes(car.goodsId)
            })
          })
        }
      })
      listData_1.value = list
      if (firstData.value_) {
        selectFun_1(firstData)
      }
    }
  })
}

const getList_2 = () => {
  listData_2.value = []
  loading.value = true
  searchCarList({
    limit: 9999,
    brandId: selectValue_1.value,
    isOnStatus: 1,
    saleStatus: saleStatus.value ? 1 : ''
  })
    .then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data || {}
        const list = data.list || []
        list.forEach((item) => {
          item.label_ = item.goodName
          item.value_ = item.goodId
          item.checked_ = false
          if (checkedValue_2.value.includes(item.value_)) {
            item.checked_ = true
          }
        })
        listData_2.value = list
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const checkedChange1 = (data) => {
  data.indeterminate_ = false
  if (data.checked_) {
    if (!checkedValue_1.value.includes(data.value_)) {
      checkedValue_1.value.push(data.value_)
      showSelect_1.value.push(data)
    }
  } else {
    if (checkedValue_1.value.includes(data.value_)) {
      deleteCheckedData1(data.value_)
    }
  }
  setCheckedData2(data.value_)
  setCheckedDataShow2()
  setShowData()
}

const deleteCheckedValue1 = (data) => {
  data.checked_ = !data.checked_
  checkedChange1(data)
}

const deleteCheckedData1 = (brandId) => {
  const index1 = checkedValue_1.value.findIndex((_) => _ === brandId)
  if (index1 >= 0) {
    checkedValue_1.value.splice(index1, 1)
  }
  const index2 = showSelect_1.value.findIndex((_) => _.value_ === brandId)
  if (index2 >= 0) {
    showSelect_1.value.splice(index2, 1)
  }
}

const deleteCheckedData2 = (carId) => {
  const index1 = checkedValue_2.value.findIndex((_) => _ === carId)
  if (index1 >= 0) {
    checkedValue_2.value.splice(index1, 1)
  }
  const index2 = showSelect_2.value.findIndex((_) => _.value_ === carId)
  if (index2 >= 0) {
    showSelect_2.value.splice(index2, 1)
  }
}

const setShowData = () => {
  emit('selectData', {
    brand: checkedValue_1.value.join(','),
    good: checkedValue_2.value.join(',')
  })
}

const selectFun_1 = (data) => {
  select_1.value = data || {}
  selectValue_1.value = data.value_ || ''
  search_2.value = ''
  getList_2()
}

const selectFun_2 = (data) => {
  data.checked_ = !data.checked_
  if (data.checked_) {
    if (!checkedValue_2.value.includes(data.value_)) {
      checkedValue_2.value.push(data.value_)
      showSelect_2.value.push(data)
    }
  } else {
    if (checkedValue_2.value.includes(data.value_)) {
      deleteCheckedData2(data.value_)
    }
  }
  if (checkedValue_1.value.includes(data.brandId)) {
    deleteCheckedData1(data.brandId)
  }
  setCheckedDataShow2()
  setCheckedData1()
  setShowData()
}

const selectAllFun_2 = () => {
  if (selectAllFlag_2.value) {
    listData_2.value.forEach((_) => {
      _.checked_ = false
      if (checkedValue_2.value.includes(_.value_)) {
        deleteCheckedData2(_.value_)
      }
    })
  } else {
    listData_2.value.forEach((_) => {
      _.checked_ = true
      if (!checkedValue_2.value.includes(_.value_)) {
        checkedValue_2.value.push(_.value_)
        showSelect_2.value.push(_)
      }
    })
  }
  if (checkedValue_1.value.includes(selectValue_1.value)) {
    deleteCheckedData1(selectValue_1.value)
  }
  setCheckedData1()
  setShowData()
}

const setCheckedData1 = () => {
  const idList = []
  showSelect_2.value.forEach((_) => {
    if (!idList.includes(_.brandId)) {
      idList.push(_.brandId)
    }
  })
  listData_1.value.forEach((_) => {
    _.checked_ = false
    _.indeterminate_ = false
    if (checkedValue_1.value.includes(_.brandId)) {
      _.checked_ = true
    }
    if (idList.includes(_.brandId)) {
      _.indeterminate_ = true
    }
  })
}

const setCheckedData2 = (brandId) => {
  const idList = []
  const list = []
  showSelect_2.value.forEach((_) => {
    if (_.brandId !== brandId) {
      idList.push(_.value_)
      list.push(_)
    }
  })
  checkedValue_2.value = idList
  showSelect_2.value = list
}

const setCheckedDataShow2 = () => {
  listData_2.value.forEach((item) => {
    item.checked_ = false
    if (checkedValue_2.value.includes(item.value_)) {
      item.checked_ = true
    }
  })
}

defineExpose({
  init,
  reset
})
</script>

<style>
.popper-class-no-padding {
  padding: 0 !important;
}
</style>
<style lang="scss" scoped>
.box-list {
  .vehicle-type-search {
    padding: 10px 10px 0 10px;
  }
  .box-item {
    display: inline-block;
    width: calc(100% / 2);
    padding: 0 10px;
    margin: 10px 0;

    .switch-box {
      margin-top: 3px;
    }

    .scroll-box {
      height: 250px;
      margin-top: 10px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #e0e0e0;
        border-radius: 6px;
      }

      .item {
        padding: 0 10px;
        overflow: hidden;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        position: relative;

        :deep(.el-checkbox) {
          margin-right: 6px;
          position: relative;
          top: 2px;
          z-index: 1;
        }

        .shade {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          z-index: 2;
        }

        &.active {
          color: white;
          background: #7f7f7f;
        }
      }

      .loading-box {
        display: flex;
        font-size: 12px;
        justify-content: center;
        align-items: center;
      }
    }

    .is-switch {
      height: 225px;
      margin-top: 0;
    }
  }

  .item-right {
    border-left: 1px solid gainsboro;
  }
}

.brand-car-box {
  display: flex;
  align-items: flex-start;
}

.reference-box {
  height: 32px;
  padding: 0 11px;
  overflow: hidden;
  font-size: 14px;
  line-height: 30px;
  color: #606266;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.select-brand-car {
  margin-left: 20px;
}
</style>
