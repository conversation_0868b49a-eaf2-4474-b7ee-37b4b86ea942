<template>
  <!-- <div v-loading="loading" style="margin: 20px 20px 0"> -->
  <div v-loading="loading">
    <el-form
      ref="ruleForm"
      :inline="true"
      label-width="130px"
      class="selected-brand-form-list"
    >
      <el-form-item
        v-if="launchBrand"
        :label="titleOne"
        style="vertical-align: top"
        :required="brandModelRequired"
        class="brand-list-content"
      >
        <!-- <el-checkbox
                        v-show="!isSingle"
                        v-if="showBrandNameList.length === oldBrandNameList.length && oldBrandNameList.length"
                        :indeterminate="brandIsIndeterminate"
                        v-model="brandCheckAll"
                        @change="brandCheckAllChange"
                        >全选</el-checkbox
                      > -->
        <!-- <br v-show="!isSingle" /> -->
        <!-- <div v-show="!isSingle">(多选最多选10个)</div> -->
        <div>
          <el-input
            v-show="!isSingle"
            v-if="oldBrandNameList.length"
            v-model="searchBrandValue"
            style="width: 200px"
            placeholder="查询品牌名称"
            clearable
            @change="updataShowName('brand')"
            @clear="clearShowName('brand')"
          >
            <template v-slot:append>
              <el-button v-if="searchBrandValue" :icon="IconSearch"></el-button>
            </template>
          </el-input>
          <el-checkbox-group
            v-model="brandChecked"
            :min="0"
            :max="isSingle ? 1 : 1000"
            class="brand-list mt10"
            @change="brandCheckedChange"
          >
            <el-checkbox
              v-for="(brand, index) in showBrandNameList"
              :label="brand"
              :key="index"
              class="brand-name"
              @change="updataBrandName(brand, index)"
              >{{ brand }}</el-checkbox
            >
          </el-checkbox-group>
        </div>
      </el-form-item>
      <el-form-item
        v-if="launchCar"
        :required="carModelRequired"
        :label="titleTwo"
        label-width="100px"
        class="brand-list-content"
      >
        <div>
          <div v-show="!isSingle">
            <el-switch
              v-model="selectedBrand.saleStatus"
              active-text="仅看在售"
              inactive-text="查看全部"
              @change="
                () => {
                  setShowGoodList(selectedBrand, true)
                  setBackData()
                }
              "
            >
            </el-switch>
          </div>
          <el-checkbox
            v-show="!isSingle"
            v-if="
              showGoodNameList.length === oldGoodNameList.length &&
              oldGoodNameList.length
            "
            :indeterminate="goodIsIndeterminate"
            v-model="goodCheckAll"
            @change="goodCheckAllChange"
            >全选</el-checkbox
          >
          <br v-show="!isSingle" />
          <el-input
            v-show="!isSingle"
            v-if="oldGoodNameList.length"
            v-model="searchGoodValue"
            style="width: 200px"
            placeholder="查询车型名称"
            clearable
            @change="updataShowName('good')"
            @clear="clearShowName('good')"
          >
            <template v-slot:append>
              <el-button v-if="searchGoodValue" :icon="IconSearch"></el-button>
            </template>
          </el-input>
          <el-checkbox-group
            v-model="goodChecked"
            :min="0"
            :max="isSingle ? 1 : oldGoodNameList.length + 1"
            class="brand-list mt10"
            @change="goodCheckedChange"
          >
            <el-checkbox
              v-for="(good, index) in showGoodNameList"
              :label="good"
              :key="index"
              class="brand-name"
              >{{ good }}</el-checkbox
            >
          </el-checkbox-group>
        </div>
      </el-form-item>
      <el-form-item v-if="reminder" style="vertical-align: top">
        <div>
          <span style="color: #bbbbbb">可单独选择多个品牌或车型</span> <br />
          <span v-if="selectedAllData.length">已选:</span>
          <div
            v-for="(data, index) in selectedAllData"
            :key="index"
            class="selected-brand-list"
          >
            <el-tag
              type="info"
              v-if="
                !(data.allGoodChecked && data.allGoodChecked.length) ||
                (data.allGoodChecked &&
                  data.allGoodChecked.length === data.goodList &&
                  data.goodList.length)
              "
              closable
              @close="closeSelected(data, '')"
            >
              {{ data.name }} - 全部
            </el-tag>
            <template v-else>
              <el-tag
                v-for="(good, goodIndex) in data.allGoodChecked"
                :key="goodIndex"
                type="info"
                closable
                @close="closeSelected(data, good)"
                class="tag-margin"
              >
                {{ data.name }} - {{ good }}</el-tag
              >
            </template>
          </div>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { Search as IconSearch } from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { searchBrandList } from '@/api/brand'
import { searchCarList } from '@/api/garage'
export default {
  data() {
    return {
      // 搜索品牌
      searchBrandValue: '',
      // 搜索车型
      searchGoodValue: '',
      loading: false,
      brandCheckAll: false,
      goodCheckAll: false,
      // 选中的品牌，当前展示该品牌下车型
      selectedBrand: {},
      // 所有的品牌数据
      brandList: [],
      // 仅品牌名的存储历史数据
      oldBrandNameList: [],
      // 展示的品牌名
      showBrandNameList: [],
      // 选中的品牌名
      brandChecked: [],
      // 旧的车型数据
      oldGoodNameList: [],
      // 展示的车型数据
      showGoodNameList: [],
      // 选中的车型数据
      goodChecked: [],
      // 选中的所有数据
      selectedAllData: [],
      // 品牌实现全选的效果
      brandIsIndeterminate: true,
      // 车型实现全选的效果
      goodIsIndeterminate: true,
      // 切换选择数据状态
      changeSaleStatus: false,
      IconSearch: markRaw(IconSearch)
    }
  },
  name: 'selectBrands',
  components: {},
  props: {
    brandModelRequired: {
      type: Boolean,
      default: true
    },
    carModelRequired: {
      type: Boolean,
      default: false
    },
    isSingle: {
      // 是否单选
      type: Boolean,
      default: false
    },
    isMorechecked: {
      type: Boolean,
      default: false
    },
    launchBrand: {
      type: Boolean,
      default: false
    },
    launchCar: {
      type: Boolean,
      default: false
    },
    titleOne: {
      type: String,
      default: '投放品牌'
    },
    titleTwo: {
      type: String,
      default: '投放车型'
    },
    reminder: {
      type: Boolean,
      default: false
    }
  },
  computed: {},
  watch: {
    goodChecked(newData, oldData) {
      if (this.changeSaleStatus) {
        this.changeSaleStatus = false
        return
      } // 计算相差值
      const fileData1 = newData.length > oldData.length ? newData : oldData
      const fileData2 = newData.length > oldData.length ? oldData : newData
      const addItem = fileData1.concat(fileData2).filter((v, i, arr) => {
        return fileData1.indexOf(v) === arr.lastIndexOf(v)
      })
      const me = this
      setTimeout(() => {
        me.updataSelectedBrand(addItem || [], newData.length > oldData.length)
      }, 50)
    }
  },
  mounted() {
    this.getBrandList()
  },
  methods: {
    // 设置品牌是否展示
    async setBrand(brand, good, backData) {
      console.log(brand, good)
      let getGoodList = backData.length ? [] : good
      let backGoodList = []
      if (backData.length) {
        backData.map((item) => {
          const findIndex = good.findIndex((g) => {
            return item.goodsId === g
          })
          findIndex >= 0
            ? backGoodList.push(item)
            : getGoodList.push(item.goodsId)
        })
      }
      const results = await Promise.all(
        getGoodList.map(async (item) => {
          const { data: goodData } = await searchCarList({ goodsId: item })
          return goodData.data.list && goodData.data.list[0]
        })
      )
      const arr = results.map((_) => {
        return { brandId: _.brandId, goodsId: _.goodId, goodsName: _.goodName }
      })
      backGoodList = this.setData(arr.concat(backGoodList))
      const me = this
      setTimeout(() => {
        me.setBrand1(brand, backGoodList)
      }, 100)
    },
    // 安顺序，存储数据
    setData(arr) {
      let tempArr = []
      let list = []
      arr.map((_) => {
        if (tempArr.indexOf(_.brandId) === -1) {
          list.push({
            brandId: _.brandId,
            goodsList: [_]
          })
          tempArr.push(_.brandId)
        } else {
          list.map((i) => {
            if (i.brandId === _.brandId) {
              i.goodsList.push(_)
              return
            }
          })
        }
      })
      return list
    },
    setBrand1(brand, good) {
      const selectedData = {}
      brand &&
        brand.map((_) => {
          selectedData[_] = { goodName: '' }
        })
      good &&
        good.map((_) => {
          const goodName = _.goodsList.map((item) => {
            return item.goodsName
          })
          const goodsId = _.goodsList.map((item) => {
            return item.goodsId
          })
          selectedData[_.brandId] = { goodName: goodName, goodsId: goodsId }
        })
      // const isAllStatus = this.$route.query.id && !brand.length && !good.length
      const isAllStatus = false
      this.brandList.length
        ? this.setBrandList(selectedData, isAllStatus)
        : this.getBrandList(selectedData, isAllStatus)
    },
    // 获取品牌
    getBrandList(selectedData, isAllStatus) {
      const me = this
      searchBrandList({
        isShow: 1
      }).then((response) => {
        if (response.data.code === 0) {
          const res = (response.data.data && response.data.data.list) || []
          me.oldBrandNameList = []
          me.brandList = []
          me.brandList = res.map((item) => {
            me.oldBrandNameList.push(item.brandName)
            return {
              name: item.brandName,
              id: item.brandId
            }
          })
          me.showBrandNameList = me.deepCopy(me.oldBrandNameList)
          if (selectedData !== undefined) {
            me.setBrandList(selectedData, isAllStatus)
          } else {
            me.brandChecked = []
          }
        }
      })
    },
    // 设置品牌显示(选择数据。是否是全选)
    setBrandList(selectedData, isAllStatus) {
      const me = this
      me.brandChecked = []
      Object.keys(selectedData).map((_) => {
        const brandSelected = me.brandList.find((item) => {
          return item.id === parseInt(_)
        })
        brandSelected.goodChecked = selectedData[_].goodName
        brandSelected.allGoodChecked = selectedData[_].goodName
        brandSelected.goodCheckedId = selectedData[_].goodsId || ''
        me.brandChecked.push(brandSelected.name)
      })
      if (isAllStatus) {
        me.brandList.map((_) => {
          me.brandChecked.push(_.name)
        })
        me.brandCheckAll = true
      }
      const getCarStatus =
        selectedData !== undefined &&
        Object.keys(selectedData) &&
        Object.keys(selectedData).length &&
        Object.keys(selectedData).length === 1
      console.log(getCarStatus, selectedData)
      if (getCarStatus) {
        // 判定单个，获取车型数据
        const brandId = Object.keys(selectedData)[0]
        const selectedBrand = me.brandList.find((item) => {
          return item.id === parseInt(brandId)
        })
        me.getCarList(selectedBrand)
      }
      me.setBackData()
    },
    // 变更显示的品牌名称或车型名称
    updataShowName(type) {
      const me = this
      const searchName =
        type === 'brand' ? me.searchBrandValue : me.searchGoodValue
      if (!searchName) return
      let showData =
        type === 'brand' ? me.showBrandNameList : me.showGoodNameList
      showData = []
      const oldData =
        type === 'brand' ? me.oldBrandNameList : me.oldGoodNameList
      oldData.map((_) => {
        if (_.indexOf(searchName) > -1) showData.push(_)
      })
      type === 'brand'
        ? (me.showBrandNameList = showData)
        : (me.showGoodNameList = showData)
    },
    // 清除搜索
    clearShowName(type) {
      type === 'brand'
        ? (this.showBrandNameList = [])
        : (this.showGoodNameList = [])
      type === 'brand'
        ? (this.showBrandNameList = this.oldBrandNameList)
        : (this.showGoodNameList = this.oldGoodNameList)
    },
    // 品牌全选
    brandCheckAllChange(val) {
      this.brandChecked = val ? this.oldBrandNameList : []
      this.brandIsIndeterminate = false
      this.setBackData()
    },
    // 品牌单选
    brandCheckedChange(value) {
      console.log(value)
      const checkedCount = value.length
      this.brandCheckAll = checkedCount === this.oldBrandNameList.length
      this.brandIsIndeterminate =
        checkedCount > 0 && checkedCount < this.oldBrandNameList.length
    },
    // 展示或隐藏品牌下车型
    updataBrandName(brand, index) {
      this.updataSelectedBrand()
      const findDataIndex = this.brandChecked.findIndex((_) => {
        return _ === brand
      })
      if (findDataIndex === -1) {
        this.setShowGoodList({ oldGoodNameList: [] })
        return
      }
      const seletedData = this.brandList.filter((_) => {
        return _.name === brand
      })
      seletedData.map((_) => {
        // 在售和非在售切换。重新拉数据
        if (_.goodList && _.goodList.length) {
          // 有车型
          this.setShowGoodList(_)
        } else {
          this.getCarList(_) // 没有车型，获取车型
        }
      })
    },
    // 获取车型列表
    getCarList(data) {
      const me = this
      searchCarList({
        limit: 9999,
        brand: data.name || '',
        brandId: data.id || '',
        isOnStatus: 1,
        saleStatus: data.saleStatus ? 1 : ''
      }).then((response) => {
        if (response.data.code === 0) {
          const result = response.data.data && response.data.data.list
          data.oldGoodNameList = []
          data.saleStatusData = [] // 在售车辆数据
          data.oldSaleGoodNameList = [] // 在售车辆数据（名称）
          data.goodList = result.map((item) => {
            data.oldGoodNameList.push(item.goodName)
            if (item.saleStatus === 1) {
              data.oldSaleGoodNameList.push(item.goodName)
              data.saleStatusData.push({
                name: item.goodName,
                id: item.goodId
              })
            }
            return {
              name: item.goodName,
              id: item.goodId
            }
          })
          setTimeout(() => {
            me.setShowGoodList(data)
          }, 100)
        }
      })
    },
    // 设置显示的车型名称
    setShowGoodList(data, changeSaleStatus) {
      const me = this
      me.changeSaleStatus = changeSaleStatus || false // 切换选择数据状态
      me.oldGoodNameList = me.deepCopy(
        data.saleStatus
          ? data.oldSaleGoodNameList || []
          : data.oldGoodNameList || []
      )
      me.showGoodNameList = me.deepCopy(me.oldGoodNameList || [])
      data.goodChecked = data.goodChecked || []
      data.saleGoodChecked = data.saleGoodChecked || []
      data.allGoodChecked = Array.from(
        new Set([...data.goodChecked, ...data.saleGoodChecked])
      )
      data.saleGoodChecked = me.setSaleGoodChecked(data)
      me.goodChecked = data.saleStatus ? data.saleGoodChecked : data.goodChecked
      me.goodIsIndeterminate =
        me.goodChecked.length === me.oldGoodNameList.length
      me.goodCheckAll = me.goodChecked.length === me.oldGoodNameList.length
      me.selectedBrand = data
    },
    // 确认以选择的数据
    setSaleGoodChecked(data) {
      let backData = []
      data.allGoodChecked.map((item) => {
        if (data.oldSaleGoodNameList.includes(item)) {
          backData.push(item)
        }
      })
      return backData
    },
    // 车型全选
    goodCheckAllChange(val) {
      this.goodChecked = val ? this.oldGoodNameList : []
      this.goodIsIndeterminate = false
      this.setBackData()
    },
    // 车型单选
    goodCheckedChange(value) {
      const checkedCount = value.length
      this.goodCheckAll = checkedCount === this.oldGoodNameList.length
      this.goodIsIndeterminate =
        checkedCount > 0 && checkedCount < this.oldGoodNameList.length
    },
    // 更新品牌下以选车型（单个）
    updataSelectedBrand(addItem, calculationStatus) {
      const me = this
      setTimeout(() => {
        me.setBackData()
      }, 100)
      if (!me.selectedBrand.id) return
      if (me.selectedBrand.saleStatus) {
        me.selectedBrand.saleGoodChecked = me.goodChecked
      } else {
        me.selectedBrand.goodChecked = me.goodChecked
      }
      if (addItem && addItem.length) {
        addItem.map((item) => {
          me.updataSelectedData(item, calculationStatus)
        })
      }
    },
    // 更新选择的数据
    updataSelectedData(addItem, calculationStatus) {
      const me = this
      // 在售车辆时
      if (me.selectedBrand.saleStatus) {
        const indexOf = me.selectedBrand.goodChecked.indexOf(addItem)
        calculationStatus
          ? me.selectedBrand.goodChecked.push(addItem)
          : indexOf > -1
          ? me.selectedBrand.goodChecked.splice(indexOf, 1)
          : null
      } else if (!calculationStatus) {
        // 非在售，且删除时
        const indexOf = me.selectedBrand.saleGoodChecked.indexOf(addItem)
        indexOf > -1
          ? me.selectedBrand.saleGoodChecked.splice(indexOf, 1)
          : null
      } else {
        // 非在售，且增加时
        const indexStatus =
          me.selectedBrand.oldSaleGoodNameList.includes(addItem) // 所有数据是否存在
        const saleIndexStatus =
          me.selectedBrand.saleGoodChecked.includes(addItem) // 在售车中是否存在
        me.selectedBrand.saleGoodChecked =
          indexStatus && !saleIndexStatus
            ? me.selectedBrand.saleGoodChecked.concat(addItem)
            : me.selectedBrand.saleGoodChecked
      }
      me.selectedBrand.allGoodChecked = Array.from(
        new Set([
          ...me.selectedBrand.goodChecked,
          ...me.selectedBrand.saleGoodChecked
        ])
      )
    },
    // 拷贝数据
    deepCopy(data) {
      sessionStorage.setItem('temArr', JSON.stringify(data))
      const backData = JSON.parse(sessionStorage.temArr)
      sessionStorage.removeItem('temArr')
      return backData
    },
    // 设置返回数据
    setBackData() {
      const me = this
      let selectedBrandList = []
      me.brandChecked.map((_) => {
        me.brandList.find((item) => {
          if (
            item.name === _ &&
            !(item.saleStatus && !item.allGoodChecked.length)
          ) {
            selectedBrandList.push(item)
          }
        })
      })
      const selectedId = []
      let selectedGoodId = []
      me.selectedAllData = selectedBrandList
      selectedBrandList.map((_) => {
        // 只选择品牌(全部车型非仅在售的)
        if (
          !(_.allGoodChecked && _.allGoodChecked.length) ||
          (_.allGoodChecked.length &&
            _.allGoodChecked.length ===
              (_.oldGoodNameList && _.oldGoodNameList.length) &&
            !this.isMorechecked)
        )
          return selectedId.push(_.id)
        // 编辑直接保存
        if (
          !(_.goodList && _.goodList.length) &&
          !(_.goodCheckedId && _.goodCheckedId.length)
        )
          return selectedId.push(_.id)
        // 编辑车型直，第一次查询
        if (
          _.goodCheckedId &&
          _.goodCheckedId.length &&
          !(_.goodList && _.goodList.length)
        ) {
          return (selectedGoodId = selectedGoodId.concat(_.goodCheckedId))
        }
        _.goodChecked.map((item) => {
          const selectedGood = _.goodList.filter((good) => {
            return good.name === item
          })
          selectedGood.map((_) => {
            selectedGoodId.push(_.id)
          })
        })
      })
      const brands = selectedId.length ? selectedId.join() : ''
      const backData = {
        brand: selectedId.length === me.brandList.length ? 'all' : brands,
        good: selectedGoodId.length ? selectedGoodId.join() : ''
      }
      $emit(me, 'updateBrand', backData)
    },
    // 清除数据
    closeSelected(data, goodName) {
      if (goodName) {
        data.allGoodChecked.splice(
          data.allGoodChecked.findIndex((item) => item === goodName),
          1
        )
        data.goodChecked.splice(
          data.goodChecked.findIndex((item) => item === goodName),
          1
        )
        if (!data.allGoodChecked.length) return this.closeSelected(data, '')
      } else {
        this.selectedAllData.splice(
          this.selectedAllData.findIndex((item) => item.name === data.name),
          1
        )
        this.brandChecked.splice(
          this.brandChecked.findIndex((item) => item === data.name),
          1
        )
      }
      this.setBackData()
    }
  },
  emits: ['updateBrand']
}
</script>

<style lang="scss" scoped>
.selected-brand-form-list {
  .brand-list-content {
    .brand-list {
      height: 60vh;
      overflow-y: scroll;
    }
  }

  .brand-name {
    display: block;
  }

  .selected-brand-list {
    margin-bottom: 4px;
    width: 500px;
  }
}
</style>
