<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'选择内容'"
    center
    class="choose-dialog"
    width="800px"
    append-to-body
  >
    <div v-loading="loading">
      <p>
        <el-radio-group v-model="radioSelected" @change="updateRadioSelected">
          <el-radio :label="1">内容ID</el-radio>
          <el-radio :label="2">内容口令</el-radio>
          <el-radio :label="3">作者查询</el-radio>
        </el-radio-group>
      </p>
      <el-form
        ref="ruleForm"
        :inline="true"
        label-width="100px"
        class="selected-brand-form-list"
      >
        <div v-if="radioSelected !== 2">
          <el-form-item
            v-if="radioSelected === 1"
            label="内容ID"
            class="brand-list-content"
          >
            <el-input
              v-model="ruleForm.id"
              type="text"
              placeholder="请输入内容ID"
              clearable
              @keyup.enter="goSearch()"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item
            v-if="radioSelected === 3"
            label="发布者名称"
            class="brand-list-content"
          >
            <el-select
              v-model="ruleForm.autherid"
              clearable
              filterable
              remote
              reserve-keyword
              placeholder="请输入发布者名称"
              :remote-method="querySearchAsyncAuther"
            >
              <el-option
                v-for="item in autherList"
                :key="item.uid"
                :label="item.value"
                :value="item.uid"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="goSearch()">查询</el-button>
          </el-form-item>
        </div>
        <div v-else>
          <el-form-item>
            <el-input
              v-model="command"
              type="textarea"
              placeholder="请输入口令"
              class="text-item"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="decrypt">口令解密</el-button>
          </el-form-item>
        </div>
      </el-form>
      <p>{{ radioSelected !== 2 ? '选择内容' : '解析结果' }}</p>
      <ShowEassayList
        ref="ShowEassayList"
        :showPrime="showPrime"
        :showExpire="showExpire"
        :showSelected="radioSelected === 3"
        :showAllWidth="true"
      />
      <p class="errTip" v-if="errStatus">
        当前内容暂不适合投放，去试试其他内容吧
      </p>
      <div class="footer-content">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="goPostData()">确认</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import ShowEassayList from './ShowEassayList.vue'
import { getUserAccountCorrelationV2 } from '@/api/user'

import { searchArticleList, checkEssayExpired } from '@/api/articleModule' // 文章搜索
import { decryptCommand } from '@/api/article'
export default {
  name: 'SelectEassay',
  components: {
    ShowEassayList
  },
  props: {
    showPrime: {
      type: Boolean,
      default: false
    },
    showExpire: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      errStatus: false,
      radioSelected: 1,
      authername: '',
      command: '',
      ruleForm: {},
      autherList: []
    }
  },
  methods: {
    clearData() {
      this.dialogVisible = true
      this.loading = false
      this.updateRadioSelected()
    },
    updateRadioSelected() {
      this.errStatus = false
      this.authername = ''
      this.command = ''
      this.ruleForm = {}
      this.autherList = []
      this.$refs.ShowEassayList && this.$refs.ShowEassayList.setListData([])
    },
    // 发布者
    querySearchAsyncAuther(e) {
      getUserAccountCorrelationV2({
        username: e,
        page: 1,
        limit: 10
      }).then((response) => {
        if (response.data.code === 0) {
          const userNameList = []
          const result = response.data.data && response.data.data.list
          result.map(function (value) {
            const newObj = {
              value: value.username,
              uid: value.uid
            }
            userNameList.push(newObj)
          })
          this.autherList = userNameList
        }
      })
    },
    //口令解密
    async decrypt() {
      if (!this.command) {
        return this.$message.error('请输入口令')
      }
      try {
        const res = await decryptCommand({
          command: this.command
        })
        if (res.data.code == 0) {
          this.ruleForm = {}
          this.ruleForm.id = res.data.data && res.data.data.essayId
          this.goSearch()
        } else {
          this.$message.error(res.data.msg || '解密失败')
        }
      } catch (e) {
        console.log(e)
      }
    },
    // 提交数据
    goSearch() {
      const me = this
      if (!me.ruleForm.id && !me.ruleForm.autherid) {
        return me.$message.error('请输入筛选内容')
      }
      searchArticleList({
        ...me.ruleForm,
        page: 1,
        limit: 100,
        prime: ''
      }).then(async (response) => {
        if (response.data.code === 0) {
          const res = response.data.data.listData || []
          if (res.length) {
            const result = await checkEssayExpired({
              essayIdsStr: me.ruleForm.id
            })
            if (result.data.code === 0) {
              res[0].outDate = !!result.data.data?.[0]?.expired
            }
          }
          me.$refs.ShowEassayList.setListData(res)
        }
      })
    },
    // 提交数据
    goPostData() {
      const me = this
      const backList =
        me.radioSelected === 3
          ? me.$refs.ShowEassayList.multipleSelection
          : me.$refs.ShowEassayList.listData
      if (backList.length > 1 || !backList.length)
        return me.$message.error('仅能选择一个数据')
      if (me.showPrime && !backList[0].prime)
        return me.$message.error('该内容非优质，换一篇试试吧')
      const backData = backList[0]
      const coolEndTimeStatus = backData.coolEndTime
        ? new Date(backData.coolEndTime * 1000) > new Date()
        : false
      if (backData.status !== 1 || coolEndTimeStatus)
        return (me.errStatus = true)
      this.dialogVisible = false
      $emit(me, 'setBackData', backData)
    }
  },
  emits: ['setBackData']
}
</script>

<style lang="scss" scoped>
.choose-dialog {
  .errTip {
    color: red;
  }
  .text-item {
    width: 700px;
  }
}
</style>
