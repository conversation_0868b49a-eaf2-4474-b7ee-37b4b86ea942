<template>
  <div class="upload-img">
    <el-button type="primary" :loading="loadingImage" @click="clickUploadImg()"
      >上传图片</el-button
    >
    <input
      type="file"
      name="file"
      accept="image/*"
      class="imageFile cover-uploader upload-img-view"
      @input="httpGifRequest"
    />
    <el-upload
      :show-file-list="false"
      :http-request="httpRequest"
      :on-progress="onProgress"
      :on-success="onSuccess"
      :on-error="onError"
      name="upfile"
      style="display: inline-block"
      class="avatar-uploader"
      action
    />
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { uploadGIF } from '@/api/postApk'
export default {
  name: 'uploadImg',
  props: {
    isGif: {
      // 是否上传gif
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loadingImage: false,
    }
  },
  mounted() {},
  methods: {
    init() {},
    // 点击上传图片
    clickUploadImg() {
      const dom = this.isGif ? 'cover-uploader' : 'el-upload__input'
      const setTimeNum = 50
      setTimeout(() => {
        document.querySelector(`.${dom}`).click()
      }, setTimeNum)
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1 // 无水印
      this.$oss.ossUploadImage(option)
    },
    onProgress() {
      this.loadingImage = true
    },
    onSuccess(res) {
      this.loadingImage = false
      if (!res) return
      // console.log(res)
      if (res.name) {
        $emit(this, 'getImgData', res.imgOrgUrl)
        return
      } else {
        this.$notify.error({
          title: '上传错误',
        })
      }
    },
    onError(res) {
      this.loadingImage = false
      this.$message.error(res)
      console.log(res)
    },
    // gif图上传
    httpGifRequest(e) {
      const file = Array.prototype.slice.call(e.target.files)
      let fd = new FormData()
      fd.append('file', file[0])
      const me = this
      uploadGIF(fd)
        .then((response) => {
          if (response.data.code === 0) {
            const imgOrgUrl = response.data.data
            $emit(this, 'getImgData', imgOrgUrl)
          } else {
            me.$message.error('上传错误')
          }
        })
        .finally(() => {
          e.target.value = ''
        })
    },
  },
  emits: ['getImgData'],
}
</script>

<style lang="scss">
.upload-img {
  display: inline-block;
  .upload-img-view {
    display: none;
  }
}
</style>
