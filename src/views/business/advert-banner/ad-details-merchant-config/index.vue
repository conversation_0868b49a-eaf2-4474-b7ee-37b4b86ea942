<template>
  <div class="ad-details-config">
    <SelectAdPlan ref="SelectAdPlan" @next="updateNext" />
    <div class="title" v-if="nextStatus">
      <span>选择广告位置</span>
      <div class="line"></div>
    </div>
    <SelectAdPlacement
      v-if="nextStatus"
      ref="SelectAdPlacement"
      :isMerchant="true"
    />
    <div v-if="$store.state.adConfig.adDetailShow">
      <div class="title">
        <span>配置广告内容</span>
        <div class="line"></div>
      </div>
      <AdConfigDifferentA
        ref="AdConfigDifferentA"
        @requiredItem="requiredItem"
      />
      <AdConfigIdentical
        ref="AdConfigIdentical"
        @requiredItem="requiredItemB"
        :isMerchant="true"
      />
      <div class="title">
        <span>店铺及用户配置</span>
        <div class="line"></div>
      </div>
      <AdConfigDifferentB ref="AdConfigDifferentB" @requiredItem="submitAd" />
      <div class="submit-config" v-if="showStatus">
        <el-button type="primary" @click="requiredItemA">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import SelectAdPlan from '../ad-details-config/components/SelectAdPlan.vue'
import SelectAdPlacement from '../ad-details-config/components/SelectAdPlacement.vue'
import AdConfigDifferentA from './components/AdConfigDifferentA.vue'
import AdConfigIdentical from '../ad-details-config/components/AdConfigIdentical.vue'
import AdConfigDifferentB from './components/AdConfigDifferentB.vue'
import {
  adAdvertManageDetail,
  adAdvertManageAdd,
  adAdvertManageUpdate
} from '@/api/advertModule'
import { deepCopy } from '@/utils'
import { mapGetters } from 'vuex'
import { timeFullS } from '@/filters'
export default {
  name: 'AdDetailsConfig',
  components: {
    SelectAdPlan,
    SelectAdPlacement,
    AdConfigDifferentA,
    AdConfigIdentical,
    AdConfigDifferentB
  },
  data() {
    return {
      id: '', // 广告id
      copy: '', // 是否是复制
      flag: true,
      isShopType: false, // 是否是经销商广告
      nextStatus: false, // 下一步
      showStatus: false, // 隐藏下一步
      adIdOld: ''
    }
  },
  activated() {
    this.id = this.$route.query.id || ''
    this.copy = this.$route.query.copy || ''
    this.adIdOld = sessionStorage.getItem('adIdOld') || ''
    let idNew = this.$route.query.id || ''
    this.isShopType = !!this.$route.query.shopType
    idNew = idNew && idNew.toString()
    if (idNew !== this.adIdOld) {
      sessionStorage.setItem('adIdOld', idNew)
      this.$refs.SelectAdPlan.initData({})
      this.flag = true
    }
    this.showStatus = true
    if (this.flag) {
      this.flag = false
      this.$store.dispatch('resetAdDetail')
      this.$refs.SelectAdPlacement && this.$refs.SelectAdPlacement.init()
      if (this.id) {
        this.adAdvertManageDetail(this.id)
      }
    }
    this.$store.dispatch('getCityList')
  },
  computed: {
    ...mapGetters(['uid', 'name', 'cachedViews'])
  },
  watch: {
    cachedViews: {
      deep: true,
      handler(newVal) {
        if (!newVal.includes('AdDetailsConfig')) {
          this.flag = true
        }
      }
    }
  },
  methods: {
    // 获取广告详情
    adAdvertManageDetail(id) {
      const me = this
      adAdvertManageDetail({ id }).then((res) => {
        if (res.data.code === 0) {
          me.nextStatus = true
          const data = res.data.data
          data.platform = data.platform.split(',')
          data.isRepeat = data.isRepeat ? true : false
          data.exclusiveFlag = data.exclusiveFlag ? true : false
          data.regionListStr = data.regionList
          data.relationListStr = data.relationList
          data.isRemind = data.isRemind === 1
          data.pageSelect = ''
          data.shopRegisterType = Number(data.shopRegisterType)
          data.timeSeries = data.timeSeries || []
          me.showStatus = me.copy || data.status !== -1
          delete data.regionList
          delete data.relationList
          me.$refs.SelectAdPlan.initData(data)
          me.$store.dispatch('changeAdDetail', data)
          setTimeout(() => {
            me.$refs.SelectAdPlacement && me.$refs.SelectAdPlacement.init()
          }, 10)
        }
      })
    },
    requiredItemA() {
      const flag = sessionStorage.getItem('adMerchantStatus')
      if (flag) {
        return this.$message.error('该店铺状态异常')
      }
      if (this.copy) {
        const detailsForm = deepCopy(this.$store.state.adConfig.adDetailData)
        if (
          (detailsForm.adProjectType === 2 || detailsForm.orgType === 2) &&
          !detailsForm.orderNum
        ) {
          return this.$message.error('请选择关联广告服务订单')
        }
      }
      this.$refs.AdConfigDifferentA &&
        this.$refs.AdConfigDifferentA.requiredItem()
      this.isShopType = this.isShopType
        ? this.isShopType
        : this.$refs.SelectAdPlacement.updateShopType
    },
    requiredItem() {
      this.$refs.AdConfigIdentical &&
        this.$refs.AdConfigIdentical.requiredItem()
    },
    requiredItemB() {
      this.$refs.AdConfigDifferentB &&
        this.$refs.AdConfigDifferentB.requiredItem()
    },
    // 创建广告
    submitAd(checkConflict) {
      const isShopType = this.isShopType
        ? this.isShopType
        : this.$refs.SelectAdPlacement.updateShopType
      const detailsForm = deepCopy(this.$store.state.adConfig.adDetailData) // 广告数据
      detailsForm.platform = detailsForm.platform.join(',')
      detailsForm.channel = Array.isArray(detailsForm.channel)
        ? detailsForm.channel.join(',')
        : detailsForm.channel
      detailsForm.isRepeat = detailsForm.isRepeat ? 1 : 0
      detailsForm.exclusiveFlag = detailsForm.exclusiveFlag ? 1 : 0
      detailsForm.regionListStr =
        JSON.stringify(detailsForm.regionListStr) === '[]'
          ? ''
          : JSON.stringify(detailsForm.regionListStr)
      detailsForm.relationListStr =
        JSON.stringify(detailsForm.relationListStr) === '[]'
          ? ''
          : JSON.stringify(detailsForm.relationListStr)
      detailsForm.linkType = detailsForm.linkType
        ? detailsForm.linkType
        : detailsForm.pageSelect
      detailsForm.advertPlatform = 2
      detailsForm.operatorId = this.uid // 操作人ID
      detailsForm.operatorName = this.name // 操作人姓名
      detailsForm.shopType = isShopType ? 1 : ''
      detailsForm.isRemind = detailsForm.isRemind ? 1 : 0
      detailsForm.remindUserName = detailsForm.remindUserName || this.name
      console.log(detailsForm, 'detailsFormdetailsFormdetailsForm')
      if (checkConflict === 0) {
        detailsForm.checkConflict = checkConflict
      }
      if (this.id && !this.copy) {
        return this.adAdvertManageUpdate(detailsForm)
      }
      this.adAdvertManageAdd(detailsForm)
    },
    // 创建
    adAdvertManageAdd(obj) {
      const currentTime = timeFullS(Math.floor(new Date())).split(' ')[0]
      const beginTime = timeFullS(obj.beginTime).split(' ')[0]
      const postData = {
        ...obj,
        beginTime:
          beginTime === currentTime
            ? timeFullS(Math.floor(new Date()) + 60000)
            : obj.beginTime // 跟pc时间日期一致，传递时间增加一分钟
      }
      adAdvertManageAdd(postData)
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('创建成功')
            this.flag = true
            this.goBack()
          } else {
            this.$message.error(res.data.msg || '创建失败')
          }
        })
        .catch((err) => {
          this.repetition(err)
        })
    },
    // 更新
    adAdvertManageUpdate(obj) {
      adAdvertManageUpdate({
        adUnitId: this.id,
        ...obj
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('更新成功')
            this.flag = true
            this.goBack()
          } else {
            this.$message.error(res.data.msg || '更新失败')
          }
        })
        .catch((err) => {
          this.repetition(err)
        })
    },
    // 重复广告
    repetition(err) {
      const data = err.response.data
      if (data?.data?.repeatId) {
        this.$confirm(`${err.message}`, {
          showConfirmButton: data.data.shopFlag ? false : true,
          cancelButtonText: '去查看',
          confirmButtonText: '继续配置',
          showClose: false
        })
          .then(() => {
            this.submitAd(0)
          })
          .catch(() => {
            const toTaskLog = this.$router.resolve({
              name: 'AdvertisingMerchantList',
              query: {
                repeatData: encodeURIComponent(JSON.stringify(data.data || {}))
              }
            })
            window.open(toTaskLog.href, '_blank')
          })
      }
    },
    // 返回
    goBack() {
      sessionStorage.setItem('adIdOld', '')
      this.$store.dispatch('resetAdDetail')
      this.$refs.SelectAdPlan.initData({})
      this.nextStatus = false
      if (this.isShopType) {
        return this.$router.go(-1)
      }
      this.$router.push({
        name: 'AdvertisingMerchantList'
      })
    },
    // 广告方及计划 下一步
    updateNext(status) {
      if (this.nextStatus && status)
        return this.$refs.SelectAdPlacement.resetData()
      this.nextStatus = status
      if (!status) {
        this.$store.dispatch('changeAdDetailShow', false)
      }
    }
  }
}
</script>

<style lang="scss">
.ad-details-config {
  padding: 0 20px 0 10px;
  .title {
    margin: 20px 0;
    span {
      font-size: 21px;
      font-weight: 600;
    }
    .line {
      background-color: #c7c7c7;
      width: calc(100% - 140px);
      position: relative;
      left: 140px;
      bottom: 10px;
    }
  }
  .submit-config {
    margin: 10px 0 50px 50px;
  }
  .line {
    height: 1px;
    background-color: #dedede;
  }
  .margin-bottom {
    margin-bottom: 22px;
    width: 75%;
  }
}
</style>
