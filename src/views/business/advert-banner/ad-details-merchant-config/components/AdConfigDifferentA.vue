<template>
  <div class="ad-config-different_A">
    <el-form ref="form" :model="detailsForm" label-width="130px">
      <el-form-item label="广告名称" required>
        <el-input v-model="detailsForm.adName" clearable style="width: 200px" />
      </el-form-item>
      <div class="inarow">
        <el-form-item label="类型" required>
          <el-select
            v-model="detailsForm.linkType"
            clearable
            placeholder="请选择类型"
            @change="changeLinkType"
          >
            <el-option
              v-for="(value, index) in linkTypeEnum"
              :key="index"
              :label="value.name"
              :value="value.linkType"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="
            [26, 40].includes(detailsForm.linkType) ? '关联ID/链接' : '链接'
          "
        >
          <el-input
            v-model="detailsForm.linkUrl"
            clearable
            style="width: 350px"
          />
        </el-form-item>
      </div>
      <el-form-item label="图片" required>
        <img :src="detailsForm.coverImage" alt="" class="imgsize" />
        <uploadImg @getImgData="getImgData" />
        <span>图片规格：{{ ImgSize }}</span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import uploadImg from '../../ad-details-config/components/uploadImg.vue'
import { getLinkTypeList } from '@/api/advertModule'
export default {
  name: 'AdConfigDifferentA',
  components: {
    uploadImg
  },
  data() {
    return {
      detailsForm: {
        adName: '', // 广告名称
        linkType: '', // 类型
        linkUrl: '', // 链接或id
        coverImage: '' // 广告封面图片
      },
      adProjectType: '', // 广告方类型
      adProjectTypeEnum: {},
      linkTypeEnum: [],
      campaignList: [], // 计划列表
      typeId: '',
      clientPage: '',
      linkCarShow: '', // 展示选中的车型
      linkUrlExtShow: '', // 展示显示的经销商
      shopList: [], // 获取的经销商列表
      linkUrlExtList: [] // 经销商列表
    }
  },
  computed: {
    // 图片大小
    ImgSize() {
      const flag = [187, 188].includes(this.clientPage)
      if (flag) {
        const ImgSizeEnum = {
          187: '500*520',
          188: '广告展示尺寸：702*140  广告制图尺寸：750*140'
        }
        return ImgSizeEnum[this.clientPage]
      }
      return '广告展示尺寸：690*200  广告制图尺寸：750*200'
    }
  },
  watch: {
    // 车型数据空时
    linkCarShow(val) {
      if (!val) {
        this.detailsForm.linkUrl = ''
        this.detailsForm.linkUrlName = ''
        this.linkUrlExtShow = '' // 展示显示的经销商
        this.detailsForm.linkUrlExt = '' // 展示的经销商数据
        this.linkUrlExtList = []
      }
    }
  },
  mounted() {
    this.detailsForm = this.$store.state.adConfig.adDetailData || {}
    this.typeId = this.detailsForm.adTypeId || ''
    this.clientPage = this.detailsForm.clientPage || ''
    if (this.detailsForm.linkType === 24) {
      // 询价页面单独设置
      this.linkCarShow = this.detailsForm.linkUrlName || ''
      this.linkUrlExtList = this.detailsForm.linkUrlExt
        ? JSON.parse(this.detailsForm.linkUrlExt)
        : ''
    } else {
      this.linkUrlExtShow = this.detailsForm.linkUrlName || ''
    }
    this.getLinkTypeList()
    this.getAdType()
  },
  methods: {
    // 必选
    requiredItem() {
      if (!this.detailsForm.adName) {
        return this.$message.error('请填写广告名称')
      }
      if (!this.detailsForm.linkType && this.linkTypeShow) {
        if (this.detailsForm.linkType !== 0) {
          return this.$message.error('请选择类型')
        }
      }
      if (!this.detailsForm.coverImage) {
        return this.$message.error('请上传图片')
      }
      $emit(this, 'requiredItem')
    },
    // 获取广告方类型
    getAdType() {
      // outside 内外部 1:内部 2:外部 3:内外部
      const outside = this.detailsForm.outside || 3
      if (outside === 1) {
        this.adProjectTypeEnum = { 厂商: 1, 经销商: 2, 非车企: 3, 摩托范: 5 }
      }
      if (outside === 2) {
        this.adProjectTypeEnum = { 第三方: 4 }
      }
      if (outside === 3) {
        this.adProjectTypeEnum = {
          厂商: 1,
          经销商: 2,
          非车企: 3,
          第三方: 4,
          摩托范: 5
        }
      }
      this.adProjectType = this.detailsForm.orgType
      this.$store.dispatch('changeAdType', this.adProjectType || '')
    },
    // 获取广告跳转类型列表
    getLinkTypeList() {
      getLinkTypeList({
        typeId: this.detailsForm.adTypeId || '',
        advertPlatform: 2
      }).then((res) => {
        if (res.data.code === 0) {
          this.linkTypeEnum = res.data.data || []
          if (this.detailsForm.linkType) {
            this.setPageSelect(this.linkTypeEnum)
          }
        }
      })
    },
    changeLinkType() {
      this.detailsForm.linkUrl = ''
    },
    setPageSelect(data) {
      if (data) {
        const linkTypeArr = data.map((_) => {
          return _.linkType
        })
        if (!linkTypeArr.includes(this.detailsForm.linkType)) {
          this.detailsForm.pageSelect = this.detailsForm.linkType
          this.detailsForm.linkType = 0
        }
      }
    },
    // 返回图片数据
    getImgData(imgOrgUrl) {
      console.log(imgOrgUrl)
      this.detailsForm.coverImage = imgOrgUrl
    }
  },
  emits: ['requiredItem']
}
</script>

<style lang="scss">
.ad-config-different_A {
  .inarow {
    display: flex;
  }

  .imgsize {
    max-width: 500px;
  }
}
</style>
