<template>
  <div class="ad-config-different_B">
    <el-form ref="form" :model="detailsForm" label-width="130px">
      <el-form-item label="店铺" required>
        <el-select
          v-model="detailsForm.shopRegisterType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="(value, index) in shopTypeList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="店铺类型" required>
        <el-checkbox
          :indeterminate="typeIsIndeterminate"
          v-model="typeCheckAll"
          @change="typeCheckAllChange"
          class="checkbox-style"
          >全选
        </el-checkbox>
        <el-checkbox-group
          v-model="typeChecked"
          class="brand-list"
          @change="typeCheckedChange"
        >
          <el-checkbox label="1">售新车会员</el-checkbox>
          <el-checkbox label="2">售新车过期未续费会员</el-checkbox>
          <el-checkbox label="10">售新车会员（已开通年包）</el-checkbox>
          <el-checkbox label="11">售新车会员（未开通年包）</el-checkbox>
          <el-checkbox label="12">
            售新车会员（会员有效-年包已过期）
          </el-checkbox>
          <br />
          <el-checkbox label="3">二手车会员</el-checkbox>
          <el-checkbox label="4">二手车过期未续费会员</el-checkbox>
          <el-checkbox label="13">未开通过二手车会员</el-checkbox>
          <br />
          <el-checkbox label="5">驾考会员</el-checkbox>
          <el-checkbox label="6">驾考过期未续费会员</el-checkbox>
          <br />
          <el-checkbox label="7">租车会员</el-checkbox>
          <el-checkbox label="8">租车过期未续费会员</el-checkbox>
          <br />
          <el-checkbox label="9">未开通会员</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="用户角色" required>
        <el-checkbox
          :indeterminate="memberIndeterminate"
          v-model="memberCheckAll"
          @change="memberCheckAllChange"
          class="checkbox-style"
          >全选
        </el-checkbox>
        <el-checkbox-group
          v-model="memberChecked"
          class="brand-list"
          @change="memberCheckedChange"
        >
          <el-checkbox label="1">管理员</el-checkbox>
          <el-checkbox label="2">店长</el-checkbox>
          <el-checkbox label="3">店员</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <div class="title" v-if="adLocation">
        <span>广告详情配置</span>
        <div class="line"></div>
      </div>
      <el-form-item v-if="adLocation" label="广告位置" required>
        <el-select
          v-model="detailsForm.position"
          placeholder="请选择"
          style="width: 120px; margin-right: 10px"
        >
          <el-option
            v-for="(value, index) in positionEnum"
            :key="index"
            :label="value"
            :value="value"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { $emit } from '../../../../../utils/gogocodeTransfer'
import { laterPickerOptions } from '@/utils/configData'
export default {
  name: 'AdConfigDifferentB',
  components: {},
  data() {
    return {
      pickerOptions: laterPickerOptions,
      clientPage: '',
      detailsForm: {},
      positionEnum: [], // 广告位置枚举
      shopTypeList: {
        全部: 0,
        新店铺: 1,
        老店铺: 2
      },
      oldTypeList: [
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        '10',
        '11',
        '12',
        '13'
      ], // 店铺类型
      typeCheckAll: false, // 店铺类型掐全选
      typeChecked: [], // 店铺类型选择
      typeIsIndeterminate: true, // 店铺类型实现全选的效果
      oldMemberList: ['1', '2', '3'], // 用户角色
      memberCheckAll: false, // 用户角色全选
      memberChecked: [], // 用户角色选择
      memberIndeterminate: true // 用户角色实现全选的效果
    }
  },
  computed: {
    // 控制显示-广告位置
    adLocation() {
      const pageArr = [187] // 187, 商家版-首页-弹窗广告
      const falg = !pageArr.includes(this.clientPage)
      return falg
    }
  },
  mounted() {
    this.detailsForm = this.$store.state.adConfig.adDetailData || {}
    this.clientPage = this.detailsForm.clientPage || ''
    this.memberChecked =
      this.detailsForm.userRole && this.detailsForm.userRole.length
        ? this.detailsForm.userRole.split(',')
        : []
    this.memberCheckedChange(this.memberChecked)
    this.typeChecked =
      this.detailsForm.shopMemberType && this.detailsForm.shopMemberType.length
        ? this.detailsForm.shopMemberType.split(',')
        : []
    this.typeCheckedChange(this.typeChecked)
    if (this.detailsForm.positionMax) {
      const getArr = (nums) => {
        return Array.from(new Array(nums), (_, i) => ++i)
      }
      this.positionEnum = getArr(this.detailsForm.positionMax)
    }
  },
  methods: {
    // 类型全选
    typeCheckAllChange(val) {
      this.typeCheckAll = val
      this.typeChecked = val ? this.oldTypeList : []
      this.typeIsIndeterminate = false
    },
    // 类型单选
    typeCheckedChange(value) {
      const checkedCount = value.length
      this.typeCheckAll = checkedCount === this.oldTypeList.length
      this.typeIsIndeterminate =
        checkedCount > 0 && checkedCount < this.oldTypeList.length
    },
    // 类型全选
    memberCheckAllChange(val) {
      this.memberCheckAll = val
      this.memberChecked = val ? this.oldMemberList : []
      this.memberIndeterminate = false
    },
    // 用户角色单选
    memberCheckedChange(value) {
      const checkedCount = value.length
      this.memberCheckAll = checkedCount === this.oldMemberList.length
      this.memberIndeterminate =
        checkedCount > 0 && checkedCount < this.oldMemberList.length
    },
    // 必选
    requiredItem() {
      const me = this
      if (!me.typeChecked.length && !me.typeIsIndeterminate) {
        return me.$message.error('请选择店铺类型')
      }
      if (!me.memberChecked.length && !me.memberIndeterminate) {
        return me.$message.error('请选择用户角色')
      }
      if (me.positionEnum.length && !me.detailsForm.position && me.adLocation) {
        return me.$message.error('请选择广告位置')
      }
      me.detailsForm.userRole = me.memberChecked.join(',')
      me.detailsForm.shopMemberType = me.typeChecked.join(',')
      $emit(this, 'requiredItem')
    }
  },
  emits: ['requiredItem']
}
</script>

<style lang="scss">
.ad-config-different_B {
  .inarow {
    display: flex;
  }

  .imgsize {
    max-width: 500px;
  }

  .checkbox-style {
    margin-right: 30px;
  }
}
</style>
