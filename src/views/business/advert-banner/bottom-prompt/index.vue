<template>
  <div v-loading="loading" style="margin: 20px 20px 0">
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <el-form-item label="标题">
        <el-input
          v-model="ruleForm.name"
          type="text"
          placeholder="请选择标题"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="ruleForm.status">
          <el-option
            v-for="(value, index) in statusList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="活动时间" label-width="80px">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          :disabled-date="pickerOptions && pickerOptions.disabledDate"
          :cell-class-name="pickerOptions && pickerOptions.cellClassName"
          v-model="daterange"
          style="width: 400px"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="至"
          start-placeholder="发布开始日期"
          end-placeholder="发布结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search(1)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
        <el-button type="primary" @click="add()">新增</el-button>
        <el-button :loading="refreshLoading" type="warning" @click="refresh()"
          >刷新缓存</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      ref="articleList"
      :data="dataList"
      row-key="articleList"
      border
      style="width: 100%"
      @row-dblclick="rowDoubleClick"
    >
      <el-table-column prop="id" label="ID" align="center" width="80" />
      <el-table-column prop="name" label="主标题" align="center" />
      <el-table-column prop="subheading" label="副标题" align="center" />
      <el-table-column prop="location" label="展示位置" align="center">
        <template v-slot="scope">
          <span>{{ convertBannerLocation[scope.row.location] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template v-slot="scope">
          <el-switch
            v-model="scope.row.status"
            @change="changeSwitch(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="minimumVersion" label="适合版本" align="center" />
      <el-table-column prop="begindate" align="center" label="开始时间">
        <template v-slot="scope">{{
          scope.row.begindate ? $filters.timeFullS(scope.row.begindate) : ''
        }}</template>
      </el-table-column>
      <el-table-column prop="enddate" align="center" label="结束时间">
        <template v-slot="scope">{{
          scope.row.enddate ? $filters.timeFullS(scope.row.enddate) : ''
        }}</template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-sizes="[10, 20, 50]"
      :total="total"
      background
      layout="total, sizes, prev, pager, next, jumper"
      class="el-pagination-center"
      @size-change="handleSizeChange"
      @current-change="currentChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { deepCopy } from '@/utils'
import { timeFullS } from '@/filters'
import { getBannerList, saveBanner, clearShopCache } from '@/api/advertModule'
import { pickerOptions } from '@/utils/configData'
import { bannerLocation, frequencyList } from '@/utils/enum'
import { convertKeyValueEnum } from '@/utils/convert'
import { recordOldData, recordBeforeAlter } from '@/utils/enum/logData'
export default {
  data() {
    return {
      pickerOptions: pickerOptions,
      refreshLoading: false,
      loading: false,
      page: 1,
      total: 0,
      limit: 10,
      // 筛选列表
      ruleForm: {
        name: '', // 名称
        status: '', // 状态
        beginDate: '', // 开始时间
        endDate: '', // 结束时间
        location: '16'
      },
      statusList: {
        有效: '1',
        无效: '0'
      },
      convertBannerLocation: convertKeyValueEnum(bannerLocation),
      convertFrequencyList: convertKeyValueEnum(frequencyList),
      dataList: [],
      dayjs
    }
  },
  name: 'BottomPrompt',
  components: {},
  computed: {
    daterange: {
      get() {
        if (this.ruleForm.beginDate && this.ruleForm.endDate) {
          return [this.ruleForm.beginDate, this.ruleForm.endDate]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginDate = value[0]
          this.ruleForm.endDate = value[1]
        } else {
          this.ruleForm.beginDate = ''
          this.ruleForm.endDate = ''
        }
      }
    }
  },
  activated() {
    this.search()
    sessionStorage.setItem('menu', 'S10404')
  },
  methods: {
    search() {
      const me = this
      me.loading = true
      const postData = {
        page: me.page,
        limit: me.limit,
        ...me.ruleForm
      }
      getBannerList(postData)
        .then((response) => {
          if (response.status === 200) {
            const data = response.data
            recordOldData(data.list)
            data.list.map((_) => {
              _.status = _.status !== '0'
            })
            me.dataList = data.list
            me.total = data.total
            me.loading = false
          } else {
            me.loading = false
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.loading = false
          me.$message.error(err.message || '获取列表失败')
        })
    },
    add() {
      this.$router.push({
        name: 'BottomBoxDetails'
      })
    },
    rowDoubleClick(data) {
      this.$router.push({
        name: 'BottomBoxDetails',
        query: {
          id: data && data.bannerid
        }
      })
    },
    // 变更查询个数
    handleSizeChange(limit) {
      this.limit = limit
      this.search(this.page)
    },
    // 变更页签
    currentChange(page) {
      this.search(page)
    },
    changeSwitch(data) {
      const me = this
      const postData = deepCopy(data)
      postData.status = postData.status ? '1' : '0'
      delete postData.createtime
      delete postData.updatetime
      postData.beginDateStr = timeFullS(Math.floor(new Date(data.begindate)))
      postData.endDateStr = timeFullS(Math.floor(new Date(data.enddate)))
      recordBeforeAlter(postData, 'id')
      saveBanner(postData).then((response) => {
        me.loading = false
        if (response.data.code === 0) {
          me.$message.success(`修改成功`)
        } else {
          me.$message.error(response.data.msg)
          postData.status = !postData.status
        }
      })
    },
    resetForm() {
      this.ruleForm = {
        name: '', // 名称
        status: '', // 状态
        beginDate: '', // 开始时间
        endDate: '', // 结束时间
        location: '16'
      }
      this.search()
    },
    // 刷新缓存
    refresh() {
      const me = this
      this.$confirm('此操作将刷新banner缓存, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          me.refreshLoading = true
          clearShopCache({
            type: 'homebanner',
            _dc: Math.floor(new Date())
          })
            .then((response) => {
              if (response.data.code === 0) {
                this.$message.success('刷新成功')
              }
            })
            .finally((_) => {
              me.refreshLoading = false
            })
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }
}
</script>
