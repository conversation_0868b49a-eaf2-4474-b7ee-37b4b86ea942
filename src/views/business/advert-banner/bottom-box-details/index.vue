<template>
  <div v-loading="loading" style="margin: 20px 20px 0">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="使用版本" required>
        <el-select
          v-model="ruleForm.minimumVersion"
          placeholder="请选择使用版本"
          clearable
        >
          <el-option
            v-for="(value, index) in versionDataList"
            :key="index"
            :label="value.appVersion"
            :value="value.appVersion"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="平台" required>
        <el-select v-model="ruleForm.platformId">
          <el-option
            v-for="(value, index) in platformList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="展示位置" required>
        <el-select v-model="ruleForm.location">
          <el-option
            v-for="(value, index) in locationList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="头图" required>
        <el-upload
          :show-file-list="false"
          :http-request="httpRequest"
          :on-success="onSuccess"
          name="upfile"
          style="display: inline-block"
          class="avatar-uploader"
          action
        >
          <img
            v-if="ruleForm.picture"
            :src="ruleForm.picture"
            style="max-width: 400px; max-height: 400px"
            alt=""
          />
          <el-button type="primary" style="margin-left: 20px"
            >上传图片</el-button
          >
        </el-upload>
      </el-form-item>
      <el-form-item label="主标题" required>
        <el-input
          v-model="ruleForm.name"
          maxlength="20"
          placeholder="请输入主标题"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="副标题">
        <el-input
          v-model="ruleForm.subheading"
          type="textarea"
          maxlength="30"
          placeholder="请输入副标题"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="跳转页面" prop="type">
        <el-select v-model="ruleForm.type">
          <el-option
            v-for="(value, index) in lingTypeList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-switch v-model="ruleForm.status" />
      </el-form-item>
      <el-form-item label="有效期开始时间" required>
        <el-date-picker
          v-model="ruleForm.beginDateStr"
          type="datetime"
          value-format="x"
          placeholder="选择日期时间"
        />
      </el-form-item>

      <el-form-item label="有效期结束时间" required>
        <el-date-picker
          v-model="ruleForm.endDateStr"
          type="datetime"
          value-format="x"
          placeholder="选择日期时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm()">保存</el-button>
        <el-button type="danger" @click="goBack()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { deepCopy } from '@/utils'
import { timeFullS } from '@/filters'
import { getBannerDetailById, saveBanner } from '@/api/advertModule'
import { GetVersionList } from '@/api/system'
import { bannerLocation } from '@/utils/enum'
import { pickerOptions } from '@/utils/configData'
import { convertKeyValueEnum } from '@/utils/convert'
import { mapGetters } from 'vuex'
import { batchRecordBeforeAlter } from '@/utils/enum/logData'
export default {
  name: 'BottomBoxDetails',
  components: {},
  data() {
    return {
      id: '',
      loading: false,
      pickerOptions: pickerOptions,
      ruleForm: {}, // 筛选列表
      typeList: {
        链接: 'link',
        签到页: 'my_energy',
        活动: 'activity',
        短话题: 'short_topic',
        文章详情: 'essay_detail',
        能量商城: '0',
        有赞商城: 'yz_shop',
      },
      platformList: {
        全部: 'ANDROID,IOS',
        ios: 'IOS',
        android: 'ANDROID',
      },
      lingTypeList: {
        选车页: 'carport_index',
        用车页: 'use_motor_index',
        我的页: 'user_index',
      },
      locationList: {
        首页: '16',
      },
      convertUserTypeList: {},
      convertBannerLocation: convertKeyValueEnum(bannerLocation),
      cityList: [],
      versionDataList: [],
      rules: {},
    }
  },
  computed: {
    ...mapGetters(['uid']),
  },
  activated() {
    this.versionDataList = []
    this.getVersionList()
    this.convertUserTypeList = convertKeyValueEnum(this.userTypeList)
    this.$route.query && this.$route.query.id ? this.getData() : this.initData()
    this.id = this.$route.query && this.$route.query.id
    sessionStorage.setItem('menu', 'S10404')
  },
  methods: {
    // 获取版本列表
    getVersionList(paramsObj) {
      const me = this
      const requestParams = {
        limit: 30,
        page: 1,
      }
      const code = '3020' // 确认版本
      GetVersionList(requestParams).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data
          data.list.map(function (value, index) {
            let version = value.appVersion.replace(/[.]/g, '')
            version = version.length === 3 ? version + '0' : version
            if (version >= code) {
              me.versionDataList.push(value)
            }
          })
        }
      })
    },
    initData() {
      this.ruleForm = {
        minimumVersion: '', // 版本
        platformId: 'ANDROID,IOS',
        location: '16', // 展示位置
        picture: '', // 图片
        name: '', // 主标题
        subheading: '', // 副标题
        type: '', // 跳转页面
        status: true, // 状态
        beginDateStr: '', // 开始时间
        endDateStr: '', // 结束时间
      }
    },
    getData() {
      const me = this
      me.loading = true
      getBannerDetailById({
        bannerid: me.$route.query.id,
      })
        .then((response) => {
          me.loading = false
          if (response.status === 200) {
            me.ruleForm = response.data.data
            batchRecordBeforeAlter(me.ruleForm, me.$route.query.id)
            Object.values(me.locationList).map(function (value, index) {
              me.ruleForm.location =
                me.ruleForm.location === parseInt(value)
                  ? Object.keys(me.locationList)[index]
                  : me.ruleForm.location
            })
            if (
              me.ruleForm.platformId === '1' ||
              me.ruleForm.platformId === '2'
            ) {
              me.ruleForm.platformId =
                me.ruleForm.platformId === '1' ? 'ANDROID' : 'IOS'
            } else {
              me.ruleForm.platformId = 'ANDROID,IOS'
            }
            me.ruleForm.beginDateStr = new Date(me.ruleForm.beginDateStr)
            me.ruleForm.endDateStr = new Date(me.ruleForm.endDateStr)
            me.ruleForm.status = me.ruleForm.status === '1'
          } else {
            me.loading = false
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.loading = false
          me.$message.error(err.message || '获取列表失败')
        })
    },
    // 改变打开失效
    setShowTime(type, data) {
      const me = this
      if (type === 'down' && data.showTime - 1 < 0) {
        return me.$message.error('最小为1')
      }
      type === 'down' ? data.showTime-- : data.showTime++
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      this.$oss.ossUploadImage(option)
    },
    onSuccess(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        this.ruleForm['picture'] = res.imgOrgUrl
        return
      } else {
        this.$notify.error({
          title: '上传错误',
        })
      }
    },
    // 提交
    submitForm() {
      const me = this
      if (!me.ruleForm.minimumVersion) {
        return me.$message.error('请输入版本')
      }
      if (!me.ruleForm.platformId) {
        return me.$message.error('请选择展示平台')
      }
      if (!me.ruleForm.location) {
        return me.$message.error('请选择展示位置')
      }
      if (!me.ruleForm.picture) {
        return me.$message.error('请上传封面')
      }
      if (!me.ruleForm.name) {
        return me.$message.error('请填写名称')
      }
      if (!me.ruleForm.beginDateStr) {
        return me.$message.error('请填写开始时间')
      }
      if (!me.ruleForm.endDateStr) {
        return me.$message.error('请填写结束时间')
      }
      if (me.ruleForm.beginDateStr > me.ruleForm.endDateStr) {
        return me.$message.error('开始时间不得大于结束时间')
      }
      me.loading = true
      const postData = deepCopy(me.ruleForm)
      delete postData.createtime
      delete postData.updatetime
      delete postData.platformAndroid
      delete postData.platformIOS
      delete postData.platformPC
      delete postData.platformWAP
      postData.status = postData.status ? '1' : '0'
      postData.beginDateStr = timeFullS(
        Math.floor(new Date(me.ruleForm.beginDateStr))
      )
      postData.endDateStr = timeFullS(
        Math.floor(new Date(me.ruleForm.endDateStr))
      )
      const tip = postData.bannerid ? '编辑' : '添加'
      saveBanner(postData).then((response) => {
        if (response.data.code === 0) {
          me.$message.success(`${tip}成功`)
          me.loading = false
          setTimeout(() => {
            me.$router.go(-1)
          }, 2000)
        } else {
          me.$message.error(response.data.msg)
          me.loading = false
        }
      })
    },
    // 取消
    goBack() {
      this.$router.go(-1)
    },
  },
}
</script>
