<template>
  <div v-loading="loading">
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      :title="title"
      class="piecesBackpack"
      width="800px"
      center
    >
      <el-table
        ref="userDataList"
        :data="listData"
        highlight-current-row
        border
        height="600"
        row-key="userDataList"
        style="width: 100%; overflow-y: auto"
      >
        <el-table-column prop="operateTime" label="操作时间" align="center" />
        <el-table-column prop="operateUserName" label="操作人" align="center" />
        <el-table-column prop="operateType" label="操作类型" align="center" />
        <el-table-column prop="remark" label="操作内容" align="center" />
      </el-table>
      <el-pagination
        v-model:current-page="page"
        :page-size="20"
        :page-sizes="[20, 40, 60]"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        class="el-pagination-center"
        @size-change="currentChange"
        @current-change="currentChange"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  getAdvertiserManageOperateLog,
  getCampaignManageOperateLog,
  getAdAdvertManageOperateLog
} from '@/api/advertModuleNew'
export default {
  name: 'SeeLog',
  components: {},
  props: {},
  data() {
    return {
      loading: false,
      dialogVisible: false,
      listData: [], // 数据列表
      activeData: {}, // 当前活动数据
      page: 1,
      total: 0,
      title: '操作记录',
      urlList: {
        1: getAdvertiserManageOperateLog,
        2: getCampaignManageOperateLog,
        3: getAdAdvertManageOperateLog
      }
    }
  },
  created() {},
  methods: {
    init(data) {
      this.dialogVisible = true
      this.loading = true
      this.activeData = data
      this.page = 1
      this.getData()
    },
    // 获取数据
    getData() {
      const me = this
      me.urlList[me.activeData.urlType]({
        id: me.activeData.id,
        page: me.page,
        limit: 20
      })
        .then((res) => {
          if (res.data.code === 0) {
            me.listData = (res.data.data && res.data.data.listData) || []
            me.total = (res.data.data && res.data.data.total) || 0
          } else {
            me.$message.error(res.data.msg || '查看失败')
          }
          me.loading = false
        })
        .finally((_) => {
          me.loading = false
        })
    },
    // 变更页签
    currentChange(page) {
      this.page = page
      this.getData()
    }
  }
}
</script>
