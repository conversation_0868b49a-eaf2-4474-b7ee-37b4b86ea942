<template>
  <div v-loading="loading" style="margin: 20px 20px 0">
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <SeletedUser
        ref="selectUser"
        :name="'用户名'"
        :placeholder="'请输入关键词'"
        @sendData="setUid"
      />
      <el-form-item label="广告权限">
        <el-select v-model="ruleForm.status">
          <el-option
            v-for="(value, index) in jurisdictionList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search(1)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="articleList"
      :data="dataList"
      row-key="articleList"
      border
      style="width: 100%"
    >
      <el-table-column prop="uid" label="用户ID" align="center" width="80" />
      <el-table-column prop="userName" label="用户名" align="center" />
      <el-table-column label="广告权限" align="center">
        <template v-slot="scope">
          <span>{{ convertJurisdictionList[scope.row.status] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="location" label="操作" align="center">
        <template v-slot="scope">
          <el-button
            v-if="scope.row.status === 2"
            type="primary"
            size="small"
            @click="updataStatus('1', scope.row.uid)"
            >恢复权限</el-button
          >
          <el-button
            v-if="scope.row.status === 1"
            type="danger"
            size="small"
            @click="colseJurisdiction(scope.row.uid, '')"
            >关闭权限</el-button
          >
        </template>
      </el-table-column>
      <el-table-column align="center" label="首次开通时间">
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.createTime)
        }}</template>
      </el-table-column>
      <el-table-column align="center" label="最近一次操作时间">
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.updateTime)
        }}</template>
      </el-table-column>
      <el-table-column prop="reason" label="关闭权限原因" align="center" />
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-sizes="[20, 50]"
      :size="limit"
      :total="total"
      background
      layout="total, sizes, prev, pager, next, jumper"
      class="el-pagination-center"
      @size-change="handleSizeChange"
      @current-change="currentChange"
    />
    <reject-notice
      ref="rejectNotice"
      :title="'拒绝原因'"
      :placeholder="'填写关闭权限原因(必填)'"
      :confirm-title="'确认关闭权限并提交'"
      @confirmRejection="confirmRejection"
    />
  </div>
</template>

<script>
import { getAdList, updateStatus } from '@/api/advertModule'
import { convertKeyValueEnum } from '@/utils/convert'
import RejectNotice from '@/components/Notice/rejectNotice.vue'
import SeletedUser from '@/components/SeletedUser/SeletedUser.vue'
import { recordOldData, recordBeforeAlter } from '@/utils/enum/logData'
export default {
  name: 'BottomPrompt',
  components: {
    RejectNotice,
    SeletedUser,
  },
  data() {
    return {
      loading: false,
      page: 1,
      total: 0,
      limit: 20,
      updataUid: '', // 更新id
      ruleForm: {
        uid: '', // 用户id
        status: '', // 状态
      }, // 筛选列表
      jurisdictionList: {
        全部: '',
        已开通: '1',
        小编关闭: '2',
        用户关闭: '0',
      },
      convertJurisdictionList: {},
      dataList: [],
    }
  },
  computed: {},
  activated() {
    this.convertJurisdictionList = convertKeyValueEnum(this.jurisdictionList)
    this.search()
  },
  methods: {
    search() {
      const me = this
      me.loading = true
      const postData = {
        page: me.page,
        limit: me.limit,
        ...me.ruleForm,
      }
      // 获取数据
      getAdList(postData)
        .then((response) => {
          if (response.status === 200) {
            const data = response.data.data
            me.dataList = data.listData
            me.total = data.total
            me.loading = false
            recordOldData(me.dataList)
          } else {
            me.loading = false
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.loading = false
          me.$message.error(err.message || '获取列表失败')
        })
    },
    // 变更查询个数
    handleSizeChange(limit) {
      this.limit = limit
      this.search(this.page)
    },
    // 变更页签
    currentChange(page) {
      this.search(page)
    },
    // 设置返回uid
    setUid(id) {
      this.ruleForm.uid = id
    },
    // 重置
    resetForm() {
      this.ruleForm = {
        uid: '', // 用户id
        status: '', // 状态
      }
      this.$refs.selectUser.clearData()
      this.search()
    },
    // 关闭权限
    colseJurisdiction(uid) {
      this.updataUid = uid
      this.$refs.rejectNotice.init()
    },
    // 确认或取消
    confirmRejection(mes) {
      this.updataStatus('2', this.updataUid, mes)
    },
    updataStatus(type, uid, rejectData) {
      const me = this
      recordBeforeAlter({ uid: uid }, 'uid')
      updateStatus({
        uid: uid,
        status: type,
        reason: rejectData,
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('操作成功')
          me.rejectShowStatus = false
          me.search()
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.content {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 10px;
  display: block;
  width: 100%;
  resize: none;
}
</style>
