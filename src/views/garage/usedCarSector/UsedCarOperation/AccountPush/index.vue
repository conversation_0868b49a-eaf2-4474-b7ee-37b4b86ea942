<template>
  <div v-loading="loading" class="used-form" style="margin: 20px 20px 0">
    <p>
      选择发送账号：
      <el-select
        v-model="userData.name"
        placeholder="请输入品牌名称"
        filterable
        clearable
        @change="setName"
      >
        <el-option
          v-for="item in useList"
          :key="item.uid"
          :label="item.name"
          :value="item.name"
        />
      </el-select>
    </p>
    <p>当前账号：{{ userData.uid }} &ensp;&ensp;&ensp;{{ userData.name }}</p>
    <el-form
      ref="activitySearch"
      :model="ruleForm"
      :inline="true"
      class="activitySearch"
    >
      <el-form-item label="发送时间" label-width="100px">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          v-model="daterange"
          style="width: 400px"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          clearable
          range-separator="至"
          start-placeholder="发送开始日期"
          end-placeholder="发送结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search(1)">查询</el-button>
        <el-button @click="setShowPostStatus()">发送私信</el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="articleList"
      :data="dataList"
      row-key="articleList"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="序号" align="center" width="80" />
      <el-table-column
        prop="content"
        label="私信内容"
        align="center"
        width="180"
      />
      <el-table-column prop="createTime" align="center" label="发布时间">
        <template v-slot="scope">{{
          $filters.timeFullS(scope.row.createTime)
        }}</template>
      </el-table-column>
      <el-table-column
        prop="issuerName"
        label="发布人员"
        align="center"
        width="80"
      />
      <el-table-column label="接收对象" align="center" width="80">
        <template v-slot="scope">{{ scope.row.accepterCount }}人</template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template v-slot="scope">
          <el-button type="primary" link size="small" @click="see(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-sizes="[10, 20, 50]"
      :total="total"
      background
      layout="total, sizes, prev, pager, next, jumper"
      class="el-pagination-center"
      @size-change="handleSizeChange"
      @current-change="currentChange"
    />
    <el-dialog
      v-model="showStatus"
      :close-on-click-modal="false"
      :title="dialogTitle"
      class="dialog-content"
      append-to-body
    >
      <template v-if="showPostStatus">
        <el-form
          ref="ruleFormData"
          :model="ruleFormData"
          :inline="true"
          label-width="100px"
        >
          <el-form-item
            label="用户名："
            label-width="80px"
            required
            style="display: block"
          >
            <Upload-Excel @onSuccess="getData" />
          </el-form-item>
          <el-form-item
            v-if="uids"
            label="展示的uid："
            label-width="90px"
            style="display: block"
          >
            <p class="show-uid">{{ uids }}</p>
          </el-form-item>
          <el-form-item
            label="内容："
            label-width="80px"
            style="display: block"
          >
            <textarea
              v-model="ruleFormData.contenData"
              placeholder="请填写内容"
              class="content"
              cols="30"
              rows="10"
            />
          </el-form-item>
          <el-form-item label="图片:" label-width="80px">
            <template v-if="imgList">
              <div
                v-for="(pic, index) in imgList"
                :key="index"
                class="img-content"
              >
                <img :src="pic" alt />
                <el-icon class="close-icon" @click="handleRemove(index)"
                  ><IconError
                /></el-icon>
              </div>
            </template>
            <el-upload
              :show-file-list="false"
              :http-request="httpRequest"
              :on-success="onSuccessShareImg"
              name="upfile"
              style="display: inline-block; vertical-align: top"
              class="avatar-uploader"
              action
              multiple
            >
              <div class="picture-card">
                <el-icon><IconPlus /></el-icon>
              </div>
            </el-upload>
          </el-form-item>
          <div class="food">
            <el-button type="danger" @click="confirm(false)">取消</el-button>
            <el-button type="success" @click="confirm(true)">确定</el-button>
          </div>
        </el-form>
      </template>
      <template v-else>
        <p class="show-time text-center">
          {{ $filters.timeFullS(showActiveData.createTime) }}
        </p>
        <p class="show-content">{{ showActiveData.content }}</p>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  CircleCloseFilled as IconError,
  Plus as IconPlus
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import { getMessageList, postMessage, getAccountList, postMessageDelete } from '@/api/garage'
import UploadExcel from '@/components/UploadExcel/index.vue'
export default {
  components: {
    UploadExcel,
    IconError,
    IconPlus
  },
  data() {
    return {
      page: 1,
      total: 0,
      limit: 10,
      dialogTitle: '发送私信',
      uids: '',
      loading: false,
      // 蒙层显示状态
      showStatus: false,
      // 是否显示发送数据
      showPostStatus: false,
      postStatus: false,
      // 选中数据
      userData: {},
      // 详情id
      ruleForm: {},
      // 发送数据
      ruleFormData: {},
      excelData: {},
      // 展示的数据
      showActiveData: {},
      dataList: [],
      useList: [],
      oldUseList: [],
      //图片列表
      imgList: [],
      dayjs
    }
  },
  name: 'AccountPush',
  computed: {
    ...mapGetters(['uid']),
    daterange: {
      get() {
        if (this.ruleForm.beginTime && this.ruleForm.endTime) {
          return [this.ruleForm.beginTime, this.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm['beginTime'] = value[0]
          this.ruleForm['endTime'] = value[1]
        } else {
          this.ruleForm.beginTime = ''
          this.ruleForm.endTime = ''
        }
      }
    }
  },
  watch: {},
  activated() {
    const me = this
    me.loading = true
    me.getUserList()
  },
  methods: {
    getUserList() {
      const me = this
      getAccountList().then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data
          if (data.length) {
            me.oldUseList = data
            me.useList = me.oldUseList.map((_) => {
              return {
                name: _.username,
                uid: _.uid
              }
            })
            me.userData = me.useList[0]
            me.search(1)
          } else {
            me.$message.error('未找到发布账号')
          }
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    search(page) {
      const me = this
      me.page = page || me.page
      const beginTime = me.ruleForm.beginTime
        ? Math.round(new Date(me.ruleForm.beginTime))
        : ''
      const endTime = me.ruleForm.endTime
        ? Math.round(new Date(me.ruleForm.endTime))
        : ''
      getMessageList({
        page: me.page,
        limit: me.limit,
        beginTime: beginTime,
        endTime: endTime,
        senderId: me.userData.uid
      })
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            me.total = data.total
            me.dataList = data.list
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 变更查询个数
    handleSizeChange(limit) {
      this.limit = limit
      this.search(this.page)
    },
    // 变更页签
    currentChange(page) {
      this.search(page)
    },
    // 设置发送用户名
    setName(value) {
      this.useList = this.oldUseList.map((_) => {
        return {
          name: _.username,
          uid: _.uid
        }
      })
      this.userData = this.useList.find((_) => {
        return _.name === value
      })
      console.log(this.userData)
      console.log(this.useList)
      this.search(1)
    },
    setShowPostStatus() {
      const me = this
      me.ruleFormData = {}
      me.uids = ''
      me.dialogTitle = '发送私信'
      me.showPostStatus = true
      me.showStatus = true
      this.imgList = []
    },
    // 获取导入Excel后的数据
    getData(data) {
      const excelData =
        data.results &&
        data.results.map((_) => {
          return _.uid
        })
      this.uids = excelData.length ? excelData.join() : ''
    },
    // 发送数据
    confirm(status) {
      const me = this
      if (!status) {
        me.showPostStatus = false
        return (me.showStatus = false)
      }
      this.$tools.debounce(confirmData, 500)()
      function confirmData() {
        if (!me.uids) {
          return me.$message.error('请上传数据')
        }
        if (!me.ruleFormData.contenData && me.imgList && !me.imgList.length) {
          return me.$message.error('请填写内容或上传图片')
        }
        if (me.postStatus) {
          return me.$message.error('正在发送数据，请稍后')
        }
        me.postStatus = true
        postMessage({
          content: me.ruleFormData.contenData,
          acceptorIds: me.uids,
          senderId: me.userData.uid,
          picUrlList: me.imgList.join(',')
        })
          .then((response) => {
            if (response.data.code === 0) {
              me.showStatus = false
              me.$message.success('发送成功')
              me.search(me.page)
            } else {
              me.$message.error(response.data.msg)
            }
          })
          .finally(() => {
            me.postStatus = false
          })
      }
    },
    see(data) {
      const me = this
      me.dialogTitle = '查看私信'
      me.showActiveData = data
      me.showPostStatus = false
      me.showStatus = true
    },
    //分享图
    onSuccessShareImg(res) {
      if (!res) return
      if (res.name) {
        this.imgList.push(res.imgOrgUrl)
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    handleRemove(index) {
      this.imgList.splice(index, 1)
    },
    // 上传图片
    async httpRequest(option) {
      console.log(option, '09')
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 10px;
  display: block;
  min-width: 400px;
  resize: none;
}
.show-uid {
  height: 30px;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.picture-card {
  display: inline-block;
  background-color: #fbfdff;
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  box-sizing: border-box;
  width: 100px;
  height: 100px;
  cursor: pointer;
  line-height: 98px;
  margin: 5px;
  vertical-align: top;
}
.img-content {
  display: inline-block;
  position: relative;
  margin: 5px;
  img {
    width: 100px;
    height: 100px;
    border-radius: 5px;
  }
  .close-icon {
    position: absolute;
    right: 2.5px;
    top: 2.5px;
    font-size: 18px;
  }
}
</style>
