<template>
  <div v-loading="loading" class="contentreview">
    <div style="margin-bottom: 10px">
      <span class="pendingReview takeTenSel">
        待审核的认证车辆: {{ myData.auditingTotal }} &ensp;&ensp;&ensp;
        <el-button class="choose" type="danger" @click="getList('asc')"
          >正取50条</el-button
        >
        <!-- <el-button class="choose" type="warning" @click="getList('desc')">倒取50条</el-button> -->
      </span>
      <p class="my-data-list">
        <span class="is-day-over num">{{ myData.completedTotal }}</span>
        <br />我的完成
      </p>
      <p class="my-data-list">
        <span class="is-day-over num">{{ myData.todayCompletedTotal }}</span>
        <br />今日总完成
      </p>
      <p class="my-data-list">
        <span class="is-month-over num">{{ myData.monthCompletedTotal }}</span>
        <br />本月总完成
      </p>
    </div>
    <el-row :gutter="24" style="min-width: 1530px">
      <el-col :span="8">
        <div>
          <el-table
            ref="table"
            :data="carList"
            class="table"
            highlight-current-row
            row-key="carLists"
            border
            style="width: 100%; overflow-y: auto"
            @row-click="openDetails"
          >
            <el-table-column prop="userId" label="用户ID" align="center" />
            <el-table-column prop="username" label="用户名" align="center" />
            <el-table-column prop="goodName" label="车辆款型" align="center">
              <template v-slot="scope">
                <span>{{
                  scope.row.goodsCarName || scope.row.goodsCarName
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="goodName"
              label="指导价格（元）"
              align="center"
            >
              <template v-slot="scope">
                <span>{{
                  scope.row.goodsPrice ? scope.row.goodsPrice : '暂无价格'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="brandName" label="品牌名称" align="center">
              <template v-slot="scope">
                <span>{{ scope.row.brandName || scope.row.tmpBrandName }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col v-show="showModule" :span="16">
        <div class="Configuration">
          <el-button type="primary" @click="changeStatus(1)"
            >审核通过</el-button
          >
          <el-button type="danger" @click="changeStatus(2)">拒绝</el-button>
          <el-form
            ref="garageDetail"
            :model="garageDetail"
            label-width="100px"
            class="garage-detail"
          >
            <el-form-item align="left" label="裸车价">
              <p style="margin: 0">
                {{ garageDetail.carPrice }} &ensp;&ensp;
                <span
                  v-if="garageDetail.carPrice / garageDetail.goodsPrice >= 1.3"
                  class="is-text-fail"
                  >裸车价高于指导价30%</span
                >
                <span
                  v-if="garageDetail.carPrice / garageDetail.goodsPrice <= 0.7"
                  class="is-text-fail"
                  >裸车价低于指导价70%</span
                >
              </p>
            </el-form-item>
            <el-form-item align="left" label="落地价格">
              <span>{{ garageDetail.realPrice }}</span>
            </el-form-item>
            <el-form-item align="left" label="购买时间">
              <span>{{ $filters.timeFullS(garageDetail.buyDate) }}</span>
            </el-form-item>
            <el-form-item align="left" label="购买地点">
              <span>{{
                garageDetail.province || garageDetail.city
                  ? garageDetail.province + garageDetail.city
                  : ''
              }}</span>
            </el-form-item>
            <el-form-item align="left" label="备注">
              <span>{{ garageDetail.remark }}</span>
            </el-form-item>
            <el-form-item label="发票照片" prop="imgUrl">
              <img
                :src="$filters.replaceImgUrl(garageDetail.invoiceUrl)"
                class="img-content"
                alt
                @click="seeBigImg(garageDetail.invoiceUrl)"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
    <CommonRejectNotice ref="rejectNotice" @confirmRejection="getRejectData" />
    <choose-show-image ref="showImage" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import CommonRejectNotice from '@/components/Notice/commonRejectNotice.vue'
import { replaceImgUrl } from '@/filters'
import {
  getRaisedStatistics,
  getRaisedPriceList,
  postRaisedPull,
  postRaisedAudit
} from '@/api/audit'
export default {
  name: 'MentionCar',
  components: {
    ChooseShowImage,
    CommonRejectNotice
  },
  data() {
    return {
      carList: [],
      status: 0,
      loading: false, // 加载状态
      showModule: false, // 是否显示
      myData: {}, // 我的数据
      ruleForm: {},
      garageDetail: {},
      isModifyStatus: false, // 是否有修改
      isContinuous: true, // 是否连续点击
      submiting: false, // 是否提交中
      isfetch: true // 是否连续取十条
    }
  },
  computed: {
    ...mapGetters(['uid', 'name'])
  },
  watch: {},
  activated() {
    const me = this
    me.loading = true
    me.getAuthInfo()
    me.getAuditList()
  },
  methods: {
    // 大图查看图片
    seeBigImg(link) {
      this.$refs.showImage.init(replaceImgUrl(link))
    },
    // 实时更新总数居
    getAuthInfo() {
      const me = this
      getRaisedStatistics({
        ossUserId: me.uid,
        ossUserName: me.name
      }).then((response) => {
        if (response.data.code === 0) {
          me.myData = response.data.data
        }
      })
    },
    // 拉取数据到池子
    getList(orderType) {
      const me = this
      if (me.carList && me.carList.length) {
        return me.$message.error('还有审核数据，不能操作')
      }
      me.loading = true
      postRaisedPull({
        order: orderType,
        ossUserId: me.uid,
        ossUserName: me.name
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.getAuditList()
            me.getAuthInfo()
            me.showModule = false
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 拉取数据到页面 待审核
    getAuditList() {
      const me = this
      me.loading = true
      getRaisedPriceList({
        status: 0,
        auditUid: me.uid,
        limit: 50,
        page: 1
      })
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data || []
            me.carList = data.listData
            me.openDetails(me.carList[0] || {})
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally(() => {
          me.loading = false
        })
    },
    openDetails(row) {
      const me = this
      me.garageDetail = row
      if (me.isContinuous && row.id) {
        me.isContinuous = false
        me.changeState()
        me.showModule = true
      }
    },
    changeState() {
      const me = this
      setTimeout(() => {
        me.isContinuous = true
      }, 1000)
    },
    changeStatus(status) {
      const me = this
      setTimeout(() => {
        if (status === 1) {
          if (me.garageDetail.carPrice / me.garageDetail.goodsPrice >= 1.3) {
            me.$confirm(
              '当前车型用户提车价已超出指导价30%，确定要通过该条数据吗？',
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )
              .then(() => {
                me.updataStatusData({}, status)
              })
              .catch()
          } else {
            me.updataStatusData({}, status)
          }
        } else {
          return me.$refs.rejectNotice.init(['购车发票内容与车辆不符'])
        }
      }, 50)
    },
    // 拒绝回来的数据
    getRejectData(data) {
      this.updataStatusData(data, 2)
    },
    updataStatusData(rejectData, status) {
      const me = this
      me.loading = true
      postRaisedAudit({
        id: me.garageDetail.id,
        status: status,
        refuseReason: rejectData.auditFailReason || '',
        ossUserId: me.uid,
        ossUserName: me.name
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('操作成功')
            me.getAuditList()
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
        })
        .finally((_) => {
          me.loading = true
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.contentreview {
  margin: 0px 20px 0;
  .my-data-list {
    display: inline-block;
    text-align: center;
    margin-left: 10px;
    .num {
      font-weight: bold;
      font-size: 30px;
    }
    .is-day-over {
      color: green;
    }
    .is-month-over {
      color: blue;
    }
    .is-achievements {
      color: green;
    }
    .is-day-ranking {
      color: blue;
    }
  }
  .garage-detail {
    margin-top: 20px;
    .img-content {
      width: 500px;
      cursor: pointer;
    }
  }
}
</style>
