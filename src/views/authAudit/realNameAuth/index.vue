<template>
  <div v-loading="loading" class="contentreview">
    <div style="margin-bottom: 30px">
      <p class="my-data-list" style="margin-right: 50px">
        <span class="num">{{ myData.waitCount }}</span>
        <br />待审核
      </p>
      <el-button type="danger" @click="getList(0)">正取20条</el-button>
      <!-- <el-button type="warning" @click="getList(1)">倒取20条</el-button> -->
      <p class="my-data-list" style="margin-left: 50px">
        <span class="num">{{ myData.myAuditCount }}</span>
        <br />我的成果
      </p>
      <p class="my-data-list" style="margin-left: 20px">
        <span class="num">{{ myData.todayAuditCount }}</span>
        <br />今日完成
      </p>
    </div>
    <el-row :gutter="24">
      <el-col :span="6">
        <div>
          <el-table
            ref="table"
            :data="userList"
            class="table"
            highlight-current-row
            row-key="carLists"
            border
            style="width: 100%; overflow-y: auto"
            @row-click="openDetails"
          >
            <el-table-column prop="uid" label="用户ID" align="center" />
            <el-table-column prop="userName" label="用户名" align="center" />
            <el-table-column prop="manageName" label="真实姓名" align="center">
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col v-if="showModule" :span="18">
        <div class="Configuration">
          <el-button type="primary" @click="changeStatus('1')"
            >审核通过</el-button
          >
          <el-button
            style="margin-left: 20px"
            type="danger"
            @click="changeStatus('2')"
            >审核不通过</el-button
          >
          <el-form
            ref="chosenUserInfo"
            :model="chosenUserInfo"
            label-width="100px"
            class="user-detail"
          >
            <el-row>
              <el-col :span="12">
                <label>用户信息</label>
                <el-form-item label="用户ID：">
                  <span>{{ chosenUserInfo.uid }}</span>
                </el-form-item>
                <el-form-item label="用户名：">
                  <span>{{ chosenUserInfo.userName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <label>实名认证信息</label>
                <el-form-item label="姓名：">
                  <span>{{ chosenUserInfo.manageName }}</span>
                </el-form-item>
                <el-form-item label="身份证号：">
                  <span>{{ chosenUserInfo.idCard }}</span>
                  <br />
                  <el-button
                    v-if="chosenUserInfo.idCard"
                    type="primary"
                    size="small"
                    @click="seeIdCard(chosenUserInfo.idCard)"
                    >查看身份证号码</el-button
                  >
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="身份证照片面">
              <img
                :src="$filters.replaceImgUrl(chosenUserInfo.certImages[0])"
                class="img-content"
                alt
                @click="seeBigImg(chosenUserInfo.certImages[0])"
              />
            </el-form-item>
            <el-form-item label="身份证国徽面">
              <img
                :src="$filters.replaceImgUrl(chosenUserInfo.certImages[1])"
                class="img-content"
                alt
                @click="seeBigImg(chosenUserInfo.certImages[1])"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
    <choose-show-image ref="showImage" />
    <choose-see-id-card ref="seeIdCard" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import {
  certApplyPullData,
  certApplyStatisticsInfo,
  certApplyWaitAuditList
} from '@/api/audit'
import {
  searchCertApply,
  updateCertApplyStatus
} from '@/api/authenticationManagement'
import { debounce } from 'lodash-es'
import { replaceImgUrl } from '@/filters'
import ChooseSeeIdCard from '@/components/Dialog/ChooseCommonDecrypt.vue'

export default {
  name: 'RealNameAuthAudit',
  components: {
    ChooseShowImage,
    ChooseSeeIdCard
  },
  data() {
    return {
      userList: [],
      loading: false, // 加载状态
      myData: {}, // 我的数据
      ruleForm: {},
      chosenUserInfo: null,
      isModifyStatus: false, // 是否有修改
      isContinuous: true, // 是否连续点击
      submiting: false // 是否提交中
    }
  },
  computed: {
    ...mapGetters(['uid', 'name']),
    showModule() {
      // 是否展示详情
      return this.chosenUserInfo
    }
  },
  async activated() {
    const me = this
    me.loading = true
    me.chosenUserInfo = null
    await Promise.all([me.getAuthInfo(), me.certApplyWaitAuditList()]).finally(
      () => {
        me.loading = false
      }
    )
  },
  methods: {
    // 大图查看图片
    seeBigImg(link) {
      this.$refs.showImage.init(replaceImgUrl(link))
    },
    // 审核统计
    getAuthInfo() {
      certApplyStatisticsInfo()
        .then((response) => {
          this.myData = response.data.data
        })
        .catch(() => {})
    },
    // 获取当前待审核的数据
    async certApplyWaitAuditList() {
      certApplyWaitAuditList()
        .then(async (response) => {
          this.userList = response.data.data
          this.userList &&
            this.userList.length &&
            (await this.openDetails(this.userList[0]))
        })
        .catch(() => {})
    },
    // 拉取数据到池子
    getList(type) {
      const me = this
      if (me.userList && me.userList.length) {
        return me.$message.error('还有审核数据未审核完，请先审核完再拉取新数据')
      }
      me.loading = true
      certApplyPullData({
        size: 20,
        orderType: type // 0:正取 1：倒取
      })
        .then((response) => {
          if (response.data.data.length) {
            this.userList = response.data.data
            setTimeout(() => {
              this.getAuthInfo()
              this.certApplyWaitAuditList()
            }, 1000)
          } else {
            this.$message.warning('没有更多待审核数据')
          }
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
        })
    },
    openDetails: debounce(function (row) {
      searchCertApply({
        category: 9,
        uid: row.uid,
        limit: 1,
        page: 1
      }).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data
          this.chosenUserInfo = data.list[0]
          this.chosenUserInfo.certImages =
            this.chosenUserInfo.certImage.split(',')
        }
      })
    }, 1000),
    // 提交
    changeStatus(status) {
      updateCertApplyStatus({
        id: this.chosenUserInfo.id,
        status
      })
        .then((response) => {
          this.$message.success('操作成功,已发站内信通知用户')
          this.chosenUserInfo = null
          setTimeout(() => {
            this.getAuthInfo()
            this.certApplyWaitAuditList()
          }, 1000) // 审核成功更新统计数据
        })
        .catch(() => {})
    },
    // 查看身份证号码
    seeIdCard(id) {
      this.$refs.seeIdCard &&
        this.$refs.seeIdCard.init({
          decryptContent: id,
          source: 'realNameAuthAudit',
          type: 3, // 1手机号解密，2微信号解密，3，身份证解密，4支付宝
          operateType: 8
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.contentreview {
  padding: 0px 20px;
  .my-data-list {
    display: inline-block;
    text-align: center;
    font-size: 14px;
    .num {
      font-weight: bold;
      font-size: 30px;
      color: #f56c6c;
    }
  }
  .user-detail {
    margin-top: 30px;
    label {
      display: block;
      margin-bottom: 25px;
    }
    .img-content {
      width: 500px;
      cursor: pointer;
    }
  }
}
</style>
