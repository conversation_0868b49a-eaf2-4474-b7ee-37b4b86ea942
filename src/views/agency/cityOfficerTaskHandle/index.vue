<template>
  <div v-loading="loading" class="city-visit_shop-audit">
    <header class="action" style="margin-bottom: 10px">
      <el-form :model="ruleForm" :inline="true">
        <el-form-item label="审核状态">
          <el-select v-model="ruleForm.status" clearable>
            <el-option
              v-for="(value, index) in auditStatusTypes"
              :key="value"
              :label="value"
              :value="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈问题">
          <el-select v-model="ruleForm.errorType" clearable>
            <el-option
              v-for="(value, index) in questionTypes"
              :key="value"
              :label="value"
              :value="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈者ID">
          <el-input
            v-model="ruleForm.uid"
            type="text"
            placeholder="请输入反馈者ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="省份">
          <el-input
            v-model="ruleForm.province"
            type="text"
            placeholder="请输入省份名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="城市">
          <el-input
            v-model="ruleForm.city"
            type="text"
            placeholder="请输入城市名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="商家名称">
          <el-select
            v-model="ruleForm.shopId"
            :remote-method="remoteMethodShop"
            :loading="searchLoading"
            placeholder="请输入经销商名称"
            filterable
            remote
            clearable
            style="width: 200px"
            @clear="clearShopName()"
          >
            <el-option
              v-for="item in shopList"
              :key="item.shopId"
              :label="item.name"
              :value="item.shopId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈时间">
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="createDateRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label=" ">
          <el-checkbox v-model="seeNew">仅查看未建店的新店</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()">查询</el-button>
          <el-button @click="resetForm()">重置</el-button>
        </el-form-item>
      </el-form>
    </header>
    <div class="table-wrapper">
      <el-table
        :data="shopAuditList"
        highlight-current-row
        row-key="shopAuditList"
        border
        height="280"
        style="width: 100%"
        @row-dblclick="skipDetail"
      >
        <el-table-column prop="shopId" label="商家ID" align="center">
          <template #default="{ row }">
            {{ row.shopId }}
            <el-button
              v-if="row.errorType === 4 && row.shopId === 0"
              @click="upDateTaskDetailId(row, true)"
              >{{ row.taskDetailId ? '编辑' : '新增' }}</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="shopName" label="商家名称" align="center" />
        <el-table-column prop="province" label="省份" align="center" />
        <el-table-column prop="city" label="城市" align="center" />
        <el-table-column prop="uid" label="反馈者ID" align="center" />
        <el-table-column prop="userName" label="反馈者姓名" align="center" />
        <el-table-column prop="mobile" label="反馈者手机" align="center" />
        <el-table-column label="反馈内容" align="center">
          <template #default="{ row }">
            <span>{{ questionTypes[row.errorType] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="审核状态" align="center">
          <template #default="{ row }">
            <span>{{ auditStatusTypes[row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="反馈时间" align="center">
          <template #default="{ row }">{{
            $filter.format(row.createTime, 'YYYY-MM-DD HH:mm')
          }}</template>
        </el-table-column>
        <el-table-column prop="auditUser" label="处理人" align="center" />
      </el-table>
      <el-pagination
        v-model:current-page="ruleForm.page"
        :page-size="10"
        :page-sizes="[10, 20, 40, 60]"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        style="text-align: center; margin-top: 20px"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </div>
    <div v-if="shopAuditDetail" class="detail">
      <el-row>
        <el-col v-if="shopAuditDetail.oldShopVO" :span="12">
          <div class="grid-content">
            <div class="content-title">原商家信息</div>
            <div class="grid-title">位置信息</div>
            <div class="grid-info">
              {{ shopAuditDetail.oldShopVO.shopAddress || '-' }}
            </div>
            <div class="grid-title">商铺名称</div>
            <div class="grid-info">
              {{ shopAuditDetail.oldShopVO.shopName || '-' }}
            </div>
            <div class="grid-title">商家图片（周边环境／门头）</div>
            <div class="grid-info">
              <span v-if="!shopAuditDetail.oldShopVO.shopImages.length">-</span>
              <template v-else>
                <viewer :images="shopAuditDetail.oldShopVO.shopImages">
                  <img
                    v-for="(imgUrl, index) in shopAuditDetail.oldShopVO
                      .shopImages"
                    :key="index"
                    :src="imgUrl"
                    class="img-cover"
                    alt=""
                  />
                </viewer>
              </template>
            </div>
          </div>
        </el-col>
        <el-col v-if="shopAuditDetail.exploreVO" :span="12">
          <div class="grid-content">
            <div class="content-title">城市官提交商家信息</div>
            <div class="grid-title">城市官定位信息</div>
            <div class="grid-info">
              {{ shopAuditDetail.exploreVO.locationAddress || '-' }}
            </div>
            <div class="grid-title">商铺名称</div>
            <div class="grid-info">
              {{ shopAuditDetail.exploreVO.shopName || '-' }}
            </div>
            <div class="grid-title">商家图片（周边环境／门头）</div>
            <div class="grid-info img-info">
              <span
                v-if="
                  !shopAuditDetail.exploreVO.shopHeadImg.length &&
                  !shopAuditDetail.exploreVO.shopEnvironmentImg.length
                "
                >-</span
              >
              <template v-else>
                <viewer :images="shopAuditDetail.exploreVO.shopHeadImg">
                  <img
                    v-for="(imgUrl, index) in shopAuditDetail.exploreVO
                      .shopHeadImg"
                    :key="index"
                    :src="imgUrl"
                    class="img-cover"
                    alt=""
                  />
                </viewer>
                <viewer :images="shopAuditDetail.exploreVO.shopEnvironmentImg">
                  <img
                    v-for="(imgUrl, index) in shopAuditDetail.exploreVO
                      .shopEnvironmentImg"
                    :key="index"
                    :src="imgUrl"
                    class="img-cover"
                    alt=""
                  />
                </viewer>
              </template>
            </div>
            <div class="grid-title">商家联系信息</div>
            <div class="grid-info">
              {{ shopAuditDetail.exploreVO.contactInformation || '-' }} &ensp;
              <el-button
                v-if="shopAuditDetail.exploreVO.contactInformation"
                type="primary"
                size="small"
                @click="seeMobile(shopAuditDetail.exploreVO.contactInformation)"
                >查看号码</el-button
              >
            </div>
            <div class="grid-title">问题反馈</div>
            <div class="grid-info">
              商铺信息：{{ questionTypes[shopAuditDetail.exploreVO.errorType] }}
            </div>
            <div class="grid-info">
              位置错误：{{ shopAuditDetail.exploreVO.locationError || '-' }}
            </div>
            <div v-if="shopAuditDetail.exploreVO.mapError" class="grid-info">
              位置错误：商家位置和地图定位不符
            </div>
            <div class="grid-info">
              名称错误：{{ shopAuditDetail.exploreVO.nameError || '-' }}
            </div>
            <div class="grid-info">
              门牌号错误：<img
                v-if="shopAuditDetail.exploreVO.houseNumberError"
                :src="shopAuditDetail.exploreVO.houseNumberError"
                class="img-cover"
              /><span v-else>-</span>
            </div>
            <div class="grid-info">
              其他错误：{{ shopAuditDetail.exploreVO.otherError || '-' }}
            </div>
            <div class="grid-info">
              备注：{{ shopAuditDetail.exploreVO.remark || '-' }}
            </div>
            <div
              v-if="
                shopAuditDetail.exploreVO.errorType === 4 &&
                shopAuditDetail.exploreVO.shopId === 0 &&
                shopAuditDetail.exploreVO.status === 1
              "
              style="margin-top: 20px; text-align: right"
            >
              <el-button type="primary" @click="addNewShop()"
                >创建新店</el-button
              >
            </div>
            <div v-if="shopAuditDetail.canOperate" style="margin-top: 20px">
              <el-button type="primary" @click="postAudit(true)"
                >审核通过</el-button
              >
              <el-button @click="postAudit(false)">审核不通过</el-button>
            </div>
          </div>
        </el-col>
        <el-col
          v-if="!shopAuditDetail.oldShopVO && !shopAuditDetail.exploreVO"
          style="text-align: center"
        >
          暂无详情数据
        </el-col>
      </el-row>
    </div>
    <!-- 新增、作废Dialog -->
    <el-dialog
      v-model="showDialog"
      :close-on-click-modal="false"
      title="回填商家id"
      width="500px"
      @close="upDateTaskDetailId({}, false)"
    >
      <el-form label-width="80px">
        <el-form-item label="商家ID" required>
          <el-input
            v-model="form.shopId"
            type="primary"
            link
            maxlength="11"
            placeholder="请输入商家ID"
            clearable
            style="width: 160px"
            @change="searchShopInfo"
          />
        </el-form-item>
        <el-form-item label="商家名称" required>
          <el-input
            v-model="form.shopName"
            :disabled="selectedShopStatus"
            type="text"
            placeholder="输入商家ID自动获取商家名称"
            clearable
            style="width: 250px"
          />
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="upDateTaskDetailId({}, false)">取 消</el-button>
          <el-button type="primary" @click="confirmAction">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <choose-see-phone ref="seePhone" />
    <choose-show-image ref="showImage" />
  </div>
</template>

<script>
import { deepCopy } from '@/utils'
import { clone } from 'lodash-es'
import { mapGetters } from 'vuex'
import { auditStatusTypes, questionTypes } from '@/utils/enum/city-visit-shop'
// import { convertKeyValueEnum } from '@/utils/convert'
import { forwardPickerOptions } from '@/utils/configData'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import {
  adminCityOfficerListExplore,
  adminCityOfficerExploreDetail,
  adminCityOfficerAuditExploreInfo
} from '@/api/audit'
import {
  GetShopApplyByName,
  saveNewShopId,
  merchantDetail,
  createNewShop
} from '@/api/garage'
import ChooseSeePhone from '@/components/Dialog/ChooseSeePhone.vue'
export default {
  data() {
    return {
      loading: false,
      // 查看新店
      seeNew: false,
      showDialog: false,
      pickerOptions: forwardPickerOptions,
      // 审核状态
      auditStatusTypes: clone(auditStatusTypes),
      // 反馈问题
      questionTypes: clone(questionTypes),
      ruleForm: {},
      form: {
        shopId: '',
        shopName: ''
      },
      total: 0,
      // 列表数据
      shopAuditList: [],
      selectedShopStatus: true,
      searchLoading: false,
      // 模糊查询经销商数据
      shopList: [],
      chosenTaskId: null,
      shopAuditDetail: null
    }
  },
  name: 'CityOfficerTaskHandle',
  components: {
    ChooseSeePhone,
    ChooseShowImage
  },
  computed: {
    ...mapGetters(['name', 'uid']),
    createDateRange: {
      get() {
        if (this.ruleForm.beginTime && this.ruleForm.endTime) {
          return [this.ruleForm.beginTime, this.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginTime = value[0]
          this.ruleForm.endTime = value[1]
        } else {
          this.ruleForm.beginTime = ''
          this.ruleForm.endTime = ''
        }
      }
    }
  },
  activated() {
    this.resetForm()
  },
  methods: {
    // 查询
    search() {
      this.ruleForm.page = 1
      this.getShopAuditList()
    },
    // 获取列表数据
    getShopAuditList() {
      const me = this
      const requestParams = {
        ...this.ruleForm,
        newShopFlag: me.seeNew ? 1 : 0
      }
      me.loading = true

      adminCityOfficerListExplore(requestParams)
        .then((response) => {
          me.shopAuditList = response.data.data
            ? response.data.data.listData
            : []
          me.total = response.data.data ? response.data.data.total : 0
        })
        .catch(() => {})
        .finally(() => {
          me.loading = false
          this.chosenTaskId = null
          this.shopAuditDetail = null
        })
    },
    // 更新每页数量
    sizeChange(pageSize) {
      this.ruleForm.page = 1
      this.ruleForm.limit = pageSize
      this.getShopAuditList()
    },
    // 更新页码
    currentChange(page) {
      this.ruleForm.page = page
      this.getShopAuditList()
    },
    // 重置search 系列
    resetForm() {
      this.shopList = []
      this['ruleForm'] = {
        ...this.ruleForm,
        errorType: '',
        uid: '',
        status: '',
        shopName: '',
        shopId: '',
        province: '',
        city: '',
        beginTime: '',
        endTime: '',
        page: 1, // 页码
        limit: 10
      }
      this.seeNew = false
      this.getShopAuditList()
    },
    searchShopInfo() {
      const me = this
      merchantDetail({
        shopId: me.form.shopId
      })
        .then((response) => {
          if (response.data.code === 0) {
            if (response.data.data === null) {
              me.$message.error('请输入正确的商家ID')
              return
            }
            me.selectedShopStatus = false
            const data = response.data.data
            me.form.shopName = data.shopName
            me.form.address = data.locationAddress
            me.selectedShopStatus = true
          }
        })
        .catch((err) => {
          me.$message.error(err.message)
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 打开详情页面
    skipDetail(item) {
      this.upDateTaskDetailId(item, false)
      this.chosenTaskId = item.taskId
      adminCityOfficerExploreDetail(this.chosenTaskId)
        .then((response) => {
          const auditDetail = response.data.data
          auditDetail.canOperate = ![1, 2].includes(item.status)
          if (auditDetail.oldShopVO) {
            auditDetail.oldShopVO.shopImages = auditDetail.oldShopVO.shopImages
              ? auditDetail.oldShopVO.shopImages.split(',')
              : []
          }
          if (auditDetail.exploreVO) {
            auditDetail.exploreVO.shopHeadImg = auditDetail.exploreVO
              .shopHeadImg
              ? auditDetail.exploreVO.shopHeadImg.split(',')
              : []
            auditDetail.exploreVO.shopEnvironmentImg = auditDetail.exploreVO
              .shopEnvironmentImg
              ? auditDetail.exploreVO.shopEnvironmentImg.split(',')
              : []
          }
          this.shopAuditDetail = auditDetail
        })
        .catch(() => {})
        .finally(() => {
          this.$nextTick(() => {
            window.scrollTo(0, 570)
          })
        })
    },
    // 查询经销商名称索引
    remoteMethodShop(query) {
      this.searchLoading = true
      this.$tools.debounce(() => this.getShopList(query), 300)()
    },
    // 查询经销商
    getShopList(query = '') {
      const me = this
      GetShopApplyByName({
        shopName: query || me.ruleForm.shopName, // 经销商名称
        limit: 100
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.shopList = []
            const result = response.data.data
            result.map(function (value) {
              me.shopList.push({
                name: value.shopName,
                shopId: value.shopId
              })
            })
          }
        })
        .finally(() => {
          me.searchLoading = false
        })
    },
    clearShopName() {
      this.ruleForm.shopId = ''
      this.ruleForm.shopName = ''
    },
    postAudit(isPassed) {
      if (!isPassed) {
        this.$prompt('请填写不通过原因', '', {
          confirmButtonText: '提交',
          cancelButtonText: '关闭',
          inputPattern: /.+/,
          inputErrorMessage: '请填写不通过原因'
        })
          .then(({ value: reason }) => {
            adminCityOfficerAuditExploreInfo({
              status: 2,
              taskId: this.chosenTaskId,
              auditUser: this.name,
              failReason: reason.trim()
            })
              .then(() => {
                this.getShopAuditList()
              })
              .catch((err) => {
                console.log(err)
              })
          })
          .catch(() => {})
      } else {
        adminCityOfficerAuditExploreInfo({
          status: 1,
          taskId: this.chosenTaskId,
          auditUser: this.name
        })
          .then(() => {
            this.getShopAuditList()
          })
          .catch((err) => {
            console.log(err)
          })
      }
    },
    // 更新 id
    upDateTaskDetailId(shop = {}, status) {
      console.log('upDateTaskDetailId')
      this.showDialog = status
      this.form = shop.taskId ? deepCopy(shop) : {}
    },
    // 查看手机号码
    seeMobile(mobile) {
      this.$refs.seePhone &&
        this.$refs.seePhone.init({ mobile: mobile, source: 'reportDetail' })
    },
    // 提交id
    confirmAction() {
      const me = this
      saveNewShopId({
        shopId: me.form.shopId,
        taskDetailId: me.form.id
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('回填成功')
            me.getShopAuditList()
          }
        })
        .catch((err) => {
          me.$message.error(err.message)
        })
        .finally(() => {
          me.showDialog = false
        })
    },
    // 创建新店
    addNewShop() {
      const me = this
      me.$confirm(
        `是否新建${me.shopAuditDetail.exploreVO.shopName}商家店铺?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }
      )
        .then(() => {
          createNewShop({
            taskDetailId: me.shopAuditDetail.exploreVO.id
          })
            .then((response) => {
              if (response.data.code === 0) {
                me.$message.success('新店创建成功')
                me.getShopAuditList()
              }
            })
            .catch((err) => {
              me.$message.error(err.message)
            })
            .finally(() => {
              me.showDialog = false
            })
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 大图查看图片
    seeBigImg(link) {
      this.$refs.showImage.init(link)
    }
  }
}
</script>

<style lang="scss" scoped>
.city-visit_shop-audit {
  padding: 10px 20px 50px;
  .img-cover {
    display: inline-block;
    width: 200px;
    margin-bottom: 20px;
    margin-right: 20px;
  }
  .detail {
    margin-top: 30px;
    .grid-content {
      padding: 0 20px;
      .content-title {
        font-size: 22px;
        margin-bottom: 20px;
      }
      .grid-title {
        margin-top: 15px;
        font-size: 16px;
        font-weight: bold;
      }
      .grid-info {
        display: flex;
        align-items: flex-start;
        color: #aaa;
        margin-top: 10px;
        line-height: 20px;
      }
      .img-info {
        display: block;
      }
    }
  }
}
</style>
