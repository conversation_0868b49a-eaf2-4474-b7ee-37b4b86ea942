<template>
  <el-dialog
    v-model="isShowBindDialog"
    :close-on-click-modal="false"
    width="42%"
    class="bind-view-dialog"
    @close="onCLose"
  >
    <el-row>
      <el-col :span="12">
        <div class="col-name">人员</div>
        <div class="col-list">
          <div
            v-for="user in merchantUsers"
            :key="user.id"
            :class="{ active: user.active }"
            class="col-list_item addhover"
            @click="chooseUser(user.id)"
          >
            {{ user.name }}
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="col-name">经销商</div>
        <div v-show="selectedUserId" class="col-list">
          <div v-if="!merchantShopList.length">暂无数据</div>
          <div v-else>
            <div
              v-for="shop in merchantShopList"
              :key="shop.shopId"
              class="col-list_item"
            >
              <span>{{ shop.shopName }}</span>
              <el-button size="small" @click="unBindMerchant(shop.shopId)"
                >解绑</el-button
              >
            </div>
          </div>
          <el-button
            type="primary"
            size="small"
            style="margin-top: 15px"
            @click="innerVisible = true"
            >绑定经销商</el-button
          >
        </div>
      </el-col>
    </el-row>
    <el-dialog
      v-model="innerVisible"
      :close-on-click-modal="false"
      width="30%"
      title="添加经销商"
      append-to-body
    >
      <div>
        <SerchantMerchant
          v-if="innerVisible"
          @on-update="(shop) => (searchShopId = shop.shopId)"
          @on-clear="searchShopId = ''"
        />
        <el-button type="primary" @click="addBindMerchant">绑定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer'
import SerchantMerchant from '@/components/search-merchant/index.vue'
import agencyService from '@/api/agency'

export default {
  name: 'BindShop',
  components: {
    SerchantMerchant
  },
  data() {
    return {
      merchantUsers: [], // 商务列表
      merchantShopList: [], // 商务绑定的经销商列表
      isShowBindDialog: false,
      innerVisible: false,
      searchShopId: '',
      selectedUserId: ''
    }
  },
  mounted() {
    this.getMerchantUsers()
  },
  methods: {
    show() {
      this.isShowBindDialog = true
      this.merchantShopList = []
      this.selectedUserId = ''
      this.merchantUsers.forEach((user) => {
        user['active'] = false
      })
    },
    // 获取商务列表
    getMerchantUsers() {
      agencyService
        .getMerchantUsers()
        .then((response) => {
          this.merchantUsers = response.data.data
          const ossUserId = this.$store.getters.uid
          const foundUser = this.merchantUsers.find(
            (user) => user.id === ossUserId
          )
          if (foundUser) {
            $emit(this, 'on-get-info', foundUser)
          }
        })
        .catch(() => {})
    },
    chooseUser(id) {
      this.merchantUsers.forEach((user) => {
        if (user.id === id) {
          user['active'] = true
        } else {
          user['active'] = false
        }
      })
      this.selectedUserId = id
      this.getMerchantShopLists(this.selectedUserId)
    },
    getMerchantShopLists() {
      const params = {
        bizUserId: this.selectedUserId
      }
      agencyService
        .getMerchantShopLists(params)
        .then((response) => {
          this.merchantShopList = response.data.data.listData
        })
        .catch(() => {})
    },
    // 绑定经销商
    addBindMerchant() {
      if (!this.searchShopId) {
        return this.$message.warning('请选择一个经销商进行绑定')
      }
      console.log(this.searchShopId)
      agencyService
        .postMerchantBind({
          shopId: this.searchShopId,
          bizUserId: this.selectedUserId
        })
        .then(() => {
          this.$message.success('绑定成功')
          this.searchShopId = ''
          this.innerVisible = false
          this.getMerchantShopLists(this.selectedUserId)
        })
    },
    // 解绑
    unBindMerchant(shopId) {
      agencyService
        .postMerchantUnbind({
          shopId,
          bizUserId: this.selectedUserId
        })
        .then(() => {
          this.$message.success('解绑成功')
          this.getMerchantShopLists(this.selectedUserId)
        })
    },
    onCLose() {
      this.isShowBindDialog = false
      $emit(this, 'on-close')
    }
  },
  emits: ['on-get-info', 'on-close']
}
</script>

<style lang="scss">
.bind-view-dialog {
  .el-col-12:first-child {
    border-right: 1px solid #ededed;
    padding-right: 20px;
  }
  .el-col-12:last-child {
    padding-left: 20px;
  }
  .col-name {
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 10px;
    border-bottom: 1px solid #ededed;
    margin-bottom: 10px;
  }
  .col-list {
    &_item {
      height: 30px;
      line-height: 30px;
      padding: 0 5px;
      background: #f5f5f5;
      border-radius: 4px;
      margin-bottom: 5px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &.addhover:hover {
        background-color: #409eff;
        color: #fff;
      }
      &.active {
        background-color: #409eff;
        color: #fff;
      }
    }
  }
}
</style>
