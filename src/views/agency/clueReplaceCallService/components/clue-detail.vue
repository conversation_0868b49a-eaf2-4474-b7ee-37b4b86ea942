<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    width="60%"
    title="线索详情"
    class="clue-detail-dialog"
    @close="visible = false"
  >
    <el-form :model="ruleForm">
      <el-form-item>
        <el-button type="primary" @click="save">保存</el-button>
      </el-form-item>
      <el-form-item label="客户姓名">
        <el-input
          v-model.trim="ruleForm.realName"
          placeholder="请输入客户姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="预计购车时间">
        <el-radio-group v-model="ruleForm.buyCarTime">
          <el-radio-button
            v-for="(value, label) in buyCarTypes"
            :key="value"
            :label="label"
            >{{ value }}</el-radio-button
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item required label="跟进结果反馈">
        <el-radio-group v-model="ruleForm.intention">
          <el-radio-button
            v-for="(value, label) in intentionTypes"
            :key="value"
            :label="label"
            >{{ value }}</el-radio-button
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item required label="成交状态">
        <el-radio-group v-model="ruleForm.handleStatus">
          <el-radio-button
            v-for="(value, label) in handleStatusTypes"
            :key="value"
            :label="label"
            >{{ value }}</el-radio-button
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item label="填写备注">
        <el-input
          v-model.trim="ruleForm.description"
          placeholder="请输入备注"
          clearable
        />
      </el-form-item>
    </el-form>
    <!-- 历史记录 -->
    <div class="history-list">
      <h3>历史记录</h3>
      <div
        v-for="(history, index) in historyList"
        :key="index"
        class="list-item"
      >
        <div class="time">{{ $date.format(history.createTime) }}</div>
        <div class="info">信息：{{ history.goodsCarName || '-' }}</div>
        <div class="status">
          状态：
          <el-tag v-if="history.buyCarTime" size="small">{{
            buyCarTypes[history.buyCarTime]
          }}</el-tag>
          <el-tag size="small">{{ intentionTypes[history.intention] }}</el-tag>
          <el-tag size="small">{{
            handleStatusTypes[history.handleStatus]
          }}</el-tag>
        </div>
        <div class="remark">备注：{{ history.description }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import agencyService from '@/api/agency'

export default {
  name: 'ClueDetail',
  data() {
    return {
      visible: false,
      id: '',
      info: {},
      ruleForm: {
        realName: '',
        buyCarTime: '',
        intention: '',
        handleStatus: '',
        description: '',
      },
      buyCarTypes: {
        7: '7天内',
        15: '15天内',
        30: '1个月内',
        90: '3个月内',
        180: '半年内',
      },
      intentionTypes: {
        1: '高购车意向',
        2: '中购车意向',
        3: '低购车意向',
        // '4': '空号错号',
        // '5': '已成交',
        6: '无效客户',
        // '7': '未联系上客户',
      },
      handleStatusTypes: {
        // '1': '未跟进',
        2: '跟进中',
        3: '已到店',
        4: '已成交',
        5: '战败',
      },
      historyList: [],
    }
  },
  methods: {
    show(item) {
      this.visible = true
      this.id = item.id
      Object.keys(this.ruleForm).map((key) => {
        this.ruleForm[key] = ''
      })
      this.getClueDetail(this.id)
      this.getClueDetailHistory(this.id)
    },
    getClueDetail(id) {
      agencyService
        .getClueGenerationDetail(id)
        .then((res) => {
          this.info = res.data.data
          Object.keys(this.info).map((key) => {
            if (this.ruleForm.hasOwnProperty(key)) {
              this.ruleForm[key] = this.info[key].toString()
            }
          })
          console.log(this.info.customerName)
          this.ruleForm.realName = this.info.customerName
        })
        .catch(() => {})
    },
    getClueDetailHistory(id) {
      agencyService
        .getClueDetailHistory(id)
        .then((res) => {
          this.historyList = res.data.data || []
        })
        .catch(() => {})
    },
    save() {
      if (!Object.keys(this.intentionTypes).includes(this.ruleForm.intention)) {
        return this.$message.error('请选择跟进结果反馈')
      }
      if (
        !Object.keys(this.handleStatusTypes).includes(
          this.ruleForm.handleStatus
        )
      ) {
        return this.$message.error('请选择成交状态')
      }
      agencyService
        .postClueUpdate({
          id: this.info.id,
          ...this.ruleForm,
        })
        .then(() => {
          this.$message.success('保存成功')
          this.getClueDetailHistory(this.info.id)
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss">
.clue-detail-dialog {
  .history-list {
    border-top: 1px solid #ededed;
    padding: 20px 0;
    .list-item {
      margin-top: 10px;
      .time {
        margin-bottom: 10px;
      }
      .info,
      .status,
      .remark {
        margin-bottom: 5px;
        color: #aaa;
      }
    }
  }
}
</style>
