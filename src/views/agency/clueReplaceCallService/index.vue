<template>
  <div v-loading="loading" class="clue-replace_call-service">
    <header class="action" style="margin: 10px 0">
      <el-form :model="ruleForm" :inline="true">
        <el-form-item label="经销商">
          <SerchantMerchant
            @on-update="(shop) => (ruleForm.shopId = shop.shopId)"
            @on-clear="ruleForm.shopId = ''"
          />
        </el-form-item>
        <!-- <el-form-item label="经销商ID">
                        <el-input v-model.trim="ruleForm.shopId" placeholder="请输入经销商ID" style="width:160px;" clearable />
                      </el-form-item> -->
        <el-form-item label="手机号码">
          <el-input
            v-model.trim="ruleForm.mobile"
            placeholder="请输入手机号码"
            maxlength="11"
            style="width: 160px"
            clearable
          />
        </el-form-item>
        <el-form-item label="拨打状态">
          <el-select v-model="ruleForm.called" style="width: 120px">
            <el-option
              v-for="(value, index) in allCalledTypes"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()">查询</el-button>
          <!-- 注意此按钮权限只有一个人有 uid=45（王长文） uid=36（伏星宇）parseInt(uid)=== 36 -->
          <el-button @click="$refs.bindShop.show()">绑定经销商</el-button>
        </el-form-item>
      </el-form>
    </header>
    <div class="table-wrapper">
      <div class="title" style="margin-bottom: 20px">询价线索</div>
      <el-table
        :data="clueList"
        highlight-current-row
        border
        height="580"
        style="width: 100%"
        @row-dblclick="skipDetail"
      >
        <el-table-column label="用户姓名" align="center">
          <template #default="{ row }">
            <span>{{ row.realName }}</span>
            <el-tag v-if="row.called === 1" size="small">已拨</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="手机号" align="center">
          <template #default="{ row }">
            <span>{{ row.mobile }}</span>
            <el-button size="small" @click.stop="bindMobile(row.id)"
              >绑定手机</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="cityName" label="客户位置" align="center" />
        <el-table-column prop="brandName" label="品牌" align="center" />
        <el-table-column prop="goodsCarName" label="款型名称" align="center" />
        <el-table-column prop="shopName" label="商家名称" align="center" />
        <el-table-column prop="toMobile" label="商家联系方式" align="center" />
        <el-table-column prop="obtainTime" label="询价时间" align="center">
          <template #default="{ row }">
            <span>{{ $date.format(row.obtainTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="ruleForm.page"
        :page-size="ruleForm.limit"
        :page-sizes="[20, 10, 40, 60]"
        :total="total"
        background
        layout="total,sizes, prev, pager, next, jumper"
        style="text-align: center; margin-top: 20px"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </div>
    <!-- 绑定的dialog -->
    <BindShop ref="bindShop" @on-get-info="getUserInfo" @on-close="search()" />
    <ClueDetail ref="clueDetail" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SerchantMerchant from '@/components/search-merchant/index.vue'
import ClueDetail from './components/clue-detail.vue'
import BindShop from './components/bind-shop.vue'
import agencyService from '@/api/agency'

export default {
  name: 'ClueReplaceCallService',
  components: {
    SerchantMerchant,
    ClueDetail,
    BindShop
  },
  data() {
    return {
      loading: false,
      ruleForm: {},
      allCalledTypes: { 全部: '', 已拨: '1', 未拨: '0' },
      total: 0,
      clueList: [], // 线索列表
      user: {}
    }
  },
  computed: {
    ...mapGetters(['name', 'uid'])
  },
  activated() {
    this.resetForm()
    this.getClueList()
  },
  methods: {
    // 查询
    search() {
      this.ruleForm.page = 1
      this.getClueList()
    },
    // 获取列表数据
    getClueList() {
      this.loading = true
      agencyService
        .getClueGenerationList(this.ruleForm)
        .then((response) => {
          const resData = response.data.data
          this.clueList = resData ? resData.listData : []
          this.total = resData ? resData.total : 0
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    // 更新每页数量
    sizeChange(pageSize) {
      this.ruleForm.page = 1
      this.ruleForm.limit = pageSize
      this.getClueList()
    },
    // 更新页码
    currentChange(page) {
      this.ruleForm.page = page
      this.getClueList()
    },
    // 重置参数
    resetForm() {
      this.clueList = []
      this.total = 0
      this['ruleForm'] = {
        ...this.ruleForm,
        shopId: '',
        mobile: '',
        called: '',
        page: 1, // 页码
        limit: 20
      }
    },
    bindMobile(id) {
      agencyService
        .postClueBindMobile({
          ossMobile: this.user.mobile,
          id
        })
        .then(() => {
          this.$message.success('绑定成功')
        })
    },
    getUserInfo(user) {
      this.user = user
    },
    // 打开详情页面
    skipDetail(item) {
      this.$refs.clueDetail.show(item)
    }
  }
}
</script>

<style lang="scss">
.clue-replace_call-service {
  padding: 10px 20px 50px;
}
</style>
