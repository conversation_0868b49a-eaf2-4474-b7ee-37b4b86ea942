<template>
  <div class="no-auth__structure">
    <search-field
      @on-search="searchResult"
      @on-reset="resetResult"
    ></search-field>
    <el-main style="white-space: nowrap; margin-top: -50px">
      <div class="side">
        <h3>品牌</h3>
        <brand-multi-list
          :sort-fileds="brand.alpha"
          :data-list="brand.data"
          :prop-brand-id="brand.curBrandId"
          @on-select-data="getCurBrandData"
        ></brand-multi-list>
      </div>
      <div v-show="isShowProviceLine" class="side">
        <h3>省</h3>
        <agent-list
          :region-data="province.data"
          :cur-region-code="province.curProvinceCode"
          :agent-level="1"
          :authorized="false"
          @on-choose-region="chooseRegin"
        >
        </agent-list>
      </div>
      <div v-show="isShowCityLine" class="side">
        <h3>市</h3>
        <agent-list
          :region-data="city.data"
          :cur-region-code="city.curCityCode"
          :agent-level="2"
          :authorized="false"
          @on-choose-region="chooseRegin"
        ></agent-list>
      </div>
      <div v-show="isShowDistinctLine" class="side">
        <h3>代理</h3>
        <agent-list
          :agent-data="agentData"
          :cur-agent-id="chosenAgentId"
          :is-agent-data-loaded="isAgentDataLoaded"
          :agent-level="3"
          :authorized="false"
          @on-add-agent="addAgentRelation"
        ></agent-list>
      </div>
    </el-main>
  </div>
</template>

<script>
import SearchField from '../components/search-field.vue'
import BrandMultiList from '../components/brand-multi-list.vue'
import AgentList from '../components/agent-list.vue'
import { searchBrandList } from '@/api/brand'
import { getProvinceListMap, getCityListMap } from '@/api/commonModule'
import agencyService from '@/api/agency'

export default {
  name: 'NoAuthAgentStructure',
  components: {
    SearchField,
    BrandMultiList,
    AgentList,
  },
  data() {
    return {
      brand: {
        alpha: [],
        data: [],
        curBrandId: 0,
      },
      province: {
        data: [],
        curProvinceCode: '',
        curProvinceName: '',
      },
      city: {
        data: [],
        curCityCode: '',
        curCityName: '',
      },
      agentData: [],
      isAgentDataLoaded: false,
      chosenAgentId: null,
      isShowProviceLine: false, // 是否显示省份代理商
      isShowCityLine: false, // 是否显示市代理商
      isShowDistinctLine: false, // 是否显示区县代理商
    }
  },
  watch: {
    'brand.curBrandId'(newVal) {
      this.isShowProviceLine = !!newVal
      this.province.curProvinceCode = ''
      this.province.curProvinceName = ''
    },
    'province.curProvinceCode': async function (newVal) {
      this.city.curCityCode = ''
      this.city.curCityName = ''
      if (newVal) {
        this.city.data = await getCityListMap(this.province.curProvinceCode)
        this.isShowCityLine = true
      } else {
        this.isShowCityLine = false
      }
    },
    'city.curCityCode'(newVal) {
      if (newVal) {
        this.getAgentRelationList()
        this.isShowDistinctLine = true
      } else {
        this.isShowDistinctLine = false
      }
    },
  },
  async mounted() {
    const alphaArr = []
    const { data: brandData } = await searchBrandList({})
    if (brandData.code !== 0) {
      return
    }
    // 按照 aleph 排序
    this.brand.data = brandData.data.list
      .filter((item) => item.isShow)
      .sort((a, b) => {
        if (a.aleph < b.aleph) {
          return -1
        }
        if (a.aleph > b.aleph) {
          return 1
        }
        return 0
      })
    this.brand.data.map(({ aleph }) => {
      if (!alphaArr.includes(aleph)) {
        alphaArr.push(aleph)
      }
    })
    this.brand.alpha = ['HOT', ...alphaArr.sort()]
    this.province.data = await getProvinceListMap()
  },
  methods: {
    searchResult({ brandId, agencyId }) {
      agencyService
        .queryUnauthorizedAgentRelation({
          brandId,
          shopId: agencyId,
        })
        .then(async (res) => {
          const rData = res.data.data
          if (!rData) {
            this.$message.error('没有找到当前品牌和经销商的关联数据')
            return
          }
          this.brand.curBrandId = rData.brandId
          await this.$nextTick()
          this.province.curProvinceCode = rData.provinceCode
          this.province.curProvinceName = rData.provinceName
          await this.$nextTick()
          this.city.curCityCode = rData.cityCode
          this.city.curCityName = rData.cityName
          await this.$nextTick()
          this.chosenAgentId = rData.id
        })
        .catch(() => {})
    },

    resetResult() {
      this.brand.curBrandId = 0
    },

    getCurBrandData(brand) {
      this.brand.curBrandId = brand.brandId
    },
    // 选择区域
    chooseRegin({ region, agentLevel }) {
      switch (agentLevel) {
        case 1:
          this.province.curProvinceCode = region.provinceCode
          this.province.curProvinceName = region.name
          break
        case 2:
          this.city.curCityCode = region.cityCode
          this.city.curCityName = region.name
          break
        default:
      }
    },

    // 获取未授权经销商的代理数据
    getAgentRelationList() {
      agencyService
        .getAgentRelationList({
          brandId: this.brand.curBrandId,
          agentLevel: 2,
          locationCode: this.city.curCityCode,
          locationName: this.city.curCityName,
          parentId: 0,
          authorized: false,
        })
        .then((res) => {
          this.agentData = res.data.data
          this.isAgentDataLoaded = true
        })
        .catch((err) => console.log(err))
    },
    // 品牌经销商关联
    addAgentRelation({ shopId }) {
      const params = {
        provinceCode: this.province.curProvinceCode,
        provinceName: this.province.curProvinceName,
        agentLevel: 2,
        brandId: this.brand.curBrandId,
        shopId,
        parentId: 0,
        cityCode: this.city.curCityCode,
        cityName: this.city.curCityName,
        authorized: false,
      }
      agencyService
        .addAgentRelation(params)
        .then((res) => {
          const data = res.data.data
          this.agentData.push(data)
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss">
.no-auth__structure {
  .side {
    display: inline-block;
    vertical-align: top;
    margin-right: 50px;
  }
}
</style>
