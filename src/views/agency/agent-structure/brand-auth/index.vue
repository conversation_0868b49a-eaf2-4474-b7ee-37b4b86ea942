<template>
  <div class="brand-auth__structure">
    <search-field
      @on-search="searchResult"
      @on-reset="resetResult"
    ></search-field>
    <el-main style="white-space: nowrap; margin-top: -50px">
      <div class="side">
        <h3>品牌</h3>
        <brand-multi-list
          :sort-fileds="brand.alpha"
          :data-list="brand.data"
          :prop-brand-id="brand.curBrandId"
          @on-select-data="getCurBrandData"
        ></brand-multi-list>
      </div>
      <div v-show="isShowProviceLine" class="side">
        <h3>省级代理</h3>
        <agent-list
          :region-data="province.data"
          :agent-data="province.agentData"
          :cur-region-code="province.curProvinceCode"
          :cur-agent-id="city.parentId"
          :is-agent-data-loaded="province.isAgentDataLoaded"
          :agent-level="1"
          @on-choose-region="chooseRegin"
          @on-choose-agent="getChildAgents"
          @on-add-agent="addAgentRelation"
        >
        </agent-list>
      </div>
      <div v-show="isShowCityLine" class="side">
        <h3>市级代理</h3>
        <agent-list
          :region-data="city.data"
          :agent-data="city.agentData"
          :cur-region-code="city.curCityCode"
          :cur-agent-id="distinct.parentId"
          :is-agent-data-loaded="city.isAgentDataLoaded"
          :agent-level="2"
          @on-choose-region="chooseRegin"
          @on-choose-agent="getChildAgents"
          @on-add-agent="addAgentRelation"
        ></agent-list>
      </div>
      <div v-show="isShowDistinctLine" class="side">
        <h3>区县代理</h3>
        <agent-list
          :agent-data="distinct.agentData"
          :cur-agent-id="distinct.chosenAgentId"
          :is-agent-data-loaded="distinct.isAgentDataLoaded"
          :agent-level="3"
          @on-add-agent="addAgentRelation"
        ></agent-list>
      </div>
    </el-main>
  </div>
</template>

<script>
import SearchField from '../components/search-field.vue'
import BrandMultiList from '../components/brand-multi-list.vue'
import AgentList from '../components/agent-list.vue'
import { searchBrandList } from '@/api/brand'
import { getProvinceListMap, getCityListMap } from '@/api/commonModule'
import agencyService from '@/api/agency'
import { clone } from 'lodash-es'

export default {
  name: 'BrandAuthAgentStructure',
  components: {
    SearchField,
    BrandMultiList,
    AgentList,
  },
  data() {
    return {
      brand: {
        alpha: [],
        data: [],
        curBrandId: 0,
      },
      province: {
        data: [],
        agentData: [],
        curProvinceCode: '',
        curProvinceName: '',
        isAgentDataLoaded: false,
      },
      city: {
        data: [],
        agentData: [],
        curCityCode: '',
        curCityName: '',
        parentId: null,
        isAgentDataLoaded: false,
      },
      distinct: {
        agentData: [],
        parentId: null,
        chosenAgentId: null,
        isAgentDataLoaded: false,
      },
      isShowProviceLine: false, // 是否显示省份代理商
      isShowCityLine: false, // 是否显示市代理商
      isShowDistinctLine: false, // 是否显示区县代理商
    }
  },
  watch: {
    'brand.curBrandId'(newVal) {
      this.isShowProviceLine = !!newVal
      this.province.curProvinceCode = ''
      this.province.curProvinceName = ''
    },
    'province.curProvinceCode'(newVal) {
      this.city.curCityCode = ''
      this.city.curCityName = ''
      this.isShowCityLine = false
      this.city.parentId = null
      if (newVal) {
        this.getAgentRelationList(1)
      } else {
        this.province.agentData = []
        this.province.isAgentDataLoaded = false
      }
    },
    'city.curCityCode'(newVal) {
      this.distinct.parentId = null
      this.isShowDistinctLine = false
      if (newVal) {
        this.getAgentRelationList(2)
      } else {
        this.city.agentData = []
        this.city.isAgentDataLoaded = false
      }
    },
    'distinct.parentId'(newVal) {
      if (!newVal) {
        this.distinct.agentData = []
        this.distinct.isAgentDataLoaded = false
      }
    },
  },
  async mounted() {
    const alphaArr = []
    const { data: brandData } = await searchBrandList({})
    // 按照 aleph 排序
    this.brand.data = brandData.data.list
      .filter((item) => item.isShow)
      .sort((a, b) => {
        if (a.aleph < b.aleph) {
          return -1
        }
        if (a.aleph > b.aleph) {
          return 1
        }
        return 0
      })
    this.brand.data.map(({ aleph }) => {
      if (!alphaArr.includes(aleph)) {
        alphaArr.push(aleph)
      }
    })
    this.brand.alpha = ['HOT', ...alphaArr.sort()]
    this.province.data = await getProvinceListMap()
  },
  methods: {
    searchResult({ brandId, agencyId }) {
      agencyService
        .queryAgentRelation({
          brandId,
          shopId: agencyId,
        })
        .then(async (res) => {
          const rData = res.data.data
          if (!rData) {
            this.$message.error('没有找到当前品牌和经销商的关联数据')
            return
          }
          this.brand.curBrandId = rData.brandId
          await this.$nextTick()
          const searchAgentLevel = rData.agentLevel // 搜索的代理商所在的层级
          // 根据结构得出省市区对应选中的数据
          let provinceData
          let cityData
          let distinctData
          if (searchAgentLevel === 1) {
            provinceData = clone(rData)
          } else if (searchAgentLevel === 2) {
            cityData = clone(rData) // 由于不需要取深层次的数据 浅拷贝即可
            provinceData = clone(rData.parent)
          } else {
            distinctData = clone(rData)
            cityData = clone(rData.parent)
            provinceData = clone(rData.parent.parent)
          }
          // 因为省份的code不一定变化 watch不一定触发 所以需要手动清空市相关数据
          this.city.curCityCode = ''
          this.city.curCityName = ''
          this.isShowCityLine = false
          // 模拟点击
          this.chooseRegin({
            region: {
              provinceCode: provinceData.locationCode,
              name: provinceData.locationName,
            },
            agentLevel: 1,
          })
          await this.$nextTick()
          this.city.parentId = provinceData.id
          if (searchAgentLevel >= 2) {
            // 有市结构
            await this.getChildAgents({
              // 通过省级关联出市级结构
              agent: {
                id: provinceData.id,
              },
              agentLevel: 1,
            })
            await this.$nextTick()
            // 区的清空
            this.isShowDistinctLine = false
            this.chooseRegin({
              region: {
                cityCode: cityData.locationCode,
                name: cityData.locationName,
              },
              agentLevel: 2,
            })
            await this.$nextTick()
            this.distinct.parentId = cityData.id
            if (searchAgentLevel === 3) {
              // 有区结构
              await this.getChildAgents({
                agent: {
                  id: cityData.id,
                },
                agentLevel: 2,
              })
              this.distinct.chosenAgentId = distinctData.id
            }
          }
        })
        .catch(() => {})
    },

    resetResult() {
      this.brand.curBrandId = 0
    },

    getCurBrandData(brand) {
      this.brand.curBrandId = brand.brandId
    },
    // 选择区域
    chooseRegin({ region, agentLevel }) {
      switch (agentLevel) {
        case 1:
          this.province.curProvinceCode = region.provinceCode
          this.province.curProvinceName = region.name
          break
        case 2:
          this.city.curCityCode = region.cityCode
          this.city.curCityName = region.name
          break
        default:
      }
    },

    // 获取某个区域下的代理
    getAgentRelationList(agentLevel) {
      let locationCode // 根据类型得出编码
      let locationName // 省市名称
      let parentId // 市和区需要上传上级代理商的关联ID
      if (agentLevel === 1) {
        locationCode = this.province.curProvinceCode
        locationName = this.province.curProvinceName
      } else if (agentLevel === 2) {
        locationCode = this.city.curCityCode
        locationName = this.city.curCityName
        parentId = this.city.parentId
      } else {
        parentId = this.distinct.parentId
      }
      agencyService
        .getAgentRelationList({
          brandId: this.brand.curBrandId,
          locationName,
          agentLevel,
          locationCode,
          parentId,
        })
        .then((res) => {
          const agentData = res.data.data
          switch (agentLevel) {
            case 1:
              this.province.agentData = agentData
              this.province.isAgentDataLoaded = true
              break
            case 2:
              this.city.agentData = agentData
              this.city.isAgentDataLoaded = true
              break
            case 3:
              this.distinct.agentData = agentData
              this.distinct.isAgentDataLoaded = true
              break
            default:
          }
        })
        .catch((err) => console.log(err))
    },

    // 获取子代理的结构
    async getChildAgents({ agent, agentLevel }) {
      switch (agentLevel) {
        case 1: // 省份获取市
          this.city.curCityName = ''
          this.city.curCityCode = ''
          this.city.parentId = agent.id
          this.city.data = await getCityListMap(this.province.curProvinceCode)
          this.isShowCityLine = true
          break
        case 2: // 市获取区
          this.distinct.parentId = agent.id
          this.getAgentRelationList(3)
          this.isShowDistinctLine = true
          break
        default:
      }
    },

    // 品牌经销商关联
    addAgentRelation({ shopId, agentLevel }) {
      const params = {
        provinceCode: this.province.curProvinceCode,
        provinceName: this.province.curProvinceName,
        agentLevel,
        brandId: this.brand.curBrandId,
        shopId,
        parentId:
          agentLevel === 1
            ? 0
            : agentLevel === 2
            ? this.city.parentId
            : this.distinct.parentId,
        cityCode: this.city.curCityCode,
        cityName: this.city.curCityName,
      }
      agencyService
        .addAgentRelation(params)
        .then((res) => {
          const data = res.data.data
          if (agentLevel === 1) {
            this.province.agentData.push(data)
          } else if (agentLevel === 2) {
            this.city.agentData.push(data)
          } else {
            this.distinct.agentData.push(data)
          }
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss">
.brand-auth__structure {
  .side {
    display: inline-block;
    vertical-align: top;
    margin-right: 50px;
  }
}
</style>
