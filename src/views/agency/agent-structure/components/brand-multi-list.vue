<template>
  <div class="brand-auth__multi-list">
    <!-- 排序列 -->
    <div v-if="sortFileds && sortFileds.length" class="sort-side">
      <div
        v-for="(alpha, index) in sortFileds"
        :key="index"
        :class="{ active: alpha === currentAleph }"
        class="sort-side__item"
        @click="selectAlpha(alpha)"
      >
        {{ alpha }}
      </div>
    </div>
    <!-- 数据列 -->
    <div v-if="sortedData && sortedData.length" class="data-side">
      <div
        v-for="(item, index) in sortedData"
        :key="index"
        :title="item.brandName"
        :class="{ active: currentBrandId === item.brandId }"
        class="data-side__item"
        @click="selectData(item)"
      >
        {{ item.brandName }}
      </div>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer'
import scrollIntoView from 'scroll-into-view'

const DEFAULT_ALEPH = 'HOT'

export default {
  name: 'BrandMultiList',
  props: {
    sortFileds: {
      // 排序序列 默认是首字母排序
      type: Array,
      default: () => {
        return []
      },
    },
    dataList: {
      // 数据序列
      type: Array,
      default: () => {
        return []
      },
    },
    propBrandId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      currentAleph: DEFAULT_ALEPH,
      currentBrandId: null,
    }
  },
  computed: {
    // 通过排序过滤的数据
    sortedData() {
      if (this.currentAleph) {
        if (this.currentAleph === DEFAULT_ALEPH) {
          return this.dataList.filter((item) =>
            [
              '春风',
              '无极',
              '贝纳利',
              '赛科龙',
              '豪爵',
              '豪爵铃木',
              '五羊本田',
              '新大洲本田',
              'SYM三阳',
              '光阳',
              '奔达',
              '升仕',
              '凯越',
            ].includes(item.brandName)
          )
        }
        return this.dataList.filter((item) => item.aleph === this.currentAleph)
      }
      return this.dataList
    },
  },
  watch: {
    propBrandId(val) {
      this.currentBrandId = val
      const currentBrandInfo = this.dataList.find(
        (list) => list.brandId === this.currentBrandId
      )
      if (currentBrandInfo) {
        if (
          this.sortedData.find((item) => item.brandId === this.currentBrandId)
        ) {
          // 当前排序序列里面有这个品牌
          return
        } else {
          this.currentAleph = currentBrandInfo.aleph
        }
      } else {
        this.currentAleph = DEFAULT_ALEPH
      }
    },
    currentAleph: async function () {
      await this.$nextTick()
      scrollIntoView(document.querySelector('.sort-side__item.active'))
    },
    currentBrandId: async function () {
      await this.$nextTick()
      scrollIntoView(document.querySelector('.data-side__item.active'))
    },
  },
  methods: {
    // 选择字母
    selectAlpha(alpha) {
      this.currentAleph = alpha
    },
    // 选择数据
    selectData(item) {
      $emit(this, 'on-select-data', item)
    },
  },
  emits: ['on-select-data'],
}
</script>

<style lang="scss">
.brand-auth__multi-list {
  display: flex;
  .sort-side {
    background: #f6f6f8;
    margin-right: 2px;
    max-height: 600px;
    overflow-y: auto;
    &__item {
      border-bottom: 2px solid #fff;
      padding: 5px 8px;
      text-align: center;
      cursor: pointer;
      &:last-child {
        border-bottom-width: 0;
      }
      &:hover,
      &.active {
        background: #3d9ffe;
        color: #fff;
      }
    }
  }
  .data-side {
    width: 150px;
    text-align: center;
    background: #f6f6f8;
    max-height: 600px;
    overflow-y: auto;
    &__item {
      border-bottom: 2px solid #fff;
      padding: 8px;
      font-size: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      &:last-child {
        border-bottom-width: 0;
      }
      &:hover,
      &.active {
        background: #3d9ffe;
        color: #fff;
      }
    }
  }
}
</style>
