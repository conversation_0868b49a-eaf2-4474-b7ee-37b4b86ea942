<template>
  <div class="brand-auth__agent-list">
    <!-- 显示地区 -->
    <div v-if="regionData.length" class="region-side">
      <div
        v-for="(region, index) in regionData"
        :key="index"
        :title="region.name"
        :class="{
          active:
            curRegionCode ===
            (agentLevel === 1 ? region.provinceCode : region.cityCode),
        }"
        class="region-side__item"
        @click="selectRegion(region)"
      >
        {{ region.name }}
      </div>
    </div>
    <!-- 显示代理商 操作 -->
    <div v-show="isAgentDataLoaded" class="operate-side">
      <div
        v-for="agent in agentData"
        :key="agent.id"
        class="operate-side__item"
        @click.stop="chooseAgent(agent)"
      >
        <router-link
          v-if="agent.status === 1"
          :to="{
            name: 'DistributorDetails',
            query: {
              id: agent.shopId,
            },
          }"
          :title="agent.shopName"
          :class="{ active: curAgentId === agent.id }"
          target="_blank"
          class="agent-name"
          @click.stop="() => {}"
          >{{ agent.shopName }} {{ agent.authStatus === 4 ? '(已过期)' : '' }}
        </router-link>
        <span
          v-else
          :title="agent.shopName"
          :class="{ active: curAgentId === agent.id }"
          class="agent-name"
          @click.stop="() => {}"
          >暂无代理经销商
        </span>
        <span
          v-if="agent.status === 1"
          class="oper-btn"
          @click.stop="unbindAgent(agent)"
          >删除</span
        >
        <!-- 品牌授权的代理结构才可以编辑 -->
        <span
          v-if="authorized && agent.status === 0"
          class="oper-btn"
          @click.stop="openModifyAgentPopup(agent)"
          >编辑</span
        >
      </div>
      <div v-if="!agentData.length" class="no-data">暂无数据</div>
      <el-button type="primary" class="link-btn" @click="openBindAgentPopup"
        >关联</el-button
      >
    </div>
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      title="关联经销商"
      width="30%"
    >
      <el-input
        v-model="relateAgentId"
        maxlength="8"
        placeholder="请输入经销商ID"
      ></el-input>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button
            type="primary"
            @click="!isInModify ? addRelatedAgent() : modifyAgent()"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer'
import agencyService from '@/api/agency'
import scrollIntoView from 'scroll-into-view'

export default {
  name: 'AgentList',
  props: {
    regionData: {
      // 地区数据
      type: Array,
      default: () => {
        return []
      },
    },
    agentData: {
      // 代理数据
      type: Array,
      default: () => {
        return []
      },
    },
    curRegionCode: {
      // 当前区域码
      type: String,
      default: '',
    },
    curAgentId: {
      // 当前代理商的id
      type: Number,
      default: 0,
    },
    agentLevel: {
      type: Number,
      default: 1, // 代理级别 1省 2市 3区县
    },
    isAgentDataLoaded: {
      type: Boolean,
      default: false,
    },
    authorized: {
      // 是否已授权
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: false,
      isInModify: false, // 是否是编辑
      modifyId: '', // 编辑的id
      relateAgentId: '', // 关联经销商的id
    }
  },
  watch: {
    curRegionCode: async function () {
      await this.$nextTick()
      scrollIntoView(document.querySelector('.region-side__item.active'))
    },
    curAgentId: async function () {
      await this.$nextTick()
      scrollIntoView(document.querySelector('.agent-name.active'))
    },
  },
  methods: {
    selectRegion(region) {
      $emit(this, 'on-choose-region', {
        region,
        agentLevel: this.agentLevel,
      })
    },
    chooseAgent(agent) {
      if (agent.agentLevel === 3) {
        return
      }
      $emit(this, 'on-choose-agent', {
        agent,
        agentLevel: this.agentLevel,
      })
    },
    unbindAgent(agent) {
      this.$confirm('确定删除当前的经销商吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          agencyService
            .deleteAgentRelation({
              id: agent.id,
            })
            .then(() => {
              if (this.authorized) {
                agent.status = 0 // 删除只是更改status
              } else {
                const index = this.agentData.findIndex(
                  ({ id }) => agent.id === id
                )
                this.agentData.splice(index, 1)
              }
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    openModifyAgentPopup(agent) {
      this.isInModify = true
      this.modifyId = agent.id
      this.openBindAgentPopup()
    },
    openBindAgentPopup() {
      this.dialogVisible = true
    },
    // 关联品牌经销商
    addRelatedAgent() {
      const agentId = this.relateAgentId.trim()
      if (agentId === '') {
        return this.$message.error('请输入经销商ID')
      }
      if (!new RegExp(/^\d+$/).test(agentId)) {
        return this.$message.error('请输入有效的经销商ID')
      }
      $emit(this, 'on-add-agent', {
        shopId: agentId,
        agentLevel: this.agentLevel,
      })
      this.dialogVisible = false
      this.relateAgentId = ''
    },
    // 编辑关联经销商
    modifyAgent() {
      const agentId = this.relateAgentId.trim()
      if (agentId === '') {
        return this.$message.error('请输入经销商ID')
      }
      if (!new RegExp(/^\d+$/).test(agentId)) {
        return this.$message.error('请输入有效的经销商ID')
      }
      agencyService
        .modifyAgentRelation({
          id: this.modifyId,
          shopId: agentId,
        })
        .then((res) => {
          const index = this.agentData.findIndex(
            (agent) => agent.id === this.modifyId
          )
          this.agentData.splice(index, 1, res.data.data)
          this.dialogVisible = false
          this.relateAgentId = ''
          this.isInModify = false
          this.modifyId = ''
        })
        .catch(() => {})
    },
  },
  emits: ['on-choose-region', 'on-choose-agent', 'on-add-agent'],
}
</script>

<style lang="scss">
.brand-auth__agent-list {
  display: flex;
  .region-side {
    width: 150px;
    text-align: center;
    background: #f6f6f8;
    height: 600px;
    overflow-y: auto;
    &__item {
      border: 1px solid transparent;
      padding: 8px;
      font-size: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      &.active {
        border-color: #eee;
        color: #0779ef;
        background: #fff;
      }
    }
  }
  .operate-side {
    width: 350px;
    height: 600px;
    overflow-y: auto;
    position: relative;
    padding-bottom: 50px;
    border: 1px solid #eee;
    &__item {
      border-bottom: 1px solid #eee;
      padding: 8px;
      font-size: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      .agent-name {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &.active,
        &:hover {
          color: #0779ef;
        }
      }
      > span {
        cursor: default;
      }
      .oper-btn {
        cursor: pointer;
        color: #ff4d1e;
      }
    }
    .no-data {
      text-align: center;
      padding-top: 300px;
    }
    .link-btn {
      position: absolute;
      bottom: 15px;
      right: 15px;
    }
  }
}
</style>
