<template>
  <div class="brand-auth__search-field">
    <el-form :inline="true" :model="form" class="search-area">
      <el-form-item label="品牌">
        <el-select
          v-model="form.brandId"
          :remote-method="searchBrandList"
          :loading="brandloading"
          placeholder="请输入品牌名称"
          filterable
          remote
          clearable
        >
          <el-option
            v-for="item in brandList"
            :key="item.labelId"
            :label="item.brandName"
            :value="item.brandId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="经销商ID">
        <el-input
          v-model.trim="form.agencyId"
          placeholder="请输入经销商ID"
        ></el-input>
      </el-form-item>
    </el-form>
    <div class="search-btns">
      <el-button type="default" @click="reset">重置</el-button>
      <el-button type="primary" @click="search">查询</el-button>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer'
import { debounce } from 'lodash-es'
import { searchBrand } from '@/api/articleModule'
export default {
  name: 'AgencySearchField',
  data() {
    return {
      form: {
        brandId: '',
        agencyId: '',
      },
      brandList: [], // 搜索车型品牌列表
      brandloading: false,
    }
  },
  mounted() {
    this.searchBrandList('')
  },
  methods: {
    searchBrandList(query) {
      this.brandloading = true
      this.getBrandList(query.trim())
    },
    // 获取品牌列表
    getBrandList: debounce(function (name) {
      searchBrand({
        name,
        page: 1,
        limit: 10,
      })
        .then((response) => {
          if (response.data.code === 0) {
            const result = response.data.data && response.data.data.listData
            this.brandList = [...result]
          }
        })
        .finally((_) => {
          this.brandloading = false
        })
    }, 600),

    reset() {
      Object.keys(this.form).map((key) => {
        this.form[key] = ''
      })
      $emit(this, 'on-reset')
    },
    search: debounce(function () {
      if (!this.form.brandId) {
        this.$message.error('请输入品牌名称')
        return
      }
      if (!this.form.agencyId) {
        this.$message.error('请输入经销商ID')
        return
      }
      $emit(this, 'on-search', this.form)
    }, 600),
  },
  emits: ['on-search', 'on-reset'],
}
</script>

<style lang="scss">
.brand-auth__search-field {
  padding: 30px 20px;
  display: flex;
  justify-content: space-between;
}
</style>
