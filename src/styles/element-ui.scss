 //覆盖一些element-ui样式

 .el-breadcrumb__inner, .el-breadcrumb__inner a{
  font-weight: 400!important;
}

 .el-upload {
   input[type="file"] {
     display: none !important;
   }
 }

 .el-upload__input {
   display: none;
 }

 .cell {
   .el-tag {
     margin-right: 0px;
   }
 }

 .small-padding {
   .cell {
     padding-left: 5px;
     padding-right: 5px;
   }
 }

 .fixed-width{
  .el-button--mini{
    padding: 7px 10px;
    width: 60px;
  }
 }

 .status-col {
   .cell {
     padding: 0 10px;
     text-align: center;
     .el-tag {
       margin-right: 0px;
     }
   }
 }

 //暂时性解决dialog 问题 https://github.com/ElemeFE/element/issues/2461
 .el-dialog {
   transform: none;
   left: 0;
   position: relative;
   margin: 0 auto;
 }

 //文章页textarea修改样式
 .article-textarea {
   textarea {
     padding-right: 40px;
     resize: none;
     border: none;
     border-radius: 0px;
     border-bottom: 1px solid #bfcbd9;
   }
 }

 //element ui upload
 .upload-container {
   .el-upload {
     width: 100%;
     .el-upload-dragger {
       width: 100%;
       height: 200px;
     }
   }
 }

//dropdown
 .el-dropdown-menu{
  a{
    display: block
  }
}

 .el-time-panel__content::after, .el-time-panel__content::before {
   margin-top: -7px;
 }

 .el-select-dropdown {
   max-width: 400px;
 }

 .el-table .cell,
  .el-table th div {
    text-overflow: unset;
  }

  .el-message-box__message p {
    word-wrap: break-word;
  }