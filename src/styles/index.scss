@import './variables.scss';
@import './mixin.scss';
@import './util.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app{
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus{
  outline: none;
}

mdd-topic {
  display: inline-block;
  line-height: 0;
  color: #5288f6;
}

.center {
  @include center();
}

.center2 {
  @include center2();
}

.center3 {
  @include center3();
}

.flex-center {
  @include flex-center();
}

.allcover {
  position: absolute;
  top: 0;
  right: 0;
}

.ct {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.cl {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

.line-th {
  text-decoration: line-through;
}

.flex {
  @include flex();
}

.flex1 {
  @include flex-num(1);
}

.flex3 {
  @include flex-num(3);
}

.flex-center {
  @include flex-center();
}

.fl-left {
    float: left;
}

.fl-right {
    float: right;
}

.font-0 {
    font-size: 0;
}

.dotdotdot1 {
  @include dotdotdot1();
}

.dotdotdot2 {
  @include dotdotdot2();
}

.dotdotdot3 {
  @include dotdotdot2(3);
}

.dotdotdot4 {
  @include dotdotdot2(4);
}

.dotdotdot6 {
  @include dotdotdot2(6);
}

.dotdotdot8 {
  @include dotdotdot2(8);
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}


.fr {
  float: right;
}

.fl {
  float: left;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 5px;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.mr-5 {
  margin-right: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.block {
  display: block;
}

.hide {
  display: none!important;
}

.show {
  display: block!important;
}

.disabled {
  background-color: #ccc;
  color: #fff;
  border-color: #ccc;
}

.el-time-spinner__list{
  .disabled {
    background-color: #fff;
  }
}

.mask {
  position: fixed;
  z-index: 998;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
}

.btn {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  box-sizing: border-box;
  color: inherit;
  display: block;;
  outline: 0;
  overflow: hidden;
  position: relative;
  text-align: center;
  cursor: pointer;
}

.btn::after {
  background-color: #fff;
  content: " ";
  opacity: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: absolute;
}

.btn:active::after {
  opacity: .2;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.red {
  color: red;
}

code {
  background: #eef1f6;
  padding: 15px 16px;
  margin-bottom: 20px;
  display: block;
  line-height: 36px;
  font-size: 15px;
  font-family: "Source Sans Pro", "Helvetica Neue", Arial, sans-serif;
  a {
    color: #337ab7;
    cursor: pointer;
    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

.warn-content{
  background: rgba(66,185,131,.1);
  border-radius: 2px;
  padding: 16px;
  padding: 1rem;
  line-height: 1.6rem;
  word-spacing: .05rem;
  a{
    color: #42b983;
    font-weight: 600;
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);
  .subtitle {
    font-size: 20px;
    color: #fff;
  }
  &.draft {
    background: #d0d0d0;
  }
  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.el-table th {
  color: #333;
}

.set-content-margin{
  margin: 10px
}
.pos-rel{
  position: relative;
}
.font-size-16{
  font-size: 16px;
}
.font-size-14{
  font-size: 14px;
}
.font-size-15{
  font-size: 15px;
}
.gray{
  color: gray;
}
.point-none {
  pointer-events: none;
  cursor: not-allowed;
}
.point-auto {
  pointer-events: auto;
  cursor: auto;
}