import menu from '@/router/menuIds'

export const constantRouterMap = [
  {
    path: '/redirect',
    component: () => import('@/views/layout/Layout.vue'),
    meta: { menuId: menu.noLimit },
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: () => import('@/views/redirect/index.vue'),
        meta: { menuId: menu.noLimit }
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: { menuId: menu.noLimit },
    hidden: true
  },
  {
    path: '/test',
    component: () => import('@/views/login/test.vue'),
    meta: { menuId: menu.noLimit },
    hidden: true
  },
  {
    path: '/auth-redirect',
    component: import('@/views/login/authredirect.vue'),
    meta: { menuId: menu.noLimit },
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/errorPage/404.vue'),
    meta: { menuId: menu.noLimit },
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/errorPage/401.vue'),
    meta: { menuId: menu.noLimit },
    hidden: true
  },
  {
    path: '',
    name: 'Dashboard',
    component: () => import('@/views/layout/Layout.vue'),
    redirect: '/dashboard',
    hidden: true,
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        name: 'DashboardHome',
        meta: {
          title: '首页',
          icon: 'dashboard',
          noCache: true,
          menuId: menu.noLimit
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/errorPage/404.vue')
  }
]
