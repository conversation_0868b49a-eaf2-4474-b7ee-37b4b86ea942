import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const shopManagementList = {
  path: '/shopOrder',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '订单列表', icon: 'peoples', menuId: 'S124' },
  children: [
    {
      path: 'ShopOrderList',
      component: () =>
        import('@/views/garage/dealerManagement/ShopOrderList/index.vue'),
      name: 'ShopOrderList',
      meta: { title: '商家订单列表', icon: 'list', menuId: 'S12401' }
    },
    {
      path: 'DealerOrderList',
      component: () => import('@/views/garage/DealerOrderList/index.vue'),
      name: 'DealerOrderList',
      meta: {
        title: '会员订单表',
        icon: 'list',
        noCache: true,
        menuId: 'S12402'
      }
    },
    {
      path: 'clue-order',
      component: () =>
        import('@/views/garage/dealerManagement/ClueOrder/index.vue'),
      name: 'ClueOrder',
      meta: {
        title: '线索业务付费订单页',
        icon: 'list',
        noCache: true,
        menuId: 'S12403'
      }
    },
    {
      path: 'dealerAdvertising',
      component: () =>
        import('@/views/garage/dealerManagement/dealerAdvertising/index.vue'),
      name: 'dealerAdvertising',
      meta: { title: '商家广告订单管理', icon: 'list', menuId: 'S12404' }
    },
    {
      path: 'goldRechargeOrder',
      component: () =>
        import('@/views/garage/dealerManagement/goldRechargeOrder/index.vue'),
      name: 'goldRechargeOrder',
      meta: {
        title: '金币充值订单',
        icon: 'list',
        noCache: true,
        menuId: 'S12405'
      }
    },
    {
      path: 'DealerOrderListNew',
      component: () =>
        import('@/views/garage/financeOrderList/DealerOrderList.vue'),
      name: 'DealerOrderListNew',
      meta: {
        title: '会员费充值订单明细(新)',
        icon: 'list',
        noCache: true,
        menuId: 'S12406'
      }
    },
    {
      path: 'ClueRechargeOrderNew',
      component: () => import('@/views/garage/financeOrderList/ClueOrder.vue'),
      name: 'ClueRechargeOrderNew',
      meta: {
        title: '线索包充值订单明细(新)',
        icon: 'list',
        noCache: true,
        menuId: 'S12407'
      }
    },
    {
      path: 'goldRechargeOrderNew',
      component: () =>
        import('@/views/garage/financeOrderList/goldRechargeOrder.vue'),
      name: 'goldRechargeOrderNew',
      meta: {
        title: '金币充值订单明细(新)',
        icon: 'list',
        noCache: true,
        menuId: 'S12408'
      }
    },
    {
      path: 'dealerAdvertisingNew',
      component: () =>
        import('@/views/garage/financeOrderList/dealerAdvertising.vue'),
      name: 'dealerAdvertisingNew',
      meta: {
        title: '广告费充值订单明细(新)',
        icon: 'list',
        noCache: true,
        menuId: 'S12409'
      }
    }
  ]
}

export default shopManagementList
