/** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const circlePlatform = {
  path: '/circlePlatform',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '圈子平台', icon: 'peoples', menuId: 'S109' },
  children: [
    {
      path: 'circleManageList',
      component: () => import('@/views/circle/circleManageList/index.vue'),
      name: 'CircleManageList',
      meta: { title: '摩友圈内容列表', icon: 'list', menuId: 'S10906' }
    },
    {
      path: 'circleList',
      component: () => import('@/views/circle/circleList/index.vue'),
      name: 'NewCircleList',
      meta: { title: '圈子列表', icon: 'list', menuId: 'S10901' }
    },
    {
      path: 'combineCircle',
      component: () => import('@/views/circle/combineCircle/index.vue'),
      name: 'combineCircle',
      meta: { title: '合并圈子', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'circleDetails',
      component: () => import('@/views/circle/circleDetails/index.vue'),
      name: 'CircleDetails',
      meta: {
        title: '圈子详情页',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'circleUsers',
      component: () => import('@/views/circle/circleUsers/index.vue'),
      name: 'CircleUsers',
      meta: { title: '圈子用户', icon: 'list', menuId: 'S10902' }
    },
    {
      path: 'circleApplyList',
      component: () => import('@/views/circle/circleApplyList/index.vue'),
      name: 'circleApplyList',
      meta: { title: '圈主申请列表', icon: 'list', menuId: 'S10903' }
    },
    {
      path: 'circleGuide',
      component: () => import('@/views/circle/circleGuide/index.vue'),
      name: 'circleGuide',
      meta: { title: '圈子指南', icon: 'list', menuId: 'S10904' }
    },
    {
      path: 'circleUserOperation',
      component: () => import('@/views/circle/circleUserOperation/index.vue'),
      name: 'circleUserOperation',
      meta: { title: '圈主操作列表', icon: 'list', menuId: 'S10905' }
    },
    {
      path: 'reportCircleLord',
      component: () => import('@/views/circle/reportCircleLord/index.vue'),
      name: 'reportCircleLord',
      meta: {
        title: '举报圈主审核',
        icon: 'list',
        noCache: true,
        menuId: 'S10907'
      }
    },
    {
      path: 'circleUserMoonTest',
      component: () => import('@/views/circle/circleUserMoonTest/index.vue'),
      name: 'circleUserMoonTest',
      meta: { title: '圈主月度考核', icon: 'list', menuId: 'S10908' }
    }
  ]
}

export default circlePlatform
