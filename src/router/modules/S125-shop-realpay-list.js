import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const shopRealPayList = {
  path: '/shopReal',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '订单实付列表', icon: 'peoples', menuId: 'S125' },
  children: [
    {
      path: 'RealpayDealerOrderList',
      component: () => import('@/views/garage/realPay/DealerOrderList.vue'),
      name: 'RealpayDealerOrderList',
      meta: { title: '会员订单实付表', icon: 'list', menuId: 'S12501' }
    },
    {
      path: 'RealpayClueRechargeOrder',
      component: () => import('@/views/garage/realPay/ClueRechargeOrder.vue'),
      name: 'RealpayClueRechargeOrder',
      meta: {
        title: '线索包订单实付表',
        icon: 'list',
        noCache: true,
        menuId: 'S12502'
      }
    },
    {
      path: 'RealpayGoldRechargeOrder',
      component: () => import('@/views/garage/realPay/goldRechargeOrder.vue'),
      name: 'RealpayGoldRechargeOrder',
      meta: {
        title: '金币订单实付表',
        icon: 'list',
        noCache: true,
        menuId: 'S12503'
      }
    },
    {
      path: 'RealpayDealerAdvertising',
      component: () => import('@/views/garage/realPay/dealerAdvertising.vue'),
      name: 'RealpayDealerAdvertising',
      meta: { title: '广告费订单实付表', icon: 'list', menuId: 'S12504' }
    },
    {
      path: 'RealpayShopOrderPaymentList',
      component: () =>
        import('@/views/garage/realPay/ShopOrderPaymentList.vue'),
      name: 'RealpayShopOrderPaymentList',
      meta: { title: '商务订单实付表', icon: 'list', menuId: 'S12505' }
    }
  ]
}

export default shopRealPayList
