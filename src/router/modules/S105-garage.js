import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const garage = {
  path: '/garage',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '车库平台', icon: 'peoples', menuId: 'S105' },
  children: [
    {
      path: 'garageManage',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '车库管理', icon: 'peoples', menuId: 'S10501' },
      children: [
        {
          path: 'brandPropertyList',
          component: () =>
            import('@/views/garage/garageManage/brandPropertyList/index.vue'),
          name: 'brandPropertyList',
          meta: { title: '品牌列表', icon: 'list', menuId: 'S1050101' }
        },
        {
          path: 'garagePropertyList',
          component: () =>
            import('@/views/garage/garageManage/garagePropertyList/index.vue'),
          name: 'garagePropertyList',
          meta: { title: '车辆列表', icon: 'list', menuId: 'S1050102' }
        },
        {
          path: 'createGarage',
          component: () =>
            import(
              '@/views/garage/garageManage/garagePropertyList/createGarage/index.vue'
            ),
          name: 'createGarage',
          meta: { title: '创建款型', icon: 'list', menuId: menu.noLimit },
          hidden: true
        },
        {
          path: 'editGarage',
          component: () =>
            import(
              '@/views/garage/garageManage/garagePropertyList/editGarage/index.vue'
            ),
          name: 'editGarage',
          meta: { title: '编辑款型', icon: 'list', menuId: menu.noLimit },
          hidden: true
        },
        {
          path: 'createVehicleType',
          component: () =>
            import(
              '@/views/garage/garageManage/garagePropertyList/createVehicleType/index.vue'
            ),
          name: 'createVehicleType',
          meta: { title: '创建车型', icon: 'list', menuId: menu.noLimit },
          hidden: true
        },
        {
          path: 'EditVehicleType',
          component: () =>
            import(
              '@/views/garage/garageManage/garagePropertyList/editVehicleType/index.vue'
            ),
          name: 'EditVehicleType',
          meta: {
            title: '编辑车型',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        // {
        //   path: 'VehicleTypeList',
        //   component: () =>
        //     import('@/views/garage/garageManage/garageTypeList/index.vue'),
        //   name: 'VehicleTypeList',
        //   meta: {
        //     title: '款型列表',
        //     icon: 'list',
        //     noCache: true,
        //     menuId: 'S1050103'
        //   }
        // },
        {
          path: 'UsedCarManagement',
          component: () =>
            import('@/views/garage/garageManage/UsedCarManagement/index.vue'),
          name: 'UsedCarManagement',
          meta: {
            title: '二手车管理',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'UsedCarDetail',
          component: () =>
            import('@/views/garage/garageManage/UsedCarDetail/index.vue'),
          name: 'UsedCarDetail',
          meta: {
            title: '二手车详情',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'BuyCar',
          component: () =>
            import('@/views/garage/garageManage/BuyCar/index.vue'),

          name: 'BuyCar',
          meta: {
            title: '新车购买',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'actualConfigDetails',
          component: () =>
            import(
              '@/views/garage/garageManage/vehicleMeasurement/details.vue'
            ),
          name: 'ActualConfigDetails',
          meta: {
            title: '实测配置详情页',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'blindImageManagement',
          component: () =>
            import(
              '@/views/garage/garageManage/blindImageManagement/index.vue'
            ),
          name: 'blindImageManagement',
          meta: {
            title: '盲水印管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1050108'
          }
        },
        {
          path: 'garageImagePool',
          component: () =>
            import('@/views/garage/garageManage/garageImagePool/index.vue'),
          name: 'garageImagePool',
          meta: {
            title: '车库图片池',
            icon: 'list',
            noCache: true,
            menuId: 'S1050109'
          }
        }
      ]
    },
    {
      path: 'garageOperation',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '车库运营', icon: 'peoples', menuId: 'S10502' },
      children: [
        {
          path: 'customVehicleFeedbackList',
          component: () =>
            import(
              '@/views/garage/garageManage/customVehicleFeedbackList/index.vue'
            ),
          name: 'customVehicleFeedbackList',
          meta: {
            title: '自定义车型反馈列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1050212'
          }
        },
        {
          path: 'vehicleErrorCorrection',
          component: () =>
            import(
              '@/views/garage/garageManage/vehicleErrorCorrection/index.vue'
            ),
          name: 'vehicleErrorCorrection',
          meta: {
            title: '车辆纠错',
            icon: 'list',
            noCache: true,
            menuId: 'S1050201'
          }
        },
        {
          path: 'garage-problem-feedback',
          component: () =>
            import(
              '@/views/garage/garageManage/garage-problem-feedback/index.vue'
            ),
          name: 'GarageProblemFeedback',
          meta: {
            title: '车库问题反馈',
            icon: 'list',
            noCache: true,
            menuId: 'S1050202'
          }
        },
        {
          path: 'vehicleRecommended',
          component: () =>
            import('@/views/garage/vehicle-recommended/index.vue'),

          name: 'VehicleRecommended',
          meta: {
            title: '首页车辆组件',
            icon: 'list',
            noCache: true,
            menuId: 'S1050203'
          }
        },
        {
          path: 'keywordErrorCorrection',
          component: () =>
            import('@/views/garage/keyword-error-correction/index.vue'),
          name: 'KeywordErrorCorrection',
          meta: {
            title: '搜索错词纠正',
            icon: 'list',
            noCache: true,
            menuId: 'S1050204'
          }
        },
        {
          path: 'promptManagement',
          component: () => import('@/views/garage/prompt-management/index.vue'),

          name: 'PromptManagement',
          meta: {
            title: '搜索结果提示词',
            icon: 'list',
            noCache: true,
            menuId: 'S1050205'
          }
        },
        {
          path: 'RecordsViolations',
          component: () =>
            import('@/views/garage/records-violations/index.vue'),

          name: 'RecordsViolations',
          meta: {
            title: '查违章记录',
            icon: 'list',
            noCache: true,
            menuId: 'S1050206'
          }
        },
        {
          path: 'VehicleWordOfMouthList',
          component: () =>
            import('@/views/garage/VehicleWordOfMouthList/index.vue'),

          name: 'VehicleWordOfMouthList',
          meta: {
            title: '车辆口碑列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1050207'
          }
        },
        // {
        //   path: 'WordOfMouthNegativeList',
        //   component: () =>
        //     import('@/views/garage/WordOfMouthNegativeList/index.vue'),

        //   name: 'WordOfMouthNegativeList',
        //   meta: {
        //     title: '负面口碑管理',
        //     icon: 'list',
        //     noCache: true,
        //     menuId: 'S1050211'
        //   }
        // },
        {
          path: 'garageApproveList',
          component: () =>
            import('@/views/garage/garageManage/garageApproveList/index.vue'),
          name: 'garageApproveList',
          meta: {
            title: '车辆认证管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1050208'
          }
        },
        {
          path: 'garageEncyclopedia',
          component: () =>
            import('@/views/garage/garageManage/garageEncyclopedia/index.vue'),
          name: 'GarageEncyclopedia',
          meta: {
            title: '车辆参数百科',
            icon: 'list',
            noCache: true,
            menuId: 'S1050209'
          }
        },
        {
          path: 'carRecognitionList',
          component: () =>
            import('@/views/garage/garageManage/carRecognitionList/index.vue'),
          name: 'carRecognitionList',
          meta: {
            title: '拍照识车列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1050210'
          }
        },
        {
          path: 'searchProhibitedWords',
          component: () =>
            import(
              '@/views/garage/garageManage/searchProhibitedWords/index.vue'
            ),
          name: 'searchProhibitedWords',
          meta: {
            title: '车库搜索禁用词',
            icon: 'list',
            noCache: true,
            menuId: 'S1050213'
          }
        }
      ]
    }
  ]
}

export default garage
