import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const supplyChain = {
  path: '/supplyChain',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: 'B端供应链', icon: 'peoples', menuId: 'S119' },
  children: [
    {
      path: 'shopManage',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '商品管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11901'
      },
      children: [
        {
          path: 'supplyShopManageList',
          component: () =>
            import('@/views/supplyChain/shopManage/shopManageList/index.vue'),
          name: 'supplyShopManageList',
          meta: {
            title: '供应链商品列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1190101'
          }
        },
        {
          path: 'supplyShopBProprietary',
          component: () =>
            import('@/views/supplyChain/shopManage/proprietary/index.vue'),
          name: 'supplyShopBProprietary',
          meta: {
            title: '供应链B端自营商品',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'supplyLiveManageCar',
          component: () =>
            import(
              '@/views/supplyChain/shopManage/supplyLiveManageCar/index.vue'
            ),
          name: 'supplyLiveManageCar',
          meta: {
            title: 'B端自营车辆列表',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'chainSukidList',
          component: () =>
            import(
              '@/views/supplyChain/shopManage/shopManageList/sku-list.vue'
            ),
          name: 'chainSukidList',
          meta: {
            title: '供应链sukid列表',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'shopBClassificationManage',
          component: () =>
            import(
              '@/views/supplyChain/shopManage/shopClassificationManage/index.vue'
            ),
          name: 'shopBClassificationManage',
          meta: {
            title: 'B端平台商品分类',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'brandBClassification',
          component: () =>
            import(
              '@/views/supplyChain/shopManage/brandClassification/index.vue'
            ),
          name: 'brandBClassification',
          meta: {
            title: 'B端品牌分类',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'projectList',
          component: () =>
            import('@/views/supplyChain/shopManage/projectList/index.vue'),
          name: 'projectList',
          meta: {
            title: '专题列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1190104'
          }
        },
        {
          path: 'shopBAfterRules',
          component: () =>
            import('@/views/supplyChain/shopManage/afterRules/index.vue'),
          name: 'shopBAfterRules',
          meta: {
            title: '售后规则',
            icon: 'list',
            noCache: true,
            menuId: 'S1190106'
          }
        }
      ]
    },
    {
      path: 'orderManage',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '订单管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11902'
      },
      children: [
        {
          path: 'supplyOrderManageList',
          component: () =>
            import('@/views/supplyChain/supplyOrderManageList/index.vue'),
          name: 'supplyOrderManageList',
          meta: { title: '供应链订单列表', icon: 'list', menuId: 'S1190201' }
        },
        {
          path: 'supplyOrderManageDetail',
          component: () =>
            import(
              '@/views/supplyChain/supplyOrderManageList/orderDetail/index.vue'
            ),
          name: 'supplyOrderManageDetail',
          meta: {
            title: '供应链订单详情',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'supplyBillIs',
          component: () =>
            import(
              '@/views/supplyChain/supplyOrderManageList/billIs/index.vue'
            ),
          name: 'supplyBillIs',
          meta: {
            title: '账单情况',
            icon: 'list',
            noCache: true,
            menuId: 'S1190202'
          }
        },
        {
          path: 'supplyAfterSalesList',
          component: () =>
            import(
              '@/views/supplyChain/supplyOrderManageList/afterSalesList/index.vue'
            ),
          name: 'supplyAfterSalesList',
          meta: { title: '售后列表', icon: 'list', menuId: 'S1190203' }
        },
        {
          path: 'bRefundAudit',
          component: () =>
            import(
              '@/views/supplyChain/supplyOrderManageList/bRefundAudit/index.vue'
            ),
          name: 'bRefundAudit',
          meta: { title: 'B端退款审核', icon: 'list', menuId: 'S1190203' }
        }
      ]
    }
  ]
}

export default supplyChain
