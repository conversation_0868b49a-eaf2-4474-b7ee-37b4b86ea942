import * as Vue from 'vue'


import menu from '@/router/menuIds'
const workOrderSystem = {
  path: '/workOrderSystem',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '工单系统', icon: 'peoples', menuId: 'S123' },
  children: [
    {
      path: 'WorkerOrderManagement',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '反馈工单', icon: 'peoples', menuId: 'S12301' },
      children: [
        {
          path: 'WorkerOrderList',
          component: () =>
            import(
              '@/views/system/WorkerOrderManagement/WorkerOrderList/index.vue'
            ),
          name: 'WorkerOrderList',
          meta: {
            title: '工单系统列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1230101'
          }
        },
        {
          path: 'WorkerOrderDetail',
          component: () =>
            import(
              '@/views/system/WorkerOrderManagement/WorkerOrderDetail/index.vue'
            ),
          name: 'WorkerOrderDetail',
          hidden: true,
          meta: {
            title: '工单系统详情',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          }
        },
        {
          path: 'WorkerOrderGroupConfig',
          component: () =>
            import(
              '@/views/system/WorkerOrderManagement/WorkerOrderGroupConfig/index.vue'
            ),
          name: 'WorkerOrderGroupConfig',
          meta: {
            title: '工单群组配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1230102'
          }
        },
        {
          path: 'PersonnelAllocation',
          component: () =>
            import(
              '@/views/system/WorkerOrderManagement/PersonnelAllocation/index.vue'
            ),
          name: 'PersonnelAllocation',
          hidden: true,
          meta: {
            title: '人员配置',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          }
        }
      ]
    },
    {
      path: 'EssentialData',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '基础数据', icon: 'peoples', menuId: 'S12302' },
      children: [
        {
          path: 'EnterprisePersonnelInfo',
          component: () =>
            import(
              '@/views/system/EssentialData/EnterprisePersonnelInfo/index.vue'
            ),
          name: 'EnterprisePersonnelInfo',
          meta: {
            title: '企业人员信息',
            icon: 'list',
            noCache: true,
            menuId: 'S1230201'
          }
        }
      ]
    }
  ]
}

export default workOrderSystem
