import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

//
import menu from '@/router/menuIds'

const advertBanner = {
  path: '/advertBanner',

  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '广告平台', icon: 'peoples', menuId: 'S104' },
  children: [
    {
      path: 'AdvertisingManageList',
      component: () =>
        import(
          '@/views/business/advert-banner/advertising-manage-list/index.vue'
        ),
      name: 'AdvertisingManageList',
      meta: { title: '广告方管理列表', icon: 'list', menuId: 'S10410' }
      // hidden: true
    },

    {
      path: 'AdvertisingPlanList',
      component: () =>
        import(
          '@/views/business/advert-banner/advertising-plan-list/index.vue'
        ),
      name: 'AdvertisingPlanList',
      meta: { title: '计划管理列表', icon: 'list', menuId: 'S10411' }
      // hidden: true
    },
    {
      path: 'AdvertisingPlanDetail',
      component: () =>
        import(
          '@/views/business/advert-banner/advertising-plan-list/commponents/detail.vue'
        ),
      name: 'AdvertisingPlanDetail',
      meta: { title: '计划管理详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },

    {
      path: 'AdvertisingList',
      component: () =>
        import('@/views/business/advert-banner/advertising-list/index.vue'),
      name: 'AdvertisingList',
      meta: { title: '广告列表', icon: 'list', menuId: 'S10412' }
      // hidden: true
    },
    {
      path: 'AdvertisingDealerList',
      component: () =>
        import(
          '@/views/business/advert-banner/advertising-dealer-list/index.vue'
        ),
      name: 'AdvertisingDealerList',
      meta: { title: '经销商广告列表', icon: 'list', menuId: 'S10413' }
    },
    {
      path: 'AdvertisingLocation',
      component: () =>
        import('@/views/business/advert-banner/advertising-location/index.vue'),
      name: 'AdvertisingLocation',
      meta: { title: '广告位置管理', icon: 'list', menuId: 'S10414' }
    },
    {
      path: 'AdvertisingMerchantList',
      component: () =>
        import(
          '@/views/business/advert-banner/advertising-merchant-list/index.vue'
        ),
      name: 'AdvertisingMerchantList',
      meta: { title: '商家版广告列表', icon: 'list', menuId: 'S10409' }
    },
    {
      path: 'AdDetailsMerchantConfig',
      component: () =>
        import(
          '@/views/business/advert-banner/ad-details-merchant-config/index.vue'
        ),
      name: 'AdDetailsMerchantConfig',
      meta: { title: '商家版广告内容配置', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    // {
    //   path: 'externalAdvert',
    //   component: () => import('@/views/business/advert-banner/external-advert/index.vue'),
    //   name: 'ExternalAdvert',
    //   meta: { title: '外部广告', icon: 'list', menuId: 'S10402' }
    // },
    {
      path: 'PageBulletWindow',
      component: () =>
        import('@/views/business/advert-banner/bullet-window/index.vue'),
      name: 'PageBulletWindow',
      meta: { title: '页面弹框', icon: 'list', menuId: 'S10403' }
    },
    {
      path: 'PageBulletDetail',
      component: () =>
        import('@/views/business/advert-banner/pageBullet-detail/index.vue'),
      name: 'PageBulletDetail',
      meta: { title: '页面详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'BottomPromptBox',
      component: () =>
        import('@/views/business/advert-banner/bottom-prompt/index.vue'),
      name: 'BottomPromptBox',
      meta: { title: '底部提示框', icon: 'list', menuId: 'S10404' }
    },
    {
      path: 'BottomBoxDetails',
      component: () =>
        import('@/views/business/advert-banner/bottom-box-details/index.vue'),
      name: 'BottomBoxDetails',
      meta: { title: '底部提示框详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'AdvertisingFunctionUsers',
      component: () =>
        import(
          '@/views/business/advert-banner/advertising-functionUsers/index.vue'
        ),
      name: 'AdvertisingFunctionUsers',
      meta: { title: '开通广告功能用户', icon: 'list', menuId: 'S10405' }
    },
    {
      path: 'AuthorDefinedAdvertising',
      component: () =>
        import(
          '@/views/business/advert-banner/author-defined-advertising/index.vue'
        ),
      name: 'AuthorDefinedAdvertising',
      meta: { title: '作者自定义广告', icon: 'list', menuId: 'S10406' }
    },
    {
      path: 'packetConfiguration',
      component: () =>
        import('@/views/business/advert-banner/packet-configuration/index.vue'),
      name: 'PacketConfiguration',
      meta: { title: '小包配置', icon: 'list', menuId: 'S10407' }
    },
    {
      path: 'AdDetailsConfig',
      component: () =>
        import('@/views/business/advert-banner/ad-details-config/index.vue'),
      name: 'AdDetailsConfig',
      meta: { title: '广告详情配置', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'AdConfigManage',
      component: () =>
        import('@/views/business/advert-banner/ad-config-manage/index.vue'),
      name: 'AdConfigManage',
      meta: { title: '广告配置管理', icon: 'list', menuId: 'S10408' }
    }
  ]
}

export default advertBanner
