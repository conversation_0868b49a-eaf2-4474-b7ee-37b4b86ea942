import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const dealerManagement = {
  path: '/dealerManagement',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '经销商管理', icon: 'peoples', menuId: 'S106' },
  children: [
    {
      path: 'distributorAttributes',
      component: () =>
        import(
          '@/views/garage/dealerManagement/distributorAttributes/index.vue'
        ),
      name: 'distributorAttributes',
      meta: { title: '经销商列表', icon: 'list', menuId: 'S10601' }
    },
    {
      path: 'virtual-number-warning',
      component: () =>
        import('@/views/garage/dealerManagement/virtualNumber/warn-list.vue'),
      name: 'virtualNumberWarning',
      meta: { title: '虚拟号预警', icon: 'list', menuId: 'S10649' }
    },
    {
      path: 'AddDistributor',
      component: () =>
        import('@/views/garage/dealerManagement/addDistributor/index.vue'),
      name: 'AddDistributor',
      meta: {
        title: '新建经销商',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'DistributorDetails',
      component: () =>
        import('@/views/garage/dealerManagement/distributorDetails/index.vue'),
      name: 'DistributorDetails',
      meta: {
        title: '经销商详情',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    // {
    //   path: 'priceReductionInformationManagement',
    //   component: () =>
    //     import(
    //       '@/views/garage/dealerManagement/priceReductionInformationManagement/index.vue'
    //     ),
    //   name: 'priceReductionInformationManagement',
    //   meta: {
    //     title: '降价信息管理',
    //     icon: 'list',
    //     noCache: true,
    //     menuId: 'S10602'
    //   }
    // },

    {
      path: 'DetailsEntry',
      component: () =>
        import('@/views/garage/dealerManagement/detailsEntry/index.vue'),
      name: 'DetailsEntry',
      meta: {
        title: '经销商审核详情',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'entryAuditV2',
      component: () =>
        import('@/views/garage/dealerManagement/entryAuditV2/index.vue'),
      name: 'entryAuditV2',
      meta: {
        title: '店铺资料审核',
        icon: 'list',
        noCache: true,
        menuId: 'S10628'
      }
    },
    {
      path: 'DetailsEntryV2',
      component: () =>
        import('@/views/garage/dealerManagement/detailsEntryV2/index.vue'),
      name: 'DetailsEntryV2',
      meta: {
        title: '店铺资料审核详情',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'qualificationAudit',
      component: () =>
        import('@/views/garage/dealerManagement/qualificationAudit/index.vue'),
      name: 'qualificationAudit',
      meta: {
        title: '业务资质审核',
        icon: 'list',
        noCache: true,
        menuId: 'S10629'
      }
    },
    {
      path: 'MaintenanceAndRescue',
      component: () =>
        import(
          '@/views/garage/dealerManagement/MaintenanceAndRescue/index.vue'
        ),

      name: 'MaintenanceAndRescue',
      meta: { title: '维修救援审核', icon: 'list', menuId: 'S10648' }
    },
    {
      path: 'MaintenanceAndRescueDetail',
      component: () =>
        import(
          '@/views/garage/dealerManagement/MaintenanceAndRescue/detail.vue'
        ),

      name: 'MaintenanceAndRescueDetail',
      meta: { title: '维修救援审核详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'BusinessEntryApplication',
      component: () =>
        import(
          '@/views/garage/dealerManagement/BusinessEntryApplication/index.vue'
        ),
      name: 'BusinessEntryApplication',
      meta: {
        title: '商家入驻申请',
        icon: 'list',
        noCache: true,
        menuId: 'S10631'
      }
    },
    {
      path: 'DetailsQualification',
      component: () =>
        import(
          '@/views/garage/dealerManagement/DetailsQualification/index.vue'
        ),
      name: 'DetailsQualification',
      meta: {
        title: '资质审核详情',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'brandAuthorizationReview',
      component: () =>
        import(
          '@/views/garage/dealerManagement/BrandAuthorizationReview/index.vue'
        ),
      name: 'brandAuthorizationReview',
      meta: {
        title: '品牌授权审核详情',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'authorizationAudit',
      component: () =>
        import('@/views/garage/dealerManagement/authorizationAudit/index.vue'),
      name: 'AuthorizationAudit',
      meta: {
        title: '品牌授权审核',
        icon: 'list',
        noCache: true,
        menuId: 'S10604'
      }
    },
    {
      path: 'activityManagement',
      component: () =>
        import('@/views/garage/dealerManagement/activityManagement/index.vue'),
      name: 'activityManagement',
      meta: { title: '活动管理', icon: 'list', menuId: '********' }
    },
    {
      path: 'ActivityContentConfiguration',
      component: () =>
        import(
          '@/views/garage/dealerManagement/ActivityContentConfiguration/index.vue'
        ),
      name: 'ActivityContentConfiguration',
      meta: { title: '活动内容配置', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'driveRecord',
      component: () =>
        import('@/views/garage/dealerManagement/driveRecord/index.vue'),
      name: 'driveRecord',
      meta: {
        title: '试驾记录',
        icon: 'list',
        menuId: 'S10624'
      }
    },
    {
      path: 'phoneClueRecord',
      component: () =>
        import('@/views/garage/dealerManagement/phoneClueRecord/index.vue'),
      name: 'phoneClueRecord',
      meta: {
        title: '电话线索记录',
        icon: 'list',
        menuId: 'S10636'
      }
    },
    {
      path: 'chatClueRecord',
      component: () =>
        import('@/views/garage/dealerManagement/chatClueRecord/index.vue'),
      name: 'ChatClueRecord',
      meta: {
        title: '微信线索',
        icon: 'list',
        menuId: 'S10639'
      }
    },
    // {
    //   path: 'ShopOrderList',
    //   component: () =>
    //     import('@/views/garage/dealerManagement/ShopOrderList/index.vue'),
    //   name: 'ShopOrderList',
    //   meta: { title: '商家订单列表', icon: 'list', menuId: 'S10647' }
    // },
    {
      path: 'inquiryRecordList',
      component: () =>
        import('@/views/garage/dealerManagement/inquiryRecordList/index.vue'),
      name: 'inquiryRecordList',
      meta: { title: '询价记录', icon: 'list', menuId: 'S10606' }
    },
    {
      path: 'customerRecordList',
      component: () =>
        import('@/views/garage/dealerManagement/customerRecordList/index.vue'),
      name: 'CustomerRecordList',
      meta: { title: '客户记录表', icon: 'list', menuId: 'S10644' }
    },
    // {
    //   path: 'rentalCluesList',
    //   component: () =>
    //     import('@/views/garage/dealerManagement/rentalCluesList/index.vue'),
    //   name: 'RentalCluesList',
    //   meta: { title: '租车线索', icon: 'list', menuId: 'S10643' }
    // },
    {
      path: 'onePriceRecordList',
      component: () =>
        import('@/views/garage/dealerManagement/onePriceRecordList/index.vue'),
      name: 'onePriceRecordList',
      meta: { title: '一口价记录', icon: 'list', menuId: 'S10640' }
    },
    // {
    //   path: 'inquiryBusinessProcessing',
    //   component: () =>
    //     import(
    //       '@/views/garage/dealerManagement/inquiryBusinessProcessing/index.vue'
    //     ),
    //   name: 'inquiryBusinessProcessing',
    //   meta: { title: '询价业务处理', icon: 'list', menuId: 'S10614' }
    // },
    {
      path: 'myWorkList',
      component: () =>
        import('@/views/garage/dealerManagement/myWorkList/index.vue'),

      name: 'MyWorkList',
      meta: { title: '我的工作', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'dealerCorrection',
      component: () =>
        import('@/views/garage/dealerManagement/dealerCorrection/index.vue'),
      name: 'dealerCorrection',
      meta: { title: '经销商纠错', icon: 'list', menuId: 'S10607' }
    },
    {
      path: 'AuthorizationUsedVehicles',
      component: () =>
        import(
          '@/views/garage/dealerManagement/AuthorizationUsedVehicles/index.vue'
        ),
      name: 'AuthorizationUsedVehicles',
      meta: {
        title: '二手车权限审核',
        icon: 'list',
        noCache: true,
        menuId: 'S10608'
      }
    },
    {
      path: 'UsedCarRightsApplicationDetails',
      component: () =>
        import(
          '@/views/garage/dealerManagement/UsedCarRightsApplicationDetails/index.vue'
        ),
      name: 'UsedCarRightsApplicationDetails',
      meta: {
        title: '二手车权限申请详情页',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'BusinessWordOfMouthList',
      component: () =>
        import('@/views/garage/BusinessWordOfMouthList/index.vue'),

      name: 'BusinessWordOfMouthList',
      meta: {
        title: '商家口碑列表',
        icon: 'list',
        noCache: true,
        menuId: 'S10610'
      }
    },
    // {
    //   path: 'DealerOrderList',
    //   component: () => import('@/views/garage/DealerOrderList/index.vue'),

    //   name: 'DealerOrderList',
    //   meta: {
    //     title: '会员订单表',
    //     icon: 'list',
    //     noCache: true,
    //     menuId: 'S10611'
    //   }
    // },
    {
      path: 'directTrainOrderList',
      component: () =>
        import(
          '@/views/garage/dealerManagement/directTrainOrderList/index.vue'
        ),
      name: 'DirectTrainOrderList',
      meta: {
        title: '直通车业务充值订单',
        icon: 'list',
        noCache: true,
        menuId: 'S10618'
      }
    },
    // {
    //   path: 'goldRechargeOrder',
    //   component: () =>
    //     import('@/views/garage/dealerManagement/goldRechargeOrder/index.vue'),
    //   name: 'goldRechargeOrder',
    //   meta: {
    //     title: '金币充值订单',
    //     icon: 'list',
    //     noCache: true,
    //     menuId: 'S10627'
    //   }
    // },
    // {
    //   path: 'clue-order',
    //   component: () =>
    //     import('@/views/garage/dealerManagement/ClueOrder/index.vue'),

    //   name: 'ClueOrder',
    //   meta: {
    //     title: '线索业务付费订单页',
    //     icon: 'list',
    //     noCache: true,
    //     menuId: 'S10622'
    //   }
    // },
    {
      path: 'distributorAccountList',
      component: () =>
        import(
          '@/views/garage/dealerManagement/distributorAccountList/index.vue'
        ),
      name: 'DistributorAccountList',
      meta: {
        title: '经销商账户体系',
        icon: 'list',
        noCache: true,
        menuId: 'S10617'
      }
    },
    {
      path: 'distributorAccountDetail',
      component: () =>
        import(
          '@/views/garage/dealerManagement/distributorAccountDetail/index.vue'
        ),
      name: 'DistributorAccountDetail',
      meta: { title: '经销商账户明细', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'distributorAllAccount',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '账户明细汇总表',
        icon: 'peoples',
        noCache: true,
        menuId: 'S10623'
      },
      children: [
        {
          path: 'withdraw-account',
          component: () =>
            import(
              '@/views/garage/dealerManagement/distributorAllAccount/withdraw-account.vue'
            ),
          name: 'WithdrawAccount',
          meta: { title: '可提现账户', icon: 'list', menuId: 'S1062301' }
        },
        {
          path: 'directtrain-account',
          component: () =>
            import(
              '@/views/garage/dealerManagement/distributorAllAccount/directtrain-account.vue'
            ),
          name: 'DirecttrainAccount',
          meta: { title: '直通车可用余额', icon: 'list', menuId: 'S1062302' }
        },
        {
          path: 'experiencegold-account',
          component: () =>
            import(
              '@/views/garage/dealerManagement/distributorAllAccount/experiencegold-account.vue'
            ),
          name: 'ExperiencegoldAccount',
          meta: { title: '直通车体验金', icon: 'list', menuId: 'S1062303' }
        }
      ]
    },
    {
      path: 'distributorWithDrawList',
      component: () =>
        import(
          '@/views/garage/dealerManagement/distributorWithDrawList/index.vue'
        ),
      name: 'DistributorWithDrawList',
      meta: {
        title: '经销商提现申请列表',
        icon: 'list',
        noCache: true,
        menuId: 'S10616'
      }
    },
    {
      path: 'distributorWithDrawDetail',
      component: () =>
        import(
          '@/views/garage/dealerManagement/distributorWithDrawDetail/index.vue'
        ),
      name: 'DistributorWithDrawDetail',
      meta: { title: '经销商提现申请详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'distributorCarApply',
      component: () =>
        import('@/views/garage/dealerManagement/distributorCarApply/index.vue'),
      name: 'DistributorCarApply',
      meta: {
        title: '经销商线上售车申请',
        icon: 'list',
        noCache: true,
        menuId: 'S10615'
      }
    },
    {
      path: 'invoiceManagement',
      component: () =>
        import('@/views/garage/dealerManagement/invoiceManagement/index.vue'),
      name: 'InvoiceManagement',
      meta: {
        title: '发票管理',
        icon: 'list',
        noCache: true,
        menuId: 'S10612'
      }
    },
    {
      path: 'serviceInvoiceList',
      component: () =>
        import('@/views/garage/dealerManagement/serviceInvoiceList/index.vue'),
      name: 'ServiceInvoiceList',
      meta: {
        title: '经销商服务费发票表',
        icon: 'list',
        noCache: true,
        menuId: 'S10613'
      }
    },
    {
      path: 'invoiceDetail',
      component: () =>
        import('@/views/garage/dealerManagement/invoiceDetail/index.vue'),
      name: 'InvoiceDetail',
      meta: { title: '发票详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'dealer-rights',
      component: () =>
        import('@/views/garage/dealerManagement/dealer-rights/index.vue'),
      name: 'DealerRights',
      meta: {
        title: '经销商权益表',
        icon: 'list',
        noCache: true,
        menuId: 'S10619'
      }
    },
    {
      path: 'brandAuthAgentStructure',
      component: () =>
        import('@/views/agency/agent-structure/brand-auth/index.vue'),

      name: 'BrandAuthAgentStructure',
      meta: { title: '官方授权经销商结构', icon: 'list', menuId: 'S10620' }
    },
    {
      path: 'noAuthAgentStructure',
      component: () =>
        import('@/views/agency/agent-structure/no-auth/index.vue'),

      name: 'NoAuthAgentStructure',
      meta: { title: '非官方授权经销商结构', icon: 'list', menuId: 'S10621' }
    },
    {
      path: 'cityOfficerTaskHandle',
      component: () => import('@/views/agency/cityOfficerTaskHandle/index.vue'),

      name: 'cityOfficerTaskHandle',
      meta: { title: '城市官探店审核菜单', icon: 'list', menuId: 'S10625' }
    },
    {
      path: 'clueReplaceCallService',
      component: () =>
        import('@/views/agency/clueReplaceCallService/index.vue'),

      name: 'clueReplaceCallService',
      meta: { title: '线索代打服务', icon: 'list', menuId: 'S10626' }
    },
    // {
    //   path: 'drivingTestRecord',
    //   component: () => import('@/views/garage/dealerManagement/drivingTestRecord/index.vue'),
    //   name: 'drivingTestRecord',
    //   meta: { title: '驾考报名记录', icon: 'list', menuId: 'S10630' },
    // },
    {
      path: 'goldCoin',
      component: () =>
        import('@/views/garage/dealerManagement/goldCoin/index.vue'),

      name: 'goldCoin',
      meta: { title: '金币流转', icon: 'list', menuId: 'S10631' }
    },
    // {
    //   path: 'dealerAdvertising',
    //   component: () =>
    //     import('@/views/garage/dealerManagement/dealerAdvertising/index.vue'),
    //   name: 'dealerAdvertising',
    //   meta: { title: '商家广告订单管理', icon: 'list', menuId: 'S10633' }
    // },
    {
      path: 'dealerAdDetail',
      component: () =>
        import(
          '@/views/garage/dealerManagement/dealerAdvertising/components/dealerDetail.vue'
        ),
      name: 'dealerAdDetail',
      meta: { title: '商家广告订单详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'complaintManagement',
      component: () =>
        import('@/views/garage/dealerManagement/complaintManagement/index.vue'),
      name: 'complaintManagement',
      meta: { title: '投诉管理', icon: 'list', menuId: 'S10635' }
    },
    {
      path: 'complaintDetail',
      component: () =>
        import(
          '@/views/garage/dealerManagement/complaintManagement/components/complaintDetail.vue'
        ),
      name: 'complaintDetail',
      meta: { title: '投诉管理详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'activityOrderList',
      component: () =>
        import('@/views/garage/dealerManagement/activityOrderList/index.vue'),
      name: 'activityOrderList',
      meta: { title: '活动订单', icon: 'list', menuId: 'S10634' }
    },
    {
      path: 'BusinessGrowthContentList',
      component: () =>
        import(
          '@/views/garage/dealerManagement/BusinessGrowthContentList/index.vue'
        ),
      name: 'BusinessGrowthContentList',
      meta: { title: '商家成长内容列表', icon: 'list', menuId: 'S10637' }
    },
    {
      path: 'BusinessGrowthNewArticle',
      component: () =>
        import(
          '@/views/garage/dealerManagement/BusinessGrowthContentList/newArticle.vue'
        ),
      name: 'BusinessGrowthNewArticle',
      meta: { title: '商家成长内容编辑', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'ManufacturerClueGift',
      component: () =>
        import(
          '@/views/garage/dealerManagement/ManufacturerClueGift/index.vue'
        ),
      name: 'ManufacturerClueGift',
      meta: { title: '厂商线索赠送', icon: 'list', menuId: 'S10638' }
    },
    {
      path: 'couponInvoiceAudit',
      component: () => import('@/views/newCar/couponInvoiceAudit/index.vue'),

      name: 'CouponInvoiceAudit',
      meta: { title: '购新车发票审核列表', icon: 'list', menuId: 'S10641' }
    },
    {
      path: 'RecordingManagement',
      component: () =>
        import('@/views/garage/dealerManagement/RecordingManagement/index.vue'),
      name: 'RecordingManagement',
      meta: { title: '经销商录音管理', icon: 'list', menuId: 'S10645' }
    },
    {
      path: 'virtualNumber',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '虚拟号码管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S10642'
      },
      children: [
        {
          path: 'axn-management',
          component: () =>
            import(
              '@/views/garage/dealerManagement/virtualNumber/axn-management.vue'
            ),
          name: 'AxnManagement',
          meta: { title: 'AXN', icon: 'list', menuId: 'S1064201' }
        },
        {
          path: 'axb-management',
          component: () =>
            import(
              '@/views/garage/dealerManagement/virtualNumber/axb-management.vue'
            ),
          name: 'AxbMmanagement',
          meta: { title: 'AXB', icon: 'list', menuId: 'S1064202' }
        },
        {
          path: 'black-list',
          component: () =>
            import(
              '@/views/garage/dealerManagement/virtualNumber/black-list.vue'
            ),
          name: 'VirtualNumberBlackList',
          meta: {
            title: '商家虚拟号黑名单',
            icon: 'list',
            menuId: 'S1064203'
          }
        }
      ]
    },
    {
      path: 'MotoBeanDetail',
      component: () =>
        import('@/views/garage/dealerManagement/MotoBeanDetail/index.vue'),

      name: 'MotoBeanDetail',
      meta: { title: '摩豆明细', icon: 'list', menuId: 'S10646' }
    },
    {
      path: 'ShopOrderDetail',
      component: () =>
        import(
          '@/views/garage/dealerManagement/ShopOrderList/shopOrderDetail.vue'
        ),
      name: 'ShopOrderDetail',
      meta: { title: '商家订单详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'orderService',
      component: () =>
        import('@/views/garage/dealerManagement/orderService/index.vue'),
      name: 'OrderService',
      meta: { title: '创建订单服务', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'realPersonAuthentication',
      component: () =>
        import(
          '@/views/garage/dealerManagement/realPersonAuthentication/index.vue'
        ),
      name: 'RealPersonAuthentication',
      meta: { title: '实人认证记录审核', icon: 'list', menuId: 'S10650' }
    },
    {
      path: 'vendorMerchantAssocLists',
      component: () =>
        import(
          '@/views/garage/dealerManagement/vendorMerchantAssocLists/index.vue'
        ),
      name: 'VendorMerchantAssocLists',
      meta: { title: '厂商&商家数据关联列表', icon: 'list', menuId: 'S10651' }
    },
    {
      path: 'dealerContractManage',
      component: () =>
        import(
          '@/views/garage/dealerManagement/dealerContractManage/index.vue'
        ),
      name: 'DealerContractManage',
      meta: { title: '经销商合同管理', icon: 'list', menuId: 'S10652' }
    }
  ]
}

export default dealerManagement
