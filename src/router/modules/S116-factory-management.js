import menu from '@/router/menuIds'

const factoryManagement = {
  path: '/factoryManagement',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '厂家平台', icon: 'peoples', menuId: 'S116' },
  children: [
    {
      path: 'factoryManagement',
      component: () =>
        import('@/views/garage/factotyManagment/factoryManagement/index.vue'),
      name: 'factoryManagement',
      meta: { title: '厂家列表', icon: 'list', menuId: 'S11601' }
    },
    // {
    //   path: 'personnelManagement',
    //   component: () => import('@/views/garage/factotyManagment/factoryManagement/components/personnelManagement/index.vue'),
    //   name: 'personnelManagement',
    //   meta: { title: '厂家-人员管理', icon: 'list', menuId: menu.noLimit },
    //   hidden: true
    // },

    {
      path: 'publicSentiment',
      component: () =>
        import(
          '@/views/garage/factotyManagment/factoryManagement/components/publicSentiment.vue'
        ),
      name: 'publicSentiment',
      meta: { title: '厂家详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'orderManagement',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '订单管理', icon: 'peoples', menuId: 'S11603' },
      children: [
        {
          path: 'advertisementOrderManagement',
          component: () =>
            import('@/views/garage/factotyManagment/orderManagement/index.vue'),
          name: 'advertisementOrderManagement',
          meta: { title: '厂家广告订单管理', icon: 'list', menuId: 'S1160301' }
        },
        {
          path: 'orderDetail',
          component: () =>
            import('@/views/garage/factotyManagment/orderDetail/index.vue'),
          name: 'orderDetail',
          meta: { title: '厂家订单详情', icon: 'list', menuId: menu.noLimit },
          hidden: true
        },
        {
          path: 'projectListAll',
          component: () =>
            import(
              '@/views/garage/factotyManagment/orderDetail/projectListAll.vue'
            ),
          name: 'projectListAll',
          meta: {
            title: '厂家订单-合作项目',
            icon: 'list',
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'collectionList',
          component: () =>
            import(
              '@/views/garage/factotyManagment/orderDetail/collectionListAll.vue'
            ),
          name: 'collectionList',
          meta: {
            title: '厂家订单-收款记录',
            icon: 'list',
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'projectOrderManagement',
          component: () =>
            import(
              '@/views/garage/factotyManagment/orderDetail/components/projectOrderManagement.vue'
            ),
          name: 'projectOrderManagement',
          meta: {
            title: '合作项目及订单金额管理',
            icon: 'list',
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'orderMemberManagement',
          component: () =>
            import(
              '@/views/garage/factotyManagment/orderMemberManagement/index.vue'
            ),
          name: 'orderMemberManagement',
          meta: { title: '厂家会员订单管理', icon: 'list', menuId: 'S1160302' }
        },
        {
          path: 'orderMemberDetail',
          component: () =>
            import(
              '@/views/garage/factotyManagment/orderMemberDetail/index.vue'
            ),
          name: 'orderMemberDetail',
          meta: {
            title: '厂家会员订单详情',
            icon: 'list',
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'manufacturerInvoiceManagement',
          component: () =>
            import(
              '@/views/garage/factotyManagment/manufacturerInvoiceManagement/index.vue'
            ),
          name: 'manufacturerInvoiceManagement',
          meta: { title: '厂家发票管理', icon: 'list', menuId: 'S1160303' }
        },
        {
          path: 'manufacturerInvoiceDetail',
          component: () =>
            import(
              '@/views/garage/factotyManagment/manufacturerInvoiceDetail/index.vue'
            ),
          name: 'manufacturerInvoiceDetail',
          meta: { title: '厂家发票详情', icon: 'list', menuId: menu.noLimit },
          hidden: true
        },
        {
          path: 'manufacturerBusinessOrderManagement',
          component: () =>
            import(
              '@/views/garage/factotyManagment/manufacturerBusinessOrderManagement/index.vue'
            ),
          name: 'manufacturerBusinessOrderManagement',
          meta: { title: '厂家业务订单管理', icon: 'list', menuId: 'S1160307' }
        },
        {
          path: 'manufacturerBusinessOrderDetail',
          component: () =>
            import(
              '@/views/garage/factotyManagment/manufacturerBusinessOrderDetail/index.vue'
            ),
          name: 'manufacturerBusinessOrderDetail',
          meta: {
            title: '厂家业务订单详情',
            icon: 'list',
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'manufacturerContractManagement',
          component: () =>
            import(
              '@/views/garage/factotyManagment/manufacturerContractManagement/index.vue'
            ),
          name: 'manufacturerContractManagement',
          meta: { title: '厂家合同管理', icon: 'list', menuId: 'S1160306' }
        },
        {
          path: 'manufacturerContractDetail',
          component: () =>
            import(
              '@/views/garage/factotyManagment/manufacturerContractDetail/index.vue'
            ),
          name: 'manufacturerContractDetail',
          meta: { title: '厂家合同详情', icon: 'list', menuId: menu.noLimit },
          hidden: true
        },
        {
          path: 'manufacturerFlowList',
          component: () =>
            import(
              '@/views/garage/factotyManagment/manufacturerFlowList/index.vue'
            ),
          name: 'manufacturerFlowList',
          meta: { title: '厂家流水列表', icon: 'list', menuId: 'S1160304' }
        },
        {
          path: 'manufacturerProjectList',
          component: () =>
            import(
              '@/views/garage/factotyManagment/manufacturerProjectList/index.vue'
            ),
          name: 'manufacturerProjectList',
          meta: { title: '合作项目列表', icon: 'list', menuId: 'S1160305' }
        }
      ]
    },
    {
      path: 'publicOpinionManagement',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '舆情管理', icon: 'peoples', menuId: 'S11604' },
      children: [
        {
          path: 'coolingManagementList',
          component: () =>
            import(
              '@/views/garage/factotyManagment/publicOpinionManagement/coolingManagementList/index.vue'
            ),
          name: 'coolingManagementList',
          meta: { title: '冷却管理列表', icon: 'list', menuId: 'S1160401' }
        },
        {
          path: 'negativeWord',
          component: () =>
            import('@/views/garage/factotyManagment/negativeWord/index.vue'),
          name: 'negativeWord',
          meta: { title: '负面词管理', icon: 'list', menuId: 'S1160402' }
        }
      ]
    },
    {
      path: 'modelFollowList',
      component: () =>
        import('@/views/garage/factotyManagment/modelFollowList/index.vue'),
      name: 'modelFollowList',
      meta: { title: '车型关注列表', icon: 'list', menuId: 'S11605' }
    },
    {
      path: 'wordMouthOperationManagement',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '口碑运营管理', icon: 'peoples', menuId: 'S11606' },
      children: [
        {
          path: 'WordOfMouthNegativeList',
          component: () =>
            import('@/views/garage/WordOfMouthNegativeList/index.vue'),

          name: 'WordOfMouthNegativeList',
          meta: {
            title: '负面口碑管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1160601'
          }
        },
        {
          path: 'topMouthManagement',
          component: () =>
            import(
              '@/views/garage/factotyManagment/topMouthManagement/index.vue'
            ),
          name: 'topMouthManagement',
          meta: { title: '口碑置顶管理', icon: 'list', menuId: 'S1160602' }
        }
      ]
    },
    {
      path: 'commentTopManagement',
      component: () =>
        import(
          '@/views/garage/factotyManagment/commentTopManagement/index.vue'
        ),
      name: 'commentTopManagement',
      meta: { title: '评论置顶管理', icon: 'list', menuId: 'S11607' }
    },
    {
      path: 'launchManagement',
      component: () =>
        import('@/views/garage/factotyManagment/launchManagement/index.vue'),
      name: 'launchManagement',
      meta: { title: '投放管理', icon: 'list', menuId: 'S11608' }
    },
    {
      path: 'contentPoolManagement',
      component: () =>
        import(
          '@/views/garage/factotyManagment/contentPoolManagement/index.vue'
        ),
      name: 'contentPoolManagement',
      meta: { title: '内容池管理', icon: 'list', menuId: 'S11609' }
    },
    {
      path: 'contentPoolDetail',
      component: () =>
        import(
          '@/views/garage/factotyManagment/contentPoolManagement/components/detail.vue'
        ),
      name: 'contentPoolDetail',
      meta: { title: '内容池详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'bulletinBoardAudit',
      component: () =>
        import('@/views/garage/factotyManagment/bulletinBoardAudit/index.vue'),
      name: 'bulletinBoardAudit',
      meta: { title: '公告栏审核', icon: 'list', menuId: 'S11610' }
    }
  ]
}

export default factoryManagement
