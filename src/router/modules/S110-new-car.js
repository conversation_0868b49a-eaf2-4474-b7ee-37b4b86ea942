import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/


import menu from '@/router/menuIds'

const newCar = {
  path: '/newCar',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '售车页面', icon: 'peoples', menuId: 'S110' },
  children: [
    {
      path: 'carManagement',
      component: () => import('@/views/newCar/carManagement/index.vue'),

      name: 'CarManagement',
      meta: {
        title: '车辆管理',
        icon: 'list',
        noCache: true,
        menuId: 'S11008',
      },
    },
    {
      path: 'carManagement-detail',
      component: () => import('@/views/newCar/carManagement/detail.vue'),

      name: 'CarManagementDetail',
      meta: {
        title: '车辆创建',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit,
      },
      hidden: true,
    },
    {
      path: 'agreementMerchantList',
      component: () => import('@/views/newCar/agreementMerchantList/index.vue'),

      name: 'AgreementMerchantList',
      meta: { title: '协议经销商', icon: 'list', menuId: 'S11009' },
    },
    {
      path: 'purchaseOrderManagement',
      component: () =>
        import('@/views/newCar/purchaseOrderManagement/index.vue'),

      name: 'PurchaseOrderManagement',
      meta: {
        title: '订单管理',
        icon: 'list',
        noCache: true,
        menuId: 'S11010',
      },
    },
    {
      path: 'purchaseOrderManagement-detail',
      component: () =>
        import('@/views/newCar/purchaseOrderManagement/detail.vue'),

      name: 'PurchaseOrderManagementDetail',
      meta: {
        title: '订单详情',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit,
      },
      hidden: true,
    },
    {
      path: 'selectCarConfig',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '售车（历史）', icon: 'peoples', menuId: 'S11003' },
      children: [
        {
          path: 'activityManagment',
          component: () =>
            import(
              '@/views/newCar/selectCarConfig/activityManagment/index.vue'
            ),
          name: 'activityManagment',
          meta: { title: '活动配置', icon: 'list', menuId: 'S1100301' },
        },
        {
          path: 'activityConfig',
          component: () =>
            import('@/views/newCar/selectCarConfig/activityConfig/index.vue'),
          name: 'activityConfig',
          meta: {
            title: '新建/编辑售车活动配置',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit,
          },
          hidden: true,
        },
        {
          path: 'carOrderList',
          component: () => import('@/views/newCar/carOrderList/index.vue'),

          name: 'carOrderList',
          meta: { title: '车辆订单', icon: 'list', menuId: 'S1100302' },
        },
        {
          path: 'sellableCarConfig',
          component: () => import('@/views/newCar/sellableCarConfig/index.vue'),

          name: 'sellableCarConfig',
          meta: { title: '售车可售车辆配置', icon: 'list', menuId: 'S1100303' },
        },
        {
          path: 'editConfig',
          component: () => import('@/views/newCar/editConfig/index.vue'),

          name: 'editConfig',
          meta: {
            title: '新建/编辑可售车辆',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit,
          },
          hidden: true,
        },
        {
          path: 'editCoupon',
          component: () => import('@/views/newCar/editCoupon/index.vue'),

          name: 'editCoupon',
          meta: {
            title: '新建/编辑优惠券',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit,
          },
          hidden: true,
        },
        {
          path: 'couponOrderList',
          component: () => import('@/views/newCar/couponOrderList/index.vue'),

          name: 'CouponOrderList',
          meta: { title: '优惠券订单', icon: 'list', menuId: 'S1100304' },
        },
        {
          path: 'create-coupon',
          component: () => import('@/views/newCar/create-coupon/index.vue'),

          name: 'CreateCoupon',
          meta: { title: '创建优惠券', icon: 'list', menuId: 'S1100305' },
        },
        // {
        //   path: 'decodeMobile',
        //   component: () => import('@/views/newCar/decodeMobile/index.vue'),
        //   name: 'decodeMobile',
        //   meta: { title: '电话解密', icon: 'list', noCache: true, menuId: 'S1100306' }
        // }
        // {
        //   path: 'rentCarOrder',
        //   component: () => import('@/views/tools/RentCarOrder/index.vue'),
        //   name: 'RentCarOrder',
        //   meta: { title: '租车订单', icon: 'list', menuId: 'S11007' }
        // },
      ],
    },
  ],
}

export default newCar
