import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const content = {
  path: '/channel',
  meta: { title: '渠道', icon: 'peoples', menuId: 'S115' },
  redirect: 'noredirect',
  component: () => import('@/views/layout/Layout.vue'),
  children: [
    {
      path: 'channelLaunchList',
      component: () => import('@/views/channel/channelLaunchList/index.vue'),

      name: 'channelLaunchList',
      meta: {
        title: '渠道投放列表',
        icon: 'list',
        noCache: true,
        menuId: 'S11501',
      },
    },
    {
      path: 'channelManage',
      component: () => import('@/views/channel/channelManage/index.vue'),

      name: 'channelManage',
      meta: {
        title: '渠道管理',
        icon: 'list',
        noCache: true,
        menuId: 'S11502',
      },
    },
    {
      path: 'channelClassification',
      component: () =>
        import('@/views/channel/channelClassification/index.vue'),

      name: 'channelClassification',
      meta: {
        title: '分级管理',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit,
      },
      hidden: true,
    },
  ],
}

export default content
