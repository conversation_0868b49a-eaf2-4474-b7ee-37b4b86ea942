import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

//
import menu from '@/router/menuIds'

const audit = {
  path: '/audit',

  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '审核平台', icon: 'peoples', menuId: 'S111' },
  children: [
    {
      path: 'repeatedReviewContent',
      component: () =>
        import(
          '@/views/business/articleManage/repeatedReviewContent/index.vue'
        ),
      name: 'repeatedReviewContent',
      meta: {
        title: '内容复审',
        icon: 'list',
        noCache: true,
        menuId: 'S11133'
      }
    },
    {
      path: 'auditRecord',
      component: () =>
        import('@/views/business/articleManage/auditRecord/index.vue'),

      name: 'auditRecord',
      meta: {
        title: '当日审核数据',
        icon: 'list',
        noCache: true,
        menuId: 'S11101'
      }
    },
    // {
    //   path: 'articleManageList',
    //   component: () => import('@/views/business/articleManage/articleManageList/index.vue'),
    //   name: 'articleManageList',
    //   meta: { title: '内容列表', icon: 'list', menuId: 'S11102' }
    // },
    {
      path: 'reptileArticleFailList',
      component: () =>
        import(
          '@/views/business/articleManage/reptileArticleFailList/index.vue'
        ),
      name: 'reptileArticleFailListAudit',
      meta: { title: '爬虫文章失效列表', icon: 'list', menuId: 'S11129' }
    },
    // {
    //   path: 'ContentReview',
    //   component: () => import('@/views/business/articleManage/contentreview/index.vue'),
    //   name: 'ContentReview',
    //   meta: { title: '文章审核', icon: 'list', noCache: true, menuId: 'S11104' }
    // },
    // {
    //   path: 'VideoReview',
    //   component: () => import('@/views/business/articleManage/contentreview/index.vue'),
    //   name: 'VideoReview',
    //   meta: { title: '视频审核', icon: 'list', noCache: true, menuId: 'S11105' }
    // },
    // {
    //   path: 'DynamicAudit',
    //   component: () => import('@/views/business/articleManage/dynamicAudit/index.vue'),
    //   name: 'DynamicAudit',
    //   meta: { title: '动态审核', icon: 'list', noCache: true, menuId: 'S11106' }
    // },
    // {
    //   path: 'UsedCarAudit',
    //   component: () => import('@/views/garage/usedCarSector/UsedCarAudit/index.vue'),
    //   name: 'UsedCarAudit',
    //   meta: { title: '二手车审核', icon: 'list', noCache: true, menuId: 'S11109' }
    // },
    {
      path: 'my-used-car-audit-list',
      component: () =>
        import('@/views/garage/usedCarSector/my-used-car-audit-list/index.vue'),
      name: 'MyUsedCarAuditList',
      meta: {
        title: '我的二手车审核列表',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'garageApproveAudit',
      component: () =>
        import('@/views/garage/garageManage/garageApproveAudit/index.vue'),
      name: 'garageApproveAudit',
      meta: {
        title: '车辆认证审核',
        icon: 'list',
        noCache: true,
        menuId: 'S11110'
      }
    },
    // {
    //   path: 'VehicleWordOfMouth',
    //   component: () => import('@/views/garage/VehicleWordOfMouth/index.vue'),
    //   name: 'VehicleWordOfMouth',
    //   meta: { title: '车辆口碑审核', icon: 'list', noCache: true, menuId: 'S11111' }
    // },
    // {
    //   path: 'activityControleList',
    //   // component: () => import('@/views/garage/dealerManagement/activityControleList/index.vue'),
    //   component: () => import('@/views/garage/dealerManagement/activityManagement/index.vue'),
    //   name: 'activityControleList',
    //   meta: { title: '经销商活动审核', icon: 'list', menuId: 'S11112' }
    // },
    // {
    //   path: 'activityControleListNew',
    //   component: () => import('@/views/garage/dealerManagement/activityControleList/indexNew/index.vue'),
    //   // component: () => import('@/views/garage/dealerManagement/activityManagement/index.vue'),
    //   name: 'activityControleListNew',
    //   meta: { title: '经销商活动审核', icon: 'list', menuId: 'S11112' }
    // },
    // {
    //   path: 'ActivityDetail',
    //   component: () =>
    //     import('@/views/garage/dealerManagement/activityDetail/index.vue'),
    //   name: 'ActivityDetail',
    //   meta: {
    //     title: '活动审核详情',
    //     icon: 'list',
    //     noCache: true,
    //     menuId: menu.noLimit
    //   },
    //   hidden: true
    // },
    {
      path: 'couponInvoiceAuditDetail',
      component: () =>
        import('@/views/newCar/couponInvoiceAuditDetail/index.vue'),

      name: 'CouponInvoiceAuditDetail',
      meta: {
        title: '购新车发票审核详情',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    // {
    //   path: 'carPurchaseInvoiceReview',
    //   component: () => import('@/views/newCar/carPurchaseInvoiceReview/index.vue'),
    //   name: 'CarPurchaseInvoiceReview',
    //   meta: { title: '购新车发票审核', icon: 'list', menuId: 'S11116' }
    // },
    {
      path: 'createArticle',
      component: () =>
        import(
          '@/views/business/articleManage/articleManageList/newArticle/index.vue'
        ),
      name: 'createArticle',
      meta: { title: '创建内容', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'EditArticle',
      component: () =>
        import(
          '@/views/business/articleManage/articleManageList/editArticle/index.vue'
        ),
      name: 'EditArticle',
      meta: {
        title: '编辑内容',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'MyTask',
      component: () =>
        import('@/views/business/articleManage/mytask/index.vue'),

      name: 'MyTask',
      meta: {
        title: '我的工作',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'realNameAuthAudit',
      component: () => import('@/views/authAudit/realNameAuth/index.vue'),

      name: 'realNameAuthAudit',
      meta: { title: '实名认证审核', icon: 'list', menuId: 'S11115' }
    },
    // {
    //   path: 'cityVisitShopAuthAudit',
    //   component: () => import('@/views/authAudit/cityVisitShopAuthAudit/index.vue'),
    //   name: 'cityVisitShopAuthAudit',
    //   meta: { title: '城市官探店审核', icon: 'list', menuId: 'S11117' },
    // },
    // {
    //   path: 'RentCarAudit',
    //   component: () => import('@/views/garage/usedCarSector/RentCarAudit/index.vue'),
    //   name: 'RentCarAudit',
    //   meta: { title: '租车业务审核', icon: 'list', noCache: true, menuId: 'S11119' }
    // },
    {
      path: 'my-rent-car-audit-list',
      component: () =>
        import('@/views/garage/usedCarSector/my-rent-car-audit-list/index.vue'),
      name: 'MyRentCarAuditList',
      meta: {
        title: '我的租车审核列表',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    // {
    //   path: 'UserNameAudit',
    //   component: () => import('@/views/business/articleManage/userNameAudit/index.vue'),
    //   name: 'UserNameAudit',
    //   meta: { title: '用户名审核', icon: 'list', noCache: true, menuId: 'S11132' }
    // },
    // {
    //   path: 'UserReviewAvatar',
    //   component: () => import('@/views/business/articleManage/userReviewAvatar/index.vue'),
    //   name: 'UserReviewAvatar',
    //   meta: { title: '用户头像审核', icon: 'list', noCache: true, menuId: 'S11120' }
    // },
    // {
    //   path: 'complaintManage',
    //   component: () => import('@/views/business/articleManage/complaintManage/index.vue'),
    //   name: 'complaintManage',
    //   meta: { title: '投诉审核', icon: 'list', noCache: true, menuId: 'S11122' }
    // },
    // {
    //   path: 'reportCircleLord',
    //   component: () => import('@/views/circle/reportCircleLord/index.vue'),
    //   name: 'reportCircleLord',
    //   meta: { title: '举报圈主审核', icon: 'list', noCache: true, menuId: 'S11123' }
    // },
    // {
    //   path: 'mentionCar',
    //   component: () => import('@/views/authAudit/mentionCar/index.vue'),
    //   name: 'mentionCar',
    //   meta: { title: '提车价审核', icon: 'list', noCache: true, menuId: 'S11124' }
    // },
    // {
    //   path: 'circleOPerationReview',
    //   component: () => import('@/views/circle/circleOPerationReview/index.vue'),
    //   name: 'circleOPerationReview',
    //   meta: { title: '圈主操作审核', icon: 'list', noCache: true, menuId: 'S11125' }
    // },
    {
      path: 'privateLetterPictures',
      component: () =>
        import(
          '@/views/business/articleManage/privateLetterPictures/index.vue'
        ),
      name: 'PrivateLetterPictures',
      meta: {
        title: '私信图片审核',
        icon: 'list',
        noCache: true,
        menuId: 'S11126'
      }
    },
    {
      path: 'myPrivateLetterPictures',
      component: () =>
        import(
          '@/views/business/articleManage/myPrivateLetterPictures/index.vue'
        ),
      name: 'MyPrivateLetterPictures',
      meta: {
        title: '我的私信图片审核列表',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'privateLetterReview',
      component: () =>
        import('@/views/business/articleManage/privateLetterReview/index.vue'),
      name: 'PrivateLetterReview',
      meta: {
        title: '私信审核',
        icon: 'list',
        noCache: true,
        menuId: 'S11127'
      }
    },
    {
      path: 'myPrivateLetterReview',
      component: () =>
        import(
          '@/views/business/articleManage/myPrivateLetterReview/index.vue'
        ),
      name: 'myPrivateLetterReview',
      meta: {
        title: '我的私信审核列表',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'replyAudit',
      component: () => import('@/views/replyAudit/index.vue'),

      name: 'replyAudit',
      meta: {
        title: '自动回复审核',
        icon: 'list',
        noCache: true,
        menuId: 'S11128'
      }
    },
    // {
    //   path: 'commentAudit',
    //   component: () => import('@/views/business/articleManage/commentAudit/index.vue'),
    //   name: 'commentAudit',
    //   meta: { title: '评论审核', icon: 'list', noCache: true, menuId: 'S11131' }
    // },
    {
      path: 'secondCommentAudit',
      component: () =>
        import('@/views/business/articleManage/secondCommentAudit/index.vue'),
      name: 'secondCommentAudit',
      meta: {
        title: '二手车评论审核',
        icon: 'list',
        noCache: true,
        menuId: 'S11130'
      }
    }
  ]
}

export default audit
