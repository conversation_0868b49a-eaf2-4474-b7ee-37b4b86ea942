import * as Vue from 'vue'

import menu from '@/router/menuIds'
const driveTestManage = {
  path: '/driveTestManage',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '驾考管理', icon: 'peoples', menuId: 'S120' },
  children: [
    {
      path: 'drivingTestTopicAudit',
      component: () =>
        import('@/views/driveTestManage/drivingTestTopicAudit/index.vue'),
      name: 'drivingTestTopicAudit',
      meta: { title: '驾考题库更新审核', icon: 'list', menuId: 'S12003' }
    },
    {
      path: 'drivingTestVIPQuestion',
      component: () =>
        import('@/views/driveTestManage/drivingTestVIPQuestion/index.vue'),
      name: 'drivingTestVIPQuestion',
      meta: { title: '驾考VIP题库更新', icon: 'list', menuId: 'S12006' }
    },
    {
      path: 'drivingTestVIPVideo',
      component: () =>
        import('@/views/driveTestManage/drivingTestVIPVideo/index.vue'),
      name: 'drivingTestVIPVideo',
      meta: { title: '驾考视频配置', icon: 'list', menuId: 'S12007' }
    },
    {
      path: 'drivingTestVIPOrder',
      component: () =>
        import('@/views/driveTestManage/drivingTestVIPOrder/index.vue'),
      name: 'drivingTestVIPOrder',
      meta: { title: '驾考订单明细', icon: 'list', menuId: 'S12008' }
    },
    {
      path: 'driveTestCuleSet',
      component: () =>
        import('@/views/driveTestManage/driveTestCuleSet/index.vue'),
      name: 'driveTestCuleSet',
      meta: { title: '驾考线索配置', icon: 'list', menuId: 'S12001' }
    },
    {
      path: 'driveTestEnrollCuleNote',
      component: () =>
        import('@/views/driveTestManage/driveTestEnrollCuleNote/index.vue'),
      name: 'driveTestEnrollCuleNote',
      meta: { title: '驾考报名线索记录', icon: 'list', menuId: 'S12002' }
    },
    {
      path: 'shifInfoReview',
      component: () =>
        import('@/views/driveTestManage/shifInfoReview/index.vue'),
      name: 'shifInfoReview',
      meta: { title: '班型信息审核', icon: 'list', menuId: 'S12004' }
    },
    {
      path: 'classTypeManage',
      component: () =>
        import('@/views/driveTestManage/classTypeManage/index.vue'),
      name: 'ClassTypeManage',
      meta: { title: '驾考班型列表', icon: 'list', menuId: 'S12005' }
    },
    {
      path: 'classTypeManageDetail',
      component: () =>
        import('@/views/driveTestManage/classTypeManage/detail.vue'),
      name: 'ClassTypeManageDetail',
      meta: { title: '驾考班型详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    }
  ]
}

export default driveTestManage
