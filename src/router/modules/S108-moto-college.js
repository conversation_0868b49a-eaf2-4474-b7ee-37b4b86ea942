import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

// 
// import menu from '@/router/menuIds'

const motoCollege = {
  path: '/motoCollege',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '摩托学院', icon: 'peoples', menuId: 'S108' },
  children: [
    {
      path: 'CollegeEssay',
      component: () =>
        import('@/views/business/motoCollege/collegeEssay/index.vue'),

      name: 'CollegeEssay',
      meta: { title: '学院文章', icon: 'list', menuId: 'S10801' },
    },
    {
      path: 'CollegeInstructor',
      component: () =>
        import('@/views/business/motoCollege/collegeInstructor/index.vue'),
      name: 'CollegeInstructor',
      meta: { title: '学院教官', icon: 'list', menuId: 'S10802' },
    },
  ],
}

export default motoCollege
