/** When your routing table is too long, you can split it into small modules**/
import menu from '@/router/menuIds'

const content = {
  path: '/content',
  meta: { title: '内容平台', icon: 'peoples', menuId: 'S113' },
  redirect: 'noredirect',
  component: () => import('@/views/layout/Layout.vue'),
  children: [
    {
      path: 'contentManage',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '内容管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11301'
      },
      children: [
        {
          path: 'articleManageList',
          component: () =>
            import(
              '@/views/business/articleManage/articleManageList/index.vue'
            ),
          name: 'articleManageList',
          meta: { title: '内容列表', icon: 'list', menuId: 'S1130101' }
        },
        {
          path: 'reptileArticleFailList',
          component: () =>
            import(
              '@/views/business/articleManage/reptileArticleFailList/index.vue'
            ),
          name: 'reptileArticleFailList',
          meta: { title: '爬虫待处理列表', icon: 'list', menuId: 'S1130102' }
        },
        {
          path: 'contentReviewlist',
          component: () =>
            import(
              '@/views/business/articleManage/contentReviewlist/index.vue'
            ),
          name: 'contentReviewlist',
          meta: {
            title: '内容标注抽查表',
            icon: 'list',
            noCache: true,
            menuId: 'S1130103'
          }
        },
        {
          path: 'classify',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: {
            title: '分类管理',
            icon: 'peoples',
            noCache: true,
            menuId: 'S1130104'
          },
          children: [
            {
              path: 'Grade',
              component: () =>
                import('@/views/business/articleManage/grade/index.vue'),
              name: 'Grade',
              meta: {
                title: '分级管理',
                icon: 'list',
                noCache: true,
                menuId: 'S113010401'
              }
            },
            {
              path: 'Classify',
              component: () =>
                import('@/views/business/articleManage/classify/index.vue'),
              name: 'Classify',
              meta: {
                title: '分类管理',
                icon: 'list',
                noCache: true,
                menuId: 'S113010402'
              }
            },
            {
              path: 'LevelTagReport',
              component: () =>
                import(
                  '@/views/business/articleManage/leveltagreport/index.vue'
                ),
              name: 'LevelTagReport',
              meta: {
                title: '分级分类报表',
                icon: 'list',
                noCache: true,
                menuId: 'S113010403'
              }
            }
          ]
        },
        {
          path: 'coolingManagement',
          component: () =>
            import('@/views/business/coolingManagement/index.vue'),

          name: 'coolingManagement',
          meta: { title: '冷处理管理', icon: 'list', menuId: 'S1130105' }
        },
        {
          path: 'ReplyManagement',
          component: () =>
            import('@/views/business/articleManage/replymanagement/index.vue'),
          name: 'ReplyManagement',
          meta: {
            title: '评论管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1130106'
          }
        },
        {
          path: 'search',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: {
            title: '搜索管理',
            icon: 'peoples',
            noCache: true,
            menuId: 'S1130107'
          },
          children: [
            {
              path: 'searchRecommend',
              component: () =>
                import(
                  '@/views/business/searchManage/searchRecommend/index.vue'
                ),
              name: 'searchRecommend',
              meta: { title: '热搜推荐', icon: 'list', menuId: 'S113010701' }
            }
          ]
        },
        {
          path: 'content-blacklist',
          component: () =>
            import(
              '@/views/business/articleManage/content-blacklist/index.vue'
            ),
          name: 'ContentBlacklist',
          meta: {
            title: '内容黑名单',
            icon: 'list',
            noCache: true,
            menuId: 'S1130108'
          }
        },
        {
          path: 'content-whitelist',
          component: () =>
            import(
              '@/views/business/articleManage/content-whitelist/index.vue'
            ),
          name: 'ContentWhitelist',
          meta: {
            title: '内容白名单',
            icon: 'list',
            noCache: true,
            menuId: 'S1130111'
          }
        },
        {
          path: 'content-down-exposure',
          component: () =>
            import(
              '@/views/business/articleManage/content-down-exposure/index.vue'
            ),
          name: 'contennDownExposure',
          meta: {
            title: '内容降曝光',
            icon: 'list',
            noCache: true,
            menuId: 'S1130110'
          }
        },
        {
          path: 'content-repeat-list',
          component: () =>
            import(
              '@/views/business/articleManage/content-repeat-list/index.vue'
            ),
          name: 'content-repeat-list',
          meta: {
            title: '内容查重监控列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1130109'
          }
        },
        {
          path: 'owner-real-shot',
          component: () =>
            import('@/views/business/articleManage/owner-real-shot/index.vue'),
          name: 'owner-real-shot',
          meta: {
            title: '车主实拍',
            icon: 'list',
            noCache: true,
            menuId: 'S11301010'
          }
        },
        {
          path: 'appealAudit',
          component: () =>
            import('@/views/business/articleManage/appealAudit/index.vue'),
          name: 'appealAudit',
          meta: {
            title: '内容申诉',
            icon: 'list',
            noCache: true,
            menuId: '*********'
          }
        },
        {
          path: 'garageInfoManage',
          component: () =>
            import('@/views/business/articleManage/garageInfoManage/index.vue'),
          name: 'garageInfoManage',
          meta: {
            title: '车库资讯/动态管理',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'licensePriceManage',
          component: () =>
            import(
              '@/views/business/articleManage/licensePriceManage/index.vue'
            ),
          name: 'licensePriceManage',
          meta: {
            title: '牌照交易价格管理',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },

        {
          path: 'licenseManage',
          component: () =>
            import('@/views/business/articleManage/licenseManage/index.vue'),
          name: 'licenseManage',
          meta: {
            title: '牌照交易管理',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'licenseManageDetail',
          component: () =>
            import('@/views/business/articleManage/licenseManage/detail.vue'),
          name: 'licenseManageDetail',
          meta: {
            title: '牌照交易管理详情',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'aboutRidingManage',
          component: () =>
            import(
              '@/views/business/articleManage/aboutRidingManage/index.vue'
            ),
          name: 'aboutRidingManage',
          meta: {
            title: '约骑活动管理',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'ridingLineManage',
          component: () =>
            import('@/views/business/articleManage/ridingLineManage/index.vue'),
          name: 'ridingLineManage',
          meta: {
            title: '骑行线路管理',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'clockPointManage',
          component: () =>
            import('@/views/business/articleManage/clockPointManage/index.vue'),
          name: 'clockPointManage',
          meta: {
            title: '打卡点管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1130117'
          }
        },
        {
          path: 'carryAccount',
          component: () =>
            import('@/views/business/articleManage/carryAccount/index.vue'),
          name: 'carryAccount',
          meta: {
            title: '搬运账号维护',
            icon: 'list',
            noCache: true,
            menuId: 'S1130118'
          }
        }
      ]
    },
    {
      path: 'contentOperating',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '内容运营',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11302'
      },
      children: [
        {
          path: 'push',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: {
            title: '推送管理',
            icon: 'peoples',
            noCache: true,
            menuId: 'S1130201'
          },
          children: [
            {
              path: 'PushLibrary',
              component: () => import('@/views/user/pushLibrary/index.vue'),
              name: 'PushLibrary',
              meta: { title: '推送文库', icon: 'list', menuId: 'S113020101' }
            },
            {
              path: 'PushManage',
              component: () => import('@/views/user/push/index.vue'),
              name: 'PushManage',
              meta: { title: '系统推送', icon: 'list', menuId: 'S113020102' }
            },
            {
              path: 'PrivateAccountPush',
              component: () =>
                import(
                  '@/views/garage/usedCarSector/UsedCarOperation/AccountPush/index.vue'
                ),
              name: 'PrivateAccountPush',
              meta: { title: '私信推送', icon: 'list', menuId: 'S113020104' }
            },
            {
              path: 'editPush',
              component: () => import('@/views/user/push/editPush/index.vue'),
              name: 'editPush',
              meta: {
                title: '查看/新建/编辑/复制推送',
                icon: 'list',
                noCache: true,
                menuId: menu.noLimit
              },
              hidden: true
            },
            {
              path: 'PushBusinessManage',
              component: () =>
                import('@/views/user/PushBusinessManage/index.vue'),
              name: 'PushBusinessManage',
              meta: {
                title: '商家推送列表',
                icon: 'list',
                menuId: 'S113020103'
              }
            },
            {
              path: 'editBusinessPush',
              component: () =>
                import(
                  '@/views/user/PushBusinessManage/editBusinessPush/index.vue'
                ),
              name: 'editBusinessPush',
              meta: {
                title: '查看/新建/编辑/复制推送',
                icon: 'list',
                noCache: true,
                menuId: menu.noLimit
              },
              hidden: true
            }
          ]
        },
        {
          path: 'business-activity',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: {
            title: '内容活动管理',
            icon: 'peoples',
            noCache: true,
            menuId: 'S1130203'
          },
          children: [
            {
              path: 'activeConfiguration',
              component: () =>
                import(
                  '@/views/business/activity-management/active-configuration/index.vue'
                ),
              name: 'ActiveConfiguration',
              meta: {
                title: '专题配置',
                icon: 'list',
                noCache: true,
                menuId: 'S113020301'
              }
            },
            {
              path: 'activeConfigurationDetail',
              component: () =>
                import(
                  '@/views/business/activity-management/active-configuration/components/act-detail/index.vue'
                ),
              name: 'ActiveConfigurationDetail',
              meta: {
                title: '专题详情',
                icon: 'list',
                menuId: menu.noLimit
              },
              hidden: true
            },
            {
              path: 'activityManage',
              component: () =>
                import(
                  '@/views/business/activity-management/activity-manage/index.vue'
                ),
              name: 'activityManage',
              meta: {
                title: '专题展示',
                icon: 'list',
                noCache: true,
                menuId: 'S113020302'
              }
            },
            {
              path: 'VotingManagement',
              component: () =>
                import(
                  '@/views/business/activity-management/voting-management/index.vue'
                ),
              name: 'VotingManagement',
              meta: { title: '投票管理', icon: 'list', menuId: 'S113020303' }
            },
            {
              path: 'EditVoting',
              component: () =>
                import(
                  '@/views/business/activity-management/edit-voting/index.vue'
                ),
              name: 'EditVoting',
              meta: { title: '投票编辑', icon: 'list', menuId: menu.noLimit },
              hidden: true
            },
            {
              path: 'InstructorManagement',
              component: () =>
                import(
                  '@/views/business/activity-management/instructor-management/index.vue'
                ),
              name: 'InstructorManagement',
              meta: { title: '评选管理', icon: 'list', menuId: 'S113020304' }
            },
            {
              path: 'InstructorDetails',
              component: () =>
                import(
                  '@/views/business/activity-management/instructor-details/index.vue'
                ),
              name: 'InstructorDetails',
              meta: { title: '教头详情', icon: 'list', menuId: menu.noLimit },
              hidden: true
            },
            {
              path: 'BrandActivityManagement',
              component: () =>
                import(
                  '@/views/business/activity-management/brand-activity-management/index.vue'
                ),
              name: 'BrandActivityManagement',
              meta: {
                title: '品牌活动管理',
                icon: 'list',
                menuId: 'S113020305'
              }
            }
          ]
        },
        {
          path: 'ContentPoolFilteringManagement',
          component: () =>
            import(
              '@/views/business/activity-management/content-pool-filtering-management/index.vue'
            ),
          name: 'ContentPoolFilteringManagement',
          meta: { title: '内容池过滤管理', icon: 'list', menuId: 'S1130205' }
        },
        {
          path: 'ContentPoolFilteringDetail',
          component: () =>
            import(
              '@/views/business/activity-management/content-pool-filtering-management/components/editDetail.vue'
            ),
          name: 'ContentPoolFilteringDetail',
          meta: {
            title: '内容池过滤详情',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'topic',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: {
            title: '话题模块',
            icon: 'peoples',
            noCache: true,
            menuId: 'S1130204'
          },
          children: [
            {
              path: 'classification',
              component: () =>
                import(
                  '@/views/business/topic-module/classification/index.vue'
                ),
              name: 'TopicClassification',
              meta: { title: '话题分类', icon: 'list', menuId: 'S113020401' }
            },
            {
              path: 'articleTopic',
              component: () =>
                import('@/views/business/topic-module/article-topic/index.vue'),
              name: 'ArticleTopic',
              meta: { title: '动态话题', icon: 'list', menuId: 'S113020402' }
            },
            {
              path: 'topicMerge',
              component: () =>
                import('@/views/business/topic-module/topic-merge/index.vue'),
              name: 'TopicMerge',
              meta: { title: '话题合并', icon: 'list', menuId: 'S113020403' }
            },
            {
              path: 'topicRecommended',
              component: () =>
                import(
                  '@/views/business/topic-module/topicRecommended/index.vue'
                ),
              name: 'TopicRecommended',
              meta: { title: '小组件话题', icon: 'list', menuId: 'S113020404' }
            },
            {
              path: 'CircleList',
              component: () =>
                import('@/views/business/topic-module/circle-list/index.vue'),
              name: 'CircleList',
              meta: { title: '话题列表', icon: 'list', menuId: 'S113020405' }
            },
            {
              path: 'FocusCircleRecommendation',
              component: () =>
                import(
                  '@/views/business/topic-module/focus-circle-recommendation/index.vue'
                ),
              name: 'FocusCircleRecommendation',
              meta: {
                title: '新关注话题推荐',
                icon: 'list',
                menuId: 'S113020406'
              }
            }
          ]
        },
        {
          path: 'OfficialAccountContentManagement',
          component: () =>
            import(
              '@/views/business/activity-management/official-account-content-management/index.vue'
            ),
          name: 'OfficialAccountContentManagement',
          meta: {
            title: '公众号内容管理列表',
            icon: 'list',
            menuId: 'S1130206'
          }
        },
        {
          path: 'OfficialAccountCooperationProjects',
          component: () =>
            import(
              '@/views/business/activity-management/official-account-cooperation-projects/index.vue'
            ),
          name: 'OfficialAccountCooperationProjects',
          meta: {
            title: '公众号合作项目列表',
            icon: 'list',
            menuId: 'S1130207'
          }
        }
      ]
    },
    {
      path: 'contentSecurity',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '内容安全',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11303'
      },
      children: [
        {
          path: 'dead-word-manage',
          component: () => import('@/views/user/dead-word-manage/index.vue'),

          name: 'DeadWordManage',
          meta: { title: '致命词管理', icon: 'list', menuId: 'S1130301' }
        },
        {
          path: 'sensitive-word-manage',
          component: () =>
            import('@/views/user/sensitive-word-manage/index.vue'),

          name: 'SensitiveWordManage',
          meta: { title: '敏感词管理', icon: 'list', menuId: 'S1130302' }
        },
        {
          path: 'ReportManagement',
          component: () =>
            import('@/views/user/reportManagement/new-report-management.vue'),

          name: 'ReportManagement', // 原用户平台下，举报管理
          meta: { title: '内容举报管理', icon: 'list', menuId: 'S1130303' }
        },
        {
          path: 'UserHighRiskTreatmentList',
          component: () =>
            import('@/views/user/UserHighRiskTreatmentList/index.vue'),
          name: 'UserHighRiskTreatmentList',
          meta: {
            title: '内容高危用户处理',
            icon: 'list',
            noCache: true,
            menuId: 'S1130304'
          }
        },
        {
          path: 'UserHighRiskTreatmentDetail',
          component: () =>
            import(
              '@/views/user/UserHighRiskTreatmentList/components/detail.vue'
            ),
          name: 'UserHighRiskTreatmentDetail',
          meta: {
            title: '内容高危用户处理详情',
            icon: 'list',
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'UserHighRiskManagementList',
          component: () =>
            import('@/views/user/UserHighRiskManagementList/index.vue'),
          name: 'UserHighRiskManagementList',
          meta: {
            title: '内容高危用户管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1130305'
          }
        }
      ]
    },
    {
      path: 'home-config',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '首页配置',
        icon: 'peoples',
        noCache: true,
        menuId: 'S11304'
      },
      children: [
        {
          path: 'homepageRecommend',
          component: () =>
            import(
              '@/views/business/homepageConfig/homepageRecommend/index.vue'
            ),
          name: 'homepageRecommend',
          meta: { title: '首页发现', icon: 'list', menuId: 'S1130401' }
        },
        {
          path: 'homepageBlock',
          component: () =>
            import('@/views/business/homepageConfig/homepageBlock/index.vue'),
          name: 'homepageBlock',
          meta: { title: '首页小组件', icon: 'list', menuId: 'S1130402' }
        }
      ]
    },
    {
      path: 'answerInteraction',
      component: () =>
        import('@/views/business/question-module/answer-interaction/index.vue'),
      name: 'AnswerInteraction',
      meta: { title: '回答互动', icon: 'list', menuId: 'S11305' }
    }
  ]
}

export default content
