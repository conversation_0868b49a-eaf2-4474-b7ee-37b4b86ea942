/** When your routing table is too long, you can split it into small modules**/
import menu from '@/router/menuIds'

const compose = {
  path: '/compose',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '创作平台', icon: 'peoples', menuId: 'S112' },
  children: [
    {
      path: 'cooperationList',
      component: () =>
        import('@/views/business/contentTreasure/cooperationList/index.vue'),
      name: 'CooperationList',
      meta: { title: '内容宝合作项目列表', icon: 'list', menuId: 'S11213' }
    },
    {
      path: 'reviewPage',
      component: () =>
        import('@/views/business/contentTreasure/reviewPage/index.vue'),
      name: 'ReviewPage',
      meta: { title: '内容宝内容管理', icon: 'list', menuId: 'S11210' }
    },
    {
      path: 'managementActivity',
      component: () =>
        import('@/views/business/contentTreasure/managementActivity/index.vue'),
      name: 'ManagementActivity',
      meta: { title: '内容宝活动管理', icon: 'list', menuId: 'S11211' }
    },
    {
      path: 'managementActivityDetail',
      component: () =>
        import(
          '@/views/business/contentTreasure/managementActivity/detail.vue'
        ),
      name: 'managementActivityDetail',
      meta: { title: '内容宝活动详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'creatorManagement',
      component: () =>
        import('@/views/business/composer/creatorManagement/index.vue'),
      name: 'creatorManagement',
      meta: { title: '创作者管理', icon: 'list', menuId: 'S11212' }
    },
    {
      path: 'actList',
      component: () =>
        import('@/views/business/creation-act/act-list/index.vue'),

      name: 'actList',
      meta: { title: '创作活动列表', icon: 'list', menuId: 'S11207' }
    },
    {
      path: 'actEssayList',
      component: () =>
        import('@/views/business/creation-act/act-essay-list/index.vue'),
      name: 'actEssayList',
      meta: { title: '创作活动文章', icon: 'list', menuId: 'S11208' }
    },
    {
      path: 'actDetail',
      component: () =>
        import('@/views/business/creation-act/act-detail/index.vue'),

      name: 'actDetail',
      meta: { title: '创作活动编辑', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'newApplicationList',
      component: () =>
        import(
          '@/views/business/creatorWithdrawal/newApplicationList/index.vue'
        ),
      name: 'newApplicationList',
      meta: { title: '新创作者提现申请', icon: 'list', menuId: 'S112011' }
    },
    {
      path: 'applicationList',
      component: () =>
        import('@/views/business/creatorWithdrawal/applicationList/index.vue'),
      name: 'ApplicationList',
      meta: { title: '创作者提现申请', icon: 'list', menuId: 'S11209' }
    },
    {
      path: 'newOperationList',
      component: () =>
        import('@/views/business/creatorWithdrawal/newOperationList/index.vue'),
      name: 'newOperationList',
      meta: { title: '新创作者明细', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'operationList',
      component: () =>
        import('@/views/business/creatorWithdrawal/operationList/index.vue'),
      name: 'OperationList',
      meta: { title: '创作者明细', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'incomeEquityManagement',
      component: () =>
        import(
          '@/views/business/incentiveModule/incomeEquityManagement/index.vue'
        ),
      name: 'incomeEquityManagement',
      meta: { title: '收益权限管理', icon: 'list', menuId: 'S11201' }
    },
    {
      path: 'incomeArticleDetails',
      component: () =>
        import(
          '@/views/business/incentiveModule/incomeArticleDetails/index.vue'
        ),
      name: 'incomeArticleDetails',
      meta: { title: '收益文章明细', icon: 'list', menuId: 'S11202' }
    },
    {
      path: 'presentationDetails',
      component: () =>
        import(
          '@/views/business/incentiveModule/presentationDetails/index.vue'
        ),
      name: 'presentationDetails',
      meta: { title: '提现明细', icon: 'list', menuId: 'S11203' }
    },
    {
      path: 'composer-equity',
      component: () => import('@/views/business/composer/equity/index.vue'),

      name: 'composer-equity',
      meta: { title: '作者等级权益', icon: 'list', menuId: 'S11204' }
    },
    {
      path: 'composer-manage',
      component: () => import('@/views/business/composer/manage/index.vue'),

      name: 'composer-manage',
      meta: { title: '作者等级管理', icon: 'list', menuId: 'S11205' }
    },
    {
      path: 'composer-detail',
      component: () => import('@/views/business/composer/detail/index.vue'),

      name: 'composer-detail',
      meta: { title: '作者等级详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'expertCollege',
      component: () =>
        import('@/views/business/articleManage/expertCollege/index.vue'),
      name: 'expertCollege',
      meta: {
        title: '行家平台创作学院',
        icon: 'list',
        noCache: true,
        menuId: 'S11206'
      }
    },
    {
      path: 'spical-author-activity',
      component: () =>
        import(
          '@/views/business/articleManage/spical-author-activity/index.vue'
        ),
      name: 'spicalAuthorActivity',
      meta: {
        title: '特邀内容活动列表',
        icon: 'list',
        noCache: true,
        menuId: 'S112010'
      }
    },
    {
      path: 'real-time-revenue',
      component: () =>
        import('@/views/business/articleManage/real-time-revenue/index.vue'),
      name: 'realTimeRevenue',
      meta: {
        title: '创作者实时收益',
        icon: 'list',
        noCache: true,
        menuId: 'S112012'
      }
    },
    {
      path: 'revenue-report',
      component: () =>
        import('@/views/business/articleManage/revenue-report/index.vue'),
      name: 'revenueReport',
      meta: {
        title: '收益报表',
        icon: 'list',
        noCache: true,
        menuId: 'S112013'
      }
    },
    {
      path: 'expert-management',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '平台管理', icon: 'peoples', menuId: 'S1121' },
      children: [
        {
          path: 'expert-help',
          component: () =>
            import(
              '@/views/business/composer/expert-management/expert-help/index.vue'
            ),
          name: 'expert-help',
          meta: {
            title: '帮助中心',
            icon: 'list',
            noCache: true,
            menuId: 'S112101'
          }
        },
        {
          path: 'expert-notice',
          component: () =>
            import(
              '@/views/business/composer/expert-management/expert-notice/index.vue'
            ),
          name: 'expert-notice',
          meta: {
            title: '后台公告',
            icon: 'list',
            noCache: true,
            menuId: 'S112102'
          }
        }
      ]
    }
  ]
}

export default compose
