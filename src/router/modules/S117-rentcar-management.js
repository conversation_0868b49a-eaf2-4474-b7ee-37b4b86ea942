import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const rentcarManagement = {
  path: '/rentcarManagement',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '租车管理', icon: 'peoples', menuId: 'S117' },
  children: [
    {
      path: 'rentcarVehicleList',
      component: () =>
        import('@/views/garage/rentcarManagement/rentcarVehicleList/index.vue'),
      name: 'RentcarVehicleList',
      meta: { title: '租车车辆列表', icon: 'list', menuId: 'S11701' }
    },

    {
      path: 'rentcarVehicleDetail',
      component: () =>
        import(
          '@/views/garage/rentcarManagement/components/rentcarVehicleDetail.vue'
        ),
      name: 'RentcarVehicleDetail',
      meta: { title: '租车车辆详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'rentCarOrder',
      component: () => import('@/views/tools/RentCarOrder/index.vue'),

      name: 'RentCarOrder',
      meta: { title: '租车订单', icon: 'list', menuId: 'S11702' }
    },
    {
      path: 'rentCarOrderDetail',
      component: () =>
        import(
          '@/views/garage/rentcarManagement/components/rentCarOrderDetail.vue'
        ),
      name: 'RentCarOrderDetail',
      meta: { title: '租车订单详情', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'rentCarTopOrder',
      component: () =>
        import('@/views/garage/rentcarManagement/rentCarTopOrder/index.vue'),
      name: 'rentCarTopOrder',
      meta: { title: '租车置顶订单', icon: 'list', menuId: 'S11706' }
    },
    {
      path: 'rentCarReporting',
      component: () =>
        import('@/views/garage/rentcarManagement/rentCarReporting/index.vue'),
      name: 'RentCarReporting',
      meta: { title: '租车举报管理', icon: 'list', menuId: 'S11703' }
    },
    {
      path: 'RentCarClue',
      component: () =>
        import('@/views/garage/rentcarManagement/rentCarClue/index.vue'),
      name: 'RentCarClue',
      meta: { title: '基础线索配置', icon: 'list', menuId: 'S11704' }
    },
    {
      path: 'rentalCluesList',
      component: () =>
        import('@/views/garage/dealerManagement/rentalCluesList/index.vue'),
      name: 'RentalCluesList',
      meta: { title: '租车线索', icon: 'list', menuId: 'S11705' }
    }
  ]
}

export default rentcarManagement
