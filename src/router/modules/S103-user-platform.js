/** When your routing table is too long, you can split it into small modules**/
import menu from '@/router/menuIds'

const userPlatform = {
  path: '/userPlatform',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '用户平台', icon: 'user', menuId: 'S103' },
  children: [
    {
      path: 'userManagement',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '用户管理', icon: 'peoples', menuId: 'S10301' },
      children: [
        {
          path: 'userAccountCorrelation',
          component: () =>
            import('@/views/user/userAccountCorrelation/index.vue'),
          name: 'UserAccountCorrelation',
          meta: {
            title: '用户账号管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1030101'
          }
        },
        {
          path: 'userAccountCorrelationDetail',
          component: () =>
            import('@/views/user/userAccountCorrelation/detail.vue'),
          name: 'userAccountCorrelationDetail',
          meta: {
            title: '用户账号详情',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'authenticationManagement',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: { title: '认证管理', icon: 'peoples', menuId: 'S1030102' },
          children: [
            {
              path: 'femalefriend',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/femalefriend/index.vue'
                ),
              name: 'femalefriend',
              meta: { title: '女魔友', icon: 'list', menuId: 'S103010201' }
            },
            {
              path: 'expert',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/expert/index.vue'
                ),
              name: 'expert',
              meta: { title: '创作者', icon: 'list', menuId: 'S103010202' }
            },
            {
              path: 'security-officer',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/security-officer/index.vue'
                ),
              name: 'SecurityOfficer',
              meta: {
                title: '摩托范安全官',
                icon: 'list',
                menuId: 'S103010203'
              }
            },
            {
              path: 'city-officer',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/city-officer/index.vue'
                ),
              name: 'CityOfficer',
              meta: { title: '城市官审核', icon: 'list', menuId: 'S103010204' }
            },
            {
              path: 'city-haro',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/city-haro/index.vue'
                ),
              name: 'CityHaro',
              meta: { title: '城市官管理', icon: 'list', menuId: 'S103010205' }
            },
            {
              path: 'city-officer-data-config',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/city-officer-data-config/index.vue'
                ),
              name: 'CityOfficerDataConfig',
              meta: {
                title: '城市官数据池配置',
                icon: 'list',
                menuId: 'S103010214'
              }
            },
            {
              path: 'growth-officer-audit',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/growth-officer-audit/index.vue'
                ),
              name: 'GrowthOfficerAaudit',
              meta: { title: '增长官审核', icon: 'list', menuId: 'S103010206' }
            },
            {
              path: 'growth-officer',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/growth-officer/index.vue'
                ),
              name: 'GrowthOfficer',
              meta: { title: '增长官管理', icon: 'list', menuId: 'S103010207' }
            },
            {
              path: 'patrol-officer-audit',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/patrol-officer-audit/index.vue'
                ),
              name: 'PatrolOfficerAaudit',
              meta: { title: '巡查官审核', icon: 'list', menuId: 'S103010208' }
            },
            {
              path: 'inspection-college',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/inspection-college/index.vue'
                ),
              name: 'InspectionCollege',
              meta: {
                title: '巡查学院内容配置',
                icon: 'list',
                menuId: 'S103010211'
              }
            },
            {
              path: 'patrol-officer',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/patrol-officer/index.vue'
                ),
              name: 'PatrolOfficer',
              meta: { title: '巡查官管理', icon: 'list', menuId: 'S103010209' }
            },
            {
              path: 'patrol-officer-data-config',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/patrol-officer-data-config/index.vue'
                ),
              name: 'PatrolOfficerDataConfig',
              meta: {
                title: '巡查官数据池配置',
                icon: 'list',
                menuId: 'S103010210'
              }
            },
            {
              path: 'commentor-audit',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/commentor/index.vue'
                ),
              name: 'CommentorAudit',
              meta: { title: '点评官审核', icon: 'list', menuId: 'S103010212' }
            },
            {
              path: 'commentor-management',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/commentor/management.vue'
                ),
              name: 'CommentorManagement',
              meta: { title: '点评官管理', icon: 'list', menuId: 'S103010213' }
            },
            {
              path: 'commentor-record',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/commentor/record.vue'
                ),
              name: 'CommentorRecord',
              meta: { title: '操作日志', icon: 'list', menuId: menu.noLimit },
              hidden: true
            },
            {
              path: 'grow-record',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/grow-record/index.vue'
                ),
              name: 'GrowRecord',
              meta: { title: '操作日志', icon: 'list', menuId: menu.noLimit },
              hidden: true
            },
            {
              path: 'patrol-record',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/patrol-record/index.vue'
                ),
              name: 'PatrolRecord',
              meta: { title: '操作日志', icon: 'list', menuId: menu.noLimit },
              hidden: true
            }
          ]
        },
        {
          path: 'vestaccount',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: { title: '马甲号管理', icon: 'peoples', menuId: 'S1030103' },
          children: [
            {
              path: 'list',
              component: () =>
                import('@/views/tools/vest-account/list/index.vue'),
              name: 'VestList',
              meta: { title: '马甲号列表', icon: 'list', menuId: 'S103010301' }
            },
            {
              path: 'crawler',
              component: () =>
                import('@/views/tools/vest-account/crawler/index.vue'),
              name: 'Crawler',
              meta: { title: '爬虫配置', icon: 'list', menuId: 'S103010302' }
            },
            {
              path: 'interactive',
              component: () =>
                import('@/views/tools/vest-account/interactive/index.vue'),
              name: 'VestActive',
              meta: { title: '互动', icon: 'eye', menuId: 'S103010303' }
            }
          ]
        },
        {
          path: 'helpCenterConfig',
          component: () => import('@/views/user/helpCenterConfig/index.vue'),
          name: 'HelpCenterConfig',
          meta: {
            title: '帮助中心常用问题',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        }
      ]
    },
    {
      path: 'userSecurity',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '用户安全', icon: 'peoples', menuId: 'S10302' },
      children: [
        {
          path: 'UserExceptionHandling',
          component: () =>
            import('@/views/user/userExceptionHandling/index.vue'),
          name: 'UserExceptionHandling',
          meta: { title: '用户异常处理', icon: 'list', menuId: '********' }
        },
        {
          path: 'audit-adjustment',
          component: () => import('@/views/user/audit-adjustment/index.vue'),
          name: 'AuditAdjustment',
          meta: { title: '触发限制用户列表', icon: 'list', menuId: '********' }
        },
        {
          path: 'UserBanList',
          component: () => import('@/views/user/userBanList/index.vue'),
          name: 'UserBanList',
          meta: { title: '用户封禁列表', icon: 'list', menuId: '********' }
        },
        {
          path: 'UserForbid',
          component: () => import('@/views/user/forbid/index.vue'),
          name: 'UserForbidHandling',
          meta: { title: '封禁ip/设备处理', icon: 'list', menuId: '********' }
        },
        {
          path: 'whitelist-management',
          component: () =>
            import('@/views/user/whitelist-management/index.vue'),
          name: 'WhitelistManagement', // 原白名单
          meta: {
            title: '举报/异常处理白名单',
            icon: 'list',
            menuId: '********'
          }
        },
        {
          path: 'real-person-management',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: { title: '实人管理', icon: 'list', menuId: '********' },
          children: [
            {
              path: 'realAuthentication',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/realAuthentication/index.vue'
                ),
              name: 'realAuthentication',
              meta: {
                title: '用户实名认证',
                icon: 'list',
                menuId: '********01'
              }
            },
            {
              path: 'realAuthenticationList',
              component: () =>
                import(
                  '@/views/user/authenticationManagement/realAuthenticationList/index.vue'
                ),
              name: 'realAuthenticationList',
              meta: {
                title: '实人认证记录',
                icon: 'list',
                menuId: '********02'
              }
            }
          ]
        },
        {
          path: 'avatar-nickname-update',
          component: () =>
            import('@/views/user/avatar-nickname-update/index.vue'),
          name: 'AvatarNicknameUpdate',
          meta: { title: '头像昵称批量修改', icon: 'list', menuId: 'S1030207' },
          hidden: false
        },
        {
          path: 'privacy-management',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: { title: '私信风控', icon: 'list', menuId: 'S1030208' },
          children: [
            {
              path: 'privacyPhone',
              component: () => import('@/views/user/privacyLimit/phone.vue'),
              name: 'privacyPhone',
              meta: {
                title: '手机号重复',
                icon: 'list',
                menuId: 'S103020801'
              }
            },

            {
              path: 'privacyImage',
              component: () => import('@/views/user/privacyLimit/image.vue'),
              name: 'privacyImage',
              meta: {
                title: '图片重复',
                icon: 'list',
                menuId: 'S103020802'
              }
            },

            {
              path: 'privacyWechat',
              component: () => import('@/views/user/privacyLimit/wechat.vue'),
              name: 'privacyWechat',
              meta: {
                title: '微信重复',
                icon: 'list',
                menuId: 'S103020803'
              }
            }
          ]
        },
        {
          path: 'relatedUser',
          component: () => import('@/views/user/privacyLimit/relatedUser.vue'),
          name: 'relatedUser',
          meta: {
            title: '相关账户',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        }
      ]
    },
    {
      path: 'userOperations',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '用户运营', icon: 'peoples', menuId: 'S10303' },
      children: [
        {
          path: 'private-management',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: { title: '私信管理', icon: 'list', menuId: 'S1030301' },
          children: [
            {
              path: 'PrivateMessageQuery',
              component: () =>
                import('@/views/user/PrivateMessageQuery/index.vue'),
              name: 'PrivateMessageQuery',
              meta: {
                title: '私信对话查询',
                icon: 'list',
                menuId: 'S103030101'
              }
            },
            {
              path: 'GroupMessageQuery',
              component: () =>
                import('@/views/user/GroupMessageQuery/index.vue'),
              name: 'GroupMessageQuery',
              meta: {
                title: '群聊对话查询',
                icon: 'list',
                menuId: menu.noLimit
              },
              hidden: true
            },
            {
              path: 'group-chat-search',
              component: () =>
                import('@/views/user/group-chat-search/index.vue'),
              name: 'GroupChatSearch',
              meta: { title: '群聊查询', icon: 'list', menuId: 'S103030103' }
            },
            {
              path: 'private-letter-search',
              component: () =>
                import('@/views/user/private-letter-search/index.vue'),
              name: 'PrivateLetterSearch',
              meta: { title: '私信查询', icon: 'list', menuId: 'S103030102' }
            }
          ]
        },
        {
          path: 'user-wallet',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: { title: '用户钱包', icon: 'list', menuId: 'S1030302' },
          children: [
            {
              path: 'UserWalletDetails',
              component: () =>
                import('@/views/user/usersPurse/userWalletDetails/index.vue'),
              name: 'UserWalletDetails',
              meta: {
                title: '用户钱包明细',
                icon: 'list',
                menuId: 'S103030201'
              }
            },
            {
              path: 'UserWithdrewOrder',
              component: () =>
                import('@/views/user/usersPurse/UserWithdrewOrder/index.vue'),
              name: 'UserWithdrewOrder',
              meta: {
                title: '用户提现订单',
                icon: 'list',
                menuId: 'S103030202'
              }
            }
          ]
        },
        {
          path: 'energy-management',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: { title: '能量管理', icon: 'list', menuId: 'S1030303' },
          children: [
            {
              path: 'enerage-accounting-risk',
              component: () =>
                import('@/views/user/enerage-accounting-risk/index.vue'),
              name: 'EnerageAccountingRisk',
              meta: {
                title: '能量入账风控',
                icon: 'list',
                menuId: 'S103030301'
              }
            },
            {
              path: 'energy-refund',
              component: () => import('@/views/user/EnergyRefund/index.vue'),
              name: 'EnergyRefund',
              meta: {
                title: '能量定向发放',
                icon: 'list',
                menuId: 'S103030302'
              }
            }
          ]
        },
        {
          path: 'recommendation-management',
          component: () => import('@/views/routerView/index.vue'),
          redirect: 'noredirect',
          meta: { title: '推荐管理', icon: 'list', menuId: 'S1030304' },
          children: [
            {
              path: 'userRecommendation',
              component: () =>
                import('@/views/user/userRecommendation/index.vue'),
              name: 'UserRecommendation',
              meta: {
                title: '用户推荐',
                icon: 'list',
                noCache: true,
                menuId: 'S103030401'
              }
            },
            {
              path: 'FocusUserRecommendation',
              component: () =>
                import('@/views/user/focus-user-recommendation/index.vue'),
              name: 'FocusUserRecommendation',
              meta: {
                title: '新关注用户推荐',
                icon: 'list',
                menuId: 'S103030402'
              }
            },
            {
              path: 'creatorColumn',
              component: () => import('@/views/user/creator-column/index.vue'),
              name: 'CreatorColumn',
              meta: {
                title: '创作者专栏',
                icon: 'list',
                menuId: 'S103030403'
              }
            }
          ]
        },
        {
          path: 'grow-officer-activity',
          component: () =>
            import('@/views/user/grow-officer-activity/index.vue'),
          name: 'grow-officer-activity',
          meta: { title: '增长官活动列表', icon: 'list', menuId: 'S1030305' }
        },
        {
          path: 'grow-officer-audit-list',
          component: () =>
            import(
              '@/views/user/grow-officer-activity/grow-officer-audit-list.vue'
            ),
          name: 'grow-officer-audit-list',
          meta: {
            title: '增长官任务审核列表',
            icon: 'list',
            menuId: '********'
          }
        },
        {
          path: 'shouqianba-refund-list',
          component: () =>
            import('@/views/user/shouqianba-refund-list/index.vue'),
          name: 'ShouqianbaRefundList',
          meta: { title: '收钱吧退款记录表', icon: 'list', menuId: '********' }
        },
        {
          path: 'RidingMonitoring',
          component: () => import('@/views/user/ridingMonitoring/index.vue'),
          name: 'RidingMonitoring',
          meta: { title: '骑行监控处理', icon: 'list', menuId: '********' }
        },
        {
          path: 'RidingRanks',
          component: () => import('@/views/user/ridingRanks/index.vue'),
          name: 'RidingRanks',
          meta: { title: '骑行榜单', icon: 'list', menuId: '********' }
        },
        {
          path: 'cycling-blacklist',
          component: () => import('@/views/user/cyclingBlacklist/index.vue'),
          name: 'CyclingBlacklist',
          meta: { title: '骑行黑名单', icon: 'list', menuId: '********' }
        },
        {
          path: 'resign-card-list',
          component: () => import('@/views/user/resign-card-list/index.vue'),
          name: 'resign-card-list',
          meta: { title: '补签卡发放', icon: 'list', menuId: 'S1030309' }
        },
        {
          path: 'city-riding-part-audit',
          component: () =>
            import('@/views/user/city-riding-part-audit/index.vue'),
          name: 'city-riding-part-audit',
          meta: {
            title: '城市官约骑活动审核',
            icon: 'list',
            menuId: 'S1030315'
          }
        },
        {
          path: 'commentor-monitor',
          component: () => import('@/views/user/commentor-monitor/index.vue'),
          name: 'commentor-monitor',
          meta: {
            title: '点评官数据监控',
            icon: 'list',
            menuId: 'S1030313'
          }
        },
        {
          path: 'energy-lottery-list',
          component: () => import('@/views/user/energy-lottery-list/index.vue'),
          name: 'energy-lottery-list',
          meta: { title: '能量夺宝', icon: 'list', menuId: 'S1030312' }
        },
        {
          path: 'link-conversion',
          component: () => import('@/views/user/link-conversion/index.vue'),
          name: 'link-conversion',
          meta: { title: '长链转短链', icon: 'list', menuId: 'S1030318' }
        },
        {
          path: 'energy-grid-lottery',
          component: () => import('@/views/user/energy-grid-lottery/index.vue'),
          name: 'energy-grid-lottery',
          meta: { title: '转盘能量抽奖', icon: 'list', menuId: 'S1030316' }
        },
        {
          path: 'phoneBillList',
          component: () =>
            import('@/views/shopPing/returnSettlement/rechargeList/index.vue'),
          name: 'phoneBillList',
          meta: { title: '奖品话费充值', icon: 'list', menuId: 'S1030317' }
        },
        {
          path: 'special-officer-switch',
          component: () =>
            import('@/views/user/special-officer-switch/index.vue'),
          name: 'special-officer-switch',
          meta: { title: '特聘官申请开关', icon: 'list', menuId: 'S1030314' }
        }
      ]
    },
    // {
    //   path: 'UserPolicy',
    //   component: () => import('@/views/user/user-policy'),
    //   name: 'UserPolicy',
    //   meta: { title: '用户保单', icon: 'list', noCache: true, menuId: 'S10307' }
    // },
    {
      path: 'StoreRule',
      component: () => import('@/views/user/pushLibrary/store-rule.vue'),
      name: 'StoreRule',
      meta: { title: '入库规则', icon: 'list', menuId: menu.noLimit },
      hidden: true
    },
    {
      path: 'sensitive-word-redact',
      component: () => import('@/views/user/sensitive-word-redact/index.vue'),
      name: 'SensitiveWordRedact',
      meta: { title: '词库配置', icon: 'list', menuId: menu.noLimit },
      hidden: true
    }
  ]
}

export default userPlatform
