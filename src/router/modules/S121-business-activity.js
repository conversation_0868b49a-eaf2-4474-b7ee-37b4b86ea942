import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const businessActivity = {
  path: '/businessActivity',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '业务活动管理', icon: 'peoples', menuId: 'S121' },
  children: [
    {
      path: 'activeTemplateManage',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '活动模版管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S12102'
      },
      children: [
        {
          path: 'ActiveAdTemplate',
          component: () =>
            import(
              '@/views/business/activity-management/active-shop-adTemplate/index.vue'
            ),
          name: 'ActiveAdTemplate',
          meta: { title: '厂家专题管理', icon: 'list', menuId: 'S1210201' }
        },
        {
          path: 'AdShopTemplate1',
          component: () =>
            import(
              '@/views/business/activity-management/active-shop-adTemplate/detail/template1.vue'
            ),
          name: 'AdShopTemplate1',
          meta: { title: '配置信息', icon: 'list', menuId: menu.noLimit },
          hidden: true
        },
        {
          path: 'ActiveTemplateConfiguration',
          component: () =>
            import(
              '@/views/business/activity-management/active-template-configuration/index.vue'
            ),
          name: 'ActiveTemplateConfiguration',
          meta: { title: '活动管理', icon: 'list', menuId: 'S1210202' }
        },
        {
          path: 'ActiveTemplateConfigurationDetail',
          component: () =>
            import(
              '@/views/business/activity-management/active-template-configuration/detail.vue'
            ),
          name: 'ActiveTemplateConfigurationDetail',
          meta: {
            title: '活动模版配置详情',
            icon: 'list',
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'UgcActiveConfiguration',
          component: () =>
            import(
              '@/views/business/activity-management/ugc-active-configuration/index.vue'
            ),
          name: 'UgcActiveConfiguration',
          meta: { title: 'UGC活动', icon: 'list', menuId: '********' }
        },
        {
          path: 'UgcActiveConfigurationDetail',
          component: () =>
            import(
              '@/views/business/activity-management/ugc-active-configuration/detail.vue'
            ),
          name: 'UgcActiveConfigurationDetail',
          meta: {
            title: 'UGC活动详情',
            icon: 'list',
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'AwardList',
          component: () =>
            import(
              '@/views/business/activity-management/ugc-active-configuration/awardList.vue'
            ),
          name: 'AwardList',
          meta: {
            title: '领奖列表',
            icon: 'list',
            menuId: menu.noLimit
          },
          hidden: true
        }
      ]
    },
    {
      path: 'homeTabManger',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '首页tab管理',
        icon: 'peoples',
        noCache: true,
        menuId: 'S12101'
      },
      children: [
        {
          path: 'homeAcitvityConfig',
          component: () =>
            import(
              '@/views/businessActivity/homeTabManger/homeAcitvityConfig/index.vue'
            ),
          name: 'homeAcitvityConfig',
          meta: {
            title: '首页活动配置',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        }
      ]
    }
  ]
}

export default businessActivity
