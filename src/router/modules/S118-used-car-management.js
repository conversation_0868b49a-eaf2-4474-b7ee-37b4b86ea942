import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const usedCarManagement = {
  path: '/usedCarManagement',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '二手车管理', icon: 'peoples', menuId: 'S118' },
  children: [
    {
      path: 'UsedCarList',
      component: () =>
        import('@/views/garage/usedCarSector/UsedCarList/index.vue'),
      name: 'UsedCarList',
      meta: {
        title: '二手车车源列表',
        icon: 'list',
        noCache: true,
        menuId: 'S10801'
      }
    },
    {
      path: 'ReceiptClues',
      component: () =>
        import('@/views/garage/usedCarSector/ReceiptClues/index.vue'),
      name: 'ReceiptClues',
      meta: {
        title: '收车&估价线索',
        icon: 'list',
        noCache: true,
        menuId: 'S10810'
      }
    },
    {
      path: 'PremiumDealer',
      component: () =>
        import('@/views/garage/usedCarSector/PremiumDealer/index.vue'),
      name: 'PremiumDealer',
      meta: {
        title: '优质车商审核',
        icon: 'list',
        noCache: true,
        menuId: 'S10802'
      }
    },
    {
      path: 'UsedDetail',
      component: () =>
        import('@/views/garage/usedCarSector/UsedCarDetail/index.vue'),
      name: 'UsedDetail',
      meta: {
        title: '二手车详情',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'UsedCarSecurity',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '二手车安全',
        icon: 'peoples',
        noCache: true,
        menuId: 'S10809'
      },
      children: [
        {
          path: 'UsedCarReporting',
          component: () =>
            import('@/views/garage/usedCarSector/UsedCarReporting/index.vue'),
          name: 'UsedCarReporting',
          meta: {
            title: '二手车举报审核',
            icon: 'list',
            noCache: true,
            menuId: 'S1080903'
          }
        },
        {
          path: 'UsedCarReportManage',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarReportManage/index.vue'
            ),
          name: 'UsedCarReportManage',
          meta: {
            title: '二手车举报管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1080904'
          }
        },
        {
          path: 'UsedCarReportConfig',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarReportConfig/index.vue'
            ),
          name: 'UsedCarReportConfig',
          meta: {
            title: '二手车举报处理配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1080908'
          }
        },
        {
          path: 'UsedCarUserBlacklist',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarUserBlacklist/index.vue'
            ),
          name: 'UsedCarUserBlacklist',
          meta: {
            title: '二手车黑名单（致命信息）',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'BlackList',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/BlackList/index.vue'
            ),
          name: 'BlackList',
          meta: {
            title: '二手车黑名单（可申诉信息）',
            icon: 'list',
            noCache: true,
            menuId: '*********'
          }
        },
        {
          path: 'BlackIdList',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/BlackIdList/index.vue'
            ),
          name: 'BlackIdList',
          meta: {
            title: '二手车黑名单（身份证信息）',
            icon: 'list',
            noCache: true,
            menuId: '*********'
          }
        },
        {
          path: 'DeregistrationBlacklist',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/DeregistrationBlacklist/index.vue'
            ),
          name: 'DeregistrationBlacklist',
          meta: {
            title: '二手车黑名单（注销反复注册）',
            icon: 'list',
            noCache: true,
            menuId: '*********'
          }
        },
        {
          path: 'whiteYearList',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/whiteYearList/index.vue'
            ),
          name: 'WhiteYearList',
          meta: {
            title: '二手车白名单（超60周岁）',
            icon: 'list',
            noCache: true,
            menuId: 'S10809012'
          }
        },
        {
          path: 'UsedCarUniqueUser',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarSecurity/uniqueUserAudit/index.vue'
            ),
          name: 'UsedCarUniqueUser',
          meta: {
            title: '二手车异常用户审核',
            icon: 'list',
            noCache: true,
            menuId: 'S1080901'
          }
        },
        {
          path: 'UsedCarUniqueRule',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarSecurity/uniqueUserRule/index.vue'
            ),
          name: 'UsedCarUniqueRule',
          meta: {
            title: '二手车异常用户规则管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1080902'
          }
        },
        {
          path: 'UsedCarRiskUserManage',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarRiskUserManage/index.vue'
            ),
          name: 'UsedCarRiskUserManage',
          meta: {
            title: '二手车风险用户管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1080906'
          }
        },
        {
          path: 'UsedCarPublish',
          component: () =>
            import('@/views/garage/usedCarSector/UsedCarPublish/tabs.vue'),
          name: 'UsedCarPublish',
          meta: {
            title: '二手车处罚管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1080907'
          }
        },
        {
          path: 'PunishManageDetails',
          component: () =>
            import('@/views/garage/usedCarSector/UsedCarPublish/details.vue'),
          name: 'PunishManageDetails',
          meta: {
            title: '处罚管理',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        },
        {
          path: 'AbnormalPictureMonitoring',
          component: () =>
            import(
              '@/views/garage/usedCarSector/AbnormalPictureMonitoring/index.vue'
            ),
          name: 'AbnormalPictureMonitoring',
          meta: {
            title: '二手车异常图片监控',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'AbnormalLicensePlateMonitoring',
          component: () =>
            import(
              '@/views/garage/usedCarSector/AbnormalLicensePlateMonitoring/index.vue'
            ),
          name: 'AbnormalLicensePlateMonitoring',
          meta: {
            title: '二手车异常车牌监控',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        }
      ]
    },
    {
      path: 'UsedCarOperation',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '二手车运营', icon: 'peoples', menuId: 'S10804' },
      children: [
        {
          path: 'AuditPrompt',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/AuditPrompt/index.vue'
            ),
          name: 'AuditPrompt',
          meta: {
            title: '审核提示',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'AuditKeyword',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/AuditKeyword/index.vue'
            ),
          name: 'AuditKeyword',
          meta: {
            title: '审核关键词',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'LicensePlateManagement',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/LicensePlateManagement/index.vue'
            ),
          name: 'LicensePlateManagement',
          meta: {
            title: '车牌管理',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'UsedCarBrandWords',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/UsedCarBrandWords/index.vue'
            ),
          name: 'UsedCarBrandWords',
          meta: {
            title: '热门品牌词',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'UsedCarPopularSearch',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/UsedCarPopularSearch/index.vue'
            ),
          name: 'UsedCarPopularSearch',
          meta: {
            title: '热门搜索词',
            icon: 'list',
            noCache: true,
            menuId: '********'
          }
        },
        {
          path: 'AccountPush',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/AccountPush/index.vue'
            ),
          name: 'AccountPush',
          meta: {
            title: '二手车账号推送',
            icon: 'list',
            noCache: true,
            menuId: 'S1080403'
          }
        },
        {
          path: 'UsedCarPriceWords',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/UsedCarPriceWords/index.vue'
            ),
          name: 'UsedCarPriceWords',
          meta: {
            title: '热门价格词',
            icon: 'list',
            noCache: true,
            menuId: 'S1080405'
          }
        },
        {
          path: 'AuditReasonConfig',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/AuditReasonConfig/index.vue'
            ),
          name: 'AuditReasonConfig',
          meta: {
            title: '二手车审核不通过原因配置',
            icon: 'list',
            noCache: true,
            menuId: 'S1080406'
          }
        },
        {
          path: 'CheckOrderList',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/CheckOrderList/index.vue'
            ),
          name: 'CheckOrderList',
          meta: {
            title: '检测订单列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1080408'
          }
        },
        {
          path: 'SimilarImageAppealMnagement',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOperation/SimilarImageAppealMnagement/index.vue'
            ),
          name: 'SimilarImageAppealMnagement',
          meta: {
            title: '相似图片申诉管理',
            icon: 'list',
            noCache: true,
            menuId: 'S1080409'
          }
        }
      ]
    },
    {
      path: 'UsedCarOrderManagement',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '二手车订单管理', icon: 'peoples', menuId: 'S10805' },
      children: [
        {
          path: 'UsedCarOrderList',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOrderManagement/UsedCarOrderList/index.vue'
            ),
          name: 'UsedCarOrderList',
          meta: {
            title: '订单列表',
            icon: 'list',
            noCache: true,
            menuId: 'S1080501'
          }
        },
        {
          path: 'UsedCarOrderDetail',
          component: () =>
            import(
              '@/views/garage/usedCarSector/UsedCarOrderManagement/UsedCarOrderDetail/index.vue'
            ),
          name: 'UsedCarOrderDetail',
          meta: {
            title: '订单详情',
            icon: 'list',
            noCache: true,
            menuId: menu.noLimit
          },
          hidden: true
        }
      ]
    },
    {
      path: 'usedCarTopOrder',
      component: () =>
        import('@/views/garage/dealerManagement/usedCarTopOrder/index.vue'),
      name: 'usedCarTopOrder',
      meta: { title: '二手车置顶订单', icon: 'list', menuId: 'S10806' }
    },
    // {
    //   path: 'TwoReplyManagement',
    //   component: () => import('@/views/business/articleManage/tworeplymanagement/index.vue'),
    //   name: 'TwoReplyManagement',
    //   meta: { title: '二手车评论管理', icon: 'list', noCache: true, menuId: 'S10808' }
    // }
    {
      path: 'UsedCarInquiryClues',
      component: () =>
        import('@/views/garage/usedCarSector/UsedCarInquiryClues/index.vue'),
      name: 'UsedCarInquiryClues',
      meta: { title: '二手车询价线索', icon: 'list', menuId: 'S10812' }
    }
  ]
}

export default usedCarManagement
