import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/


import menu from '@/router/menuIds'

const algorithmSorting = {
  path: '/algorithmSorting',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '推荐系统实时监测', icon: 'peoples', menuId: 'S122' },
  children: [
    {
      path: 'homeList',
      component: () => import('@/views/algorithmSorting/home-list/index.vue'),
      name: 'homeList',
      meta: {
        title: '首页信息流',
        icon: 'list',
        noCache: true,
        menuId: 'S12208'
      }
    },
    {
      path: 'rankingList',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '排行榜',
        icon: 'peoples',
        noCache: true,
        menuId: 'S12201'
      },
      children: [
        {
          path: 'CarList',
          component: () =>
            import('@/views/algorithmSorting/ranking-list/car-list/index.vue'),
          name: 'CarList',
          meta: { title: '车型榜', icon: 'list', menuId: '********' }
        },
        {
          path: 'BrandList',
          component: () =>
            import(
              '@/views/algorithmSorting/ranking-list/brand-list/index.vue'
            ),
          name: 'BrandList',
          meta: { title: '品牌榜', icon: 'list', menuId: '********' }
        },
        {
          path: 'ShopList',
          component: () =>
            import('@/views/algorithmSorting/ranking-list/shop-list/index.vue'),
          name: 'ShopList',
          meta: { title: '商家榜', icon: 'list', menuId: '********' }
        }
      ]
    },
    {
      path: 'selectedCar',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '选车', icon: 'peoples', noCache: true, menuId: 'S12209' },
      children: [
        {
          path: 'Recommend',
          component: () =>
            import('@/views/algorithmSorting/selected-car/recommend/index.vue'),
          name: 'Recommend',
          meta: { title: '推荐车型', icon: 'list', menuId: '********' }
        },
        {
          path: 'User',
          component: () =>
            import('@/views/algorithmSorting/selected-car/user/index.vue'),
          name: 'User',
          meta: { title: '用户个性化特征', icon: 'list', menuId: 'S1220902' }
        }
      ]
    },
    {
      path: 'distributor',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '经销商',
        icon: 'peoples',
        noCache: true,
        menuId: 'S12202'
      },
      children: [
        {
          path: 'DistributorList',
          component: () =>
            import(
              '@/views/algorithmSorting/distributor/distributor-list/index.vue'
            ),
          name: 'DistributorList',
          meta: { title: '经销商列表', icon: 'list', menuId: 'S1220201' }
        },
        {
          path: 'InquiryDealer',
          component: () =>
            import(
              '@/views/algorithmSorting/distributor/inquiry-dealer/index.vue'
            ),
          name: 'InquiryDealer',
          meta: { title: '询价经销商', icon: 'list', menuId: 'S1220202' }
        }
      ]
    },
    {
      path: 'scendCarList',
      component: () =>
        import('@/views/algorithmSorting/scend-car-list/index.vue'),
      name: 'scendCarList',
      meta: {
        title: '二手摩托',
        icon: 'list',
        noCache: true,
        menuId: 'S12203'
      }
    },
    {
      path: 'schoolList',
      component: () => import('@/views/algorithmSorting/school-list/index.vue'),
      name: 'schoolList',
      meta: {
        title: '驾校商家列表',
        icon: 'list',
        noCache: true,
        menuId: 'S12204'
      }
    },
    {
      path: 'carRentalList',
      component: () =>
        import('@/views/algorithmSorting/car-rental-list/index.vue'),
      name: 'carRentalList',
      meta: {
        title: '租车列表',
        icon: 'list',
        noCache: true,
        menuId: 'S12205'
      }
    },
    {
      path: 'circleFriends',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '摩友圈',
        icon: 'peoples',
        noCache: true,
        menuId: 'S12206'
      },
      children: [
        {
          path: 'CircleFriendsSquare',
          component: () =>
            import('@/views/algorithmSorting/circle-friends/square/index.vue'),
          name: 'CircleFriendsSquare',
          meta: { title: '广场-热门', icon: 'list', menuId: 'S1220601' }
        },
        {
          path: 'CircleFriendsDetail',
          component: () =>
            import('@/views/algorithmSorting/circle-friends/detail/index.vue'),
          name: 'CircleFriendsDetail',
          meta: { title: '详情-热门', icon: 'list', menuId: 'S1220602' }
        }
      ]
    },
    {
      path: 'ShoppingMallRecommendation',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: {
        title: '商城推荐',
        icon: 'peoples',
        noCache: true,
        menuId: 'S12207'
      },
      children: [
        {
          path: 'ShoppingMallRecommendationHot',
          component: () =>
            import(
              '@/views/algorithmSorting/shopping-mall-recommendation/hot/index.vue'
            ),
          name: 'ShoppingMallRecommendationHot',
          meta: { title: '商城30日热门', icon: 'list', menuId: 'S1220701' }
        },
        {
          path: 'ShoppingMallRecommendationUser',
          component: () =>
            import(
              '@/views/algorithmSorting/shopping-mall-recommendation/user/index.vue'
            ),
          name: 'ShoppingMallRecommendationUser',
          meta: { title: '用户个性化特征', icon: 'list', menuId: 'S1220702' }
        }
      ]
    }
  ]
}

export default algorithmSorting
