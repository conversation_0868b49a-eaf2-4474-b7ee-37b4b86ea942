import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

// import menu from '@/router/menuIds'

const tools = {
  path: '/tools',
  meta: { title: '工具平台', icon: 'peoples', menuId: 'S102' },
  redirect: 'noredirect',
  component: () => import('@/views/layout/Layout.vue'),
  children: [
    {
      path: '/CommodityPlatform',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '商品平台', icon: 'peoples', menuId: 'S10202' },
      children: [
        {
          path: 'CommodityManagement',
          component: () =>
            import(
              '@/views/tools/commodityPlatform/commodityManagement/index.vue'
            ),
          name: 'CommodityManagement',
          meta: { title: '商品管理', icon: 'list', menuId: 'S1020201' }
        },
        {
          path: 'ActivityCommodityManagement',
          component: () =>
            import(
              '@/views/tools/commodityPlatform/activityCommodityManagement/index.vue'
            ),
          name: 'ActivityCommodityManagement',
          meta: { title: '活动商品管理', icon: 'list', menuId: 'S1020202' }
        },
        {
          path: 'OrderManagement',
          component: () =>
            import('@/views/tools/commodityPlatform/orderManagement/index.vue'),
          name: 'OrderManagement',
          meta: { title: '订单管理', icon: 'list', menuId: 'S1020203' }
        },
        {
          path: 'EnergyExchange',
          component: () =>
            import('@/views/tools/commodityPlatform/energyExchange/index.vue'),
          name: 'EnergyExchange',
          meta: { title: '话费订单', icon: 'list', menuId: 'S1020204' }
        },
        {
          path: 'adVideoExchange',
          component: () =>
            import('@/views/tools/commodityPlatform/adVideoExchange/index.vue'),
          name: 'AdVideoExchange',
          meta: { title: '激励视频兑换', icon: 'list', menuId: 'S1020205' }
        }
      ]
    },
    {
      path: 'youzanInformation',
      component: () => import('@/views/tools/youzanInformation/index.vue'),
      name: 'YouzanInformation',
      meta: { title: '有赞信息', icon: 'list', menuId: 'S10204' }
    },
    {
      path: 'newCarAccountsInformation',
      component: () =>
        import('@/views/tools/newCarAccountsInformation/index.vue'),
      name: 'newCarAccountsInformation',
      meta: { title: '新车销售经销商结款表', icon: 'list', menuId: 'S10205' }
    },
    {
      path: 'uesdCarAccountsInformation',
      component: () =>
        import('@/views/tools/uesdCarAccountsInformation/index.vue'),
      name: 'uesdCarAccountsInformation',
      meta: { title: '二手车财务结款表', icon: 'list', menuId: 'S10206' }
    },
    {
      path: 'uesdCarMerchantAccountsInformation',
      component: () =>
        import('@/views/tools/uesdCarMerchantAccountsInformation/index.vue'),
      name: 'uesdCarMerchantAccountsInformation',
      meta: { title: '二手车商家财务结款表', icon: 'list', menuId: 'S10209' }
    },
    {
      path: 'uesd-car-refund-order-list',
      component: () =>
        import('@/views/tools/uesd-car-refund-order-list/index.vue'),
      name: 'UesdCarRefundOrderList',
      meta: { title: '二手车退款订单列表', icon: 'list', menuId: 'S10207' }
    },
    {
      path: 'couponReturnCounter',
      component: () => import('@/views/tools/couponReturnCounter/index.vue'),
      name: 'CouponReturnCounter',
      meta: { title: '优惠券返现结款', icon: 'list', menuId: 'S10208' }
    },
    {
      path: '/Finance',
      component: () => import('@/views/routerView/index.vue'),
      redirect: 'noredirect',
      meta: { title: '财务信息', icon: 'peoples', menuId: 'S10203' },
      children: [
        {
          path: 'FinancialOrder',
          component: () =>
            import('@/views/tools/finance/financialOrder/index.vue'),
          name: 'FinancialOrder',
          meta: { title: '财务订单', icon: 'list', menuId: 'S1020301' }
        },
        {
          path: 'RefundOrder',
          component: () =>
            import('@/views/tools/finance/refundOrder/index.vue'),
          name: 'RefundOrder',
          meta: { title: '退款订单', icon: 'list', menuId: 'S1020302' }
        },
        {
          path: 'FinancialStatistics',
          component: () =>
            import('@/views/tools/finance/financialStatistics/index.vue'),
          name: 'FinancialStatistics',
          meta: { title: '财务统计', icon: 'list', menuId: 'S1020303' }
        }
      ]
    }
  ]
}

export default tools
