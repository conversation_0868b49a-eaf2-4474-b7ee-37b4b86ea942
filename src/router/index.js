/* Layout */
import { createRouter, createWebHashHistory } from 'vue-router'
import { constantRouterMap } from './constRoutes'
/* Router Modules */
// import componentsRouter from './modules/components'
// import chartsRouter from './modules/charts'
// import tableRouter from './modules/table'
// import nestedRouter from './modules/nested'
import business from './modules/S101-business'
import tools from './modules/S102-tools'
import userPlatform from './modules/S103-user-platform'
import advertBanner from './modules/S104-advert-banner'
import garage from './modules/S105-garage'
import dealerManagement from './modules/S106-dealer-management'
import system from './modules/S107-system'
import motoCollege from './modules/S108-moto-college'
import circlePlatform from './modules/S109-circle-platform'
import newCar from './modules/S110-new-car'
import audit from './modules/S111-audit'
import compose from './modules/S112-compose'
import content from './modules/S113-content'
import shopping from './modules/S114-shopping'
import channel from './modules/S115-channel'
import factoryManagement from './modules/S116-factory-management'
import usedCarManagement from './modules/S118-used-car-management'
import rentcarManagement from './modules/S117-rentcar-management'
import supplyChain from './modules/S119-supply-chain'
import driveTestManage from './modules/S120-drive-test'
import businessActivity from './modules/S121-business-activity'
import algorithmSorting from './modules/S122-algorithm-sorting'
import workOrderSystem from './modules/S123-work-order-system'
import shopManagementList from './modules/S124-shop-management-list'
import shopRealPayList from './modules/S125-shop-realpay-list'

/** note: Submenu only appear when children.length>=1
 *  detail see  https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 **/

/**
 * hidden: true                   if `hidden:true` will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu, whatever its child routes length
 *                                if not set alwaysShow, only more than one route under the children
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noredirect           if `redirect:noredirect` will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']     will control the page roles (you can set multiple roles)
    title: 'title'               the name show in submenu and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar,
    noCache: true                if true ,the page will no be cached(default is false)
  }
 **/

export default createRouter({
  // mode: 'history', // require service support
  history: createWebHashHistory(),
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})

// 路由控制层级结构 三层需要用到routerView
export const asyncRouterMap = [
  tools, // S102 工具平台
  business, // S101 业务平台
  userPlatform, // S103 用户平台
  audit, // S111 审核平台
  compose, // S112 创作平台
  content, // S113 内容平台
  advertBanner, // S104 广告平台
  businessActivity, // S121 业务活动管理
  garage, // S105 车库平台
  rentcarManagement, // S117 租车管理
  circlePlatform, // S109 圈子平台
  usedCarManagement, // S118 二手车管理
  dealerManagement, // S106 经销商管理
  shopManagementList, // 商家订单列表
  shopRealPayList, // S125 订单实付表
  factoryManagement, // s116厂家平台
  newCar, // S110 售车页面
  shopping, // S114 商城
  supplyChain, // S117 供应链商品
  motoCollege, // S108 摩托学院
  driveTestManage, // S120 驾考管理
  channel, // S115 渠道
  system, // S107 系统处理
  algorithmSorting, // S122 算法排序展示
  workOrderSystem // S123 工单系统
  // { path: '*', redirect: '/404', hidden: true }
]

// 页面数量统计
// let currentPages = []
// let r = asyncRouterMap
// r.map(i => {
//   if (i.children) {
//     i.children.map(_ => {
//       currentPages.push(_.path)
//       if (_.children) {
//         _.children.map(__ => {
//           currentPages.push(__.path)
//         })
//       } else {
//         currentPages.push(_.path)
//       }
//     })
//   } else {
//     currentPages.push(i.path)
//   }
// })
// console.log(r, currentPages)
