// set function parseTime,formatTime to filter
export { formatTime, parseTime } from '@/utils'
import store from '@/store'

function pluralize(time, label) {
  if (time === 1) {
    return time + label
  }
  return time + label + 's'
}

export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(time)
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/* 数字 格式化*/
export function numberFormatter(num, digits) {
  const si = [
    { value: 1e18, symbol: 'E' },
    { value: 1e15, symbol: 'P' },
    { value: 1e12, symbol: 'T' },
    { value: 1e9, symbol: 'G' },
    { value: 1e6, symbol: 'M' },
    { value: 1e3, symbol: 'k' }
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (
        (num / si[i].value + 0.1)
          .toFixed(digits)
          .replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
      )
    }
  }
  return num.toString()
}

/**
 * 格式化时间  部分内容特殊展示
 * 年月日时分
 * 示例：{{1494141000*1000 | tt}} 输出时间 年月日时分秒
 */
export function timeFullS(tt) {
  // 如果传入类型是date，组件有BUG
  const date =
    typeof tt === 'number'
      ? new Date(tt)
      : new Date((tt || '').replace(/-/g, '/'))
  const isValidDate =
    Object.prototype.toString.call(date) === '[object Date]' &&
    !isNaN(date.getTime())
  if (!isValidDate) {
    // console.error('not a valid date')
    return ''
  }
  const formatDate = function (date) {
    const today = new Date(date)
    const year = today.getFullYear()
    const month = ('0' + (today.getMonth() + 1)).slice(-2)
    const day = ('0' + today.getDate()).slice(-2)
    const h = ('0' + today.getHours()).slice(-2)
    const m = ('0' + today.getMinutes()).slice(-2)
    const s = ('0' + today.getSeconds()).slice(-2)
    return `${year}-${month}-${day} ${h}:${m}:${s}`
  }
  return formatDate(date)
}

/**
 * 格式化时间  部分内容特殊展示
 * 年月日时分
 * 示例：{{1494141000*1000 | tt}} 输出时间 年月日
 */
export function timeFull(tt) {
  const date =
    typeof tt === 'number'
      ? new Date(tt)
      : new Date((tt || '').replace(/-/g, '/'))
  const isValidDate =
    Object.prototype.toString.call(date) === '[object Date]' &&
    !isNaN(date.getTime())
  if (!isValidDate) {
    console.error('not a valid date')
    return ''
  }
  const formatDate = function (date) {
    const today = new Date(date)
    const year = today.getFullYear()
    const month = ('0' + (today.getMonth() + 1)).slice(-2)
    const day = ('0' + today.getDate()).slice(-2)
    return `${year}年${month}月${day}日`
  }
  return formatDate(date)
}

/**
 * 格式化时间  部分内容特殊展示
 * 年月日时分
 * 示例：{{1494141000*1000 | tt}} 输出时间 年-月-日
 */
export function dateFull(tt) {
  const date =
    typeof tt === 'number'
      ? new Date(tt)
      : new Date((tt || '').replace(/-/g, '/'))
  const isValidDate =
    Object.prototype.toString.call(date) === '[object Date]' &&
    !isNaN(date.getTime())
  if (!isValidDate) {
    console.error('not a valid date')
    return ''
  }
  const formatDate = function (date) {
    const today = new Date(date)
    const year = today.getFullYear()
    const month = ('0' + (today.getMonth() + 1)).slice(-2)
    const day = ('0' + today.getDate()).slice(-2)
    return `${year}-${month}-${day}`
  }
  return formatDate(date)
}

export function toThousandFilter(num) {
  return (+num || 0)
    .toString()
    .replace(/^-?\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

export function subString(str, length) {
  return str && str.length > length ? `${str.substring(0, length)}...` : str
}

// 替换图片地址，增加图片上的水印，具体可看 MAIN-4298
export function replaceImgUrl(imgUrl) {
  // const isDev = process.env.NODE_ENV === 'development'
  const uid = store.getters.uid
  if (uid === 1) return imgUrl // 管理员直接返回
  if (!imgUrl) return imgUrl // 空值直接返回
  const isAppoint = ['credit.58moto.com'].find((item) => {
    return imgUrl.indexOf(item) > -1
  })
  if (!isAppoint) return imgUrl // 特定域名过滤
  const formatList = ['.jpeg', '.jpg', '.png']
  const formatIndex = formatList.findIndex((item) => {
    if (imgUrl.indexOf(item) > -1) return item
  })
  if (formatIndex === -1) return // 不包含上三个格式，直接返回
  const replaceIndex = imgUrl.indexOf(formatList[formatIndex])
  const symbolIndex = imgUrl.substring(replaceIndex).indexOf('_') // 获取宽高位置（下一步获取对应的数据）
  const size =
    symbolIndex !== -1
      ? imgUrl.slice(symbolIndex + replaceIndex, imgUrl.length)
      : ''
  return `${imgUrl.slice(0, replaceIndex)}${
    formatList[formatIndex]
  }!oss_${uid}?${size}`
}
// 金额转换千分位
export function numberWithCommas(x) {
  if (!x) return ''
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}
// 大写金额转换
export function convertCurrency(money) {
  if (!money) return ''
  // 汉字的数字
  var cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
  // 基本单位
  var cnIntRadice = ['', '拾', '佰', '仟']
  // 对应整数部分扩展单位
  var cnIntUnits = ['', '万', '亿', '兆']
  // 对应小数部分单位
  var cnDecUnits = ['角', '分', '毫', '厘']
  // 整数金额时后面跟的字符
  var cnInteger = '整'
  // 整数部分金额转换为汉字的函数
  function intToChinese(num) {
    var i,
      j,
      cnChar = '',
      cnInt = '',
      cnDec = '',
      cnExp = ''
    var strNum = num.toString()
    var strLen = strNum.length
    var zeroCount = 0
    if (strNum == 'NaN') return ''
    // 处理整数部分
    if (strLen > 0) {
      for (i = 0; i < strLen; i++) {
        var numChar = strNum.charAt(i)
        var numIndex = parseInt(numChar)
        var idxPos = strLen - i - 1 // 获取数字在整数金额中的位置
        var unitPos = idxPos % 4 // 获取数字所在的基本单位
        if (numChar == '0') {
          zeroCount++
        } else {
          if (zeroCount > 0) {
            cnInt += cnNums[0]
          }
          zeroCount = 0
          cnInt += cnNums[numIndex] + cnIntRadice[unitPos]
        }
        // 处理整数部分的扩展单位
        if (unitPos == 0 && zeroCount < 4) {
          cnExp = cnIntUnits[Math.floor(idxPos / 4)]
        }
      }
      cnInt += cnExp
    }
    // 处理小数部分
    if (money != strNum) {
      var decLen = strNum.length - money.toString().length - 1
      for (i = 0; i < decLen; i++) {
        var decChar = strNum.charAt(i + money.toString().length + 1)
        var decIndex = parseInt(decChar)
        if (decChar != '0') {
          cnDec += cnNums[decIndex] + cnDecUnits[i]
        }
      }
    }
    // 返回处理结果
    if (cnInt == '') {
      cnInt = cnNums[0]
    }
    if (cnDec == '') {
      cnDec = cnInteger
    }
    return cnInt + cnDec
  }
  // 处理负数情况
  if (money < 0) {
    return '负' + intToChinese(-money)
  } else {
    return intToChinese(money)
  }
}
