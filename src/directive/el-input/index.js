// 针对 el-input做的限制，只能输入正整数
export default {
  install: function (app) {
    app.directive('Int', {
      beforeMount: function (el) {
        const input = el.getElementsByTagName('input')[0]
        input.handler = function () {
          const originalValue = input.value
          if (input.value.length === 1) {
            input.value = input.value.replace(/[^1-9]/g, '')
          } else {
            input.value = input.value.replace(/[^\d]/g, '')
          }
          if (input.value !== originalValue) {
            input.dispatchEvent(new Event('input'))
          }
        }
        input.addEventListener('input', input.handler)
      },
      unMounted: function (el) {
        const input = el.getElementsByTagName('input')[0]
        input.removeEventListener('input', input.handler)
      }
    })
    app.directive('IntZero', {
      beforeMount: function (el) {
        const input = el.getElementsByTagName('input')[0]
        input.handler = function () {
          const originalValue = input.value
          if (input.value.length === 1) {
            input.value = input.value.replace(/[^0-9]/g, '')
          } else {
            input.value = input.value.replace(/[^\d]/g, '')
          }
          if (input.value !== originalValue) {
            input.dispatchEvent(new Event('input'))
          }
        }
        input.addEventListener('input', input.handler)
      },
      unmounted: function (el) {
        const input = el.getElementsByTagName('input')[0]
        input.removeEventListener('input', input.handler)
      }
    })
  }
}
