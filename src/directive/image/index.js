/**
 * 图片默认加载
 * demo：<img v-img-default="changeImgGIF(getFirstMediaImgs(card)[0].imgUrl)" class="img-wrap-img" />
 * @param {*} app
 */
export default {
  install(app) {
    app.directive('ImgDefault', {
      beforeMount(el, bindings, vnode) {
        // 初始化设置
        const img = new Image()
        // 监听图像加载成功事件
        img.onload = () => {
          el.src = bindings.value
        }
        // 监听图像加载失败事件
        img.onerror = () => {
          el.src = '@/assets/image/<EMAIL>'
        }
        img.src = '@/assets/image/<EMAIL>'
      }
    })
  }
}
