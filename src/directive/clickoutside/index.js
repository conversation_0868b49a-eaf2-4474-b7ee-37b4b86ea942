import * as Vue from 'vue'

export default {
  install(app) {
    app.directive('clickOutside', {
      beforeMount(el, bindings, vnode) {
        el.handler = (e) => {
          if (!el.contains(e.target)) {
            const method = bindings.value
            // vnode.context当前指令绑定的组件实例
            method(e.target)
          }
        }
        document.addEventListener('click', el.handler)
      },
      unmounted(el) {
        document.removeEventListener('click', el.handler)
      }
    })
  }
}
