import request from '@/utils/request'

/**
 * 新增渠道
 * @param query
 */
export function saveChannel(data) {
  return request({
    url: '/expands/oss/promote/channel/saveChannel',
    method: 'post',
    data,
  })
}

/**
 * 删除渠道
 * @param query
 */
export function delChannel(data) {
  return request({
    url: '/expands/oss/promote/channel/delChannel',
    method: 'post',
    data,
  })
}

/**
 * 渠道列表
 * @param query
 */
export function getListChannel(query) {
  return request({
    url: '/expands/oss/promote/channel/listChannel',
    method: 'get',
    params: query,
  })
}

/**
 * 新增渠道组合
 * @param query
 */
export function saveChannelComb(data) {
  return request({
    url: '/expands/oss/promote/channel/saveChannelComb',
    method: 'post',
    data,
  })
}

/**
 * 删除渠道组合
 * @param query
 */
export function delChannelComb(data) {
  return request({
    url: '/expands/oss/promote/channel/delChannelComb',
    method: 'post',
    data,
  })
}

/**
 * 渠道组合列表
 * @param query
 */
export function listChannelComb(query) {
  return request({
    url: '/expands/oss/promote/channel/listChannelComb',
    method: 'get',
    params: query,
  })
}

/**
 * 查询所有产品
 * @param query
 */
export function listProduct(query) {
  return request({
    url: '/expands/oss/promote/channel/listProduct',
    method: 'get',
    params: query,
  })
}

/**
 * 新增投放渠道
 * @param query
 */
export function saveChannelPut(data) {
  return request({
    url: '/expands/oss/promote/channel/saveChannelPut',
    method: 'post',
    data,
  })
}

/**
 * 删除投放渠道
 * @param query
 */
export function delChannelPut(data) {
  return request({
    url: '/expands/oss/promote/channel/delChannelPut',
    method: 'post',
    data,
  })
}

/**
 * 投放渠道列表
 * @param query
 */
export function listChannelPut(query) {
  return request({
    url: '/expands/oss/promote/channel/listChannelPut',
    method: 'get',
    params: query,
  })
}

/**
 * 上传渠道包
 * @param query
 */
// export function uploadPackage(data) {
//   return request({
//     url: '/expands/oss/promote/channel/uploadPackage',
//     method: 'post',
//     transformRequest(data) {
//       const formData = new FormData()
//       for (const item in data) {
//         formData.append(item, data[item])
//       }
//       return formData
//     },
//     contentType: false,
//     data
//   })
// }

/**
 * 保存渠道包信息
 * @param query
 */
export function savePackageInfo(data) {
  return request({
    url: '/expands/oss/promote/channel/savePackageInfo',
    method: 'post',
    data,
  })
}
