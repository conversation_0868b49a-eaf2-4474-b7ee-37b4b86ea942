import request from '@/utils/request'

/**
 * 供应链 商品列表
 * @param query
 */
export function GetListGoods(query) {
  return request({
    url: `/supply/oss/jd/goods/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 商品详情
 * @param query
 */
export function GetDetail(query) {
  return request({
    url: `supply/oss/jd/goods/detail`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 商品上下架
 * @param data
 */
export function UpdateGoodStatus(data) {
  return request({
    url: `/supply/oss/jd/goods/updateStatus`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 编辑sku
 * @param data
 */
export function UpdateGoodEdit(data) {
  return request({
    url: `/supply/oss/jd/goods/edit`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 京东sku列表
 * @param query
 */
export function GetSkuList(query) {
  return request({
    url: `/supply/oss/jd/sku/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 删除商品
 * @param query
 */
export function DeleteGoods(data) {
  return request({
    url: `/supply/oss/jd/goods/delete`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 京东sku详情
 * @param query
 */
export function GetSkuDetail(query) {
  return request({
    url: `/supply/oss/jd/sku/detail`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 编辑sku
 * @param data
 */
export function SaveSkuEdit(data) {
  return request({
    url: `/supply/oss/jd/sku/edit`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 同步商品
 * @param query
 */
export function SyncGoods(data) {
  return request({
    url: `/supply/oss/jd/goods/syncGoods`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 品牌列表
 * @param query
 */
export function GetBrandList(query) {
  return request({
    url: `/supply/oss/brand/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 添加品牌
 * @param data
 */
export function AddBrandDetail(data) {
  return request({
    url: `/supply/oss/brand/add`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 编辑品牌
 * @param data
 */
export function UpdateBrandDetail(data) {
  return request({
    url: `/supply/oss/brand/update`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 删除品牌
 * @param data
 */
export function DeleteBrandDetail(data) {
  return request({
    url: `/supply/oss/brand/delete`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 类目列表
 * @param query
 */
export function GetCategoryList(query) {
  return request({
    url: `/supply/oss/category/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 添加一级类目
 * @param data
 */
export function AddFirstCategory(data) {
  return request({
    url: `/supply/oss/category/addFirstCategory`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 添加二级类目
 * @param data
 */
export function AddSecondCategory(data) {
  return request({
    url: `/supply/oss/category/addSecondCategory`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 编辑一级类目
 * @param data
 */
export function UpdateFirstCategory(data) {
  return request({
    url: `/supply/oss/category/updateFirstCategory`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 编辑二级类目
 * @param data
 */
export function UpdateSecondCategory(data) {
  return request({
    url: `/supply/oss/category/updateSecondCategory`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 删除类目
 * @param data
 */
export function DeleteCategory(data) {
  return request({
    url: `/supply/oss/category/delete`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 订单列表-新
 * @param query
 */
export function GetOrderList(query) {
  return request({
    url: `/supply/oss/order/list`,
    method: 'get',
    params: query,
  })
}
/**
 * 供应链 订单详情
 * @param query
 */
export function GetOrderDetailList(query) {
  return request({
    url: `/supply/oss/order/detail`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 订单物流信息
 * @param query
 */
export function GetQueryDelivery(query) {
  return request({
    url: `/supply/oss/order/queryDelivery`,
    method: 'get',
    params: query,
  })
}
/**
 * 供应链 手动返还金币
 * @param query
 */
export function GetReturnGold(data) {
  return request({
    url: `/supply/oss/order/returnGold`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 推荐商品列表
 * @param query
 */
export function GetRecommendGoodsList(query) {
  return request({
    url: `/supply/oss/recommend/goods/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 售后详情
 * @param query
 */
export function GetAfterSaleDetail(query) {
  return request({
    url: `/supply/oss/afterSale/detail`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 新增推荐商品
 * @param data
 */
export function PostRecommendGoodsListAdd(data) {
  return request({
    url: `/supply/oss/recommend/goods/add`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 推荐商品更新权重
 * @param data
 */
export function PostRecommendGoodsUpdateWeight(data) {
  return request({
    url: `/supply/oss/recommend/goods/update/weight`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 推荐商品删除
 * @param data
 */
export function DeleteRecommendGoods(data) {
  return request({
    url: `/supply/oss/recommend/goods/deleteById`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 售后物流
 * @param query
 */
export function GetAfterDeliveryInfo(query) {
  return request({
    url: `/supply/oss/afterSale/delivery/info`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 订单看板-交易流水列表
 * @param query
 */
export function GetSupplySerialsList(query) {
  return request({
    url: `/supply/oss/serials/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 订单看板-账户余额
 * @param query
 */
export function GetSupplySerialsAmount(query) {
  return request({
    url: `/supply/oss/serials/account/amount`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 订单看板-汇总数据
 * @param query
 */
export function GetSupplySerialsTotal(query) {
  return request({
    url: `/supply/oss/serials/total`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 查询专题
 * @param query
 */
export function GetTopicList(query) {
  return request({
    url: `/supply/oss/topic/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 专题详情
 * @param query
 */
export function GetTopicFloorList(query) {
  return request({
    url: `/supply/oss/topic/floor/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 新增/编辑/删除楼层以及楼层下商品
 * @param data
 */
export function SaveOrTopicUpdate(data) {
  return request({
    url: `/supply/oss/topic/saveOrUpdate`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 楼层状态变更
 * @param data
 */
export function UpdateTopicStatus(data) {
  return request({
    url: `/supply/oss/topic/updateStatus`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 导入商品信息快照
 * @param data
 */
export function TopicGoodsInfo(query) {
  return request({
    url: `/supply/oss/topic/goods/info`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 商品操作日志
 * @param data
 */
export function getGoodsLog(query) {
  return request({
    url: `/supply/oss/jd/goods/log`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 订单操作日志
 * @param data
 */
export function GetOperateRecord(query) {
  return request({
    url: `/supply/oss/order/operateRecord`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 商品操作日志
 * @param data
 */
export function SaveOperateRecord(data) {
  return request({
    url: `/supply/oss/order/saveOperateRecord`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 售后列表
 * @param query
 */
export function GetSupplyAfterSaleList(query) {
  return request({
    url: `/supply/oss/afterSale/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 售后退款
 * @param data
 */
export function postAfterSaleRefund(data) {
  return request({
    url: `/supply/oss/afterSale/refund`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 已售后列表 已退款金额
 * @param query
 */
export function GetSupplyAfterSaleCheckRefundquery(query) {
  return request({
    url: `/supply/oss/afterSale/checkRefund`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 商品 批量上下架
 * @param data
 */
export function BatchUpdateStatus(data) {
  return request({
    url: `/supply/oss/jd/sku/batchUpdateStatus`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 商品 批量设置价格
 * @param data
 */
export function BatchUpdatePrice(data) {
  return request({
    url: `/supply/oss/jd/sku/batchUpdatePrice`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 自营商品列表
 * @param data
 */
export function GetSelfGoodsList(query) {
  return request({
    url: `/supply/oss/self/goods/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 b端 erp店铺列表
 * @param data
 */
export function GetErpShopList(query) {
  return request({
    url: `/supply/oss/erp/shop/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 商品 同步erp商品
 * @param data
 */
export function syncGoodsErp(data) {
  return request({
    url: `/supply/oss/erp/syncGoods`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 自营商品详情
 * @param data
 */
export function getSelfGoodsDetail(query) {
  return request({
    url: `/supply/oss/self/goods/detail`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 b端 自营商品sku详情
 * @param data
 */
export function getSelfGoodsSkuDetail(query) {
  return request({
    url: `/supply/oss/self/goods/sku/detail`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 b端 自营商品sku上下架状态更新
 * @param data
 */
export function postSelfGatchUpdateStatus(data) {
  return request({
    url: `/supply/oss/self/goods/sku/batchUpdateStatus`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 编辑自营商品信息
 * @param data
 */
export function postSelfGoodsEdit(data) {
  return request({
    url: `/supply/oss/self/goods/edit`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 编辑sku信息
 * @param data
 */
export function postSelfGoodsSkuEdit(data) {
  return request({
    url: `/supply/oss/self/goods/sku/edit`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 自营商品批量更新价格
 * @param data
 */
export function postSelfBatchUpdatePrice(data) {
  return request({
    url: `/supply/oss/self/goods/sku/batchUpdatePrice`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 导出自营商品列表
 * @param data
 */
export function getSelfExportGoodsList(query) {
  return request({
    url: `/supply/oss/self/goods/export/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 b端 自营商品oss操作日志
 * @param data
 */
export function GetSelfGoodsLog(query) {
  return request({
    url: `/supply/oss/self/goods/log`,
    method: 'get',
    params: query,
  })
}
/**
 * 供应链 b端 售后单审核
 * @param data
 */
export function auditAfterSale(data) {
  return request({
    url: `/supply/oss/afterSale/selfCheck`,
    method: 'post',
    data,
  })
}
/**
 * 供应链 b端 售后换货
 * @param data
 */
export function changeAfterSale(data) {
  return request({
    url: `/supply/oss/afterSale/checkChangeGoods`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 模糊搜索商品
 * @param data
 */
export function GetSelfSearchGoodsList(query) {
  return request({
    url: `/supply/oss/goods/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 b端 售后规则列表
 * @param data
 */
export function GetAftersaleRuleList(query) {
  return request({
    url: `/supply/oss/afterrule/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 b端 编辑售后规则
 * @param data
 */
export function saveRuleOrUpdate(data) {
  return request({
    url: `/supply/oss/afterrule/saveOrUpdate`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 更新规则状态
 * @param data
 */
export function updateRuleSatus(data) {
  return request({
    url: `/supply/oss/afterrule/updateStatus`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 自营 售后退款
 * @param data
 */
export function checkSelfRefund(data) {
  return request({
    url: `/supply/oss/afterSale/checkSelfRefund`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 自营商品更新是否展示
 * @param data
 */
export function UpdateSelfShowFlag(data) {
  return request({
    url: `/supply/oss/self/goods/updateShowFlag`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 更新京东商品是否展示
 * @param data
 */
export function UpdateJdShowFlag(data) {
  return request({
    url: `/supply/oss/jd/goods//updateShowFlag`,
    method: 'post',
    data,
  })
}

/**
 * 供应链 b端 自营商品批量更新价格
 * @param data
 */
export function deleteAftersaleRule(data) {
  return request({
    url: `/supply/oss/afterrule/deleteById`,
    method: 'post',
    data,
  })
}

/*
 * 供应链 b端 更新京东商品是否展示
 * @param data
 */
export function postBatchDelSku(data) {
  return request({
    url: `/supply/oss/self/goods/sku/batchDel`,
    method: 'post',
    data,
  })
}
/*
 * 供应链 b端 更新商品权重
 * @param data
 */
export function updateOrderWeight(data) {
  return request({
    url: `/supply/oss/self/goods/updateOrderWeight`,
    method: 'post',
    data,
  })
}
/*
 * 供应链 b端 编辑属性排序
 * @param data
 */
export function postPropertiesEdit(data) {
  return request({
    url: `/supply/oss/self/goods/properties/editSort`,
    method: 'post',
    data,
  })
}
/*
 * 供应链 b端 批量编辑规格名
 * @param data
 */
export function postPropertiesBatchUpdate(data) {
  return request({
    url: `/supply/oss/self/goods/properties/batchUpdate`,
    method: 'post',
    data,
  })
}
/*
 * 供应链 b端 属性排序列表
 * @param data
 */
export function getPropertiesList(query) {
  return request({
    url: `/supply/oss/self/goods/properties/list`,
    method: 'get',
    params: query,
  })
}

/**
 * 供应链 b端 更新订单价格
 * @param data
 */
export function updateOrderPrice(data) {
  return request({
    url: `/supply/oss/order/updateOrderPrice`,
    method: 'post',
    data,
  })
}
/**
 * 供应链 b端 oss申请退款
 * @param data
 */
export function PostRefundMoney(data) {
  return request({
    url: `/supply/oss/self/refund/apply`,
    method: 'post',
    data,
  })
}
/**
 * 供应链 b端 oss退款详情
 * @param data
 */
export function GetRefundMoneyDetail(query) {
  return request({
    url: `/supply/oss/self/refund/detail`,
    method: 'get',
    params: query,
  })
}
/**
 * 供应链 b端 oss退款申请列表
 * @param data
 */
export function GetRefundMoneyList(query) {
  return request({
    url: `/supply/oss/self/refund/list`,
    method: 'get',
    params: query,
  })
}
/**
 * 供应链 b端 oss申请退款审核
 * @param data
 */
export function PostRefundMoneyAudit(data) {
  return request({
    url: `/supply/oss/self/refund/auth`,
    method: 'post',
    data,
  })
}
/*
 * b端 自营商品 编辑订单收货地址信息
 * @param data
 */
export function updateSupplyDeliveryAddress(data) {
  return request({
    url: `/supply/oss/order/updateDeliveryAddress`,
    method: 'post',
    data,
  })
}
/*
 * b端 自营商品 换货售后sku列表
 * @param data
 */
export function GetNewGoods(query) {
  return request({
    url: `/supply/oss/afterSale/skuInfo`,
    method: 'get',
    params: query,
  })
}
/*
 * b端 自营商品 申请售后
 * @param data
 */
export function postAfterSale(data) {
  return request({
    url: `/supply/oss/afterSale/apply`,
    method: 'post',
    data,
  })
}

/*
 * b端 线下新增订单
 * @param data
 */
export function postAddOffLineOrder(data) {
  return request({
    url: `/supply/oss/order/add`,
    method: 'post',
    data,
  })
}
/*
 * b端 线下新增 skuList
 * @param data
 */
export function getOrderSkuList(params) {
  return request({
    url: `/supply/oss/self/goods/listSku`,
    method: 'get',
    params,
  })
}
