import request from '@/utils/request'

export enum UpgradeType {
  /**会员升级 */
  UPGRADEMEMBER = 1,
  /**年包补差价 */
  MAKEUPDIFFERENCE = 2
}
export type IserviceCfg = {
  trialDay?: any
  orgDiscount?: any
  settingName?: string
  itemName?: string
  shopName: string
  serviceType: string | number
  itemId: number | string
  trial?: boolean
  shopId: string
  level?: number
  orgPrice?: string
  discount?: string
  price: string
  beginTime?: string
  endTime?: string
  number?: number
  giveReason?: string
  orderNumber?: string
  settingType?: number
  upgrade?: UpgradeType
  minPrice?: string
  brandPriceDetail?: string
  addBrand?: string | number
  advertTitle?: string
  isGive?: string | number
}
export interface IPayForm {
  payType: any
  payTime: string
  payNumber: string
  transactionNumber: string
  payUser: string
}
export type IOrderParams = {
  payPersonName?: string
  payTypeName?: string
  shopId?: string
  serviceCfgs: string
  price: number | string
  paySource?: any
  payOtherVoucher?: any
  transferReceipt?: any
  transferredServiceInfo?: string
  remark?: string
} & IPayForm
// 转移订单
export interface ITransferServiceInfo {
  shopId: string
  orderNumber: string
  serviceType: string
  serviceName: string
  level: number
  usedNumber: string
  restNumber: string
  refundPrice: string
}
// 创建订单(0元或者线下支付)
export function createOrderByOffline(data: IOrderParams) {
  return request({
    url: '/shop/oss/service/order/create',
    method: 'post',
    data
  })
}

// 创建订单(生成二维码)
export function createOrderByQrcode(data: IOrderParams) {
  return request({
    url: '/shop/oss/service/order/create/genQrCode',
    method: 'post',
    data
  })
}
// 升级订单(0元或者线下支付)
export function upgradeOrderByOffline(data: IOrderParams) {
  return request({
    url: '/shop/oss/service/order/upgrade',
    method: 'post',
    data
  })
}
// 补差价订单
export function upgradeOrderDiffByOffline(data: IOrderParams) {
  return request({
    url: '/shop/oss/service/order/annualPackage/upgrade',
    method: 'post',
    data
  })
}
// 升级订单(生成二维码)
export function upgradeOrderByQrcode(data: IOrderParams) {
  return request({
    url: '/shop/oss/service/order/upgrade/genQrCode',
    method: 'post',
    data
  })
}
// 升级补差价订单(生成二维码)
export function upgradeOrderDiffByQrcode(data: IOrderParams) {
  return request({
    url: '/shop/oss/service/order/annualPackage/upgrade/genQrCode',
    method: 'post',
    data
  })
}
// 转移订单
export function transferOrder(data: Omit<IOrderParams, keyof IPayForm>) {
  return request({
    url: '/shop/oss/service/order/transfer',
    method: 'post',
    data
  })
}
// 转移订单基本信息
export function getTransferOrderDetail(params: { orderNum: string }) {
  return request({
    url: '/shop/oss/service/order/transfer/baseInfo',
    method: 'get',
    params
  })
}
// 转移订单基本信息
export function getOrderUpgradeDetail(params: any) {
  return request({
    url: '/shop/oss/service/order/upgrade/calculate',
    method: 'get',
    params
  })
}
// 补差价订单基本信息
export function getOrderDiffDetail(params: any) {
  return request({
    url: '/shop/oss/service/order/annualPackage/calculate',
    method: 'get',
    params
  })
}
// 获取会员续费时间
export function getTimeInfo(params: any) {
  return request({
    url: '/shop/oss/service/order/member/timeInfo',
    method: 'get',
    params
  })
}
// 品牌线索付费信息
export function getBrandQueryInfo(data: any) {
  return request({
    url: '/shop/oss/service/order/brand/queryInfo',
    method: 'post',
    data
  })
}
// 获取年包信息
export function getClueOrderList(params: any) {
  return request({
    url: '/shop/oss/clue/getClueOrderList',
    method: 'get',
    params
  })
}
