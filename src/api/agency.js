import http from '@/utils/request'

class Agency {
  // 代理商列表查询接口
  getAgentRelationList({
    brandId, // 品牌ID
    locationName,
    locationCode, // 省市编码 查区代理不需要
    agentLevel, // 代理级别 1省 2市 3区县
    parentId = 0, // 上级代理商关联ID 省级传0
    authorized = true, // 是否是授权经销商 默认是
  }) {
    // console.log(arguments[0])
    return http.get('shop/oss/brand/agent/relation/list', {
      params: {
        brandId,
        locationName,
        locationCode,
        agentLevel,
        parentId,
        authorized,
      },
    })
    // return new Promise((resolve, reject) => {
    //   resolve({
    //     data: {
    //       data: [
    //         {
    //           id: '12345',
    //           shopId: '23456',
    //           shopName: '春风江苏代理',
    //           type: 1,
    //           status: 1
    //         }
    //       ]
    //     }
    //   })
    // })
  }

  // 添加品牌经销商关联
  addAgentRelation({
    agentLevel, // 代理级别 1省 2市 3区县
    authorized = true, // 是否是授权经销商
    brandId, // 品牌ID
    shopId, //  经销商ID
    provinceCode,
    provinceName,
    cityCode,
    cityName,
    parentId,
  }) {
    const postData = {
      agentLevel,
      authorized,
      brandId,
      shopId,
      provinceCode,
      provinceName,
      cityCode,
      cityName,
      parentId,
    }
    // return new Promise((resolve, reject) => {
    //   resolve({
    //     data: {
    //       data: {
    //         id: '12346',
    //         shopId: '23457',
    //         shopName: '春风江苏代理11',
    //         type: 1,
    //         status: 1
    //       }
    //     }
    //   })
    // })
    return http.post('shop/oss/brand/agent/relation', postData)
  }

  // 删除关联经销商接口
  deleteAgentRelation({ id }) {
    // return new Promise((resolve, reject) => {
    //   resolve()
    // })
    return http.post('shop/oss/brand/agent/relation/delete', {
      id,
    })
  }

  // 编辑关联经销商接口
  modifyAgentRelation({ id, shopId }) {
    // return new Promise((resolve, reject) => {
    //   resolve({
    //     data: {
    //       data: {
    //         id: '12347',
    //         shopId: '23458',
    //         shopName: '春风江苏代理123',
    //         type: 1,
    //         status: 1
    //       }
    //     }
    //   })
    // })
    return http.post('shop/oss/brand/agent/relation/modify', {
      id,
      shopId,
    })
  }

  // 根据品牌和经销商查询关联
  queryAgentRelation({ brandId, shopId, authorized = true }) {
    // return new Promise((resolve, reject) => {
    //   resolve({
    //     data: {
    //       code: 0,
    //       msg: 'success',
    //       data: {
    //         id: 5,
    //         locationCode: '340800',
    //         locationName: '安庆市',
    //         agentLevel: 3,
    //         brandId: 193,
    //         shopId: 256422,
    //         shopName: '东方店铺5',
    //         status: 1,
    //         parent: {
    //           id: 4,
    //           locationCode: '340800',
    //           locationName: '安庆市',
    //           agentLevel: 2,
    //           brandId: 193,
    //           shopId: 256423,
    //           shopName: '东方店铺6',
    //           status: 1,
    //           parent: {
    //             id: 3,
    //             locationCode: '340000',
    //             locationName: '安徽省',
    //             agentLevel: 1,
    //             brandId: 193,
    //             shopId: 257012,
    //             shopName: 'Zzb的香蕉店',
    //             status: 1,
    //             parent: null,
    //           },
    //         },
    //       },
    //       error: false,
    //       success: true,
    //     },
    //   })
    // })
    return http.get('shop/oss/brand/agent/relation/query', {
      params: {
        brandId,
        shopId,
        authorized,
      },
    })
  }

  // 根据品牌和经销商查询关联-未授权
  queryUnauthorizedAgentRelation({ brandId, shopId, authorized = false }) {
    return http.get('shop/oss/brand/agent/relation/queryUnauthorized', {
      params: {
        brandId,
        shopId,
        authorized,
      },
    })
  }

  // 经销商添加品牌
  addShopBrandRel({
    shopId,
    type,
    superShopName,
    superBrandName,
    flag = 0, // 校验限制品牌 0:校验 1:不校验
  }) {
    return http.post('shop/oss/merchant/brand/addShopBrandRel', {
      shopId,
      type,
      superShopName,
      superBrandName,
      flag,
    })
  }

  // 线索代打列表
  getClueGenerationList(params) {
    return http.get('shop/oss/clue/generation/list', {
      params,
    })
  }

  // 线索代打详情
  getClueGenerationDetail(id) {
    return http.get('/shop/oss/clue/generation/detail', {
      params: {
        id,
      },
    })
  }

  // 商务列表
  getMerchantUsers() {
    return http.get('shop/oss/business/merchant/users', {
      params: {},
    })
  }

  // 商务绑定经销商列表
  getMerchantShopLists(params) {
    return http.get('/shop/oss/business/merchant/shopList', {
      params,
    })
  }

  // 线索历史记录
  getClueDetailHistory(id) {
    return http.get('/shop/oss/clue/generation/detail/history', {
      params: {
        id,
      },
    })
  }

  // 商务绑定经销商
  postMerchantBind(postData) {
    return http.post('/shop/oss/business/merchant/bind', postData)
  }

  // 商务解绑经销商
  postMerchantUnbind(postData) {
    return http.post('/shop/oss/business/merchant/unbind', postData)
  }

  // 商务绑定用户手机号
  postClueBindMobile(postData) {
    return http.post('/shop/oss/clue/generation/bindMobile', postData)
  }

  // 保存线索
  postClueUpdate({
    id, // 线索id
    buyCarTime, // 预计购车时间(预计购车 7.7天内 15:15天内 30：30天内 90：3个月内 180:半年内)
    intention, // 客户意向 1：高 2：中等 3：低 4：空号错号 5:已成交 6.无效客户 7.未联系上客户，待再次联系
    handleStatus, // 成交状态 1：未跟进 2：跟进中 3:已到店 4:已成交 5 战败
    description, // 备注
    realName, // 姓名
  }) {
    return http.post('/shop/oss/clue/generation/update', {
      id,
      buyCarTime,
      intention,
      handleStatus,
      description,
      realName,
    })
  }
}

const agencyService = new Agency()

export default agencyService
