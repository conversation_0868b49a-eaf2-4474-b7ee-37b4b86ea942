import request from '@/utils/request'

// 热搜列表
export function hotsearchList(query) {
  return request({
    url: '/forum/oss/remmdController/listTrend',
    method: 'get',
    params: query,
  })
}

// 热搜更新
export function hotsearchUpdateTrend(data) {
  return request({
    url: '/forum/oss/remmdController/updateTrend',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010501',
    data,
  })
}

// 热搜生效
export function hotsearchEffect(data) {
  return request({
    url: '/forum/oss/remmdController/cacheTrend',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010501',
    data,
  })
}
