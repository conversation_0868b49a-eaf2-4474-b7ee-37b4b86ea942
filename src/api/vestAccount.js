import request from '@/utils/request'
import { CRAWLERURL } from '@/utils/configData/config'

export function uploadImg(data) {
  return request({
    url: '/uic/operate/vest/uploadAvatar64',
    method: 'post',
    data,
  })
}

export function updateVest(data) {
  return request({
    url: '/uic/operate/vest/save',
    method: 'post',
    data,
  })
}

// 获取马甲号列表
export function getVestList({ operateUid, userId, page, limit }) {
  return request({
    url: '/uic/operate/vest/list',
    method: 'post',
    data: arguments[0],
  })
}

// 新增马甲号请求接口
export function addVest(data) {
  return request({
    url: '/uic/operate/vest/add',
    method: 'post',
    data,
  })
}

// 加入发布者库
export function publisher({ uid, username, publisherFlag }) {
  return request({
    url: '/platform/basecodetype/addNewPublisher.do',
    method: 'post',
    data: arguments[0],
  })
}

// 为马甲开通收益权益
export function equity({ uid, username, openIncomeStatus }) {
  return request({
    url: '/uic/oss/user/ad/openVestEssayIncomePermission',
    method: 'post',
    data: arguments[0],
  })
}

// 爬虫列表
export function getCrawlerList({
  uid,
  source,
  noneRecordWithin24h,
  page,
  limit,
}) {
  return request({
    url: '/oss/crawler/media/list.do',
    method: 'get',
    params: arguments[0],
  })
}

// 新增 作者同步信息
export function addCrawlerUser({
  mediaId,
  username,
  source,
  pageLink,
  autoSyncStatus,
}) {
  return request({
    url: '/oss/crawler/media/add.do',
    method: 'post',
    data: arguments[0],
  })
}

// 保存 作者同步信息
export function saveCrawlerUser({
  mediaId,
  username,
  source,
  pageLink,
  autoSyncStatus,
}) {
  return request({
    url: '/oss/crawler/media/update.do',
    method: 'post',
    data: arguments[0],
  })
}

// 手动同步
export function manualSync({ data, isWx }) {
  let url = 'crawler/task/trigger'
  if (isWx) {
    url = CRAWLERURL + url
  }
  return request({
    url,
    method: 'post',
    data: arguments[0],
  })
}

// 最近同步内容
export function recentCrawlerContent({ mediaId, source, limit }) {
  return request({
    url: '/oss/crawler/media/recent.do',
    method: 'get',
    params: arguments[0],
  })
}

export function vestLogin(data) {
  return request({
    url: '/uic/operate/vest/vestLogin',
    method: 'post',
    data,
  })
}

// 有赞报表列表查询
export function GetYouzanOrderReportList(params) {
  return request({
    url: '/activity/oss/orderReportController/list',
    method: 'get',
    params,
  })
}

// 有赞报表详情
export function GetYouzanOrderReportDetail(params) {
  return request({
    url: '/activity/oss/orderReportController/detail',
    method: 'get',
    params,
  })
}

// 保存/修改有赞报表信息
export function SaveYouzanOrderReport(data) {
  return request({
    url: '/activity/oss/orderReportController/save',
    method: 'post',
    data,
  })
}

// 赠送能量出列表
export function GetEnergyReportList(params) {
  return request({
    url: '/coins/oss/energy/sendEnergyList',
    method: 'get',
    params,
  })
}

// 保存/修改能量报表信息
export function SaveEnergyReport(data) {
  return request({
    url: '/coins/oss/energy/sendEnergy',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10317',
    data,
  })
}

// 能量风控列表
export function getEntryRiskList(params) {
  return request({
    url: '/coins/oss/energy/entry/risk/list',
    method: 'get',
    params,
  })
}

// 能量风控列表 处理风控
export function postUpdateEntryStatus(data) {
  return request({
    url: '/coins/oss/energy/entry/update/status',
    method: 'post',
    data,
  })
}
