import request from '@/utils/request'

// 商品管理，淘宝商品列表
export function getGoodList(data) {
  return request({
    url: '/activity/oss/goodManagerController/getGoodList',
    method: 'post',
    data
  })
}

// 获取商品信息
export function getMallGoodsDetailById(data) {
  return request({
    url: '/activity/oss/goodManagerController/getMallGoodsDetailById',
    method: 'post',
    data
  })
}

// 保存商品信息
export function saveGoodManager(data) {
  return request({
    url: '/activity/oss/goodManagerController/saveGoods',
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    data
  })
}

// 商品管理，活动淘宝商品列表
export function getActivityGoodsList(data) {
  return request({
    url: '/activity/oss/activityGoodManagerController/getActivityGoodsList',
    method: 'post',
    data
  })
}

// 商品管理，活动淘宝商品列表下活动商品
export function getActivityGoods(data) {
  return request({
    url: '/activity/oss/activityGoodManagerController/getActivityGoods',
    method: 'post',
    data
  })
}

// 获取活动商品详情
export function getActivityGoodsDetail(data) {
  return request({
    url: '/activity/oss/activityGoodManagerController/getActivityGoodsDetail',
    method: 'post',
    data
  })
}

// 保存活动商品信息
export function saveActivityGoods(data) {
  return request({
    url: '/activity/oss/activityGoodManagerController/saveActivityGoods',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1020202',
    data
  })
}

// 订单管理，订单列表
export function getOrderList(data) {
  return request({
    url: '/activity/oss/orderManagerController/getOrderListNew',
    method: 'post',
    data
  })
}

// 订单管理，订单列表，订单商品
export function getGoodsByOrderId(data) {
  return request({
    url: '/activity/oss/orderManagerController/getGoodsByOrderIdNew',
    method: 'post',
    data
  })
}

// 订单管理，订单列表，订单详情
export function getOrderDetail(data) {
  return request({
    url: '/activity/oss/orderManagerController/getOrderDetailNew',
    method: 'post',
    data
  })
}

// oss能量商城修改快递信息
export function postChangeExpress(data) {
  return request({
    url: '/activity/oss/orderManagerController/changeExpress',
    method: 'post',
    data
  })
}
// // oss能量商城查询物流
// export function GetQueryEnergyDelivery(params) {
//   return request({
//     url: '/activity/oss/orderManagerController/queryDelivery',
//     method: 'get',
//     params
//   })
// }

// 订单管理，订单列表，更新订单（发货）
export function saveOrderDetail(data) {
  return request({
    url: '/activity/oss/orderManagerController/expressOrderNew',
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    data
  })
}

// 订单管理，订单列表，更新订单（退款）
export function postRefund(data) {
  return request({
    url: '/activity/oss/orderManagerController/refund',
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    data
  })
}

// 获取列表 财务订单
export function getFinanceList(data) {
  return request({
    url: '/activity/oss/mall/order/finance/listNew',
    method: 'post',
    data
  })
}

// 获取列表 退款订单
export function getRefundList(data) {
  return request({
    url: '/activity/oss/mall/order/refund/list',
    method: 'post',
    data
  })
}

// 获取列表 财务统计
export function getStatisticsList(data) {
  return request({
    url: '/activity/oss/mall/order/finance/statistics/dataNew',
    method: 'post',
    data
  })
}

// 充值话费
export function updateOrderStatus(data) {
  return request({
    url: '/activity/oss/orderManagerController/updateOrderStatusNew',
    method: 'post',
    data
  })
}

// 获取尾款结算列表
export function getBalanceSettlement(query) {
  return request({
    url: '/trade/oss/order/shop/findBalanceSettlement',
    method: 'get',
    params: query
  })
}

// 结款操作
export function setTlement(data) {
  return request({
    url: '/trade/oss/order/shop/settlement',
    method: 'post',
    data
  })
}

// 订单管理-导入excel
export function postVerifyAccount(data) {
  return request({
    url: '/activity/oss/adminVerifyAccountRecordController/verifyAccountNew',
    method: 'post',
    data
  })
}

// 订单管理-能量退款
export function postRefundEnergy(data) {
  return request({
    url: '/activity/oss//orderManagerController/refund',
    method: 'post',
    data
  })
}

// 二手车财务结款表-结款成功金额合计
export function getRemitTotalAmount(query) {
  return request({
    url: '/transaction/adminMotorSecondHandRemitController/remitTotalAmount',
    method: 'get',
    params: query
  })
}

// 二手车财务结款表-结款成功金额合计
export function getRemitTotalAmountMerchant(query) {
  return request({
    url: '/transaction/shop/secondHandCar/remit/totalAmount',
    method: 'get',
    params: query
  })
}

// 二手车财务结款表-打款列表
export function getBendRemitList(query) {
  return request({
    url: '/transaction/adminMotorSecondHandRemitController/bendRemitList',
    method: 'get',
    params: query
  })
}

// 二手车财务结款表-打款列表
export function getMerchantBendRemitList(query) {
  return request({
    url: '/transaction/shop/secondHandCar/remit/list',
    method: 'get',
    params: query
  })
}

// 二手车财务结款表-打款成功
export function postRemitSuccess(data) {
  return request({
    url: '/transaction/adminMotorSecondHandRemitController/remitSuccess',
    method: 'post',
    data
  })
}

// 二手车财务打款
export function Settlement(data) {
  return request({
    url: '/transaction/adminMotorSecondHandRemitController/remit',
    method: 'post',
    data
  })
}
// 二手车财务打款-结算操作
export function settlementOperate(data) {
  return request({
    url: '/transaction/adminMotorSecondHandRemitController/settlementOperate',
    method: 'post',
    data
  })
}
// 二手车商家结款
export function SettlementMerchant(data) {
  return request({
    url: '/transaction/shop/secondHandCar/remit/shopRemit',
    method: 'post',
    data
  })
}

// 二手车财务结款表-打款失败
export function postRemitFail(data) {
  return request({
    url: '/transaction/adminMotorSecondHandRemitController/remitFail',
    method: 'post',
    data
  })
}

// 二手车退款订单列表列表
export function getUsedCarRefundList(query) {
  return request({
    url: '/transaction/adminMotorUsedCarOrderController/refund/list',
    method: 'get',
    params: query
  })
}

// 兑奖列表
export function getListPrizeRecord(query) {
  return request({
    url: '/coins/oss/adPrize/listPrizeRecord',
    method: 'get',
    params: query
  })
}

// 处理兑奖
export function postUpdateDealStatus(data) {
  return request({
    url: '/coins/oss/adPrize/updateDealStatus',
    method: 'post',
    data
  })
}

// oss租车订单列表
export function getRentalCarOrder(query) {
  return request({
    url: '/rental/oss/admin/order/list',
    method: 'get',
    params: query
  })
}

// 修改活动商品顺序
export function postModifyOrder(data) {
  return request({
    url: '/activity/oss/activityGoodManagerController/sortOperate',
    method: 'post',
    data
  })
}

// oss租车订单新增汇总金额
export function rentalTotalAmount(params) {
  return request({
    url: '/rental/oss/admin/order/rentalTotalAmount',
    method: 'get',
    params
  })
}

// 二手车相关订单
export function getUserRelatedOrder(params) {
  return request({
    url: '/transaction/adminMotorUsedCarOrderController/user/related/order',
    method: 'get',
    params
  })
}

// 租车相关订单
export function getRentalUserRelatedOrder(params) {
  return request({
    url: '/rental/oss/admin/order/user/related/order',
    method: 'get',
    params
  })
}

// 打款失败消息推送
export function paymentFailureMessagePush(data) {
  return request({
    url: '/transaction/adminMotorSecondHandRemitController/push/remit/error',
    method: 'post',
    data
  })
}
