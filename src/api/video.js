/* eslint-disable no-magic-numbers */
import request from '@/utils/request'
import { APIURL } from '@/utils/configData/config'

if (!window.AliyunUpload) {
  const isOnLine =
    window.location.origin.indexOf('jddmoto') > -1 ||
    window.location.origin.indexOf('58moto') > -1
  const pre = isOnLine ? '/oss' : ''
  window.loadJs(`${pre}/static/js/aliyun-upload-sdk/lib/es6-promise.min.js`)
  window.loadJs(
    `${pre}/static/js/aliyun-upload-sdk/lib/aliyun-oss-sdk-5.3.1.min.js`
  )
  window.loadJs(
    `${pre}/static/js/aliyun-upload-sdk/aliyun-upload-sdk-1.5.0.min.js`
  )
}

// 1、获取签名 videoSign 2、调用阿里云sdk上传视频 3、视频上传完成后转码 creatingVideoTranscoding 4、定时器监听转码结果 getVideoTaskStatus 5、定时器监听：转码成功后获取视频信息 getVideoMes

// 1、视频签名
export function videoSign() {
  return request({
    url: `${APIURL}forum/public/aliyunVideoController.do?action=20205&fromSource=3696441120A402F793A704766540E69E`,
    method: 'get'
  })
}
// 获取上传凭证

export function getVideoUploadAuth(params = {}) {
  return request({
    url: `${APIURL}pirate/media/aliyun/video/upload/auth`,
    method: 'get',
    params
  })
}
// 2、调用阿里云sdk上传视频 AliyunUpload
export function createUploader(file) {
  const _this = this
  const self = {}
  return new Promise((resolve, reject) => {
    if (self.uploader) {
      self.uploader.stopUpload()
    } else {
      self.uploader = new window.AliyunUpload.Vod({
        timeout: self.timeout || 60000,
        partSize: self.partSize || 1048576,
        parallel: self.parallel || 5,
        retryCount: self.retryCount || 3,
        retryDuration: self.retryDuration || 2,
        region: self.region || 'cn-beijing',
        userId: self.userId || '1303984639806000',
        // 添加文件成功
        // addFileSuccess: function (uploadInfo) {
        //   _this.loadingText = '添加文件成功, 等待上传...'
        //   console.log("addFileSuccess: " + uploadInfo.file.name)
        // },
        // 开始上传
        onUploadstarted: function (uploadInfo) {
          // 如果是 STSToken 上传方式, 需要调用 uploader.setUploadAuthAndAddress 方法
          // 用户需要自己获取 accessKeyId, accessKeySecret,secretToken
          // const needUpdateAccess = !self.exp || new Date() >= new Date(self.exp)
          // needUpdateAccess &&
          //   videoSign()
          //     .then(({ data }) => {
          //       const info = data.data
          //       const accessKeyId = info.accessKeyId
          //       const accessKeySecret = info.accessKeySecret
          //       const secretToken = info.stsToken
          //       self.exp = info.exp
          //       self.uploader.setSTSToken(
          //         uploadInfo,
          //         accessKeyId,
          //         accessKeySecret,
          //         secretToken
          //       )
          //     })
          //     .catch((_) => {
          //       reject(`签名获取失败..., message: ${JSON.stringify(_)}`)
          //     })
          getVideoUploadAuth({
            fileName: uploadInfo.file.name,
            businessType: 6
          })
            .then(({ data }) => {
              const info = data.data
              self.uploader.setUploadAuthAndAddress(
                uploadInfo,
                info.uploadAuth,
                info.uploadAddress,
                info.videoId
              )
            })
            .catch((_) => {
              reject(`凭证获取失败..., message: ${JSON.stringify(_)}`)
            })
          // console.log('文件开始上传...')
          // console.log(uploadInfo)
          _this.loadingText = '文件开始上传...'
        },
        // 文件上传成功
        async onUploadSucceed(uploadInfo) {
          console.log(`文件上传成功! onUploadSucceed`, uploadInfo)
          getVideoMes(uploadInfo.videoId)
            .then(function (res) {
              if (res.data.code === 0 && res.data.data !== null) {
                uploadInfo.videoUrl = res.data.data.sourceVideoUrl // 视频源地址
                uploadInfo.coverUrl = res.data.data.coverUrl // 视频封面
                uploadInfo.duration = res.data.data.duration // 视频时长
              }
            })
            .catch(function (err) {
              console.error(err)
            })
            .finally((_) => {
              _this.loadingText = '文件上传成功!'
              uploadInfo.videoUrl =
                uploadInfo.videoUrl || window.URL.createObjectURL(file)
              resolve(uploadInfo)
            })
        },
        // 文件上传失败
        onUploadFailed: function (uploadInfo, code, message) {
          console.log(
            'onUploadFailed: file:' +
              uploadInfo.file.name +
              ', code:' +
              code +
              ', message:' +
              message
          )
          _this.loadingText = '文件上传失败!'
          reject(`文件上传失败... code: ${code}, message: ${message}`)
        },
        // 取消文件上传
        // onUploadCanceled: function (uploadInfo, code, message) {
        //   console.log("Canceled file: " + uploadInfo.file.name + ", code: " + code + ", message:" + message)
        //   _this.loadingText = '文件已暂停上传'
        // },
        // 文件上传进度, 单位：字节, 可以在这个函数中拿到上传进度并显示在页面上
        onUploadProgress: function (uploadInfo, totalSize, progress) {
          const progressPercent = Math.ceil(progress * 100)
          console.log(
            '文件上传中..., onUploadProgress:file:' +
              uploadInfo.file.name +
              ', fileSize:' +
              totalSize +
              ', percent:' +
              progressPercent +
              '%'
          )
          _this.loadingText = `文件上传中..., 视频文件名：${
            file.name.split('.')[0]
          }, 上传进度为${progressPercent}%}`
        },
        // 上传凭证超时
        onUploadTokenExpired: function (uploadInfo) {
          // 上传文件过大时可能在上传过程中 sts token 就会失效, 所以需要在 token 过期的回调中调用 resumeUploadWithSTSToken 方法
          // const needUpdateAccess = !self.exp || new Date() >= new Date(self.exp)
          // needUpdateAccess &&
          //   videoSign()
          //     .then(({ data }) => {
          //       const info = data.data
          //       const accessKeyId = info.accessKeyId
          //       const accessKeySecret = info.accessKeySecret
          //       const secretToken = info.stsToken
          //       const expiration = info.exp
          //       self.exp = info.exp
          //       self.uploader.resumeUploadWithSTSToken(
          //         accessKeyId,
          //         accessKeySecret,
          //         secretToken,
          //         expiration
          //       )
          //     })
          //     .catch((_) => {
          //       reject(
          //         `上传凭证超时, 签名获取失败..., message: ${JSON.stringify(_)}`
          //       )
          //     })
          getVideoUploadAuth({
            fileName: uploadInfo.file.name,
            businessType: 6
          })
            .then(({ data }) => {
              const info = data.data
              self.uploader.resumeUploadWithAuth(
                uploadInfo,
                info.uploadAuth,
                info.uploadAddress,
                info.videoId
              )
            })
            .catch((_) => {
              reject(`凭证获取失败..., message: ${JSON.stringify(_)}`)
            })
          console.log('文件超时...')
          _this.loadingText = '文件超时...'
        }
        // 全部文件上传结束
        // onUploadEnd: function (uploadInfo) {
        //   console.log("onUploadEnd: uploaded all the files")
        //   _this.loadingText = '文件上传完毕'
        //   console.log('文件上传完毕')
        // }
      })
    }
    const userData = '{"Vod":{}}'
    self.uploader.addFile(file, null, null, null, userData)
    self.uploader.startUpload()
    // return self.uploader
  })
}

// 3、创建 视频转码 任务
// export function creatingVideoTranscoding(
//   vId,
//   type = '',
//   fromSource = '3696441120A402F793A704766540E69E'
// ) {
//   return request({
//     url: `${APIURL}forum/public/aliyunVideoController.do`,
//     method: 'get',
//     params: {
//       action: 20209,
//       fromSource,
//       id: vId,
//       type, // 操作类型 1.行家 2.爬取 3.pgc视频
//     },
//   })
// }

// 4、获取 视频转码 任务结果
export function getVideoTaskStatus(vId) {
  return request({
    url: `${APIURL}forum/public/aliyunVideoController.do`,
    method: 'get',
    params: {
      action: 20208,
      vodTaskId: vId
    }
  })
}

// 5、获取视频信息
export function getVideoMes(vId) {
  return request({
    url: `${APIURL}forum/public/aliyunVideoController.do`,
    method: 'get',
    params: {
      action: 20206,
      id: vId
    }
  })
}
