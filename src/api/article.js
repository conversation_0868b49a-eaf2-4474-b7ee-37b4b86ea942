import {
  APIURL,
  MANAGERURL,
  EMOTOFINEMANAGERURL,
  LIVEURL
} from '@/utils/configData/config'
import request from '@/utils/request'

/**
 * 验证文章状态
 *   http://article-similarity.beautdata.com/articleSimilarity?articleId=376220
 * @param {int} query 文章id
 */
export function validateArticle(query) {
  return request({
    url: '/forum/oss/businessEssayController/articleSimilarity',
    method: 'get',
    params: query
  })
}

/**
 * 验证视频状态
 * @param {int} query 文章id
 */
export function validateVideo(query) {
  return request({
    url: '/forum/oss/businessEssayController/videoSimilarity',
    method: 'get',
    params: query
  })
}

export function fetchList(query) {
  return request({
    url: '/article/list',
    method: 'get',
    params: query
  })
}

export function fetchArticle(id) {
  return request({
    url: '/article/detail',
    method: 'get',
    params: { id }
  })
}

export function fetchPv(pv) {
  return request({
    url: '/article/pv',
    method: 'get',
    params: { pv }
  })
}

export function createArticle(data) {
  return request({
    url: '/article/create',
    method: 'post',
    data
  })
}

export function updateArticle(data) {
  return request({
    url: '/article/update',
    method: 'post',
    data
  })
}

/**
 * 文章关联话题
 * @param data
 */
export function ArticleBindTopic(data) {
  return request({
    url: '/forum/oss/shortTopicEssayController/addShortTopicEssay',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010202',
    data
  })
}
/**
 * 文章解除关联话题
 * @param data
 */
export function ArticleUnbindTopic(data) {
  return request({
    url: '/forum/oss/shortTopicEssayController/deleteShortTopicEssay',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010202',
    data
  })
}

/**
 * 随机发布者
 * @param data
 */
export function randomAuthor(data) {
  return request({
    url: '/uic/operate/vest/randomVest',
    method: 'post',
    data
  })
}
/**
 * 获取我的工作记录
 * @param data
 */
export function workRecord(query) {
  return request({
    url: '/forum/oss/essay/auth/authRecord',
    method: 'get',
    params: query
  })
}

/**
 * 查询个人待审核 放到池子
 * @param data
 */
export function postPendingReview(query) {
  return request({
    url: '/forum/oss/essay/auth/pullAuthContent',
    method: 'post',
    params: query
  })
}

/**
 * 从池子中获取 个人待审核列表
 * @param data
 */
export function getAcquiredPending(query) {
  return request({
    url: '/forum/oss/essay/auth/authList',
    method: 'get',
    params: query
  })
}

/**
 * 获取内容审核页面基本信息
 * @param data
 */
export function getUpdatesDate(query) {
  return request({
    url: '/forum/oss/essay/auth/baseInfo',
    method: 'get',
    params: query
  })
}

/**
 * 批量动态审核通过
 * @param data
 */
export function batchApproveMoment(query) {
  return request({
    url: '/forum/oss/essay/auth/batchApproveMoment',
    method: 'post',
    params: query,
    timeout: 10000
  })
}

/*
 * 更新内容状态
 * @param data
 */
export function updateContentStatus(data) {
  return request({
    url: '/forum/oss/essay/auth/updateContentStatus',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010109',
    data,
    timeout: 10000
  })
}

/**
 * 文章或动态审核
 * @param data
 */
export function reviewArticle(data) {
  return request({
    url: '/forum/oss/essay/auth/updateContentInfo',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010105',
    data,
    timeout: 10000
  })
}

/**
 * 圈子或动态审核
 * @param data
 */
export function updateMomentContentInfo(data) {
  return request({
    url: '/forum/oss/essay/auth/updateMomentContentInfo',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010105',
    data
  })
}

/*
 * 圈子审核
 * @param data
 */
export function updateHoopContentInfo(data) {
  return request({
    url: '/forum/oss/essay/auth/updateHoopContentInfo',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010109',
    data
  })
}

/*
 * 当日审核数据
 * @param query
 */
export function getAuditRecord(query) {
  return request({
    url: 'forum/oss/essay/auth/authProgressOverview',
    method: 'get',
    params: query
  })
}

/*
 * 审核优质文章统计报表
 * @param query
 */
export function queryEntReport(query) {
  return request({
    url: 'forum/oss/essay/reporting/primeEssayAuthCnt',
    method: 'get',
    params: query
  })
}

/**
 * OSS审核内容
 * @param data
 */
export function reviewOssArticle(data) {
  return request({
    url: '/forum/oss/essay/auth/updateCrawlerContentInfo',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010105',
    data,
    timeout: 10000
  })
}

/**
 * 文章详情
 * http://10.33.73.190:8181/docs/ossapi/944 查询文章
 * http://10.33.73.190:8181/docs/ossapi/852 发布文章
 * @param data
 */
export function getEssayDetail(data) {
  return request({
    url: 'forum/oss/businessEssayController/queryEssayDetail',
    method: 'post',
    beforeAlterFirst: true,
    data
  })
}

/**
 * 编辑文章
 * @param data
 */
export function updateBusinessEssay(data) {
  // 兼容接口
  if (data.voteInfo) {
    data.voteInfo = JSON.stringify(data.voteInfo)
  }
  return request({
    url: '/forum/oss/businessEssayController/updateBusinessEssay',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010101',
    data
  })
}

/**
 * 导入文章
 * @param data
 */
export function importEssay(url) {
  return request({
    url: `${APIURL}forum/public/platform/essayController.do`,
    method: 'get',
    params: url
  })
}

/**
 * 分类列表
 * @param data
 */
export function getClassifyEmotofine(url) {
  return request({
    url: `${EMOTOFINEMANAGERURL}forum/oss/content/manager/typeList`,
    method: 'get',
    params: url
  })
}

/**
 * 分类列表
 * @param data
 */
export function getClassify(url) {
  return request({
    url: `${MANAGERURL}forum/oss/content/manager/typeList`,
    method: 'get',
    params: url
  })
}

/**
 * 添加分类
 * @param data
 */
export function addClassify(url) {
  return request({
    url: `${MANAGERURL}forum/oss/content/manager/addType`,
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    params: url
  })
}

/**
 * 更改分类状态
 * @param data
 */
export function updateClassifyStatus(url) {
  return request({
    url: `${MANAGERURL}forum/oss/content/manager/updateTypeStatus`,
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    params: url
  })
}

/**
 * oss同步文章到电摩
 * @param data
 */
export function syncEMotorEssay(data) {
  return request({
    url: `${MANAGERURL}forum/oss/businessEssayController/syncEMotorEssay`,
    method: 'post',
    data
  })
}

/**
 * 更改激励状态
 * @param data
 */
export function updateInspireStatus(url) {
  return request({
    url: `${MANAGERURL}forum/oss/content/manager/updateInspireStatus`,
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    params: url
  })
}

/**
 * 标志词列表
 * @param data
 */
export function getGrade(url) {
  return request({
    url: `${MANAGERURL}forum/oss/content/manager/levelList`,
    method: 'get',
    params: url
  })
}

/**
 * 添加标志词
 * @param data
 */
export function addGrade(url) {
  return request({
    url: `${MANAGERURL}forum/oss/content/manager/addLevel`,
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    params: url
  })
}

/**
 * 更改标志词有效状态
 * @param data
 */
export function updateGradeStatus(url) {
  return request({
    url: `${MANAGERURL}forum/oss/content/manager/updateLevelStatus`,
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    params: url
  })
}

/**
 * 创作活动列表
 * @param params
 */
export function actList(params) {
  return request({
    url: `/forum/oss/essay/activity/actList`,
    method: 'get',
    params: params
  })
}

/**
 * 创作活动文章列表
 * @param params
 */
export function actEssayList(params) {
  return request({
    url: `/forum/oss/essay/activity/actEssayList`,
    method: 'get',
    params: params
  })
}

/**
 * 创作活动详情
 * @param params
 */
export function actInfo(params) {
  return request({
    url: `/forum/oss/essay/activity/actInfo`,
    method: 'get',
    params: params
  })
}

/**
 * 创作活动发送消息通知
 * @param data
 */
export function actPushMsg(data) {
  return request({
    url: '/forum/oss/essay/activity/pushMsg',
    method: 'post',
    data
  })
}

/**
 * 保存/更新创作活动详情
 * @param data
 */
export function saveAct(data) {
  return request({
    url: '/forum/oss/essay/activity/saveAct',
    method: 'post',
    data
  })
}

/**
 * 更新文章评选奖项
 * @param data
 */
export function updateActAward(data) {
  return request({
    url: '/forum/oss/essay/activity/updateAward',
    method: 'post',
    data
  })
}

/**
 * 模糊查询创作活动标题
 * @param params
 */
export function queryActivity(params) {
  return request({
    url: '/forum/oss/essay/activity/actIdList',
    method: 'get',
    params
  })
}

/**
 * 更新文章评选奖项
 * @param data
 */
export function updateBonus(data) {
  return request({
    url: '/forum/oss/essay/activity/updateBonus',
    method: 'post',
    data
  })
}

/**
 * 测评置顶
 * @param data
 */
export function evaluationStick(data) {
  return request({
    url: '/forum/oss/businessEssayController/evaluationStick',
    method: 'post',
    data
  })
}

/**
 * oss模糊下拉文章标题
 * @param params
 */
export function queryEassyIdByTitle(params) {
  return request({
    url: '/forum/oss/businessEssayController/queryEassyIdByTitle',
    method: 'get',
    params
  })
}

/**
 * 文章冷却剩余时间
 * @param data
 */
export function getQueryEssayDetailTimer(query) {
  return request({
    url: '/factory/oss/cool/essay/coolSeconds',
    method: 'get',
    params: query
  })
}

/* 我的待审核列表
 * @param params
 */
export function GetActivityAuditList(params) {
  return request({
    url: '/activity/oss/admin/activity/audit/list',
    method: 'get',
    params
  })
}

/**
 * 拉取审核
 * @param params
 */
export function GetActivityAuditPullList(data) {
  return request({
    url: '/activity/oss/admin/activity/audit/pull',
    method: 'post',
    data
  })
}

/**
 * 审核统计
 * @param params
 */
export function GetActivityAuditStatisticData(params) {
  return request({
    url: '/activity/oss/admin/activity/audit/statistic',
    method: 'get',
    params
  })
}

/**
 * 取机审不通过数据
 * @param params
 */
export function pullMachineAuthContent(data) {
  return request({
    url: '/forum/oss/essay/auth/pullMachineAuthContent',
    method: 'post',
    data
  })
}

/**
 * 根据圈子获取关联车型
 * @param params
 */
export function getRelatedCarTypes(params) {
  return request({
    url: '/hoop/oss/hoop/relatedCarById',
    method: 'get',
    params
  })
}

/**
 * 审核厂商活动文章
 * @param params
 */
export function essayActivityAudit(data) {
  return request({
    url: '/forum/oss/essay/activity/audit',
    method: 'post',
    data
  })
}

/**
 * 暂停收益
 * @param params
 */
export function essayActivityStopIncome(data) {
  return request({
    url: '/forum/oss/essay/activity/stopIncome',
    method: 'post',
    data
  })
}

/**
 * 个人待审核列表
 * @param params
 */
export function getAuthList(params) {
  return request({
    url: '/forum/oss/essay/repeat/audit/authList',
    method: 'get',
    params
  })
}

/**
 * 复审-复审审核
 * @param params
 */
export function postUpdateContent(data) {
  return request({
    url: '/forum/oss/essay/repeat/audit/updateContent',
    method: 'post',
    data
  })
}

/**
 * 复审-拉取50条复审内容
 * @param params
 */
export function postPullAuthContent(data) {
  return request({
    url: '/forum/oss/essay/repeat/audit/pullAuthContent',
    method: 'post',
    data
  })
}

/**
 * 复审-查询复审列表
 * @param params
 */
export function getAllList(params) {
  return request({
    url: '/forum/oss/essay/repeat/audit/allList',
    method: 'get',
    params
  })
}

/**
 * 爬虫内容详情
 * @param params
 */
export function getCrawlerDetail(params) {
  return request({
    url: '/forum/oss/essay/crawler/detail',
    method: 'get',
    params
  })
}
/**
 * 口令解密
 * @param params
 */
export function decryptCommand(data) {
  return request({
    url: '/forum/oss/businessEssayController/decryptCommand',
    method: 'post',
    data
  })
}
/**
 * 爬虫内容详情
 * @param data
 */
export function updateOrderWeight(data) {
  return request({
    url: '/forum/oss/content/manager/updateTypeOrderWeight',
    method: 'post',
    data
  })
}

/* 内容宝审核-日志
 * @param params
 */
export function getQueryEassyOperateLog(params) {
  return request({
    url: '/forum/oss/businessEssayController/queryEassyOperateLog',
    method: 'get',
    params
  })
}

/* 内容宝审核-更新分配曝光
 * @param data
 */
export function updateEassyExposureTimes(data) {
  return request({
    url: '/forum/oss/essay/activity/updateEassyBaseExposureTimes',
    method: 'post',
    data
  })
}
/**
 * 获取文章列表
 * @param data
 */
export function getArticleList(params) {
  return request({
    url: `${LIVEURL}/forum/public/platform/essayController.do?action=30020`,
    method: 'post',
    params
  })
}
export function uploadImageByOther(
  data = {
    itemId: '', // 图片实体id
    imageUrl: '', // 原图片地址
    businessId: 2 // 业务：1-淘宝商城,2-行家平台
  }
) {
  return request({
    url: `${LIVEURL}/pirate/media/file/transferImage`,
    method: 'post',
    data
  })
}
export function getRickText(url) {
  return request({
    url,
    method: 'get'
  })
}

/**
 * 内容宝活动管理-状态变更
 * @param params
 */
export function postUpdateActDetail(data) {
  return request({
    url: '/forum/oss/essay/activity/updateActDetail',
    method: 'post',
    data
  })
}
/**
 * 更新结算系数
 * @param data
 */
export function postUpdateIncomeFactor(data) {
  return request({
    url: '/forum/oss/content/manager/updateTypeIncomeFactor',
    method: 'post',
    data
  })
}

/**
 * 批量文章标记异常
 * @param data
 */
export function postBatchUpdateActiveStatus(data) {
  return request({
    url: '/uic/oss/user/essay/income/batchUpdateActiveStatus',
    method: 'post',
    data
  })
}
/**
 * 查重监测列表
 * @param data
 */
export function getSimilarityList(params) {
  return request({
    url: '/forum/oss/businessEssayController/getSimilarityList',
    method: 'get',
    params
  })
}

/**
 * 处理查重
 * @param data
 */
export function postHandleSimilarity(data) {
  return request({
    url: '/forum/oss/businessEssayController/handleSimilarity',
    method: 'post',
    data
  })
}
/**
 * 文章关联车型信息
 * @param data
 */
export function getArticleCarInfo(params) {
  return request({
    url: '/forum/oss/bendEnterCarPhotoController/getCarInfo',
    method: 'get',
    params
  })
}

/**
 * 更新文章关联车型信息
 * @param data
 */
export function postUpdCarInfo(data) {
  return request({
    url: '/forum/oss/bendEnterCarPhotoController/editEnterCarPhoto',
    method: 'post',
    data
  })
}

/**
 * 修改等级
 * @param data
 */
export function updateChangeLevel(data) {
  return request({
    url: '/forum/oss/essay/activity/changeLevel',
    method: 'post',
    data
  })
}

/**
 * 不推荐列表
 * @param params
 */
export function getDataConfigList(params) {
  return request({
    url: '/forum/oss/remmdController/dataConfigList',
    method: 'get',
    params
  })
}
/**
 * 新增不推荐列表
 * @param data
 */
export function postAddDataConfig(data) {
  return request({
    url: '/forum/oss/remmdController/addDataConfig',
    method: 'post',
    data
  })
}
/**
 * 删除不推荐列表
 * @param data
 */
export function postDelDataConfig(data) {
  return request({
    url: '/forum/oss/remmdController/delDataConfig',
    method: 'post',
    data
  })
}

/**
 * 车主实拍列表
 * @param params
 */
export function getEnterCarPhotoList(params) {
  return request({
    url: '/forum/oss/bendEnterCarPhotoController/enterCarPhotoList',
    method: 'get',
    params
  })
}
/**
 * 删除实拍
 * @param params
 */
export function getDeleteEnterCarPhoto(data) {
  return request({
    url: '/forum/oss/bendEnterCarPhotoController/deleteEnterCarPhoto',
    method: 'post',
    data
  })
}

/**
 * 更新关联圈子
 * @param params
 */
export function updateEssayHoop(data) {
  return request({
    url: '/forum/oss/businessEssayController/updateEssayHoop',
    method: 'post',
    data
  })
}
/**
 * 申诉列表
 * @param params
 */
export function getAppealList(params) {
  return request({
    url: '/audit/oss/appeal/manage/list',
    method: 'get',
    params
  })
}

/**
 * 收益报表
 * @param params
 */
export function getUserIncomePointList(params) {
  return request({
    url: '/uic/oss/user/essay/income/getUserIncomePointList',
    method: 'get',
    params
  })
}

/**
 * 价格列表
 * @param params
 */
export function licensePriceList(params) {
  return request({
    url: '/carport/oss/licensePrice/list',
    method: 'get',
    params
  })
}

/**
 * 更新车牌价
 * @param data
 */
export function licensePriceUpdatePrice(data) {
  return request({
    url: '/carport/oss/licensePrice/updatePrice',
    method: 'post',
    data
  })
}

/**
 * 牌照来源列表
 * @param params
 */
export function licensePriceListTab(params) {
  return request({
    url: '/carport/oss/licensePrice/listTab',
    method: 'get',
    params
  })
}

/**
 * 更新牌照来源状态
 * @param data
 */
export function updateSourceStatus(data) {
  return request({
    url: '/carport/oss/licensePrice/updateSourceStatus',
    method: 'post',
    data
  })
}

/**
 * 保存/编辑车牌来源
 * @param data
 */
export function saveOrUpdateSource(data) {
  return request({
    url: '/carport/oss/licensePrice/saveOrUpdateSource',
    method: 'post',
    data
  })
}

/**
 * 骑行活动列表
 * @param params
 */
export function getRidingActivityList(params) {
  return request({
    url: '/activity/oss/riding/activity/activityOssList',
    method: 'get',
    params
  })
}
/**
 * 车牌交易列表
 * @param data
 */
export function getLicenseListTrade(params) {
  return request({
    url: '/carport/oss/licensePlateTrade/list',
    method: 'get',
    params
  })
}

/**
 * 车牌交易详情
 * @param data
 */
export function getLicenseTradeDetail(params) {
  return request({
    url: '/carport/oss/licensePlateTrade/detail',
    method: 'get',
    params
  })
}
/**
 * 无效骑行
 * @param params
 */
export function postRidingUpdateStatus(data) {
  return request({
    url: '/activity/oss/riding/activity/delActivityInfo',
    method: 'post',
    data
  })
}
/**
 * 下架
 */
export function postDownShelves(data) {
  return request({
    url: '/carport/oss/licensePlateTrade/offShelf',
    method: 'post',
    data
  })
}
/**
 * 日志
 */
export function getLicenseTradeLog(params) {
  return request({
    url: '/carport/oss/licensePlateTrade/log',
    method: 'get',
    params
  })
}

/**
 * 补签卡列表
 */
export function getSignCardList(params) {
  return request({
    url: '/coins/oss/signCard/list',
    method: 'get',
    params
  })
}

/**
 * 补签卡
 */
export function postSignCardSend(data) {
  return request({
    url: '/coins/oss/signCard/send',
    method: 'post',
    data
  })
}

/**
 * 内容宝内容审核不通过
 */
export function postUnPass(data) {
  return request({
    url: '/forum/oss/essay/activity/unPass',
    method: 'post',
    data
  })
}

/**
 * 获取大图
 */
export function getMediaOriginal(params) {
  return request({
    url: `${APIURL}pirate/media/aliyun/original/list`,
    method: 'get',
    params
  })
}

/**
 * 内容宝合作项目列表
 */
export function treasureProjectPage(params) {
  return request({
    url: '/factory/oss/factory/order/treasureProjectPage',
    method: 'get',
    params
  })
}

/**
 * oss骑行路线列表
 */
export function getCyclingRouteList(params) {
  return request({
    url: `/carport/oss/cycling/route/list`,
    method: 'get',
    params
  })
}

/**
 * 创作活动列表(用于内容宝合作项目列表)
 */
export function actSimpleList(params) {
  return request({
    url: '/forum/oss/essay/activity/actSimpleList',
    method: 'get',
    params
  })
}

/**
 *  日志
 * @param data
 */
export function getRouteLog(params) {
  return request({
    url: `/carport/oss/cycling/route/log`,
    method: 'get',
    params
  })
}

/**
 *  打卡点列表
 * @param data
 */
export function getClockPointList(params) {
  return request({
    url: `/forum/oss/punchPointController/list`,
    method: 'get',
    params
  })
}

/**
 *  提交打卡点
 * @param data
 */
export function updatePointStatus(data) {
  return request({
    url: `/forum/oss/punchPointController/audit`,
    method: 'post',
    data
  })
}
/**
 * 停止征集
 */
export function stopSoliciting(data) {
  return request({
    url: '/forum/oss/essay/activity/stopSoliciting',
    method: 'post',
    data
  })
}

/**
 * oss骑行路线列表更新优质
 */
export function postUpdatePrime(data) {
  return request({
    url: `/carport/oss/cycling/route/updatePrime`,
    method: 'post',
    data
  })
}

/**
 * 创作活动文章 是否进入评奖池
 */
export function updateAwardPool(data) {
  return request({
    url: `/forum/oss/essay/activity/updateAwardPool`,
    method: 'post',
    data
  })
}

/**
 *  获取创作活动下各奖项文章数量
 * @param data
 */
export function bonusLevelEssayCnt(params) {
  return request({
    url: `/forum/oss/essay/activity/bonusLevelEssayCnt`,
    method: 'get',
    params
  })
}

/**
 * 批量确认奖金
 */
export function batchConfirmBonus(data) {
  return request({
    url: `/forum/oss/essay/activity/batchConfirmBonus`,
    method: 'post',
    data
  })
}
