import request from '@/utils/request'

/**
 * 商城背景设置查看
 * @param query
 */
export function getBgConfig(query) {
  return request({
    url: `/mall/oss/backGroundController/getConfig`,
    method: 'get',
    params: query,
  })
}

/**
 * 背景编辑
 * @param data
 */
export function updateBg(data) {
  return request({
    url: `/mall/oss/backGroundController/updateConfig`,
    method: 'post',
    data,
  })
}
/**
 * 重置背景
 * @param data
 */
export function resetBg(data) {
  return request({
    url: `/mall/oss/backGroundController/deleteBackConfig`,
    method: 'post',
    data,
  })
}
