// 会员接口
import http from '@/utils/request'

class MemberLevel {
  // 付费会员记录
  addAgentRelation(shopId, memberType) {
    return http.get('shop/oss/member/level/purchaseList', {
      params: {
        shopId,
        memberType,
        page: 1,
        limit: 1000,
      },
    })
  }

  // 经销商管理----会员试用配置开启
  OpenTrialShopMember(shopId, daysNumber) {
    return http.post('/shop/oss/member/level/openTrial', {
      shopId,
      daysNumber,
    })
  }

  // 经销商管理----会员等级列表接口
  GetShopMemberLevelList(shopId, memberType) {
    return http.get('/shop/oss/member/level/list', {
      params: {
        shopId,
        memberType,
      },
    })
  }

  // 经销商管理----二手车会员等级列表接口
  GetSecondhandMemberLevelList() {
    return http.get('/shop/oss/member/level/secondhand/list')
  }

  // 二手车会员信息
  getSecondhandMemberInfo() {
    return http.get('/shop/oss/member/level/secondhand/member/info')
  }

  // 经销商管理----付费会员配置---生成付款码
  MemberLevelGenOrderQrCode(postData) {
    return http.post('/shop/oss/member/level/genOrderQrCode', postData)
  }

  // 经销商管理----付费会员配置---生成二维码线索包订单
  createCluePacketOrderByCode(data) {
    return http.post('/shop/oss/clue/order/createCluePacketOrderByCode', data)
  }

  // 经销商管理----付费会员配置---生成二维码租车订单
  createCreateRentalClueOrderByCode(data) {
    return http.post('/shop/oss/clue/order/createRentalClueOrderByCode', data)
  }

  // 经销商管理----付费会员配置---生成二手会员付款码
  memberLevelGenSecondHandQrCode(postData) {
    return http.post(
      '/shop/oss/member/level/secondhand/genOrderQrCode',
      postData
    )
  }

  // OSS广告订单-生成收钱吧二维码
  orderGenOrderQrCode(postData) {
    return http.post('/shop/oss/advert/order/genOrderQrCode', postData)
  }
  // 升级付费记录
  Calculate(shopId, orderNumbers, feeType, memberType) {
    return http.post('/shop/oss/member/level/upgrade/calculate', {
      shopId,
      orderNumbers: orderNumbers.join() || '',
      feeType,
      memberType,
      page: 1,
      limit: 1000,
    })
  }

  // 确认升级
  ConfirmUpgrade(data) {
    return http({
      url: '/shop/oss/member/level/order/upgrade',
      method: 'post',
      data,
    })
  }
  // 二手车会员-预计生效时间
  getMembertime(data) {
    return http({
      url: '/shop/oss/member/level/secondhand/time',
      method: 'get',
      params: data,
    })
  }
  // 生成升级订单二维码
  genUpgradeOrderQrCode(data) {
    console.log('data', data)
    return http({
      url: '/shop/oss/member/level/genUpgradeOrderQrCode',
      method: 'post',
      data,
    })
  }
}

export default new MemberLevel()
