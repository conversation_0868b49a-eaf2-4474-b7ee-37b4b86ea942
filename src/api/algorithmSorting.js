import { APIURL, MANAGERURL } from '@/utils/configData/config'
import request from '@/utils/request'

/**
 * 广场热门动态
 */
export function getHoopSquareHot(query) {
  return request({
    url: '/hoop/oss/hoop/square/hot',
    method: 'get',
    params: query,
  })
}

/**
 * 摩友圈热门动态
 */
export function getHoopSquareHotContent(query) {
  return request({
    url: '/hoop/oss/hoop/hoop/hotContent',
    method: 'get',
    params: query,
  })
}

/**
 * 租车合规算法得分
 */
export function getAlgorithmList(query) {
  return request({
    url: '/rental/oss/admin/car/algorithm/list',
    method: 'get',
    params: query,
  })
}

/**
 * 经销商-经销商列表
 */
export function getAlgorithmShopList(query) {
  return request({
    url: '/report/algorithm/shop/list',
    method: 'get',
    params: query,
  })
}

/**
 * 经销商-询价经销商
 * 
https://manager.corp.mddmoto.com/report/algorithm/shop/query?cityName=苏州市&brandId=18
 */
export function getAlgorithmShopQuery(query) {
  return request({
    url: '/report/algorithm/shop/query',
    method: 'get',
    params: query,
  })
}

/**
 * 排行榜-车型榜
 *
 */
export function getAlgorithmGoodHotList(query) {
  return request({
    url: '/carport/oss/rank/goods/hot/list',
    method: 'get',
    params: query,
  })
}

/**
 * 排行榜-品牌榜
 * 
https://manager.corp.mddmoto.com/report/algorithm/shop/query?cityName=苏州市&brandId=18
 */
export function getAlgorithmBrandHotList(query) {
  return request({
    url: '/carport/oss/rank/brand/hot/list',
    method: 'get',
    params: query,
  })
}

/**
 * 排行榜-商家榜
 * 
https://manager.corp.mddmoto.com/report/algorithm/shop/query?cityName=苏州市&brandId=18
 */
export function getAlgorithmRankList(query) {
  return request({
    url: '/shop/oss/rank/getRankList',
    method: 'get',
    params: query,
  })
}

/**
 * 商城推荐- 用户个性化特征
 * 
https://manager.corp.mddmoto.com/report/algorithm/mall/personal/purchase?uid=1101285
 */
export function getPersonalPurchaseList(query) {
  return request({
    url: '/report/algorithm/mall/personal/purchase',
    method: 'get',
    params: query,
  })
}

/**
 * 商城推荐- 热门
 * 
https://manager.corp.mddmoto.com/report/algorithm/mall/personal/purchase?uid=1101285
 */
export function getMallHost(query) {
  return request({
    url: '/report/algorithm/mall/hot30',
    method: 'get',
    params: query,
  })
}

/**
 * 驾校商家列表
 * 
https://manager.corp.mddmoto.com/carport/oss/shop/driving/app/school/list
 */
export function getSchoolList(query) {
  return request({
    url: '/shop/oss/shop/driving/app/school/list',
    method: 'get',
    params: query,
  })
}

/**
 * 二手摩托首页--算法推荐及排序展示
 * 
https://manager.corp.mddmoto.com/carport/oss/shop/driving/app/school/list
 */
export function getUsedCarControllerList(query) {
  return request({
    url: '/transaction/adminMotorUsedCarController/orderby/list',
    method: 'get',
    params: query,
  })
}

/**
 * 二手摩托首页--算法推荐及排序展示
 *
 *  https://manager.corp.mddmoto.com/report/algorithm/car?area=上海市
 */
export function getAlgorithmCar(query) {
  return request({
    url: '/report/algorithm/car',
    method: 'get',
    params: query,
  })
}

/**
 * 首页信息流-用户个性化特征
 *
 *  https://manager.corp.mddmoto.com/report/algorithm/feed?deviceId=0000106ecd876c0edc67a71780fbd670
 */
export function getAlgorithmFeed(query) {
  return request({
    url: '/report/algorithm/feed',
    method: 'get',
    params: query,
  })
}

/**
 * 选车-用户个性化特征
 *
 *  https://manager.corp.mddmoto.com/report/algorithm/device/preference?deviceId=370E959D-9F43-4544-B9AC-92AF93737DF2
 */
export function getAlgorithmDevicePreference(query) {
  return request({
    url: '/report/algorithm/device/preference',
    method: 'get',
    params: query,
  })
}
