// 审核相关接口

import request from '@/utils/request'

/**
 * 实名制审核拉取数据接口
 * @param data
 */
export function certApplyPullData(data) {
  return request({
    url: '/uic/oss/certApply/pull/data',
    method: 'post',
    data,
  })
}

/**
 * 实名制审核统计接口
 * @param data
 */
export function certApplyStatisticsInfo(query) {
  return request({
    url: '/uic/oss/certApply/statistics/info',
    method: 'get',
    params: query,
  })
}

/**
 * 实名制审核待审核列表
 * @param data
 */
export function certApplyWaitAuditList(query) {
  return request({
    url: '/uic/oss/certApply/wait/audit/list',
    method: 'get',
    params: query,
  })
}

/**
 * 探店信息列表
 * @param data
 */
export function adminCityOfficerListExplore(query) {
  return request({
    url: '/carport/oss/adminCityOfficerController/listExplore',
    method: 'get',
    params: query,
  })
}

/**
 * 探店信息详情
 * @param data
 */
export function adminCityOfficerExploreDetail(taskId) {
  return request({
    url: '/carport/oss/adminCityOfficerController/exploreDetail',
    method: 'get',
    params: {
      taskId,
    },
  })
}

/**
 * 审核探店信息
 * @param data
 */
export function adminCityOfficerAuditExploreInfo(data) {
  return request({
    url: '/carport/oss/adminCityOfficerController/auditExploreInfo',
    method: 'post',
    data,
  })
}

/**
 * 城市官任务列表
 * @param data
 */
export function officerTaskList(query) {
  return request({
    url: '/crm/officer/task/list',
    method: 'get',
    params: query,
  })
}

/**
 * 城市官任务审批
 * @param data
 */
export function officerTaskHandle(data) {
  return request({
    url: '/crm/officer/task/handle',
    method: 'post',
    data,
  })
}

/**
 * 城市官任务审批, 回填店铺id
 * @param data
 */
export function saveNewShopId(data) {
  return request({
    url: '/crm/officer/task/fill/shopId',
    method: 'post',
    data,
  })
}

/**
 * 提车价审核统计
 * @param data
 */
export function getRaisedStatistics(query) {
  return request({
    url: '/carport/oss/raised/car/price/statistics',
    method: 'get',
    params: query,
  })
}

/**
 * 提车价审核统计
 * @param data
 */
export function getRaisedPriceList(query) {
  return request({
    url: '/carport/oss/raised/car/price/list',
    method: 'get',
    params: query,
  })
}

/**
 * 拉取待审核数据（提车价）
 * @param data
 */
export function postRaisedPull(data) {
  return request({
    url: '/carport/oss/raised/car/price/pull',
    method: 'post',
    data,
  })
}

/**
 * 提车价审核
 * @param data
 */
export function postRaisedAudit(data) {
  return request({
    url: '/carport/oss/raised/car/price/audit',
    method: 'post',
    data,
  })
}

/**
 * 拉取违规图片记录接口
 * @param data
 */
export function postPullAuthList(data) {
  return request({
    url: '/uic/oss/pic/pullAuthList',
    method: 'post',
    data,
  })
}

/**
 * 我的审核列表
 * @param data
 */
export function getMyAuditList(query) {
  return request({
    url: '/uic/oss/pic/myAuditList',
    method: 'get',
    params: query,
  })
}

/**
 * 私信上下文
 * @param data
 */
export function getPmsContext(query) {
  return request({
    url: '/uic/oss/pic/pmsContext',
    method: 'get',
    params: query,
  })
}

/**
 * 违规图片审核接口
 * @param data
 */
export function postPicAudit(data) {
  return request({
    url: '/uic/oss/pic/audit',
    method: 'post',
    data,
  })
}

/**
 * 私信审核数据统计
 * @param data
 */
export function getPicAuthProgress(query) {
  return request({
    url: '/uic/oss/pic/authProgress',
    method: 'get',
    params: query,
  })
}

/**
 * Oss用户名审核列表
 * @param data
 */
export function checkUserNameList(query) {
  return request({
    url: '/forum/oss/user/info/check/userName/list',
    method: 'get',
    params: query,
  })
}

/**
 * 用户名批量审核
 * @param data
 */
export function userNameBatchCheck(data) {
  return request({
    url: '/forum/oss/user/info/check/userName/batchCheck',
    method: 'post',
    data,
    params: {
      hideErrorMsg: true,
    },
  })
}
