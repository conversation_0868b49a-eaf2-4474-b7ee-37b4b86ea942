import request from '@/utils/request'

/**
 * 摩托学院项目列表
 * @param query
 */
export function GetProjectList(query) {
  return request({
    url: '/forum/oss/academyProjectController/list',
    method: 'get',
    params: query,
  })
}

/**
 * 摩托学院项目新建
 * @param data
 */
export function AddProject(data) {
  return request({
    url: '/forum/oss/academyProjectController/add',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院项目删除
 * @param data
 */
export function DeleteProject(data) {
  return request({
    url: '/forum/oss/academyProjectController/delete',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院项目更新
 * @param data
 */
export function UpdateProject(data) {
  return request({
    url: '/forum/oss/academyProjectController/update',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院项目排序
 * @param data
 */
export function SortProject(data) {
  return request({
    url: '/forum/oss/academyProjectController/sort',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院模块列表
 * @param query
 */
export function GetListAcademyModule(query) {
  return request({
    url: '/forum/oss/academyModuleController/listAcademyModule',
    method: 'get',
    params: query,
  })
}

/**
 * 摩托学院新增模块
 * @param data
 */
export function SaveAcademyModule(data) {
  return request({
    url: '/forum/oss/academyModuleController/saveAcademyModule',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院更新模块
 * @param data
 */
export function UpdateAcademyModule(data) {
  return request({
    url: '/forum/oss/academyModuleController/updateAcademyModule',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院删除模块
 * @param data
 */
export function DeleteAcademyModule(data) {
  return request({
    url: '/forum/oss/academyModuleController/deleteAcademyModule',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院模块排序
 * @param data
 */
export function OrderAcademyModule(data) {
  return request({
    url: '/forum/oss/academyModuleController/orderAcademyModule',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院文章列表
 * @param query
 */
export function GetAcademyEssayList(query) {
  return request({
    url: '/forum/oss/academyEssayRelController/listAcademyEssay',
    method: 'get',
    params: query,
  })
}

/**
 * 摩托学院子模块列表
 * @param query
 */
export function getSubmoduleList(query) {
  return request({
    url: '/forum/oss/academyModuleController/listSubModule',
    method: 'get',
    params: query,
  })
}

/**
 * 更新子模块
 * @param query
 */
export function UpdateSubmodule(data) {
  return request({
    url: '/forum/oss/academyModuleController/updateSubModule',
    method: 'post',
    data,
  })
}

/**
 * 更新子模块状态
 * @param query
 */
export function UpdateSubmoduleStatus(data) {
  return request({
    url: '/forum/oss/academyModuleController/updateSubModuleStatus',
    method: 'post',
    data,
  })
}
/**
 * 删除子模块
 * @param query
 */
export function deleteSubmodule(data) {
  return request({
    url: '/forum/oss/academyModuleController/deleteSubModule',
    method: 'POST',
    params: data,
  })
}
/**
 * 新建子模块
 * @param query
 */
export function newSubmodule(data) {
  return request({
    url: '/forum/oss/academyModuleController/saveSubModule',
    method: 'post',
    data,
  })
}

/**
 * 子模块排序
 * @param query
 */
export function submoduleSorting(data) {
  return request({
    url: '/forum/oss/academyModuleController/orderSubModule',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院新建文章
 * @param data
 */
export function SaveAcademyEssay(data) {
  return request({
    url: '/forum/oss/academyEssayRelController/saveAcademyEssay',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院删除文章
 * @param data
 */
export function DeleteAcademyEssay(data) {
  return request({
    url: '/forum/oss/academyEssayRelController/deleteAcademyEssay',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院文章排序
 * @param data
 */
export function OrderAcademyEssay(data) {
  return request({
    url: '/forum/oss/academyEssayRelController/orderAcademyEssay',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院教官列表
 * @param query
 */
export function GetInstructorList(query) {
  return request({
    url: '/forum/oss/academyInstructorController/list',
    method: 'get',
    params: query,
  })
}

/**
 * 摩托学院添加教官
 * @param data
 */
export function AddInstructor(data) {
  return request({
    url: '/forum/oss/academyInstructorController/add',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院删除教官
 * @param data
 */
export function DeleteInstructor(data) {
  return request({
    url: '/forum/oss/academyInstructorController/delete',
    method: 'post',
    data,
  })
}

/**
 * 摩托学院教官排序
 * @param data
 */
export function SortInstructor(data) {
  return request({
    url: '/forum/oss/academyInstructorController/sort',
    method: 'post',
    data,
  })
}

/**
 * 创作学院 模块列表
 * @param query
 */
export function GetModuleList(query) {
  return request({
    url: '/forum/oss/academyModuleController/pc/creation/modules',
    method: 'get',
    params: query,
  })
}

/**
 * 创作学院 文章列表
 * @param query
 */
export function GetEssayList(query) {
  return request({
    url: '/forum/oss/academyEssayRelController/listAcademyEssay',
    method: 'get',
    params: query,
  })
}

/**
 * 创作学院 开启/关闭模块
 * @param data
 */
export function SwitchModule(data) {
  return request({
    url: '/forum/oss/academyModuleController/updateSubModuleStatus',
    method: 'post',
    data,
  })
}

/**
 * 创作学院 oss新增关联文章
 * @param data
 */
export function AddEassay(data) {
  return request({
    url: '/forum/oss/academyEssayRelController/saveAcademyEssay',
    method: 'post',
    data,
  })
}

/**
 * 创作学院 oss移除关联文章
 * @param data
 */
export function RemoveEassay(data) {
  return request({
    url: '/forum/oss/academyEssayRelController/deleteAcademyEssay',
    method: 'post',
    data,
  })
}
