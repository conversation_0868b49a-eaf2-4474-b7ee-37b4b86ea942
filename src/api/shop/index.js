import request from '@/utils/request'

/**
 * 淘宝商品列表
 * @param query
 */
export function GetListGoods(query) {
  return request({
    url: `/mall/oss/adminMallGoodsController/listGoods`,
    method: 'get',
    params: query
  })
}

/**
 * 商品详情
 * @param query
 */
export function GetGoodsDetail(query) {
  return request({
    url: `/mall/oss/adminMallGoodsController/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 添加或编辑商品
 * @param data
 */
export function SaveOrUpdateGoods(data) {
  return request({
    url: `/mall/oss/adminMallGoodsController/saveOrUpdateGoods`,
    method: 'post',
    data
  })
}

/**
 * 商品上下架
 * @param data
 */
export function UpdateStatusGoods(data) {
  return request({
    url: `/mall/oss/adminMallGoodsController/updateStatus`,
    method: 'post',
    data
  })
}

/**
 * 一级类目列表(有二级目录)
 * @param query
 */
export function GetListFirstCategory(query) {
  return request({
    url: `/mall/oss/adminMallCategoryController/listFirstCategory`,
    method: 'get',
    params: query
  })
}

/**
 * 一级类目列表
 * @param query
 */
export function GetListAllFirstCategory(query) {
  return request({
    url: `/mall/oss/adminMallCategoryController/listAllFirstCategory`,
    method: 'get',
    params: query
  })
}

/**
 * 删除商品
 * @param data
 */
export function DeleteGoods(data) {
  return request({
    url: `/mall/oss/adminMallGoodsController/deleteGoods`,
    method: 'post',
    data
  })
}

/**
 * 类目分页列表
 * @param query
 */
export function GetListCategory(query) {
  return request({
    url: `/mall/oss/adminMallCategoryController/list`,
    method: 'get',
    params: query
  })
}

/**
 * 添加或编辑一级类目
 * @param data
 */
export function SaveOrUpdateCategory(data) {
  return request({
    url: `/mall/oss/adminMallCategoryController/saveOrUpdateCategory`,
    method: 'post',
    data
  })
}

/**
 * 添加或编辑二级类目
 * @param data
 */
export function SaveOrUpdateSecondCategoryCategory(data) {
  return request({
    url: `/mall/oss/adminMallCategoryController/saveOrUpdateSecondCategoryCategory`,
    method: 'post',
    data
  })
}

/**
 * 删除类目
 * @param data
 */
export function DeleteCategory(data) {
  return request({
    url: `/mall/oss/adminMallCategoryController/deleteCategory`,
    method: 'post',
    data
  })
}

/**
 * 导航添加接口
 * @param data
 */
export function AddNavigationController(data) {
  return request({
    url: `/mall/oss/adminMallNavigationController/add`,
    method: 'post',
    data
  })
}

/**
 * 导航编辑接口
 * @param data
 */
export function UpdateNavigationController(data) {
  return request({
    url: `/mall/oss/adminMallNavigationController/update`,
    method: 'post',
    data
  })
}

/**
 * 导航详情接口
 * @param data
 */
export function GetNavigationController(query) {
  return request({
    url: `/mall/oss/adminMallNavigationController/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 导航详情删除
 * @param data
 */
export function DeleteNavigationController(data) {
  return request({
    url: `/mall/oss/adminMallNavigationController/delete`,
    method: 'post',
    params: data
  })
}
/**
 * 导航列表
 * @param data
 */
export function GetNavigationControllerList(query) {
  return request({
    url: `/mall/oss/adminMallNavigationController/list`,
    method: 'get',
    params: query
  })
}

/**
 * 精选淘宝商品列表接口
 * @param data
 */
export function GetMotorChoiceControllerList(query) {
  return request({
    url: `/mall/oss/adminMotorChoiceController/list`,
    method: 'get',
    params: query
  })
}

/**
 * 精选商品添加接口
 * @param data
 */
export function AddAdminMotorChoiceController(data) {
  return request({
    url: `/mall/oss/adminMotorChoiceController/add`,
    method: 'post',
    data
  })
}

/**
 * 爬虫添加接口
 * @param data
 */
export function Reptile(data) {
  return request({
    url: `/mall/oss/adminMallGoodsController/syncOrientData`,
    method: 'post',
    data
  })
}

/**
 * 精选商品修改接口
 * @param data
 */
export function UpdateAdminMotorChoiceController(data) {
  return request({
    url: `/mall/oss/adminMotorChoiceController/update`,
    method: 'post',
    data
  })
}

/**
 * 精选商品删除接口
 * @param data
 */
export function RemoveAdminMotorChoiceController(data) {
  return request({
    url: `/mall/oss/adminMotorChoiceController/delete`,
    method: 'post',
    data
  })
}

/**
 * 淘宝客商品推广池列表
 * @param data
 */
export function GetTaobaoList(query) {
  return request({
    url: `/mall/oss/goods/taobao/list`,
    method: 'get',
    params: query
  })
}

/**
 * 淘宝客商品推广池同步接口
 * @param data
 */
export function SyncTaobaoGoods(data) {
  return request({
    url: `/mall/oss/goods/taobao/sysn`,
    method: 'post',
    data
  })
}

/**
 * 淘宝客商品推广池删除接口
 * @param data
 */
export function DeleteTaobaoGoods(data) {
  return request({
    url: `/mall/oss/goods/taobao/delete`,
    method: 'post',
    data
  })
}

/**
 * 淘宝订单列表接口
 * @param data
 */
export function GetTaobaoOrderList(query) {
  return request({
    url: `/mall/oss/taobao/order/list`,
    method: 'get',
    params: query
  })
}

/**
 * 淘宝订单导入接口
 * @param data
 */
export function ImportTaobaoOrder(data) {
  return request({
    url: `/mall/oss/taobao/order/import`,
    method: 'post',
    data
  })
}

/**
 * 选品库列表接口
 * @param data
 */
export function GetChooseProductDepotList(query) {
  return request({
    url: `/mall/oss/chooseProductDepot/list`,
    method: 'get',
    params: query
  })
}

/**
 * 新增选品库
 * @param data
 */
export function InsertChooseProductDepot(data) {
  return request({
    url: `/mall/oss/chooseProductDepot/insert`,
    method: 'post',
    data
  })
}

/**
 * 选品库修改接口
 * @param data
 */
export function UpdateChooseProductDepot(data) {
  return request({
    url: `/mall/oss/chooseProductDepot/update`,
    method: 'post',
    data
  })
}

/**
 * 选品库删除接口
 * @param data
 */
export function DeleteChooseProductDepot(data) {
  return request({
    url: `/mall/oss/chooseProductDepot/delete`,
    method: 'post',
    data
  })
}

/**
 * 新增自营选品库
 * @param data
 */
export function InsertZyProductDepot(data) {
  return request({
    url: `/mall/oss/chooseProductDepot/insertZyGoods`,
    method: 'post',
    data
  })
}
/**
 * 修改自营选品库
 * @param data
 */
export function UpdateZyProductDepot(data) {
  return request({
    url: `/mall/oss/chooseProductDepot/updateZyGoods`,
    method: 'post',
    data
  })
}

/**
 * 选品库淘宝商品列表接口
 * @param data
 */
export function GetChooseProductDepotGoodsList(query) {
  return request({
    url: `/mall/oss/chooseProductDepot/goods/list`,
    method: 'get',
    params: query
  })
}

/**
 * 选品库商品列表接口（京东淘宝混排）
 * @param data
 */
export function GetChooseProductDepotMixGoodsList(query) {
  return request({
    url: `/mall/oss/chooseProductDepot/mixGoods/list`,
    method: 'get',
    params: query
  })
}

/**
 * 金刚位列表接口
 * @param data
 */
export function GetSpreadModuleList(query) {
  return request({
    url: `/mall/oss/spreadModule/list`,
    method: 'get',
    params: query
  })
}

/**
 * 金刚位发布应用接口
 * @param data
 */
export function PostPublishApply(data) {
  return request({
    url: `/mall/oss/spreadModule/publishApply`,
    method: 'post',
    data
  })
}

/**
 * 金刚位新增接口
 * @param data
 */
export function InsertSpreadModule(data) {
  return request({
    url: `/mall/oss/spreadModule/insert`,
    method: 'post',
    data
  })
}

/**
 * 金刚位编辑接口
 * @param data
 */
export function UpdateSpreadModule(data) {
  return request({
    url: `/mall/oss/spreadModule/update`,
    method: 'post',
    data
  })
}

/**
 * 金刚位删除接口
 * @param data
 */
export function DeleteSpreadModule(data) {
  return request({
    url: `/mall/oss/spreadModule/delete`,
    method: 'post',
    data
  })
}

/**
 * 商品类目排序接口
 * @param data
 */
export function SortAdminMallCategoryController(data) {
  return request({
    url: `/mall/oss/adminMallCategoryController/sort`,
    method: 'post',
    data
  })
}

/**
 * 金刚位列表接口
 * @param data
 */
export function GetAccountDetailList(query) {
  return request({
    url: `/uic/oss/payment/account/detail/list`,
    method: 'get',
    params: query
  })
}

/**
 * 淘宝订单手动结算接口
 * @param data
 */
export function postManualSettlement(data) {
  return request({
    url: `/mall/oss/taobao/order/manualSettlement`,
    method: 'post',
    data
  })
}

/**
 * 品牌列表接口
 * @param data
 */
export function GetBrandList(query) {
  return request({
    url: `/mall/oss/brand/list`,
    method: 'get',
    params: query
  })
}

/**
 * 品牌新增接口
 * @param data
 */
export function AddBrand(data) {
  return request({
    url: `/mall/oss/brand/add`,
    method: 'post',
    data
  })
}

/**
 * 品牌修改接口
 * @param data
 */
export function UpdateBrand(data) {
  return request({
    url: `/mall/oss/brand/update`,
    method: 'post',
    data
  })
}

/**
 * 品牌删除接口
 * @param data
 */
export function DeleteBrand(data) {
  return request({
    url: `/mall/oss/brand/delete`,
    method: 'post',
    data
  })
}

/**
 * 淘宝商家列表
 * @param data
 */
export function GetShopList(query) {
  return request({
    url: `/mall/oss/adminMallGoodsController/shopList`,
    method: 'get',
    params: query
  })
}

/**
 * 精选关联关系详情接口
 * @param data
 */
export function GetRelationDetail(query) {
  return request({
    url: `/mall/oss/adminMotorChoiceController/relationDetail`,
    method: 'get',
    params: query
  })
}

/**
 * 精选关联关系修改接口
 * @param data
 */
export function UpdateRelation(data) {
  return request({
    url: `/mall/oss/adminMotorChoiceController/updateRelation`,
    method: 'post',
    data
  })
}

/**
 * 商品sku列表表头接口
 * @param data
 */
export function GetSkuTableHeaderList(query) {
  return request({
    url: `/mall/oss/adminMallGoodsController/sku/table/header/list`,
    method: 'get',
    params: query
  })
}

/**
 * 商品sku列表
 * @param data
 */
export function GetSkuList(query) {
  return request({
    url: `/mall/oss/adminMallGoodsController/sku/list`,
    method: 'get',
    params: query
  })
}

/**
 * 商品sku同步接口
 * @param data
 */
export function GetSyncSkuInfo(data) {
  return request({
    url: `/mall/oss/adminMallGoodsController/syncSkuInfo`,
    method: 'post',
    data
  })
}

/**
 * 清除商品sku预警接口
 * @param data
 */
export function ClearSkuEarlyWarning(data) {
  return request({
    url: `/mall/oss/adminMallGoodsController/clearSkuEarlyWarning`,
    method: 'post',
    data
  })
}

/**
 * 店铺管理-获取店铺列表
 * @param data
 */
export function getListShop(query) {
  return request({
    url: `/mall/oss/adminMallShopController/listShop`,
    method: 'get',
    params: query
  })
}
/**
 * 店铺管理-添加或更新店铺
 * @param data
 */
export function saveOrUpdateShop(data) {
  return request({
    url: `/mall/oss/adminMallShopController/saveOrUpdateShop`,
    method: 'post',
    data
  })
}
/**
 * 店铺管理-删除店铺
 * @param data
 */
export function deleteShop(data) {
  return request({
    url: `/mall/oss/adminMallShopController/deleteShop`,
    method: 'post',
    data
  })
}
/**
 * 店铺管理-获取店铺优惠券列表
 * @param data
 */
export function getListShopCoupon(query) {
  return request({
    url: `/mall/oss/adminMallShopCouponController/listShopCoupon`,
    method: 'get',
    params: query
  })
}
/**
 * 店铺管理-添加店铺优惠券列表
 * @param data
 */
export function addShopCoupon(data) {
  return request({
    url: `/mall/oss/adminMallShopCouponController/saveShopCoupon`,
    method: 'post',
    data
  })
}
/**
 * 店铺管理-删除店铺优惠券列表
 * @param data
 */
export function deleteShopCoupon(data) {
  return request({
    url: `/mall/oss/adminMallShopCouponController/deleteShopCoupon`,
    method: 'post',
    data
  })
}

/**
 * 开通/关闭租车服务
 */
export function toogleRentalStatus(data) {
  return request({
    url: '/shop/oss/merchant/info/open/rental',
    method: 'post',
    data
  })
}
/**
 * 商品-活动列表
 * @param data
 */
export function getEventList(query) {
  return request({
    url: `/mall/oss/event/list`,
    method: 'get',
    params: query
  })
}
/**
 * 商品-活动详情
 * @param data
 */
export function getEventDetail(query) {
  return request({
    url: `/mall/oss/event/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 标品列表
 * @param data
 */
export function getListStandardGoods(query) {
  return request({
    url: `/mall/oss/standardGoods/list`,
    method: 'get',
    params: query
  })
}
/**
 * 商品-活动新增
 * @param data
 */
export function postEventAdd(data) {
  return request({
    url: `/mall/oss/event/add`,
    method: 'post',
    data
  })
}

/**
 * 添加标品
 */
export function addStandardGoods(data) {
  return request({
    url: '/mall/oss/standardGoods/insert',
    method: 'post',
    data
  })
}

/**
 * 商品-活动编辑
 */
export function postEventUpdate(data) {
  return request({
    url: '/mall/oss/event/update',
    method: 'post',
    data
  })
}

/**
 * 修改标品
 */
export function updateStandardGoods(data) {
  return request({
    url: '/mall/oss/standardGoods/update',
    method: 'post',
    data
  })
}

/**
 * 店铺管理-社群客服列表
 * @param data
 */
export function getListServiceConfig(query) {
  return request({
    url: `/mall/oss/serviceConfig/list`,
    method: 'get',
    params: query
  })
}

/**
 * 商品-活动配置页-活动列表
 * @param data
 */
export function getActivityList(query) {
  return request({
    url: `/mall/oss/activity/list`,
    method: 'get',
    params: query
  })
}
/**
 * 标品详情
 * @param data
 */
export function getListStandardDetail(query) {
  return request({
    url: `/mall/oss/standardGoods/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 店铺管理-社群客服添加
 */
export function postServiceConfigAdd(data) {
  return request({
    url: '/mall/oss/serviceConfig/add',
    method: 'post',
    data
  })
}

/**
 * 商品-活动配置页-活动添加
 * @param data
 */
export function postActivityAdd(data) {
  return request({
    url: `/mall/oss/activity/add`,
    method: 'post',
    data
  })
}

/**
 * 删除标品
 */
export function deleteStandardGoods(data) {
  return request({
    url: '/mall/oss/standardGoods/delete',
    method: 'post',
    data
  })
}

/**
 * 店铺管理-社群客服更新
 */
export function postServiceConfigUpdate(data) {
  return request({
    url: '/mall/oss/serviceConfig/update',
    method: 'post',
    data
  })
}

/**
 * 关联商品
 */
export function addStandardGoodsRelation(data) {
  return request({
    url: '/mall/oss/standardGoodsSku/relation',
    method: 'post',
    data
  })
}

/**
 * 商品-活动配置页-活动修改
 * @param data
 */
export function postActivityUpdate(data) {
  return request({
    url: `/mall/oss/activity/update`,
    method: 'post',
    data
  })
}

/**
 * 店铺管理-社群客服删除
 */
export function deleteServiceConfig(data) {
  return request({
    url: '/mall/oss/serviceConfig/delete',
    method: 'post',
    data
  })
}

/**
 * 商品-活动配置页-活动删除
 * @param data
 */
export function postActivityDelete(data) {
  return request({
    url: `/mall/oss/activity/delete`,
    method: 'post',
    data
  })
}

/**
 * sku淘宝商品列表
 */
export function postStandardGoodsSkuList(data) {
  return request({
    url: '/mall/oss/standardGoodsSku/skuList',
    method: 'post',
    data
  })
}

/**
 * 商品属性列表
 * @param data
 */
export function getStandardGoodsSkuPropData(query) {
  return request({
    url: `/mall/oss/standardGoodsSku/propData`,
    method: 'get',
    params: query
  })
}

/**
 * 批量关联商品
 */
export function postBatchRelation(data) {
  return request({
    url: '/mall/oss/standardGoodsSku/batchRelation',
    method: 'post',
    data
  })
}

/**
 * 取消关联商品
 */
export function postUnRelation(data) {
  return request({
    url: '/mall/oss/standardGoodsSku/unRelation',
    method: 'post',
    data
  })
}

/**
 * 商品颜色属性值列表
 */
export function postPropertyInfo(query) {
  return request({
    url: '/mall/oss/adminMallGoodsController/propertyInfo',
    method: 'get',
    params: query
  })
}

/**
 * 商品详情-优惠券列表
 * @param data
 */
export function getSyncCoupon(query) {
  return request({
    url: `/mall/oss/goods/coupon/list`,
    method: 'get',
    params: query
  })
}

/**
 * 商品详情-同步商品优惠券
 */
export function syncCoupon(data) {
  return request({
    url: '/mall/oss/goods/coupon/syncCoupon',
    method: 'post',
    data
  })
}

/**
 * 同步商品视频信息
 */
export function syncGoodsVideoInfo(data) {
  return request({
    url: '/mall/oss/adminMallGoodsController/syncGoodsVideoInfo',
    method: 'post',
    data
  })
}

/**
 *  * 京东spuid列表
 * @param data
 */
export function getJdSpuidList(query) {
  return request({
    url: `/mall/oss/jd/goods/spuid/list`,
    method: 'get',
    params: query
  })
}

/**
 * 京东spuid详情
 * @param data
 */
export function getJdSpuidDetail(query) {
  return request({
    url: `/mall/oss/jd/goods/spuid/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 京东spuid上下架
 */
export function postJdSpuidShelf(data) {
  return request({
    url: '/mall/oss/jd/goods/spuid/shelf',
    method: 'post',
    data
  })
}

/**
 * 京东spuid删除
 */
export function postJdSpuidDelete(data) {
  return request({
    url: '/mall/oss/jd/goods/spuid/delete',
    method: 'post',
    data
  })
}

/**
 * 京东skuid列表
 * @param data
 */
export function getJdSkuidList(query) {
  return request({
    url: `/mall/oss/jd/goods/skuid/list`,
    method: 'get',
    params: query
  })
}

/**
 * 京东skuid详情
 * @param data
 */
export function getJdSkuidDetail(query) {
  return request({
    url: `/mall/oss/jd/goods/skuid/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 京东spuid更新
 * @param data
 */
export function updatJdSkuidDetail(data) {
  return request({
    url: `/mall/oss/jd/goods/spuid/update`,
    method: 'post',
    data
  })
}

/**
 * 同步京东商品
 * @param data
 */
export function syncMaterial(query) {
  return request({
    url: `/mall/oss/jd/syncMaterial`,
    method: 'get',
    params: query
  })
}

/**
 * 同步京东商品详情
 * @param data
 */
export function syncMaterialDesc(query) {
  return request({
    url: `/mall/oss/jd/syncMaterialDesc`,
    method: 'get',
    params: query
  })
}

/**
 * 京东订单列表
 * @param data
 */
export function getJdOrderList(query) {
  return request({
    url: `/mall/oss/jd/goods/order/list`,
    method: 'get',
    params: query
  })
}

/**
 * 京东订单人工结算
 * @param data
 */
export function updateManualSettlement(data) {
  return request({
    url: `/mall/oss/jd/order/manualSettlement`,
    method: 'post',
    data
  })
}
/**
 * 选品库商品列表
 * jd商品颜色属性值列表
 * @param data
 */
export function getJdPropertyInfo(query) {
  return request({
    url: `mall/oss/jd/propertyInfo`,
    method: 'get',
    params: query
  })
}
/**
 * 选品库商品列表
 * @param data
 */
export function getChooseProductListGoods(query) {
  return request({
    url: `/mall/oss/adminMallGoodsController/chooseProductListGoods`,
    method: 'get',
    params: query
  })
}
/**
 * 选品库自营商品列表
 * @param data
 */
export function getZyProductListGoods(query) {
  return request({
    url: `/mall/oss/adminMallGoodsController/chooseZyGoods`,
    method: 'get',
    params: query
  })
}

/**
 * 搜索品牌，已名称搜搜
 * @param data
 */
export function getSearchBrandName(query) {
  return request({
    url: `/mall/oss/brand/search`,
    method: 'get',
    params: query
  })
}

/**
 * 京东根据计佣金额计算结算金额
 * @param data
 */
export function getCalculateSettlementAmount(query) {
  return request({
    url: `/mall/oss/jd/order/calculateSettlementAmount`,
    method: 'get',
    params: query
  })
}
/*
 * 变更结算金额,淘宝
 * @param data
 */
export function getTaobaoCalculateSettlementAmount(query) {
  return request({
    url: `/mall/oss/taobao/order/calculateSettlementAmount`,
    method: 'get',
    params: query
  })
}
/**
 * 修改京东报警状态
 * @param data
 */
export function updateWarning(data) {
  return request({
    url: `/mall/oss/jd/goods/warning/close`,
    method: 'post',
    data
  })
}

/*
 * 获取京东淘宝操作日志
 * @param data
 */
export function getShopLogs(query) {
  return request({
    url: `/mall/oss/adminMallGoodsController/getGoodsLogInfo`,
    method: 'get',
    params: query
  })
}

/*
 * 商品列表 c端自营商品列表
 * @param data
 */
export function getSelfGoodsList(query) {
  return request({
    url: `/mall/oss/self/goods/list`,
    method: 'get',
    params: query
  })
}

/*
 * 查询代发商家信息列表
 * @param data
 */
export function getReplaceSendShopList(query) {
  return request({
    url: `/mall/oss/undertakes/list`,
    method: 'get',
    params: query
  })
}

/*
 * 查询代发商家信息详情
 * @param data
 */
export function getReplaceSendShopDetail(query) {
  return request({
    url: `/mall/oss/undertakes/detail`,
    method: 'get',
    params: query
  })
}

/*
 * 新增代发商家信息
 * @param data
 */
export function postAddReplaceSendShop(data) {
  return request({
    url: `/mall/oss/undertakes/add`,
    method: 'post',
    data
  })
}
/*
 * 更新代发商家信息
 * @param data
 */
export function postUpdateReplaceSendShop(data) {
  return request({
    url: `/mall/oss/undertakes/update`,
    method: 'post',
    data
  })
}
/*
 * 删除代发商家信息
 * @param data
 */
export function postDeleteReplaceSendShop(data) {
  return request({
    url: `/mall/oss/undertakes/delete`,
    method: 'post',
    data
  })
}

/*
 * c端 商品同步
 * @param data
 */
export function getErpSyncGoods(query) {
  return request({
    url: `/mall/oss/erp/syncGoods`,
    method: 'get',
    params: query
  })
}

/*
 * c端 商品导出
 * @param data
 */
export function getSelfGoodsExport(query) {
  return request({
    url: `/mall/oss/self/goods/export`,
    method: 'get',
    params: query
  })
}

/*
 * c端 spu商品详情
 * @param data
 */
export function getSelfSpuDetail(query) {
  return request({
    url: `/mall/oss/self/goods/spuDetail`,
    method: 'get',
    params: query
  })
}

/*
 * c端 sku商品详情
 * @param data
 */
export function getSelfSkuDetail(query) {
  return request({
    url: `/mall/oss/self/goods/skuDetail`,
    method: 'get',
    params: query
  })
}

/**
 * c端 spu商品更新
 * @param data
 */
export function updateSelfGoodsSpuUpdate(data) {
  return request({
    url: `/mall/oss/self/goods/spuUpdate`,
    method: 'post',
    data
  })
}

/**
 * c端 sku商品更新
 * @param data
 */
export function updateSelfGoodsSkuUpdate(data) {
  return request({
    url: `/mall/oss/self/goods/skuUpdate`,
    method: 'post',
    data
  })
}

/**
 * c端 单个sku上下架
 * @param data
 */
export function updateSelfGoodsSingleUpdate(data) {
  return request({
    url: `/mall/oss/self/goods/status/batchUpdate`,
    method: 'post',
    data
  })
}

/**
 * c端 价格设置（单个/批量）
 * @param data
 */
export function updateSelfGoodsPriceBatchSet(data) {
  return request({
    url: `/mall/oss/self/goods/price/batchSet`,
    method: 'post',
    data
  })
}

/*
 * c端 商品日志
 * @param data
 */
export function getSelfGoodsLog(query) {
  return request({
    url: `/mall/oss/self/goods/log`,
    method: 'get',
    params: query
  })
}

/*
 * c端 同步erp店铺
 * @param data
 */
export function getSelfShopList(query) {
  return request({
    url: `/mall/oss/erp/shop/list`,
    method: 'get',
    params: query
  })
}

/*
 * c端 自营商品订单列表
 * @param data
 */
export function getSelfOrderList(query) {
  return request({
    url: `/mall/oss/self/order/list`,
    method: 'get',
    params: query
  })
}

/*
 * c端 自营商品订单详情
 * @param data
 */
export function getSelfOrderDetail(query) {
  return request({
    url: `/mall/oss/self/order/detail`,
    method: 'get',
    params: query
  })
}

/*
 * c端 自营商品订单 日志列表
 * @param data
 */
export function getSelfOrderLog(query) {
  return request({
    url: `/mall/oss/self/order/log`,
    method: 'get',
    params: query
  })
}

/*
 * c端 自营商品订单 人工日志
 * @param data
 */
export function saveSelfOrderListAdd(data) {
  return request({
    url: `/mall/oss/self/order/log/add`,
    method: 'post',
    data
  })
}

/*
 * c端 自营商品订单 物流信息详情
 * @param data
 */
export function getSelfLogisticsDetail(query) {
  return request({
    url: `/expands/oss/logistics/detail`,
    method: 'get',
    params: query
  })
}
/*
 * 编辑卖家备注
 * @param data
 */
export function setSellerRemark(data) {
  return request({
    url: `/mall/oss/self/order/sellerRemark`,
    method: 'post',
    data
  })
}

/*
 * 修改订单价格
 * @param data
 */
export function postChangePrice(data) {
  return request({
    url: `/mall/oss/self/order/updatePrice`,
    method: 'post',
    data
  })
}

/*
 * c端 自营商品订单 申请退款
 * @param data
 */
export function postSelfRefundApply(data) {
  return request({
    url: `/mall/oss/self/refund/apply`,
    method: 'post',
    data
  })
}

/*
 * c端 自营商品订单 换货后申请退款
 * @param data
 */
export function postSelfExchangeRefundApply(data) {
  return request({
    url: `/mall/oss/self/afterSale/exchangeRefundApply`,
    method: 'post',
    data
  })
}

/*
 * c端 自营商品订单 退款审核列表
 * @param data
 */
export function getSelfRefundList(query) {
  return request({
    url: `/mall/oss/self/refund/list`,
    method: 'get',
    params: query
  })
}

/*
 * c端 自营商品订单 退款详情
 * @param data
 */
export function getSelfRefundDetail(query) {
  return request({
    url: `/mall/oss/self/refund/detail`,
    method: 'get',
    params: query
  })
}

/*
 * c端 自营商品订单 退款审核
 * @param data
 */
export function postSelfRefundAuth(data) {
  return request({
    url: `/mall/oss/self/refund/auth`,
    method: 'post',
    data
  })
}

/*
 * c端 自营商品订单， 商品名称查询
 * @param data
 */
export function getFuzzySkuName(query) {
  return request({
    url: `/mall/oss/self/order/fuzzySkuName`,
    method: 'get',
    params: query
  })
}

/*
 * c端 售后列表 自变更售后单状态
 * @param data
 */
export function postUpdateSaleStatus(data) {
  return request({
    url: `/mall/oss/self/afterSale/updateAfterSaleStatus`,
    method: 'post',
    data
  })
}

/*
 * c端 自营商品订单，各状态售后单数量
 * @param data
 */
export function getStatusCount(query) {
  return request({
    url: `/mall/oss/self/afterSale/statusCount`,
    method: 'get',
    params: query
  })
}

//今日订单数据总览
export function getStatisticalData(query) {
  return request({
    url: `/mall/oss/self/order/todayStatisticalData`,
    method: 'get',
    params: query
  })
}
/**
 * c端 更新京东商品是否展示
 * @param data
 */
export function UpdateShowStatus(data) {
  return request({
    url: `/mall/oss/self/goods/updateShowStatus`,
    method: 'post',
    data
  })
}

/*
 * c端 售后列表
 * @param data
 */
export function getAfterSaleList(query) {
  return request({
    url: `/mall/oss/self/afterSale/list`,
    method: 'get',
    params: query
  })
}

/*
 * c端 售后列表-详情
 * @param data
 */
export function getAfterSaleDetail(query) {
  return request({
    url: `/mall/oss/self/afterSale/detail`,
    method: 'get',
    params: query
  })
}

/*
 * c端 自营商品订单 退款审核
 * @param data
 */
export function postAfterSaleAudit(data) {
  return request({
    url: `/mall/oss/self/afterSale/audit`,
    method: 'post',
    data
  })
}

/*
 * c端 自营商品订单 售后退款
 * @param data
 */
export function postAfterSaleRefund(data) {
  return request({
    url: `/mall/oss/self/afterSale/refund`,
    method: 'post',
    data
  })
}

/*
 * c端 自营商品订单 获取售后退款详情
 * @param params
 */
export function getAfterSaleRefundInfo(params) {
  return request({
    url: `/mall/oss/self/afterSale/checkDetail`,
    method: 'get',
    params
  })
}

/*
 * c端 自营商品订单 售后发货
 * @param data
 */
export function postAfterSaleDelivery(data) {
  return request({
    url: `/mall/oss/self/afterSale/delivery`,
    method: 'post',
    data
  })
}

/*
 * c端 自营商品 sku 删除
 * @param data
 */
export function postBatchDelSkuSet(data) {
  return request({
    url: `/mall/oss/self/goods/batchDelSkuSet`,
    method: 'post',
    data
  })
}

/*
 * c端 自营商品 编辑订单收货地址信息
 * @param data
 */
export function updateDeliveryAddress(data) {
  return request({
    url: `/mall/oss/self/order/updateDeliveryAddress`,
    method: 'post',
    data
  })
}

/*
 * c端 自营商品 更新商品排序值
 * @param data
 */
export function updateOrderWeight(data) {
  return request({
    url: `/mall/oss/self/goods/updateOrderWeight`,
    method: 'post',
    data
  })
}

/*
 * c端 商品列表-属性排序列表
 * @param data
 */
export function getPropertiesList(query) {
  return request({
    url: `/mall/oss/goods/properties/list`,
    method: 'get',
    params: query
  })
}

/*
 * c端 商品列表-编辑属性排序
 * @param data
 */
export function postPropertiesEdit(data) {
  return request({
    url: `/mall/oss/goods/properties/edit`,
    method: 'post',
    data
  })
}

/*
 * c端 商品列表-编辑属性颜色，尺寸修改
 * @param data
 */
export function postUpdateColor(data) {
  return request({
    url: `/mall/oss/goods/properties/batchUpdate`,
    method: 'post',
    data
  })
}
/*
 * 新增优惠券
 * @param data
 */
export function postSaveCoupon(data) {
  return request({
    url: `/mall/oss/self/coupon/save`,
    method: 'post',
    data
  })
}
/* c端 秒杀活动 秒杀列表查询
 * @param data
 */
export function getSeckillList(query) {
  return request({
    url: `/mall/oss/seckill/list`,
    method: 'get',
    params: query
  })
}

/*
 * c端 秒杀活动 新增秒杀活动
 * @param data
 */
export function postSeckillAdd(data) {
  return request({
    url: `/mall/oss/seckill/add`,
    method: 'post',
    data
  })
}
/*
 * 优惠券列表
 * @param data
 */
export function getCouponList(query) {
  return request({
    url: `/mall/oss/self/coupon/list`,
    method: 'get',
    params: query
  })
}
/*
 * c端 秒杀活动 编辑秒杀活动
 * @param data
 */
export function postSeckillEdit(data) {
  return request({
    url: `/mall/oss/seckill/edit`,
    method: 'post',
    data
  })
}

/*
 * 优惠券详情
 * @param data
 */
export function getCouponDetail(query) {
  return request({
    url: `/mall/oss/self/coupon/detail`,
    method: 'get',
    params: query
  })
}
/*
 * 发券配置列表
 * @param data
 */
export function getCouponConfList(query) {
  return request({
    url: `/mall/oss/self/coupon/conf/list`,
    method: 'get',
    params: query
  })
}
/*
 * 保存发券配置
 * @param data
 */
export function postSaveCouponConf(data) {
  return request({
    url: `/mall/oss/self/coupon/conf/save`,
    method: 'post',
    data
  })
}
/*
 * 保存发券活动配置
 * @param data
 */
export function postSaveCouponActivityConf(data) {
  return request({
    url: `/mall/oss/self/coupon/share/save`,
    method: 'post',
    data
  })
}

/* c端 秒杀活动 更新spu排序值
 * @param data
 */
export function updateSpuOrderWeight(data) {
  return request({
    url: `/mall/oss/seckill/updateSpuOrderWeight`,
    method: 'post',
    data
  })
}

/*
 * c端 秒杀活动 更新spu/sku有效状态
 * @param data
 */
export function updateSeckillStatus(data) {
  return request({
    url: `/mall/oss/seckill/updateStatus`,
    method: 'post',
    data
  })
}

/*
 * c端 秒杀活动 批量设置秒杀价、活动限量、每人限购
 * @param data
 */
export function updateSeckillBatchSet(data) {
  return request({
    url: `/mall/oss/seckill/batchSet`,
    method: 'post',
    data
  })
}
/*
 * 获取优惠券配置详情
 * @param data
 */
export function getCouponConfDetail(query) {
  return request({
    url: `/mall/oss/self/coupon/conf/detail`,
    method: 'get',
    params: query
  })
}
/*
 * c端 秒杀活动 查询秒杀活动操作日志
 * @param data
 */
export function getSeckillLog(query) {
  return request({
    url: `/mall/oss/seckill/log`,
    method: 'get',
    params: query
  })
}
/*
 * 获取优惠券名称
 * @param data
 */
export function getCouponName(query) {
  return request({
    url: `/mall/oss/self/coupon/fuzzy`,
    method: 'get',
    params: query
  })
}
/*
 * c端 秒杀活动 查询商品信息
 * @param data
 */
export function getSpuDetail(query) {
  return request({
    url: `/mall/oss/seckill/getSpuDetail`,
    method: 'get',
    params: query
  })
}
/*
 * 优惠券添加商品时预览
 * @param data
 */
export function postCouponGoodsByIds(data) {
  return request({
    url: `/mall/oss/self/goods/listGoodsByIds`,
    method: 'post',
    data
  })
}
/*
 * c端自营商品，服务信息
 * @param data
 */
export function getServiceInfo(query) {
  return request({
    url: `/mall/oss/self/goods/serviceInfo`,
    method: 'get',
    params: query
  })
}

/*
 * c端自营商品，服务信息
 * @param data
 */
export function postSendCoupon(data) {
  return request({
    url: `mall/oss/self/coupon/conf/send`,
    method: 'post',
    data
  })
}
/*
 * c端 优惠券置为无效
 * @param data
 */
export function postCouponConfInvalid(data) {
  return request({
    url: `/mall/oss/self/coupon/conf/invalid`,
    method: 'post',
    data
  })
}

/*
 * c端自营商品，申请售后
 * @param data
 */
export function postAfterSale(data) {
  return request({
    url: `/mall/oss/self/afterSale/apply`,
    method: 'post',
    data
  })
}
/*
 * c端自营商品，申请售后
 * @param data
 */
export function GetNewGoods(query) {
  return request({
    url: `/mall/oss/self/afterSale/skuInfo`,
    method: 'get',
    params: query
  })
}

/*
 * c端自营商品，话费订单
 * @param data
 */
export function GetChargesList(query) {
  return request({
    url: `/mall/oss/phone/charges/list`,
    method: 'get',
    params: query
  })
}

/*
 * 我的奖品，话费订单
 * @param data
 */
export function GetPrizeChargesList(query) {
  return request({
    url: `/pirate/user/credit/oss/charge/orderList`,
    // url: `/mall/oss/snatch/balanceRecordList`,
    method: 'get',
    params: query
  })
}

/*
 * c端自营商品，话费订单状态跟新
 * @param data
 */
export function postUpdateChargesStatus(data) {
  return request({
    url: `/mall/oss/phone/charges/updateOrderStatus`,
    method: 'post',
    data
  })
}
/*
 * 淘宝 查询订单凭证
 * @param params
 */
export function getOrderBindRecord(params) {
  return request({
    url: `/mall/oss/taobao/order/bindRecord`,
    method: 'get',
    params
  })
}

/*
 *淘宝 找回订单
 * @param data
 */
export function postBindUserOrder(data) {
  return request({
    url: `/mall/oss/taobao/order/bindUserOrder`,
    method: 'post',
    data
  })
}
/*
 *批量变更sku首图
 * @param data
 */
export function batchUpdateFirstImg(data) {
  return request({
    url: `/mall/oss/self/goods/batchUpdateFirstImg`,
    method: 'post',
    data
  })
}

/*
 *定时调价列表
 * @param data
 */
export function getTimingUpdatePriceList(params) {
  return request({
    url: `/mall/oss/self/goods/timingUpdatePrice/list`,
    method: 'get',
    params
  })
}

/*新增定时调价活动
 *updatePrice
 * @param data
 */
export function postTimingUpdatePrice(data) {
  return request({
    url: `/mall/oss/self/goods/timingUpdatePrice/add`,
    method: 'post',
    data
  })
}

/*变更调价状态
 *updatePrice
 * @param data
 */
export function postTimingUpdateStatus(data) {
  return request({
    url: `/mall/oss/self/goods/timingUpdatePrice/updateStatus`,
    method: 'post',
    data
  })
}

/*修改自营商品定时调价功能
 *updatePrice
 * @param data
 */
export function postTimingUpdate(data) {
  return request({
    url: `/mall/oss/self/goods/timingUpdatePrice/update`,
    method: 'post',
    data
  })
}

/*回填售后快递信息
 *
 * @param data
 */
export function postDeliveryConfirm(data) {
  return request({
    url: `/mall/oss/self/afterSale/deliveryConfirm`,
    method: 'post',
    data
  })
}
/*
 * 能量活动列表
 * @param params
 */
export function activityEnergyList(params) {
  return request({
    url: `/mall/oss/activity/energy/list`,
    method: 'get',
    params
  })
}

/*
 * 能量活动详情
 * @param params
 */
export function activityEnergyDetail(params) {
  return request({
    url: `/mall/oss/activity/energy/detail`,
    method: 'get',
    params
  })
}

/*
 * 新增能量活动
 * @param data
 */
export function activityEnergyAdd(data) {
  return request({
    url: `/mall/oss/activity/energy/add`,
    method: 'post',
    data
  })
}

/*
 * 更新能量商城活动状态
 * @param data
 */
export function activityEnergyUpdStatus(data) {
  return request({
    url: `/mall/oss/activity/energy/updStatus`,
    method: 'post',
    data
  })
}

/*
 * 订单确认发货
 * @param data
 */
export function postSendLogistics(data) {
  return request({
    url: `/mall/oss/self/order/sendLogistics`,
    method: 'post',
    data
  })
}

/*
 * 保存活动图标
 * @param data
 */
export function saveOrUpdateImgActivity(data) {
  return request({
    url: `/mall/oss/self/goods/saveOrUpdateImgActivity`,
    method: 'post',
    data
  })
}

/*
 * 更新活动图标状态
 * @param data
 */
export function updateImgActivityStatus(data) {
  return request({
    url: `/mall/oss/self/goods/updateImgActivityStatus`,
    method: 'post',
    data
  })
}

/*
 * 商品活动图标列表
 * @param data
 */
export function getListImgActivity(params) {
  return request({
    url: `/mall/oss/self/goods/listImgActivity`,
    method: 'get',
    params
  })
}

/*
 * 活动图标下的商品
 * @param data
 */
export function getListImgActivityGoods(params) {
  return request({
    url: `/mall/oss/self/goods/listImgActivityGoods`,
    method: 'get',
    params
  })
}

/*
 * 根据spuid查询商品信息
 * @param data
 */
export function getListSpuBySpuids(params) {
  return request({
    url: `/mall/oss/self/goods/listSpuBySpuids`,
    method: 'get',
    params
  })
}

/*
 * 根据uid 反查询商家
 * @param data
 */
export function getShopIdentyMarkInfoList(params) {
  return request({
    url: `/shop/oss/shop/apply/getShopIdentyMarkInfoList`,
    method: 'get',
    params
  })
}

/*
 * oss特聘官取消订单
 * @param data
 */
export function postSpecialCancel(data) {
  return request({
    url: `/mall/oss/self/order/specialCancel`,
    method: 'post',
    data
  })
}

/*
 * oss商品评价列表
 * @param data
 */
export function getMallGoodsReplyList(params) {
  return request({
    url: `/mall/oss/adminMallGoodsReplyController/mallGoodsReplyList`,
    method: 'get',
    params
  })
}

/*
 * oss商品评价列表新增
 * @param data
 */
export function postMallGoodsReply(data) {
  return request({
    url: `/mall/oss/adminMallGoodsReplyController/addMallGoodsReply`,
    method: 'post',
    data
  })
}

/*
 * oss商品评价更新
 * @param data
 */
export function postUpdateGoodsReply(data) {
  return request({
    url: `/mall/oss/adminMallGoodsReplyController/updateGoodsReply`,
    method: 'post',
    data
  })
}

/*
 * 下单黑名单 新增
 * @param data
 */
export function postAddOrderBlack(data) {
  return request({
    url: `/mall/oss/self/order/addOrderBlack`,
    method: 'post',
    data
  })
}

/*
 * 删除下单黑名单
 * @param data
 */
export function postDelOrderBlack(data) {
  return request({
    url: `/mall/oss/self/order/delOrderBlack`,
    method: 'post',
    data
  })
}

/*
 * 下单黑名单 列表
 * @param data
 */
export function getOrderBlackList(params) {
  return request({
    url: `/mall/oss/self/order/orderBlackList`,
    method: 'get',
    params
  })
}

/*
 * 自营赠品活动保存、编辑
 * @param data
 */
export function postGiftSaveOrUpdate(data) {
  return request({
    url: `/mall/oss/self/gift/saveOrUpdate`,
    method: 'post',
    data
  })
}

/*
 * 自营赠品活动更新状态
 * @param data
 */
export function postGiftUpdateStatus(data) {
  return request({
    url: `/mall/oss/self/gift/updateStatus`,
    method: 'post',
    data
  })
}

/*
 * 自营赠品活动列表
 * @param data
 */
export function getGiftList(params) {
  return request({
    url: `/mall/oss/self/gift/list`,
    method: 'get',
    params
  })
}

/*
 * 自营赠品详情
 * @param data
 */
export function getGiftDetail(params) {
  return request({
    url: `/mall/oss/self/gift/detail`,
    method: 'get',
    params
  })
}
