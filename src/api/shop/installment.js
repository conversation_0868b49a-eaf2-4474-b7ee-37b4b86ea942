import request from '@/utils/request'

/**
 * 分期列表
 * @param params
 */
export function getInstallmentList(params) {
  return request({
    url: `/mall/oss/installment/list`,
    method: 'get',
    params
  })
}

/**
 * 分期详情
 * @param params
 */
export function getInstallmentDetail(params) {
  return request({
    url: `/mall/oss/installment/detail`,
    method: 'get',
    params
  })
}

/**
 * 编辑分期活动
 * @param data
 */
export function saveOrUpdate(data) {
  return request({
    url: `/mall/oss/installment/saveOrUpdate`,
    method: 'post',
    data
  })
}

/**
 * 更新活动状态
 * @param data
 */
export function updateStatus(data) {
  return request({
    url: `/mall/oss/installment/updateStatus`,
    method: 'post',
    data
  })
}

// 优惠券添加商品时预览 见其他
// /mall/oss/self/goods/listGoodsByIds
