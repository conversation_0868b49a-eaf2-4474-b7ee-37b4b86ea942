import request from '@/utils/request'

/**
 * 问题列表
 * @param data {page,limit}
 * @constructor
 */
export function GetListQuestion(data) {
  return request({
    url: '/forum/oss/questionManagement/listQuestion',
    method: 'post',
    data,
  })
}

/**
 * 马甲用户模糊搜索
 * @param data {keyWord,limit}
 * @constructor
 */
export function SearchUser(data) {
  return request({
    url: '/uic/operate/vest/findByVestName',
    method: 'post',
    data,
  })
}

/**
 * 添加回答、发布文章
 * @param data {content, autherid, type}
 * http://10.33.73.190:8181/docs/ossapi/852 发布文章
 * @constructor
 */
export function AddAnswer(data) {
  return request({
    url: '/forum/oss/businessEssayController/saveBusinessEssay',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010301',
    data,
  })
}
