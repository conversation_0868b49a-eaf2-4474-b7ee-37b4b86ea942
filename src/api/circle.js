import request from '@/utils/request'

/**
 * 圈子列表
 * @param query
 */
export function getCircleList(query) {
  return request({
    url: '/hoop/oss/hoop/all/list',
    method: 'get',
    params: query
  })
}

/**
 * 保存圈子
 * @param data
 */
export function saveCircle(data) {
  return request({
    url: '/hoop/oss/hoop/save',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10901',
    data
  })
}

/**
 * 圈子状态和推荐
 * @param data
 */
export function updateHoop(data) {
  return request({
    url: '/hoop/oss/hoop/updateHoop',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10901',
    data
  })
}

/**
 * 圈子用户
 * @param query
 */
export function getCircleUser(data) {
  return request({
    url: '/hoop/oss/hoop/all/user',
    method: 'post',
    data
  })
}

/**
 * 校验圈子名称
 * @param query
 */
export function checkName(query) {
  return request({
    url: '/hoop/oss/hoop/checkName',
    method: 'get',
    params: query
  })
}

/**
 * 审核成员
 * @param query
 */
export function postHoopExamine(data) {
  return request({
    url: '/hoop/oss/hoop/examine',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10902',
    data
  })
}

/**
 * 移除成员
 * @param query
 */
export function removeMember(data) {
  return request({
    url: '/hoop/oss/hoop/removeMember',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10902',
    data
  })
}

/**
 * 批量通过
 * @param query
 */
export function postBatchPass(data) {
  return request({
    url: '/hoop/oss/hoop/batchPass',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10902',
    data
  })
}

/**
 * 审核圈子介绍修改
 * @param query
 */
export function verifyHoop(data) {
  return request({
    url: '/hoop/oss/hoop/verify',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10901',
    data
  })
}

/**
 * 圈主申请列表
 * @param query
 */
export function getListOwnerApply(query) {
  return request({
    url: '/hoop/oss/hoopOwnerApply/listOwnerApply',
    method: 'get',
    params: query
  })
}

/**
 * 圈主申请列表
 * @param query
 */
export function checkOwnerApply(data) {
  return request({
    url: '/hoop/oss/hoopOwnerApply/checkOwnerApply',
    method: 'POST',
    beforeAlterTwo: false,
    data
  })
}

/**
 * 圈子详情，添加话题
 * @param query
 */
export function addRelation(data) {
  return request({
    url: '/hoop/oss/hoop/addRelation',
    method: 'POST',
    data
  })
}

/**
 * 圈子详情，下拉模糊查询话题
 * @param query
 */
export function getListTopicByName(query) {
  return request({
    url: '/hoop/oss/hoop/listTopicByName',
    method: 'get',
    params: query
  })
}

/**
 * 圈子详情，圈子详情话题列表
 * @param query
 */
export function getListTopicsByHoopId(query) {
  return request({
    url: '/hoop/oss/hoop/listTopicsByHoopId',
    method: 'get',
    params: query
  })
}

/**
 * 圈子详情，删除话题
 * @param query
 */
export function deleteTopicById(data) {
  return request({
    url: '/hoop/oss/hoop/deleteTopicById',
    method: 'POST',
    beforeAlterThree: false,
    menu: 'S10901',
    data
  })
}

/**
 * 圈子详情，修改话题显示状态
 * @param query
 */
export function updateTopicStatus(data) {
  return request({
    url: '/hoop/oss/hoop/updateTopicStatus',
    method: 'POST',
    beforeAlterThree: false,
    menu: 'S10901',
    data
  })
}

/**
 * 圈子列表，热门排序20条
 */
export function getTopHotHoopList(query) {
  return request({
    url: '/hoop/oss/hoop/topHotHoopList',
    method: 'get',
    params: query
  })
}

/**
 * 调整热门排序
 */
export function updateSortHotHoop(data) {
  return request({
    url: '/hoop/oss/hoop/sortHotHoop',
    method: 'POST',
    data
  })
}

/**
 * 重置热门圈子自定义排序
 */
export function resetHotHoop() {
  return request({
    url: '/hoop/oss/hoop/resetHotHoop',
    method: 'POST'
  })
}

/**
 * 圈子指南列表
 */
export function getHoopGuideList(query) {
  return request({
    url: '/hoop/oss/hoopGuide/list',
    method: 'get',
    params: query
  })
}

/**
 * 圈子指南列表
 */
export function getHoopGuideDetail(query) {
  return request({
    url: '/hoop/oss/hoopGuide/detail',
    method: 'get',
    params: query
  })
}

/**
 * 圈子指南上下架接口
 */
export function updatePutaway(data) {
  return request({
    url: '/hoop/oss/hoopGuide/putaway',
    method: 'POST',
    beforeAlterFirst: false,
    menu: 'S10904',
    data
  })
}

/**
 * 新增圈子指南
 */
export function addHoopGuide(data) {
  return request({
    url: '/hoop/oss/hoopGuide/insert',
    method: 'POST',
    data
  })
}

/**
 * 修改圈子指南
 */
export function updateHoopGuide(data) {
  return request({
    url: '/hoop/oss/hoopGuide/update',
    method: 'POST',
    beforeAlterFirst: false,
    menu: 'S10904',
    data
  })
}

/**
 * 审核列表
 */
export function getAuditList(query) {
  return request({
    url: '/hoop/oss/master/opt/auditList',
    method: 'get',
    params: query
  })
}

/**
 * 审核列表-审核通过
 */
export function updateOptPass(data) {
  return request({
    url: '/hoop/oss/master/opt/pass',
    method: 'post',
    data
  })
}

/**
 * 审核列表-审核拒绝
 */
export function updateOptReject(data) {
  return request({
    url: '/hoop/oss/master/opt/reject',
    method: 'post',
    data
  })
}

/**
 * 被举报圈主列表
 */
export function getReportOwnerList(query) {
  return request({
    url: '/hoop/oss/master/report/ownerList',
    method: 'get',
    params: query
  })
}

/**
 * 圈主被举报记录
 */
export function getReportReportList(query) {
  return request({
    url: '/hoop/oss/master/report/reportList',
    method: 'get',
    params: query
  })
}

/**
 * 审核举报圈主
 */
export function getReportAudit(data) {
  return request({
    url: '/hoop/oss/master/report/audit',
    method: 'post',
    data
  })
}

/**
 * 圈子管理日志列表
 */
export function getHoopManageRecord(query) {
  return request({
    url: '/hoop/oss/hoop/manageRecord',
    method: 'get',
    params: query
  })
}
/**
 * 简单圈子详情--合并圈子页面展示
 */
export function getCombineCircleDetail(query) {
  return request({
    url: '/hoop/oss/hoop/simple/detail',
    method: 'get',
    params: query
  })
}

/**
 * 合并圈子
 */
export function combineCircles(data) {
  return request({
    url: '/hoop/oss/hoop/migrate',
    method: 'post',
    data
  })
}

/**
 * 圈子内容列表
 */
export function listHoopEssayFromEs(query) {
  return request({
    url: '/forum/oss/businessEssayController/listHoopEssayFromEs',
    method: 'get',
    params: query
  })
}
/**
 * 批量撤销
 */
export function batchRollBackMaster(data) {
  return request({
    url: '/hoop/oss/hoopOwnerApply/batchRollBackMaster',
    method: 'post',
    data
  })
}

/**
 * 批量撤销
 */
export function getOwnerApplyDetail(params) {
  return request({
    url: '/hoop/oss/hoopOwnerApply/ownerApplyDetail',
    method: 'get',
    params
  })
}

/**
 * 保存推荐关联圈子
 */
export function saveOrUpdateRecommend(data) {
  return request({
    url: '/hoop/oss/hoop/saveOrUpdateRecommend',
    method: 'post',
    data
  })
}

/**
 * 推荐关联圈子
 */
export function getListRecommend(params) {
  return request({
    url: '/hoop/oss/hoop/listRecommend',
    method: 'get',
    params
  })
}

/**
 * 推荐关联圈子 根据圈子名称获取圈子id
 */
export function getHoopIdByName(params) {
  return request({
    url: '/hoop/oss/hoop/getHoopIdByName',
    method: 'get',
    params
  })
}

/**
 * 移除内容
 */
export function removeEssayByOSS(data) {
  return request({
    url: '/hoop/oss/hoop/removeEssayByOSS',
    method: 'post',
    data
  })
}

/**
 * 获取圈主月度考核列表
 */
export function getBendUserHoopCircleList(params) {
  return request({
    url: '/hoop/oss/hoop/getBendUserHoopCircleList',
    method: 'get',
    params
  })
}

/**
 * 更新考核结果
 */
export function updBendUserHoopCircleAssessResult(data) {
  return request({
    url: '/hoop/oss/hoop/updBendUserHoopCircleAssessResult',
    method: 'post',
    data
  })
}

/**
 * 推送考核结果
 */
export function pushAssessResult(data) {
  return request({
    url: '/hoop/oss/hoop/pushAssessResult',
    method: 'post',
    data
  })
}

/**
 * 考核结果通知配置列表
 */
export function getAwardList(params) {
  return request({
    url: '/hoop/oss/award/list',
    method: 'get',
    params
  })
}

/**
 * 移除奖品
 */
export function postAwardRemove(data) {
  return request({
    url: '/hoop/oss/award/remove',
    method: 'post',
    data
  })
}

/**
 * 修改奖品数量
 */
export function postAwardUpdNum(data) {
  return request({
    url: '/hoop/oss/award/updNum',
    method: 'post',
    data
  })
}

/**
 * 添加奖品
 */
export function postAwardSave(data) {
  return request({
    url: '/hoop/oss/award/save',
    method: 'post',
    data
  })
}

/**
 * 移出问答
 */
export function removeQuestionAnswer(data) {
  return request({
    url: '/forum/oss/businessEssayController/removeQuestionAnswer',
    method: 'post',
    data
  })
}
/**
 * 置顶
 */
export function topQuestion(data = {
  hoopId: '',
  essayId: '',
}) {
  return request({
    url: '/hoop/oss/hoop/topQuestion',
    method: 'post',
    data
  })
}
/**
 * 取消置顶
 */
export function cancelTopQuestion(data = {
  hoopId: '',
  essayId: '',
}) {
  return request({
    url: '/hoop/oss/hoop/cancelTopQuestion',
    method: 'post',
    data
  })
}
