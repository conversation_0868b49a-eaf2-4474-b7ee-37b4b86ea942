import request from '@/utils/request'

/**
 * 首页tab活动分页
 * @param query
 */
export function getTabList(query) {
  return request({
    url: '/activity/oss/homeTabConfigController/getTabList',
    method: 'get',
    params: query,
  })
}

/**
 *  首页tab活动-保存首页tab配置
 * @param query
 */
export function saveTabConfig(data) {
  return request({
    url: '/activity/oss/homeTabConfigController/saveTabConfig',
    method: 'post',
    data,
  })
}

/**
 * 首页tab配置- 修改生效
 * @param query
 */
export function updateTabStatus(data) {
  return request({
    url: '/activity/oss/homeTabConfigController/updateTabStatus',
    method: 'post',
    data,
  })
}
