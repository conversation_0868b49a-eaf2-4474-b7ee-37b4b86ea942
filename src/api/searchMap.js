import request from '@/utils/request'
import { RESURL, APIURL } from '@/utils/configData/config'

// // 地图搜索位置 高德
// export function searchAddress(data) {
//   return request({
//     url: 'https://restapi.amap.com/v3/assistant/inputtips',
//     method: 'get',
//     params: data
//   })
// }

// 地区编码接口(省份) 废弃
export function GetAreaCodeProvice() {
  return request({
    url: `${RESURL}areacode/provice.json`,
    method: 'get'
  })
}
// 地区编码接口(城市) 废弃
export function GetAreaCodeCity(id) {
  return request({
    url: `${RESURL}areacode/${id}/city.json`,
    method: 'get'
  })
}
// 地区编码接口(区县) 废弃
export function GetCountyCode(id, cityId) {
  return request({
    url: `${RESURL}areacode/${id}/${cityId}/area.json`,
    method: 'get'
  })
}

export function GetArea(params) {
  return request({
    url: `${APIURL}uic/map/listMap`,
    method: 'get',
    params: params
  })
}
