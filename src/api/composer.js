import request from '@/utils/request'
import { MANAGERURL } from '@/utils/configData/config'
// const MANAGERURL = 'http://10.33.72.188:8084/'

/**
 * 等级权益列表
 * @param query
 */
export function GetEquity(query) {
  return request({
    url: `${MANAGERURL}uic/oss/level/panel`,
    method: 'get',
    params: query
  })
}

/**
 * 权益/晋级条件配置更改
 * @param data
 */
export function UpdateEquity(data) {
  return request({
    url: `${MANAGERURL}uic/oss/level/configure`,
    method: 'post',
    data
    // transformRequest(data) {
    //   return JSON.stringify(data);
    // },
    // headers: {
    //   'Content-Type': 'application/json'
    // },
  })
}

/**
 * 作者等级管理列表
 * @param query
 */
export function GetGradeManage(query) {
  return request({
    url: `${MANAGERURL}uic/oss/level/manage/list`,
    method: 'get',
    params: query
  })
}

/**
 * 作者等级详情页
 * @param query
 */
export function GetGradeDetail(query) {
  return request({
    url: `${MANAGERURL}uic/oss/level/author/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 查看成长记录
 * @param query
 */
export function GetGradeDetailHistory(query) {
  return request({
    url: `${MANAGERURL}uic/oss/level/author/records`,
    method: 'get',
    params: query
  })
}

/**
 * 更改作者等级
 * @param data
 */
export function UpdateGrade(data) {
  return request({
    url: `${MANAGERURL}uic/oss/level/author/update`,
    method: 'post',
    data
  })
}

/**
 * 查看成长记录
 * @param query
 */
export function GetAuthorCreationList(query) {
  return request({
    url: `/uic/oss/level/authorCreationList`,
    method: 'get',
    params: query
  })
}

/**
 * 创作者不可参加活动操作记录
 * @param query
 */
export function GetAuthorCreationActOperateLog(query) {
  return request({
    url: `/uic/oss/level/authorCreationActOperateLog`,
    method: 'get',
    params: query
  })
}

/**
 * 开启/关闭 创作者平台/厂家活动
 * @param data
 */
export function UpdateAuthorCreationOperate(data) {
  return request({
    url: `/uic/oss/level/authorCreationOperate`,
    method: 'post',
    data
  })
}

/**
 * 开启/关闭 创作者平台/厂家活动
 * @param data
 */
export function DeleteCancelAuth(data) {
  return request({
    url: `/uic/oss/level/cancelAuth`,
    method: 'post',
    data
  })
}

/**
 * 更新创作者标签
 * @param data
 */
export function updateAuthorTag(data) {
  return request({
    url: `/uic/oss/level/updateAuthorTag`,
    method: 'post',
    data
  })
}

/**
 * 新增、编辑创作者公告
 * @param data
 */
export function postExpertSaveOrUpdate(data) {
  return request({
    url: `/uic/oss/author/notice/saveOrUpdate`,
    method: 'post',
    data
  })
}
/**
 * 创作者公告列表
 * @param data
 */
export function getNoticeList(params) {
  return request({
    url: `/uic/oss/author/notice/list`,
    method: 'get',
    params
  })
}
/**
 * 新增/编辑创作者问题分类
 * @param data
 */
export function postCategorySaveOrUpdate(data) {
  return request({
    url: `/uic/oss/author/question/category/saveOrUpdate`,
    method: 'post',
    data
  })
}
/**
 * 新增/编辑创作者问题
 * @param data
 */
export function postInfoSaveOrUpdate(data) {
  return request({
    url: `/uic/oss/author/question/info/saveOrUpdate`,
    method: 'post',
    data
  })
}

/**
 * 新增/编辑创作者问题分类
 * @param data
 */
export function getQuestionList(params) {
  return request({
    url: `/uic/oss/author/question/list`,
    method: 'get',
    params
  })
}

/**
 * 删除创作者问题
 * @param data
 */
export function postQuestionDelete(data) {
  return request({
    url: `/uic/oss/author/question/delete`,
    method: 'post',
    data
  })
}

/**
 * 创作者收益实时数据
 * @param query
 */
export function getCreatorincomeList(query) {
  return request({
    url: `/uic/oss/user/essay/income/creatorincomeList`,
    method: 'get',
    params: query
  })
}

/**
 * 创作者code
 * @param query
 */
export function getCreateCode(query) {
  return request({
    url: `/user/center/oss/platform/observer/createCode`,
    method: 'get',
    params: query
  })
}
