import request from '@/utils/request'

// 广告查询
export function advertRequest(query) {
  return request({
    url: '/forum/oss/externalAdvertController/advertList',
    method: 'get',
    params: query
  })
}

// 广告保存
export function advertSave(data) {
  return request({
    url: '/forum/oss/externalAdvertController/saveAdvert',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10402',
    data
  })
}

// 广告详情
export function getAdvertising(data) {
  return request({
    url: '/forum/oss/externalAdvertController/v2/detail',
    method: 'post',
    data
  })
}

// 查询分页(页面弹框)
export function getBannerList(data) {
  return request({
    url: '/forum/oss/bannerController/getBannerList?_dc=1565749735724',
    method: 'post',
    data
  })
}

// 分页详情(页面弹框)
export function getBannerDetailById(data) {
  return request({
    url: '/forum/oss/bannerController/getBannerDetailById',
    method: 'post',
    data
  })
}

// 保存或修改(页面弹框)
export function saveBanner(data) {
  return request({
    url: '/forum/oss/bannerController/saveBanner',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}

// 删除经销商缓存
export function clearShopCache(query) {
  return request({
    url: '/forum/oss/cacheController/handlecache?',
    method: 'get',
    params: query
  })
}

// 已开通文章广告用户列表
export function getAdList(query) {
  return request({
    url: '/uic/oss/user/ad/list',
    method: 'get',
    params: query
  })
}

// 编辑用户广告开通状态（OSS用）
export function updateStatus(data) {
  return request({
    url: '/uic/oss/user/ad/updateStatus',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10405',
    data
  })
}

// 广告列表查询
export function getCustomAdList(query) {
  return request({
    url: '/activity/oss/custom/ad/list',
    method: 'get',
    params: query
  })
}

// 更新广告状态
export function adUpdateStatus(data) {
  return request({
    url: '/activity/oss/custom/ad/updateStatus',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10406',
    data
  })
}

/**
 * 广告列表
 * @param data
 */
export function businessAdList(query) {
  return request({
    url: '/forum/oss/externalAdvertController/v2/advertList',
    method: 'get',
    params: query
  })
}

// 广告详情
export function getAdvertisingDetails(query) {
  return request({
    url: '/forum/oss/externalAdvertController/v2/detail',
    method: 'get',
    params: query
  })
}

/**
 * 复制广告，暂未使用
 * @param data
 */
export function newAdCopy(data) {
  return request({
    url: '/forum/oss/externalAdvertController/user/v2/copy',
    method: 'post',
    data
  })
}

/**
 * 删除广告
 * @param data
 */
export function dealerAdvertising(data) {
  return request({
    url: '/forum/oss/externalAdvertController/user/delete',
    method: 'post',
    data
  })
}

/**
 * 保存广告
 * @param data
 */
export function saveAdvertisementData(data) {
  return request({
    url: '/forum/oss/externalAdvertController/user/v2/saveAdvert',
    method: 'post',
    data
  })
}

/**
 * 改变广告状态
 * @param data
 */
export function changeAdStatus(data) {
  return request({
    url: '/forum/oss/externalAdvertController/user/v2/status/update',
    method: 'post',
    data
  })
}

/**
 * 广告是否每页展示
 * @param data
 */
export function repeatAd(data) {
  return request({
    url: '/forum/oss/externalAdvertController/user/repeat/update',
    method: 'post',
    data
  })
}

// 查询小包配置
export function getPacketInfo(query) {
  return request({
    url: '/forum/oss/otherController.do?action=getPacketInfo',
    method: 'get',
    params: query
  })
}

/**
 * 广告是否每页展示
 * @param data
 */
export function savePacketInfo(data) {
  return request({
    url: '/forum/oss/otherController.do?action=savePacketInfo',
    method: 'post',
    data
  })
}

// 获取广告三级页面
export function adSiteSetGetNextLevel(params) {
  return request({
    url: '/advert/oss/adSiteSet/getNextLevel',
    method: 'get',
    params
  })
}

// 获取广告跳转类型列表
export function getLinkTypeList(params) {
  return request({
    url: '/advert/oss/advertType/linkTypeList',
    method: 'get',
    params
  })
}

// 根据广告方类型获取广告方列表
export function getAdvertiserList(params) {
  return request({
    url: '/advert/oss/advertiserManage/advertiser/list',
    method: 'get',
    params
  })
}

//计划列表查询新
export function getAdvertisingPlanGetCam(query) {
  return request({
    url: '/advert/oss/campaignManage/getCampaigns',
    method: 'get',
    params: query
  })
}
//计划列表查询
export function campaignManageList(query) {
  return request({
    url: '/advert/oss/campaignManage/list',
    method: 'get',
    params: query
  })
}

// 获取广告详情
export function adAdvertManageDetail(params) {
  return request({
    url: '/advert/oss/adAdvertManage/detail',
    method: 'get',
    params
  })
}

// 新建广告
export function adAdvertManageAdd(data) {
  return request({
    url: '/advert/oss/adAdvertManage/add',
    method: 'post',
    data
  })
}

// 更新广告
export function adAdvertManageUpdate(data) {
  return request({
    url: '/advert/oss/adAdvertManage/update',
    method: 'post',
    data
  })
}

// 创建计划
export function campaignManageAdd(data) {
  return request({
    url: '/advert/oss/campaignManage/add',
    method: 'post',
    data
  })
}

// 用户数据-用户画像
export function getLabelTypeIdFuzzy(params) {
  return request({
    url: '/advert/oss/label/list',
    method: 'get',
    params
  })
}

// 查询询价经销商的简单信息
export function getQueryPriceShopList(params) {
  return request({
    url: '/shop/oss/info/attr/getQueryPriceShopList',
    method: 'get',
    params
  })
}

/**
 * 获取审核人员的名字(oss所有人员)
 */
export function getOssAuditName(params) {
  return request({
    url: '/admin/auth/system/user/getSystemUsers',
    method: 'get',
    params
  })
}

/**
 * 高德城市信息
 */
export function getListAllMap(params) {
  return request({
    url: '/uic/oss/map/listAllMap',
    method: 'get',
    params
  })
}

/**
 * 广告位置配置-列表
 */
export function getAdSiteSetPositionList(params) {
  return request({
    url: '/advert/oss/adSiteSetPosition/list',
    method: 'get',
    params
  })
}

/**
 * 广告位置配置-新增
 */
export function AddAdSiteSetPositionList(data) {
  return request({
    url: '/advert/oss/adSiteSetPosition/add',
    method: 'post',
    data
  })
}

/**
 * 广告位置配置-编辑
 */
export function EditAdSiteSetPositionList(data) {
  return request({
    url: '/advert/oss/adSiteSetPosition/edit',
    method: 'post',
    data
  })
}

/**
 * 广告位置配置-删除
 */
export function RemoveAdSiteSetPositionList(data) {
  return request({
    url: '/advert/oss/adSiteSetPosition/remove',
    method: 'post',
    data
  })
}

/**
 * 选择计划-cpt校验
 */
export function campaignManageAdCount(params) {
  return request({
    url: '/advert/oss/campaignManage/adCount',
    method: 'get',
    params
  })
}

/**
 * 经销商广告订单
 */
export function advertOrderPickOrder(params) {
  return request({
    url: '/shop/oss/advert/order/pickOrder',
    method: 'get',
    params
  })
}

/**
 * app-client-conf 扩展配置 获取
 */
export function getAppConfigExtendByName(params) {
  return request({
    url: '/expands/oss/config/getAppConfigExtendByName',
    method: 'get',
    params
  })
}

/**
 * app-client-conf 扩展配置 设置
 */
export function setAppConfigExtendByName(data) {
  return request({
    url: '/expands/oss/config/setAppConfigExtendByName',
    method: 'post',
    data
  })
}
