import request from '@/utils/request'

//获取驾考城市列表
export function getClueCityList(query) {
  return request({
    url: '/shop/oss/clue/city/config/list',
    method: 'get',
    params: query
  })
}
//获取驾考列表每项详情
export function getClueCityDetail(query) {
  return request({
    url: '/shop/oss/clue/city/config/detail',
    method: 'get',
    params: query
  })
}
//保存，更新驾考信息
export function getClueCitySave(data) {
  return request({
    url: '/shop/oss/clue/city/config/save',
    method: 'post',
    data
  })
}
//查看操作记录
export function getRecordList(query) {
  return request({
    url: '/pirate/driver/exam/oss/record/list',
    method: 'get',
    params: query
  })
}
//查看所有题库版本
export function getVersionList(query) {
  return request({
    url: '/pirate/driver/exam/oss/version/list',
    method: 'get',
    params: query
  })
}
//获取指定版本信息
export function getVersionInfo(query) {
  return request({
    url: '/pirate/driver/exam/oss/version',
    method: 'get',
    params: query
  })
}
//导出图片
export function downloadImg(query) {
  return request({
    url: '/pirate/driver/exam/oss/img/download',
    method: 'get',
    params: query
  })
}
//导出题目
export function downloadTopic(query) {
  return request({
    url: '/pirate/driver/exam/oss/examdb/download',
    method: 'get',
    params: query
  })
}
//驾考题目对比列表
export function getContrastList(query) {
  return request({
    url: '/pirate/driver/exam/oss/audit/list',
    method: 'get',
    params: query
  })
}
//查询指定题目
export function getQuestion(query) {
  return request({
    url: '/pirate/driver/exam/oss/question',
    method: 'get',
    params: query
  })
}
//更新题目内容
export function editExam(data) {
  return request({
    url: '/pirate/driver/exam/oss/editExam',
    method: 'post',
    data
  })
}
//导入图片zip
export function uploadImage(data) {
  return request({
    url: `/pirate/driver/exam/oss/uploadImage`,
    method: 'post',
    transformRequest(data) {
      const formData = new FormData()
      for (const item in data) {
        formData.append(item, data[item])
      }
      return formData
    },
    contentType: false,
    // responseType: 'blob',
    data
  })
}
//导入题目db
export function uploadExam(data) {
  return request({
    url: `/pirate/driver/exam/oss/uploadExam`,
    method: 'post',
    transformRequest(data) {
      const formData = new FormData()
      for (const item in data) {
        formData.append(item, data[item])
      }
      return formData
    },
    contentType: false,
    // responseType: 'blob',
    data
  })
}
// 同步题库
export function createDriverExam(data) {
  return request({
    url: '/pirate/driver/exam/oss/createDriverExam',
    method: 'post',
    data,
    params: {
      hideErrorMsg: false
    }
  })
}

//待审核列表
export function drivingTypeMyList(query) {
  return request({
    url: '/shop/oss/shop/driving/audit/drivingType/myList',
    method: 'get',
    params: query
  })
}

//待审核数量
export function unAuditCount(query) {
  return request({
    url: '/shop/oss/shop/driving/audit/unAuditCount',
    method: 'get',
    params: query
  })
}

//驾考班级详情
export function drivingClassDetail(query) {
  return request({
    url: '/shop/oss/shop/driving/class/detail',
    method: 'get',
    params: query
  })
}

//拉取审核
export function drivingTypePull(data) {
  return request({
    url: '/shop/oss/shop/driving/audit/drivingType/pull',
    method: 'post',
    data
  })
}

//审核
export function auditDrivingType(data) {
  return request({
    url: '/shop/oss/shop/driving/audit/drivingType',
    method: 'post',
    data
  })
}

//驾考班型列表
export function drivingTypelist(query) {
  return request({
    url: '/shop/oss/shop/driving/drivingType/list',
    method: 'get',
    params: query
  })
}

//驾考班型-日志
export function drivingTypeLog(query) {
  return request({
    url: '/shop/oss/shop/driving/drivingType/log',
    method: 'get',
    params: query
  })
}

//驾考班型-添加日志
export function addDrivingType(data) {
  return request({
    url: '/shop/oss/shop/driving/drivingType/log/add',
    method: 'post',
    data
  })
}

//查看线上题库版本
export function getOnlieVersion(query) {
  return request({
    url: '/pirate/driver/exam/oss/exam/onlie/version',
    method: 'get',
    params: query
  })
}

//查看线上题库题目信息
export function getExamOssList(query) {
  return request({
    url: '/pirate/driver/exam/oss/exam/list',
    method: 'get',
    params: query
  })
}

//保存，更新驾考信息
export function saveExamUpdate(data) {
  return request({
    url: '/pirate/driver/exam/oss/update/exam',
    method: 'post',
    data
  })
}
//忽略更新驾考信息
export function ignoreExamUpdate(data) {
  return request({
    url: '/pirate/driver/exam/oss/ignore/new',
    method: 'post',
    data
  })
}

//回滚驾考信息
export function backExamVersion(data) {
  return request({
    url: '/pirate/driver/exam/oss/back/exam/version',
    method: 'post',
    data
  })
}
//查看线上城市题目数量
export function getExamSectionNum(query) {
  return request({
    url: '/pirate/driver/exam/oss/section/num',
    method: 'get',
    params: query
  })
}
//查看分类列表
export function getExamPointList(query) {
  return request({
    url: '/pirate/driver/exam/oss/point/list',
    method: 'get',
    params: query
  })
}
//查看城市分类列表
export function getSectionList(query) {
  return request({
    url: '/pirate/driver/exam/oss/section/list',
    method: 'get',
    params: query
  })
}

//驾考视频列表
export function getDriverExamVideoList(query) {
  return request({
    url: '/pirate/driver/exam/oss/video/list',
    method: 'get',
    params: query
  })
}

//驾校视频更新
export function postDriverExamVideoSave(data) {
  return request({
    url: '/pirate/driver/exam/oss/video/saveVideo',
    method: 'post',
    data
  })
}

//驾校视频封面更新
export function postDriverExamVideoCoverSave(data) {
  return request({
    url: '/pirate/driver/exam/oss/video/saveVideoCoverImg',
    method: 'post',
    data
  })
}

//驾校订单列表
export function getDriverExamOrderList(params) {
  return request({
    url: '/pirate/driver/exam/oss/order/list',
    method: 'get',
    params
  })
}

//驾校订单列表退款
export function postDriverExamOrderRefund(data) {
  return request({
    url: '/pirate/driver/exam/oss/order/refund',
    method: 'post',
    data
  })
}

//驾校订单列表退款 (apple 专用)
export function postDriverExamCompleteOrderRefund(data) {
  return request({
    url: '/pirate/driver/exam/oss/order/complete/refund/info',
    method: 'post',
    data
  })
}

/* 题库分类题目*/
export function getVipExamList(params) {
  return request.get(`/pirate/driver/exam/oss/vip/exam/list`, { params })
}
