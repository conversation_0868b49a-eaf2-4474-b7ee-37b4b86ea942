// http://10.33.72.188:8081  又澄本地

import request from '@/utils/request'
import { RESURL } from '@/utils/configData/config'

/**
 * 活动列表
 * @param query
 */
export function GetActList(query) {
  return request({
    url: '/activity/oss/operate/activity/listActs',
    method: 'get',
    params: query
  })
}
/**
 * 活动详情
 * @param query
 */
export function GetActDetail(query) {
  return request({
    url: '/activity/oss/operate/activity/actDetail',
    method: 'get',
    params: query
  })
}

/**
 * 模板列表
 * @param query
 */
export function GetTemList(query) {
  return request({
    url: '/activity/oss/operate/activity/listTemplets',
    method: 'get',
    params: query
  })
}
/**
 * 保存活动
 * @param query
 */
export function SaveActDetail(data) {
  return request({
    url: '/activity/oss/operate/activity/saveAct',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010401',
    data
  })
}
/**
 * 删除活动
 * @param query
 */
export function DeleteActDetail(data) {
  return request({
    url: '/activity/oss/operate/activity/delAct',
    method: 'post',
    data
  })
}
/**
 * 活动生效
 * @param query
 */
export function SetActive(data) {
  return request({
    url: '/activity/oss/operate/activity/setActive',
    method: 'post',
    data
  })
}
/**
 * 配置项
 * @param query
 */
export function GetSelectsConfig(query) {
  return request({
    url: `${RESURL}conf/person_club_selects_config.json?tdsourcetag=s_pctim_aiomsg`,
    method: 'get',
    params: query
  })
}

/**
 * 获取专题列表
 * @param query
 */
export function getActivityList(query) {
  return request({
    url: `/activity/oss/feed/config/overview`,
    method: 'get',
    params: query
  })
}
/**
 * 新增或更改专题
 * @param data
 */
export function addOrUpdateActivity(data) {
  return request({
    url: '/activity/oss/feed/config/save',
    method: 'post',
    data
  })
}
/**
 * 专题拖动排序
 * @param data
 */
export function sortActivity(data) {
  return request({
    url: '/activity/oss/feed/config/batchUpdateSorts',
    method: 'post',
    data
  })
}

/**
 * 标签搜索
 * @param query
 */
export function SearchLabel(data) {
  return request({
    // url: '/forum/oss/labelController/listLabelForSearch',
    url: '/forum/oss/otherController/listLabelByName',
    method: 'post',
    data
  })
}
/**
 * 保存模板
 * @param query
 */
export function SaveTemplet(data) {
  return request({
    // url: 'http://10.33.72.188:8012/activity/oss/operate/activity/saveActTemplet',
    url: '/activity/oss/operate/activity/saveActTemplet',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010401',
    data
  })
}
/**
 * 模板详情
 * @param query
 */
export function GetTempletDetail(data) {
  return request({
    url: '/activity/oss/operate/activity/getTempletJson',
    method: 'post',
    data
  })
}
/**
 * 文章搜索
 * @param query
 */
export function SearchArticle(data) {
  return request({
    url: '/forum/oss/businessEssayController/listEssayForSearch',
    method: 'post',
    data
  })
}
/**
 * 话题搜索
 * @param query
 */
export function SearchTopic(data) {
  return request({
    url: '/forum/oss/shortTopicController/listShortTopicForSearch',
    method: 'post',
    data
  })
}
/**
 * 批量新增话题
 * @param query
 */
export function AddShortTopic(data) {
  return request({
    url: '/forum/oss/shortTopicEssayController/batchAddShortTopicEssay',
    method: 'post',
    data
  })
}

/**
 * 投票列表
 * @param query
 */
export function GetVoteList(query) {
  return request({
    url: `/forum/oss/vote/voteList`,
    method: 'get',
    params: query
  })
}
/**
 * 更改投票状态
 * @param data
 */
export function PostUpdateVoteStatus(data) {
  return request({
    url: `/forum/oss/vote/updateVoteStatus`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010402',
    data
  })
}
/**
 * 投票详情
 * @param query
 */
export function GetVoteInfo(query) {
  return request({
    url: `/forum/oss/vote/voteInfo`,
    method: 'get',
    params: query
  })
}
/**
 * 编辑/保存投票详情
 * @param data
 */
export function PostUpdateVoteDetail(data) {
  return request({
    url: `/forum/oss/vote/saveOrUpdateVoteInfo`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010402',
    data
  })
}

/**
 * 评选列表
 * @param query
 */
export function GetSelectionList(query) {
  return request({
    url: `/forum/oss/selection/selectionList`,
    method: 'get',
    params: query
  })
}
/**
 * 更改评选状态
 * @param data
 */
export function PostUpdateSelectionStatus(data) {
  return request({
    url: `/forum/oss/selection/updateSelectionStatus`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010403',
    data
  })
}
/**
 * 投票详情
 * @param query
 */
export function GetSelectionInfo(query) {
  return request({
    url: `/forum/oss/selection/selectionInfo`,
    method: 'get',
    params: query
  })
}
/**
 * 创建/编辑评选详情
 * @param data
 */
export function PostSaveOrUpdateInfo(data) {
  return request({
    url: `/forum/oss/selection/saveOrUpdateInfo`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010403',
    data
  })
}

/**
 * 商家广告模板（九宫格抽奖）
 * @param data
 */
// 客资数据返回
export function activityGetListFactoryActs(query) {
  return request({
    url: `/activity/oss/operate/activity/listFactoryActs`,
    method: 'get',
    params: query
  })
}

//  oss配置广告
export function saveActAndTempletPrize(data) {
  return request({
    url: `/activity/oss/operate/activity/saveActAndTemplet`,
    method: 'post',
    data
  })
}

// 活动是否生效
export function activityUpdateActStatus(data) {
  return request({
    url: `/activity/oss/operate/activity/updateActStatus`,
    method: 'post',
    data
  })
}

// 客资数据返回
export function activityGetUserEnterRecord(query) {
  return request({
    url: `/activity/oss/operate/activity/getUserEnterRecord`,
    method: 'get',
    params: query
  })
}

// 中奖记录导出
export function activityGetPrizeUserRecord(query) {
  return request({
    url: `/activity/oss/operate/activity/getPrizeUserRecord`,
    method: 'get',
    params: query
  })
}

// 获取活动配置
export function activityGetConfig(query) {
  return request({
    url: `/activity/oss/operate/activity/getConfig`,
    method: 'get',
    params: query
  })
}

// 保存/修改 发邮件配置
export function activitySaveEmailConfig(data) {
  return request({
    url: `/activity/oss/operate/activity/saveEmailConfig`,
    method: 'post',
    data
  })
}

/**
 * 通用 日志列表
 * @param {*} query
 * @returns
 */
export function getLogList(
  query = {
    module: '', // 所属模块
    businessId: '', // 业务ID（如：sku,spu）
    page: '',
    limit: ''
  }
) {
  return request({
    url: `/log/oss/list`,
    method: 'get',
    params: query
  })
}

// 日志保存
export function saveLog(data) {
  return request({
    url: `/log/oss/save`,
    method: 'post',
    data
  })
}

// 内容池审核列表
export function getPageAuditPlacingTreasures(query) {
  return request({
    url: '/forum/oss/placingTreasuresController/pageAuditPlacingTreasures',
    method: 'get',
    params: query
  })
}

// 确认审核
export function confirmPlacingTreasures(data) {
  return request({
    url: '/forum/oss/placingTreasuresController/confirmPlacingTreasures',
    method: 'post',
    data
  })
}

// 审核内容池文章
export function auditPlacingEssay(data) {
  return request({
    url: '/forum/oss/placingTreasuresController/auditPlacingEssay',
    method: 'post',
    data
  })
}

// 打款记录导出
export function getListRecord(query) {
  return request({
    url: `/coins/oss/prize/listRecord`,
    method: 'get',
    params: query
  })
}

// ugc活动 文章列表
export function getUgcEssayList(query) {
  return request({
    url: `/forum/oss/essayActivity/ugcEssayList`,
    method: 'get',
    params: query
  })
}

// 取消文章资格(ugc活动)
export function refuseUgcEssay(data) {
  return request({
    url: '/forum/oss/essayActivity/refuseUgcEssay',
    method: 'post',
    data
  })
}

// 新增品牌
export function postAddCarBrand(data) {
  return request({
    url: '/activity/oss/admin/activity/addCarBrand',
    method: 'post',
    data
  })
}

// 新增品牌活动
export function postAddCarActivity(data) {
  return request({
    url: `/activity/oss/admin/activity/addCarActivity`,
    method: 'post',
    data
  })
}

// 删除品牌
export function postDelCarBrand(data) {
  return request({
    url: `/activity/oss/admin/activity/delCarBrand`,
    method: 'post',
    data
  })
}

// 删除品牌活动
export function postDelCarActivity(data) {
  return request({
    url: `/activity/oss/admin/activity/delCarActivity`,
    method: 'post',
    data
  })
}

// 品牌活动列表
export function getCarActivityList(query) {
  return request({
    url: `/activity/oss/admin/activity/getCarActivityList`,
    method: 'get',
    params: query
  })
}

// 品牌列表
export function getCarBrandList(query) {
  return request({
    url: `/activity/oss/admin/activity/getCarBrandList`,
    method: 'get',
    params: query
  })
}

// 排序
export function sortCarActivity(data) {
  return request({
    url: `/activity/oss/admin/activity/sortCarActivity`,
    method: 'post',
    data
  })
}

// ugc活动 获奖列表
export function getUgcPrizeList(params) {
  return request({
    url: `/forum/oss/essayActivity/ugcPrizeList`,
    method: 'get',
    params
  })
}

// ugc 修改领奖地址
export function updateOSSPrizeReceiveInfo(data) {
  return request({
    url: `/forum/oss/essayActivity/updateOSSPrizeReceiveInfo`,
    method: 'post',
    data
  })
}

// 公众号内容管理 - 分页
export function getOfficialAccountContentPage(params) {
  return request({
    url: `/forum/oss/officialAccountContent/getPage`,
    method: 'get',
    params
  })
}

// 公众号内容管理 - 新增
export function updateofficialAccountContentAdd(data) {
  return request({
    url: `/forum/oss/officialAccountContent/add`,
    method: 'post',
    data
  })
}

// 公众号内容管理 - 修改
export function updateofficialAccountContentUpdate(data) {
  return request({
    url: `/forum/oss/officialAccountContent/update`,
    method: 'post',
    data
  })
}

// 公众号内容管理 - 废弃
export function updateofficialAccountContentAbandon(data) {
  return request({
    url: `/forum/oss/officialAccountContent/abandon`,
    method: 'post',
    data
  })
}

// 公众号合作项目列表
export function getOfficialAccountProjectPage(params) {
  return request({
    url: `/factory/oss/factory/order/officialAccountProjectPage`,
    method: 'get',
    params
  })
}
