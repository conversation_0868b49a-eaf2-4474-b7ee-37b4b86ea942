import http from '@/utils/request'
import request from '@/utils/request'
// 租车相关api
class Rental {
  // 统计数据
  getCarAuditStatistics() {
    return http.get('/rental/oss/admin/car/audit/statistics')
  }

  // 拉取待审核
  postCarAuditPull({
    limit = 20,
    orderType = 1, // 1:正序 2：倒叙
  }) {
    return http.post('/rental/oss/admin/car/audit/pull', {
      limit,
      orderType,
    })
  }

  // 审核列表
  getCarAuditList(params) {
    return http.get('/rental/oss/admin/car/audit/list', {
      params,
    })
  }

  // 我的审核(今日)
  getCarAuditTodayList(params) {
    return http.get('/rental/oss/admin/car/audit/today/list', {
      params,
    })
  }

  // 出租车辆列表
  getCarList(params) {
    return http.get('/rental/oss/admin/car/list', {
      params,
    })
  }

  // 审核车辆详情
  getCarDetail(id) {
    return http.get('/rental/oss/admin/car/audit/detail', {
      params: {
        id,
      },
    })
  }

  // 出租车辆审核
  postCarAudit({ rentalCarId, status, auditor, reason = '' }) {
    const postData = {
      rentalCarId,
      status,
      auditor,
      reason,
    }
    return http.post('/rental/oss/admin/car/audit', postData)
  }
}

const rentalService = new Rental()

export default rentalService

/**
 * 租车车辆管理
 * @param query
 */
export function getRentalCarList(query) {
  return http({
    url: '/rental/oss/admin/car/list',
    method: 'get',
    params: query,
  })
}

/**
 * 租车车辆管理详情
 * @param query
 */
export function getRentalCarDetail(query) {
  return http({
    url: '/rental/oss/admin/car/detail',
    method: 'get',
    params: query,
  })
}

/**
 * 租车订单详情
 * @param query
 */
export function getOrderDetail(query) {
  return http({
    url: '/rental/oss/admin/order/detail',
    method: 'get',
    params: query,
  })
}

/**
 * 租车车辆管理审核
 * @param data
 */
export function rentalCarAudit(data) {
  return http({
    url: '/rental/oss/admin/car/audit',
    method: 'post',
    data,
    params: {
      hideErrorMsg: true,
    },
  })
}

/**
 * 保存日志
 * @param data
 */
export function orderSaveRecord(data) {
  return http({
    url: '/rental/oss/admin/order/saveRecord',
    method: 'post',
    data,
  })
}

/**
 * 运营取消租车订单
 * @param data
 */
export function orderCancel(data) {
  return http({
    url: '/rental/oss/admin/order/cancel',
    method: 'post',
    data,
  })
}

/**
 * 运营取消租车订单
 * @param data
 */
export function orderOssRefund(data) {
  return http({
    url: '/rental/oss/admin/order/ossRefund',
    method: 'post',
    data,
  })
}

/**
 * 同意/拒绝取消订单
 * @param data
 */
export function orderConfirmCancel(data) {
  return http({
    url: '/rental/oss/admin/order/confirm/cancel',
    method: 'post',
    data,
  })
}

/**
 * 拒绝取消订单前置检查
 * @param query
 */
export function refuseCancelCheck(query) {
  return http({
    url: '/rental/oss/admin/order/refuse/cancel/check',
    method: 'get',
    params: query,
  })
}

/**
 * 同意取消订单前置检查
 * @param query
 */
export function acceptCancelCheck(query) {
  return http({
    url: '/rental/oss/admin/order/accept/cancel/check',
    method: 'get',
    params: query,
  })
}

/**
 * 同意/拒绝提前还车
 * @param data
 */
export function orderConfirmBackCar(data) {
  return http({
    url: '/rental/oss/admin/order/confirm/backCar',
    method: 'post',
    data,
  })
}

/**
 * 拒绝提前还车前置检查
 * @param query
 */
export function refuseBackCarCheck(query) {
  return http({
    url: '/rental/oss/admin/order/refuse/backCar/check',
    method: 'get',
    params: query,
  })
}

/**
 * 同意提前还车前置检查
 * @param query
 */
export function acceptBackCarCheck(query) {
  return http({
    url: '/rental/oss/admin/order/accept/backCar/check',
    method: 'get',
    params: query,
  })
}

// 租车城市线索配置-列表
export function getRentalCityClue(query) {
  return request({
    url: '/rental/oss/clue/city/config/list',
    method: 'get',
    params: query
  })
}

// 租车城市线索配置-详情
export function getRentalCityClueDetail(query) {
  return request({
    url: '/rental/oss/clue/city/config/detail',
    method: 'get',
    params: query
  })
}

/**
 * 租车城市线索配置-保存
 * @param data
 */
export function saveRentalCityClue(data) {
  return request({
    url: '/rental/oss/clue/city/config/save',
    method: 'post',
    data
  })
}

// 查看商家租车线索开关
export function getRentalClueSwitch(query) {
  return request({
    url: '/shop/oss/merchant/info/query/rentalClueSwitch',
    method: 'get',
    params: query
  })
}

/**
 * 开关经销商租车基础线索
 * @param data
 */
export function updateRentalClueSwitch(data) {
  return request({
    url: '/shop/oss/merchant/info/update/rentalClueSwitch',
    method: 'post',
    data
  })
}
