import request from '@/utils/request'
import axios from 'axios'

// 获取package上传阿里云policy
export function getPkgUploadPolicy(params) {
  return request({
    url: '/pirate/media/oss/aliyun/getPkgUploadPolicy',
    method: 'get',
    params,
  })
}
// 服务端签名后直传
export async function aliyunUploadFile(file) {
  const { data } = await getPkgUploadPolicy()
  return new Promise((resolve, reject) => {
    if (data && data.code === 0) {
      const { accessId, host, policy, signature, expire, dir, url } = data.data
      const filePath = `${url}/${dir}/${file.name}` // 文件的全路径
      const params = new FormData()
      params.append('key', `${dir}/${file.name}`)
      params.append('policy', policy)
      params.append('OSSAccessKeyId', accessId)
      params.append('success_action_status', 200)
      params.append('signature', signature)
      params.append('file', file, file.name)
      axios
        .post(host, params, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => {
          console.log('res', res)
          if (res.status === 200) {
            const fileData = {
              url: filePath,
              name: file.name,
            }
            resolve(fileData)
          } else {
            reject('上传失败')
          }
        })
        .catch((err) => {
          console.log('err', err)
          reject('上传失败')
        })
    } else {
      reject('上传失败')
    }
  })
}
