import request from '@/utils/request'

export function loginByUsername(usercode, password, captchaCode) {
  const data = {
    usercode,
    password,
    captchaCode
  }
  return request({
    url: '/operate/login/newOssLogin.do',
    method: 'post',
    data,
  })
}

export function logout() {
  return request({
    url: '/login/logout',
    method: 'post',
  })
}

export function getUserInfo(token) {
  return request({
    url: '/user/info',
    method: 'get',
    params: { token },
  })
}

export function listAllMenu(data) {
  return request({
    url: '/operate/menu/authMenus.do',
    method: 'post',
    data,
  })
}

export function listFavcMenu(data) {
  return request({
    url: '/operate/menu/listFavcMenu.do',
    method: 'post',
    data,
  })
}

export function addFavcMenu(data) {
  return request({
    url: '/operate/menu/favcMenu.do',
    method: 'post',
    data,
  })
}

export function deleteFavcMenu(data) {
  return request({
    url: '/operate/menu/unFavcMenu.do',
    method: 'post',
    data,
  })
}

export function getCaptchaData(username) {
  return request({
    url: `/operate/login/captcha/${username}`,
    method: 'get'
  })
}
