import request from '@/utils/request'

// 广告查询
export function advertRequest(query) {
  return request({
    url: '/advert/oss/externalAdvertController/advertList',
    method: 'get',
    params: query
  })
}

// 广告保存
export function advertSave(data) {
  return request({
    url: '/advert/oss/externalAdvertController/saveAdvert',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10402',
    data
  })
}

// 广告详情
export function getAdvertising(data) {
  return request({
    url: '/advert/oss/externalAdvertController/v2/detail',
    method: 'post',
    data
  })
}

// 查询分页(页面弹框)
export function getBannerList(data) {
  return request({
    url: '/forum/oss/bannerController/getBannerList?_dc=1565749735724',
    method: 'post',
    data
  })
}

// 分页详情(页面弹框)
export function getBannerDetailById(data) {
  return request({
    url: '/forum/oss/bannerController/getBannerDetailById',
    method: 'post',
    data
  })
}

// 保存或修改(页面弹框)
export function saveBanner(data) {
  return request({
    url: '/forum/oss/bannerController/saveBanner',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}

// 删除经销商缓存
export function clearShopCache(query) {
  return request({
    url: '/forum/oss/cacheController/handlecache?',
    method: 'get',
    params: query
  })
}

// 已开通文章广告用户列表
export function getAdList(query) {
  return request({
    url: '/uic/oss/user/ad/list',
    method: 'get',
    params: query
  })
}

// 编辑用户广告开通状态（OSS用）
export function updateStatus(data) {
  return request({
    url: '/uic/oss/user/ad/updateStatus',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10405',
    data
  })
}

// 广告列表查询
export function getCustomAdList(query) {
  return request({
    url: '/activity/oss/custom/ad/list',
    method: 'get',
    params: query
  })
}

// 更新广告状态
export function adUpdateStatus(data) {
  return request({
    url: '/activity/oss/custom/ad/updateStatus',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10406',
    data
  })
}

/**
 * 广告列表
 * @param data
 */
export function businessAdList(query) {
  return request({
    url: '/advert/oss/externalAdvertController/v2/advertList',
    method: 'get',
    params: query
  })
}

// 广告详情
export function getAdvertisingDetails(query) {
  return request({
    url: '/advert/oss/externalAdvertController/v2/detail',
    method: 'get',
    params: query
  })
}

/**
 * 复制广告，暂未使用
 * @param data
 */
export function newAdCopy(data) {
  return request({
    url: '/advert/oss/externalAdvertController/user/v2/copy',
    method: 'post',
    data
  })
}

/**
 * 删除广告
 * @param data
 */
export function dealerAdvertising(data) {
  return request({
    url: '/advert/oss/externalAdvertController/user/delete',
    method: 'post',
    data
  })
}

/**
 * 保存广告
 * @param data
 */
export function saveAdvertisementData(data) {
  return request({
    url: '/advert/oss/externalAdvertController/user/v2/saveAdvert',
    method: 'post',
    data
  })
}

/**
 * 改变广告状态
 * @param data
 */
export function changeAdStatus(data) {
  return request({
    url: '/advert/oss/externalAdvertController/user/v2/status/update',
    method: 'post',
    data
  })
}

/**
 * 广告是否每页展示
 * @param data
 */
export function repeatAd(data) {
  return request({
    url: '/advert/oss/externalAdvertController/user/repeat/update',
    method: 'post',
    data
  })
}

// 查询小包配置
export function getPacketInfo(query) {
  return request({
    url: '/forum/oss/otherController.do?action=getPacketInfo',
    method: 'get',
    params: query
  })
}

/**
 * 广告是否每页展示
 * @param data
 */
export function savePacketInfo(data) {
  return request({
    url: '/forum/oss/otherController.do?action=savePacketInfo',
    method: 'post',
    data
  })
}

// 广告列表_商家版
export function getShopAdvertList(query) {
  return request({
    url: '/advert/oss/shop/advert/list',
    method: 'get',
    params: query
  })
}

/**
 * 广告详情_商家版
 * @param data
 */
export function getShopAdvertDetail(query) {
  return request({
    url: '/advert/oss/shop/advert/detail',
    method: 'get',
    params: query
  })
}

/**
 * 保存/编辑广告_商家版
 * @param data
 */
export function saveShopAdvert(data) {
  return request({
    url: '/advert/oss/shop/advert/save',
    method: 'post',
    data
  })
}

/**
 * 改变商家广告状态
 * @param data
 */
export function changeShopAdStatus(data) {
  return request({
    url: '/advert/oss/shop/advert/update/status',
    method: 'post',
    data
  })
}

/**
 * 删除商家广告
 * @param data
 */
export function dealerShopAdvertising(data) {
  return request({
    url: '/advert/oss/shop/advert/delete',
    method: 'post',
    data
  })
}

//广告方列表
export function getAdvertisingManageList(query) {
  return request({
    url: '/advert/oss/advertiserManage/list',
    method: 'get',
    params: query
  })
}
//第三方广告类型列表
export function getOtherAdvertisingTypeList(query) {
  return request({
    url: '/advert/oss/advertType/thirdLinkTypeList',
    method: 'get',
    params: query
  })
}
//新建广告方类型
export function postAdvertisingTypeAdd(data) {
  return request({
    url: '/advert/oss/advertiserManage/add',
    method: 'post',
    data
  })
}
//更新广告方名称及类型
export function postAdvertisingTypeUpdate(data) {
  return request({
    url: '/advert/oss/advertiserManage/update',
    method: 'post',
    data
  })
}
//将指定的广告方状态改为失效
export function postAdvertisingStatueClose(data) {
  return request({
    url: '/advert/oss/advertiserManage/close',
    method: 'post',
    data
  })
}
//将指定的广告方状态改为有效
export function postAdvertisingStatueOpen(data) {
  return request({
    url: '/advert/oss/advertiserManage/open',
    method: 'post',
    data
  })
}

//计划列表查询
export function getAdvertisingPlanList(query) {
  return request({
    url: '/advert/oss/campaignManage/list',
    method: 'get',
    params: query
  })
}

//关闭指定计划
export function postAdvertisingPlanClose(data) {
  return request({
    url: '/advert/oss/campaignManage/close',
    method: 'post',
    data
  })
}
//开启指定计划
export function postAdvertisingPlanOpen(data) {
  return request({
    url: '/advert/oss/campaignManage/open',
    method: 'post',
    data
  })
}
//创建计划
export function postAdvertisingPlanAdd(data) {
  return request({
    url: '/advert/oss/campaignManage/add',
    method: 'post',
    data
  })
}

//编辑计划
export function postAdvertisingPlanUpdate(data) {
  return request({
    url: '/advert/oss/campaignManage/update',
    method: 'post',
    data
  })
}

//获取广告列表
export function getAdvertisingList(query) {
  return request({
    url: '/advert/oss/adAdvertManage/list',
    method: 'get',
    params: query
  })
}
//新建广告
export function postAdvertisingAdd(data) {
  return request({
    url: '/advert/oss/adAdvertManage/add',
    method: 'post',
    data
  })
}
//获取广告三级页面
export function getAdvertisingLevelPage(query) {
  return request({
    url: '/advert/oss/adSiteSet/getTree',
    method: 'get',
    params: query
  })
}

//获取广告三级页面下一级
export function getAdvertisingNextLevelPage(query) {
  return request({
    url: '/advert/oss/adSiteSet/getNextLevel',
    method: 'get',
    params: query
  })
}

//更新广告状态
export function postAdvertisingStateUpdate(data) {
  return request({
    url: '/advert/oss/adAdvertManage/updateStatus',
    method: 'post',
    data
  })
}
//获取全部广告类型
export function getAdvertiserTypeAll(query) {
  return request({
    url: 'advert/oss/advertType/fullList',
    method: 'get',
    params: query
  })
}
// 广告方操作日志
export function getAdvertiserManageOperateLog(query) {
  return request({
    url: 'advert/oss/advertiserManage/operateLog',
    method: 'get',
    params: query
  })
}
// 广告计划操作日志
export function getCampaignManageOperateLog(query) {
  return request({
    url: 'advert/oss/campaignManage/operateLog',
    method: 'get',
    params: query
  })
}
// 广告操作日志
export function getAdAdvertManageOperateLog(query) {
  return request({
    url: '/advert/oss/adAdvertManage/operateLog',
    method: 'get',
    params: query
  })
}
