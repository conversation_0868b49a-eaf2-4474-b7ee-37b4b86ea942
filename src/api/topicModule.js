import request from '@/utils/request'

// 更新分类
export function updateBusinesCategory(data) {
  return request({
    url: '/forum/oss/categoryController/updateBusinesCategory',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010201',
    data,
  })
}

// 新增分类
export function saveBusinesCategory(data) {
  return request({
    url: '/forum/oss/categoryController/saveBusinesCategory',
    method: 'post',
    data,
  })
}

// 删除分类
export function deleteBusinesCategory(data) {
  return request({
    url: '/forum/oss/categoryController/deleteBusinesCategory',
    method: 'post',
    data,
  })
}
// 获取分类列表
export function listBusinesCategory(data) {
  return request({
    url: '/forum/oss/categoryController/listBusinesCategory',
    method: 'post',
    data,
  })
}
// 拖拽分类操作
export function changeBusinesCategorySort(data) {
  return request({
    url: '/forum/oss/categoryController/changeBusinesCategorySort',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010201',
    data,
  })
}
// 获取分类下列表
export function listCategoryRelation(data) {
  return request({
    url: '/forum/oss/categoryRelationController/listCategoryRelation',
    method: 'post',
    data,
  })
}
// 删除关联分类记录
export function deleteCategoryRelation(data) {
  return request({
    url: '/forum/oss/categoryRelationController/deleteCategoryRelation',
    method: 'post',
    beforeAlterTwo: false,
    menu: 'S1010201',
    data,
  })
}
// 批量添加关联信息
export function saveCategoryRelation(data) {
  return request({
    url: '/forum/oss/categoryRelationController/saveCategoryRelation',
    method: 'post',
    data,
  })
}
// 拖拽分类关联信息操作
export function changeCategoryRelationSort(data) {
  return request({
    url: '/forum/oss/categoryRelationController/changeCategoryRelationSort',
    method: 'post',
    beforeAlterTwo: false,
    menu: 'S1010201',
    data,
  })
}

// 合并话题
export function MergeShortTopic(data) {
  return request({
    url: '/forum/oss/shortTopicController/mergeShortTopic',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010203',
    data,
  })
}
