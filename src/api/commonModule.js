import request from '@/utils/request'
import { MANAGERURL, APIURL } from '@/utils/configData/config'

// oss字典表
export function baseCodelistByView(query) {
  return request({
    url: '/operate/baseCode/getBaseCodelistByView.do',
    method: 'get',
    params: query
  })
}

// 视频说明书页签
export function getVideoPageSign(query) {
  return request({
    url: '/carport/oss/car/search/video',
    method: 'get',
    params: query
  })
}

// 图片弹框页签
export function getImgPageSign(query) {
  return request({
    url: '/carport/oss/car/search/img',
    method: 'get',
    params: query
  })
}

// 手机号码解码，用户账号相关
export function decodeMobile1(query) {
  return request({
    url: `${MANAGERURL}uic/oss/member/decryptMobile`,
    method: 'get',
    params: query
  })
}

// 手机号码解码，行家、用户实名认证
export function decodeMobile2(query) {
  return request({
    url: `${MANAGERURL}uic/oss/certApply/decryptMobile`,
    method: 'get',
    params: query
  })
}

// 获取省份列表
export async function getProvinceListMap() {
  if (localStorage.getItem('provinceMap')) {
    return JSON.parse(localStorage.getItem('provinceMap'))
  }
  const { data } = await request.get(`${APIURL}uic/map/listMap`)
  if (data.data && data.data.list && data.data.list.length) {
    localStorage.setItem('provinceMap', JSON.stringify(data.data.list))
    return data.data.list
  }
  return []
}

// 获取市列表
export async function getCityListMap(provinceCode) {
  const { data } = await request.get(`${APIURL}uic/map/listMap`, {
    params: {
      provinceCode
    }
  })
  return data.data.list || []
}

// 获取区列表
export async function getDistinctListMap(provinceCode, cityCode) {
  const { data } = await request.get(`${APIURL}uic/map/listMap`, {
    params: {
      provinceCode,
      cityCode
    }
  })
  return data.data.list || []
}

// 获取实时省市数据
export function getExpandsMapCity(query) {
  return request({
    url: '/expands/oss/map/list/city',
    method: 'get',
    params: query
  })
}
