import request from '@/utils/request'
// import { APIURL } from '@/utils/configData/config'

// 车库图片zip包上传
export function uploadZip(data) {
  return request({
    url: `/pirate/media/oss/uploadZip`,
    method: 'post',
    transformRequest(data) {
      const formData = new FormData()
      // if (typeof data === 'function') {
      for (const item in data) {
        formData.append(item, data[item])
      }
      // } else {
      //   formData.append(data.file.name.split('.')[0] || 'test', data.file);
      // }
      return formData
    },
    contentType: false,
    data,
  })
}

export function deleteImageList(data) {
  return request({
    url: '/carport/oss/car/delTransferImage',
    method: 'post',
    data,
  })
}

export function deleteImagePool(data) {
  return request({
    url: '/carport/oss/car/delTransferFolder',
    method: 'post',
    data,
  })
}

export function updateImageStatus(data) {
  return request({
    url: '/carport/oss/car/updateTransferImage',
    method: 'post',
    data,
  })
}

export function getImagePool(query) {
  return request({
    url: '/carport/oss/car/listTransferFolder',
    method: 'get',
    params: query,
  })
}
