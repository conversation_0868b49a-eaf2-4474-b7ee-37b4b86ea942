import axios from 'axios'
import request from '@/utils/request'
import { APIURL } from '@/utils/configData/config'
import { $aliOss } from '@/utils/svc/aliOss'

// export function getLatenessDetailSize(params) {
//   // axios.defaults.headers = {
//   //  'X-Requested-With': 'XMLHttpRequest',
//   //  'Access-Control-Allow-Origin': '*',
//   //  'Access-Control-Allow-Method': 'POST,GET,OPTIONS',
//   //  'Content-Type': 'multipart/form-data'
//   // }
//   return axios({
//     // nginx反向代理
//     url: '/api/forum/public/upload/file_one_pkg.do',
//     data: params,
//     method: 'post'
//   })
// }

export function postPush(params) {
  // axios.defaults.headers = {
  //  'X-Requested-With': 'XMLHttpRequest',
  //  'Access-Control-Allow-Origin': '*',
  //  'Access-Control-Allow-Method': 'POST,GET,OPTIONS',
  //  'Content-Type': 'multipart/form-data'
  // }
  return axios({
    // nginx反向代理
    url: '/api/push',
    data: params,
    method: 'post'
  })
}

// export function uploadPackage(params) {
//   return axios({
//     // nginx反向代理
//     url: '/expands/oss/promote/channel/uploadPackage',
//     method: 'post',
//     data: params
//   })
// }

// 废弃废弃了
export async function uploadGIF(data) {
  // return axios({
  //   // nginx反向代理
  //   url: '/expands/oss/img/uploadGIF?file',
  //   method: 'post',
  //   data: params
  // })
  let res = {}
  const option = {
    file: data.get('file') || data,
    onProgress: (e) => {},
    onSuccess: (response) => {
      res = {
        data: {
          code: 0,
          data: response.imgUrl
        }
      }
    },
    onError: (error) => {
      res = {
        data: {
          code: -1,
          msg: '上传失败'
        }
      }
    }
  }
  await $aliOss.ossUploadImage(option)
  return res
}

export function uploadFile(params) {
  return axios({
    // nginx反向代理
    url: '/expands/oss/file/upload?file',
    method: 'post',
    data: params
  })
}
// 上传文件，pdf
export function uploadConf(businessType) {
  return request({
    url: `/pirate/media/oss/aliyun/upload/conf?businessType=${businessType}`,
    method: 'get'
  })
}
export function getAliyunSts(params) {
  return request({
    url: `${APIURL}pirate/media/aliyun/v2/sts`,
    method: 'post',
    data: params
  })
}
