import request from '@/utils/request'

/**
 * 车辆过滤(车辆管理->车辆属性列表) // 品牌列表接口
 * @param query
 */
export function searchBrandList(data) {
  return request({
    url: '/carport/oss/brand/all/list',
    method: 'post',
    data,
  })
}

/**
 * 车辆状态更新
 * @param data
 */
export function updatePromote(data) {
  return request({
    url: '/carport/oss/brand/updateIsStatus',
    method: 'post',
    data,
  })
}

/**
 *
 * 品牌舆情开关
 * @export
 * @param {*} data
 * @returns
 */
export function updateOpinionStatus(data) {
  return request({
    url: '/carport/oss/brand/updateOpinionStatus',
    method: 'post',
    data,
  })
}

export function getBrandDetail(brandId) {
  return request({
    url: '/carport/oss/brand/info/detail',
    method: 'post',
    data: { brandId },
  })
}

export function brandSave(data) {
  return request({
    url: '/carport/oss/brand/info',
    method: 'post',
    data,
  })
}

export function clearCache(data) {
  return request({
    url: '/carport/oss/brand/list/push',
    method: 'post',
    data,
  })
}

/**
 * 车系列表
 * @param query
 */
export function getBrandSeriesList(query) {
  return request({
    url: '/carport/oss/brandseries/all/list',
    method: 'get',
    params: query,
  })
}

/**
 * 删除车系
 * @param data
 */
export function deleteBrandSeries(data) {
  return request({
    url: '/carport/oss/brandseries/info/delete',
    method: 'post',
    data,
  })
}

/**
 * 保存车系
 * @param data
 */
export function saveBrandSeries(data) {
  return request({
    url: '/carport/oss/brandseries/save',
    method: 'post',
    data,
  })
}

/**
 * 根据首字母查询品牌列表
 * @param data
 */
export function getListBrandsByAleph(query) {
  return request({
    url: '/carport/oss/brand/listBrandsByAleph',
    method: 'get',
    params: query,
  })
}

/**
 * 舆情品牌列表
 * @param data
 */
export function getOpinionBrandList(query) {
  return request({
    url: '/carport/oss/brand/opinionBrandList',
    method: 'get',
    params: query,
  })
}

/**
 * 舆情品牌列表(新)
 * @param data
 */
export function getListSentimentBrand(query) {
  return request({
    url: '/carport/oss/brand/listSentimentBrand',
    method: 'get',
    params: query,
  })
}
