import request from '@/utils/request'

/**
 * 新车销售车辆订单列表
 * @param query
 */
export function GetTradeOrderList(query) {
  return request({
    url: `trade/oss/order/index/list`,
    method: 'get',
    params: query
  })
}

/**
 * 车辆核销模块订单列表删除操作
 * @param data
 */
export function DeleteTradeOrder(data) {
  return request({
    url: `trade/oss/newCar/order/delete`,
    method: 'post',
    data
  })
}

/**
 * 可售车辆列表
 * @param query
 */
export function GetTradeCarList(query) {
  return request({
    url: `trade/oss/car/list`,
    method: 'get',
    params: query
  })
}

/**
 * 可售车辆详情
 * @param query
 */
export function GetTradeCarDetail(query) {
  return request({
    url: `trade/oss/car/detail/info`,
    method: 'get',
    params: query
  })
}

/**
 * 新增或修改可售车辆
 * @param data
 */
export function SaveTradeCarDetail(data) {
  return request({
    url: `trade/oss/car/detail/save`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S11002',
    data
  })
}

/**
 * 修改在售状态
 * @param data
 */
export function UpdateTradeCarValid(data) {
  return request({
    url: `trade/oss/car/update/valid`,
    method: 'post',
    data
  })
}

/**
 * 删除在售车辆
 * @param data
 */
export function DeleteTradeCar(data) {
  return request({
    url: `trade/oss/car/delete`,
    method: 'post',
    data
  })
}

/**
 * 查询补贴、佣金比例区间
 * @param query
 */
export function GetShopRatio(query) {
  return request({
    url: `trade/oss/shop/ratio/byBrandId`,
    method: 'get',
    params: query
  })
}

/**
 * 查询补贴、佣金金额区间
 * @param query
 */
export function GetShopPriceRatio(query) {
  return request({
    url: `trade/oss/shop/ratio/byCarId`,
    method: 'get',
    params: query
  })
}

/**
 * 查询品牌销售范围
 * @param query
 */
export function GetShopAreaRange(query) {
  return request({
    url: `trade/oss/shop/areaRange/byBrandId`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商品牌列表
 * @param query
 */
export function GetShopApplyBrand(query) {
  return request({
    url: `carport/oss/shop/apply/brand/byShopId`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商品牌关系列表
 * @param query
 */
export function GetShopBrandList(query) {
  return request({
    url: `trade/oss/shop/brand/list`,
    method: 'get',
    params: query
  })
}

/**
 * 新增/修改品牌关系
 * @param data
 */
export function SaveShopBrand(data) {
  return request({
    url: `trade/oss/shop/brand/save`,
    method: 'post',
    beforeAlterTwo: false,
    menu: 'S10601',
    data
  })
}

/**
 * 新车销售订单退款
 * @param data
 */
export function ReFundOrder(data) {
  return request({
    url: `trade/oss/order/shop/refund`,
    method: 'post',
    data
  })
}

/**
 * 新车售卖活动 新增/编辑活动
 * @param data
 */
export function SaveNewCarActivity(data) {
  return request({
    url: `activity/oss/newCar/activity/save`,
    method: 'post',
    data
  })
}

/**
 * 活动删除
 * @param data
 */
export function DeleteNewCarActivity(data) {
  return request({
    url: `activity/oss/newCar/activity/delete`,
    method: 'post',
    data
  })
}

/**
 * 新车售卖活动 活动列表
 * @param query
 */
export function GetActivityList(query) {
  return request({
    url: `activity/oss/newCar/activity/list`,
    method: 'get',
    params: query
  })
}

/**
 * 新车售卖活动 活动详情
 * @param query
 */
export function GetActivityDetail(query) {
  return request({
    url: `activity/oss/newCar/activity/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 模糊搜索在售车辆
 * @param query
 */
export function GetSearchCar(query) {
  return request({
    url: `carport/oss/car/searchCar`,
    method: 'get',
    params: query
  })
}

/**
 * 优惠券可售车辆配置列表
 * @param query
 */
export function GetCouponList(query) {
  return request({
    url: `trade/oss/coupon/list`,
    method: 'get',
    params: query
  })
}

/**
 * 修改状态或删除优惠券是否有效
 * @param data
 */
export function updateStatusCoupon(data) {
  return request({
    url: `trade/oss/coupon/updateStatus`,
    method: 'post',
    data
  })
}

/**
 * 优惠券可售车辆配置详情
 * @param query
 */
export function GetCouponDetail(query) {
  return request({
    url: `trade/oss/coupon/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 新增或编辑优惠券
 * @param data
 */
export function SaveCoupon(data) {
  return request({
    url: `trade/oss/coupon/save`,
    method: 'post',
    data
  })
}

/**
 * 优惠券商家成单价格比例
 * @param query
 */
export function GetFindProportion(query) {
  return request({
    url: `/trade/oss/coupon/find/proportion`,
    method: 'get',
    params: query
  })
}

/**
 * 优惠券可售车配置----查看范围
 * @param query
 */
export function GetCouponAreaRange(query) {
  return request({
    url: `/trade/oss/coupon/areaRange`,
    method: 'get',
    params: query
  })
}

/**
 * 优惠券发票审核列表
 * @param query
 */
export function GetCouponInvoiceList(query) {
  return request({
    url: `trade/oss/coupon/invoiceList`,
    method: 'get',
    params: query
  })
}

/**
 * 购新车发票审核列表
 * @param query
 */
export function GetInvoiceReviewList(query) {
  return request({
    url: `/trade/oss/invoiceAuditPool//unAudit/list`,
    method: 'get',
    params: query
  })
}

/**
 * 实时更新基本信息
 * @param query
 */
export function GetUpdatesDate(query) {
  return request({
    url: `/trade/oss/invoiceAuditPool/statisticsData`,
    method: 'get',
    params: query
  })
}

/**
 * 优惠券审核拉取数据接口
 * @param data
 */
export function PostPendingReview(data) {
  return request({
    url: `/trade/oss/invoiceAuditPool//pull/data`,
    method: 'post',
    data,
    params: data
  })
}

/**
 * 优惠券发票审核 通过、不通过
 * @param data
 */
export function AuditCoupon(data) {
  return request({
    url: `/trade/oss/invoiceAuditPool/audit`,
    method: 'post',
    data,
    params: {
      hideErrorMsg: true
    }
  })
}

/**
 * 优惠券发票号码和编号编辑
 * @param data
 */
export function EditCoupon(data) {
  return request({
    url: `trade/oss/coupon/edit`,
    method: 'post',
    data,
    params: {
      hideErrorMsg: true
    }
  })
}

/**
 * 优惠券列表
 * @param query
 */
export function GetCouponOrderList(query) {
  return request({
    url: `trade/oss/coupon/orderList`,
    method: 'get',
    params: query
  })
}

/**
 * 优惠券返现结款列表
 * @param query
 */
export function GetCouponRemitList(query) {
  return request({
    url: `trade/oss/coupon/remit/list`,
    method: 'get',
    params: query
  })
}

/**
 * 优惠券返现结款列表--打款成功
 * @param data
 */
export function couponRemitSuccess(data) {
  return request({
    url: `trade/oss/coupon/remit/success`,
    method: 'post',
    data
  })
}

/**
 * 优惠券返现结款列表--打款失败
 * @param data
 */
export function couponRemitFail(data) {
  return request({
    url: `trade/oss/coupon/remit/fail`,
    method: 'post',
    data
  })
}

/**
 * 直通车订单列表
 * @param query
 */
export function GetDirectTrainOrderList(query) {
  return request({
    url: `trade/oss/direct/train/balance/order/list`,
    method: 'get',
    params: query
  })
}

/**
 * 线索包列表
 * @param query
 */
export function GetClueOrderList(query) {
  return request({
    url: `shop/oss/clue/list`,
    method: 'get',
    params: query
  })
}

/**
 * 付费类型
 * @param query
 */
export function GetFeeTypes(query) {
  return request({
    url: `shop/oss/query/price/packet/listByTypes`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商账户体系列表
 * @param query
 */
export function GetAccountSystemList(query) {
  return request({
    url: `shop/oss/account/system`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商账户体系列表--体验金充值
 * @param data
 */
export function RechargeExperience(data) {
  return request({
    url: `shop/oss/account/rechargeExperience`,
    method: 'post',
    data
  })
}

/**
 * 经销商账户体系列表--体验金扣除
 * @param data
 */
export function DeductionExperience(data) {
  return request({
    url: `shop/oss/account/deduction`,
    method: 'post',
    data
  })
}

/**
 * 经销商可提现账户明细列表
 * @param query
 */
export function GetAccountPriceDetail(query) {
  return request({
    url: `shop/oss/account/priceDetail`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商可提现账户明细列表
 * @param query
 */
export function GetAccountPriceTotal(query) {
  return request({
    url: `shop/oss/account/priceTotal`,
    method: 'get',
    params: query
  })
}

/**
 * OSS商家账户明细汇总
 * @param query
 */
export function GetShopSerialsList(query) {
  return request({
    url: `pirate/user/credit/oss/shop/wallet/record/list`,
    method: 'get',
    params: query
  })
}

/**
 * OSS商家账户明细汇总-财务报表接口
 * @param query
 */
export function GetShopSerialsListFinance(query) {
  return request({
    url: `/pirate/user/credit/oss/shop/wallet/record/list`,
    method: 'get',
    params: query
  })
}
/**
 * OSS商家账户明细汇总_金额合计
 * @param query
 */
export function GetShopSerialsAccountTotal(query) {
  return request({
    url: `pirate/user/credit/oss/shop/wallet/record/accountTotal`,
    method: 'get',
    params: query
  })
}

/**
 * 直通车余额账户明细列表
 * @param query
 */
export function GetAccountDirectTrainDetail(query) {
  return request({
    url: `shop/oss/account/directTrainDetail`,
    method: 'get',
    params: query
  })
}
/**
 * 直通车余额账户变动合计
 * @param query
 */
export function GetAccountDirectTotal(query) {
  return request({
    url: `shop/oss/account/directTrainTotal`,
    method: 'get',
    params: query
  })
}

/**
 * 直通车体验金账户明细列表
 * @param query
 */
export function GetAccountExperienceGoldDetail(query) {
  return request({
    url: `shop/oss/account/experienceGoldDetail`,
    method: 'get',
    params: query
  })
}

/**
 * 直通车体验金账户合计
 * @param query
 */
export function GetAccountExperienceTotal(query) {
  return request({
    url: `shop/oss/account/experienceGoldTotal`,
    method: 'get',
    params: query
  })
}

/**
 * 创建优惠券----查询车型列表
 * @param query
 */
export function GetCouponSearchListv2(query) {
  return request({
    url: `trade/oss/coupon/search/list/v2`,
    method: 'get',
    params: query
  })
}

/**
 * 创建优惠券----查询经销商列表
 * @param query
 */
export function GetCouponSearchDetail(query) {
  return request({
    url: `trade/oss/coupon/search/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 创建优惠券---平台赠送优惠券
 * @param data
 */
export function couponCreateFreeOrder(data) {
  return request({
    url: `trade/oss/coupon/createFreeOrder`,
    method: 'post',
    data
  })
}

/**
 * 金币账户明细列表
 * @param query
 */
export function GetGoldBalanceList(query) {
  return request({
    url: `shop/oss/gold/balance/detail/list`,
    method: 'get',
    params: query
  })
}

/**
 * 金币充值订单列表
 * @param query
 */
export function GetGoldOrderList(query) {
  return request({
    url: `shop/oss/service/order/getServiceList`, //
    method: 'get',
    params: {
      ...query,
      serviceTypeList: 12 // 这个接口 单独查询12
    }
  })
}

/**
 * 金币充值订单列表 汇总
 * @param query
 */
export function GetGoldOrderListSum(query) {
  return request({
    url: `shop/oss/gold/balance/order/sum`,
    method: 'get',
    params: query
  })
}

/**
 * 金币订单退款
 * @param data
 */
export function ReGoldFundOrder(data) {
  return request({
    url: `shop/oss/gold/balance/order/refund`,
    method: 'post',
    data
  })
}

/**
 * 金币订单作废
 * @param data
 */
export function ReGoldVoidOrder(data) {
  return request({
    url: `shop/oss/service/order/serviceInvalid`,
    method: 'post',
    data
  })
}

/**
 * 回复审核列表
 * @param query
 */
export function getReplyAuditList(query) {
  return request({
    url: `uic/oss/autoReply/list`,
    method: 'get',
    params: query
  })
}
/**
 * 审核自动回复
 * @param data
 */
export function auditReply(data) {
  return request({
    url: `uic/oss/autoReply/audit`,
    method: 'post',
    data
  })
}

/**
 * 车辆管理列表
 * @param query
 */
export function getSaleCarList(query) {
  return request({
    url: `shop/oss/sale/car/list`,
    method: 'get',
    params: query
  })
}

/**
 * 车辆上下架接口
 * @param data
 */
export function saleCarUpdateStatus(data) {
  return request({
    url: `shop/oss/sale/car/downUp`,
    method: 'post',
    data
  })
}

/**
 * 单个/批量删除
 * @param data
 */
export function saleCarDelete(data) {
  return request({
    url: `shop/oss/sale/car/dels`,
    method: 'post',
    data
  })
}

/**
 * 添加车辆信息
 * @param data
 */
export function saleCarSave(data) {
  return request({
    url: `shop/oss/sale/car/save`,
    method: 'post',
    data
  })
}

/**
 * 更新车辆信息
 * @param data
 */
export function saleCarUpdate(data) {
  return request({
    url: `shop/oss/sale/car/update`,
    method: 'post',
    data
  })
}

/**
 * 车辆详情
 * @param query
 */
export function saleCarDetail(query) {
  return request({
    url: `shop/oss/sale/car/detail`,
    method: 'get',
    params: query
  })
}
/**
 * 售车协议列表
 * @param query
 */
export function getSaleCarAgreementList(query) {
  return request({
    url: `shop/oss/sale/car/agreement/list`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商销售协议详情
 * @param query
 */
export function saleCarAgreementDetail(query) {
  return request({
    url: `shop/oss/sale/car/agreement/detail`,
    method: 'get',
    params: query
  })
}

/**
 * 保存经销商售车协议
 * @param query
 */
export function saleCarAgreementSave(data) {
  return request({
    url: `/shop/oss/sale/car/agreement/save`,
    method: 'POST',
    data
  })
}

/**
 * 更新经销商售车协议
 * @param query
 */
export function saleCarAgreementUpdate(data) {
  return request({
    url: `/shop/oss/sale/car/agreement/update`,
    method: 'post',
    data
  })
}

/**
 * 查询简短会员经销商信息
 * @param query
 */
export function saleCarAgreementSimpleShopList(query) {
  return request({
    url: `shop/oss/sale/car/agreement/simpleShopList`,
    method: 'get',
    params: query
  })
}

/**
 * 经销商可售列表
 * @param query
 */
export function getSaleCarAgreementAllowList(query) {
  return request({
    url: `shop/oss/sale/car/agreement/allow/list`,
    method: 'get',
    params: query
  })
}

/**
 * 保存可售车型
 * @param query
 */
export function saleCarAgreementAllowSave(data) {
  return request({
    url: `shop/oss/sale/car/agreement/allow/save`,
    method: 'post',
    data
  })
}

/**
 * 删除经销商可售车型
 * @param query
 */
export function saleCarAgreementAllowDelete(data) {
  return request({
    url: `shop/oss/sale/car/agreement/allow/delete`,
    method: 'post',
    data
  })
}

/**
 * 查询车型简短信息
 * @param query
 */
export function getGoodsAllSimpleList(query) {
  return request({
    url: `carport/oss/goods/all/simple/list`,
    method: 'get',
    params: query
  })
}

/**
 * 删除经销商销售协议
 * @param query
 */
export function saleCarAgreementDelete(data) {
  return request({
    url: `shop/oss/sale/car/agreement/delete`,
    method: 'post',
    data
  })
}

/**
 * 售车订单列表
 * @param query
 */
export function getSaleOrderList(query) {
  return request({
    url: 'shop/oss/sale/order/list',
    method: 'get',
    params: query
  })
}

/**
 * 售车订单数量统计
 * @param query
 */
export function getSaleOrderStatusCount(query) {
  return request({
    url: 'shop/oss/sale/order/orderStatusCount',
    method: 'get',
    params: query
  })
}

/**
 * 售车订单详情
 * @param query
 */
export function getSaleOrderDetail(query) {
  return request({
    url: 'shop/oss/sale/order/detail',
    method: 'get',
    params: query
  })
}

/**
 * 发货
 * @param query
 */
export function addSaleCarLogisticsShip(data) {
  return request({
    url: `shop/oss/sale/order/logistics/ship`,
    method: 'post',
    data
  })
}

/**
 * 新增物流
 * @param query
 */
export function addSaleCarLogistics(data) {
  return request({
    url: `/shop/oss/sale/order/logistics/add`,
    method: 'post',
    data
  })
}

/**
 * 编辑物流
 * @param query
 */
export function editSaleCarLogisticsShip(data) {
  return request({
    url: `shop/oss/sale/order/logistics/update`,
    method: 'post',
    data
  })
}

/**
 * 线下支付，扭转订单状态为已支付
 * @param query
 */
export function shopSaleOrderOffLinePay(data) {
  return request({
    url: `shop/oss/sale/order/offLinePay`,
    method: 'post',
    data
  })
}

/**
 * 获取订单收货地址
 * @param query
 */
export function getSaleGetOrderAddress(query) {
  return request({
    url: 'shop/oss/sale/order/getOrderAddress',
    method: 'get',
    params: query
  })
}

/**
 * 修改订单收货地址
 * @param query
 */
export function shopSaleOrderChangeOrderAddress(data) {
  return request({
    url: `shop/oss/sale/order/changeOrderAddress`,
    method: 'post',
    data
  })
}

/**
 * 修改订单备注
 * @param query
 */
export function shopSaleOrderRemark(data) {
  return request({
    url: `shop/oss/sale/order/remark`,
    method: 'post',
    data
  })
}

/**
 * 审核退款
 * @param query
 */
export function shopSaleOrderAuth(data) {
  return request({
    url: `shop/oss/sale/order/auth`,
    method: 'post',
    data
  })
}

/**
 * 确认收货
 * @param query
 */
export function shopSaleOrderConfirmReceipt(data) {
  return request({
    url: `shop/oss/sale/order/confirmReceipt`,
    method: 'post',
    data
  })
}

/**
 * 支付渠道、支付方式
 * @param query
 */
export function getSaleOrderGetPayOption(query) {
  return request({
    url: 'shop/oss/sale/order/getPayOption',
    method: 'get',
    params: query
  })
}
