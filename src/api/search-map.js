import request from '@/utils/request'
import { APIURL } from '@/utils/configData/config'

// // 地图搜索位置 高德
// export function searchAddress(data) {
//   return request({
//     url: 'https://restapi.amap.com/v3/assistant/inputtips',
//     method: 'get',
//     params: data
//   })
// }

// 获取省份列表
export async function getProvinceListMap() {
  if (localStorage.getItem('provinceMap')) {
    return JSON.parse(localStorage.getItem('provinceMap'))
  }
  const { data } = await request.get(`${APIURL}uic/map/listMap`)
  if (data.data && data.data.list && data.data.list.length) {
    localStorage.setItem('provinceMap', JSON.stringify(data.data.list))
    return data.data.list
  }
  return []
}

// 获取市列表
export async function getCityListMap(provinceCode) {
  const { data } = await request.get(`${APIURL}uic/map/listMap`, {
    params: {
      provinceCode
    }
  })
  return data.data.list || []
}

// 获取区列表
export async function getDistinctListMap(provinceCode, cityCode) {
  const { data } = await request.get(`${APIURL}uic/map/listMap`, {
    params: {
      provinceCode,
      cityCode
    }
  })
  return data.data.list || []
}
