import request from '@/utils/request'
import { APIURL, LIVEURL } from '@/utils/configData/config'

// 查询(文章搜索新)
// export function searchArticleList(data) {
//   return request({
//     url: '/forum/oss/businessEssayController/listAllEssayForSearch',
//     method: 'post',
//     data
//   })
// }
export function searchArticleList(params) {
  return request({
    url: '/forum/oss/businessEssayController/listEssayFromEs',
    method: 'get',
    params
  })
}

export function checkEssayExpired(params) {
  return request({
    url: '/forum/oss/placingTreasuresController/checkEssayExpired',
    method: 'get',
    params
  })
}
/**
 * 用户标签关系列表
 * @param query
 */
export function listOssUserLabel(query) {
  return request({
    url: '/forum/oss/otherController/listOssUserLabel',
    method: 'get',
    params: query
  })
}
/**
 * 标签名称搜索
 * @param data
 */
export function searchLabel(data) {
  return request({
    url: '/forum/oss/otherController/listLabelByName',
    method: 'post',
    data
  })
}
/**
 * 搜索接口（搜索v1.7.3）
 * @param query
 */
export function searchController(query) {
  return request({
    url: `${APIURL}forum/public/searchController.do?action=2000101V3`,
    method: 'get',
    params: query
  })
}
/**
 * 搜索品牌（搜索v1.1.2）
 * @param data
 */
export function searchBrand(data) {
  return request({
    url: '/carport/oss/brand/listBrandBySearch',
    method: 'post',
    params: data
  })
}
/**
 * 搜索款型
 * @param params
 */
export function searchGoodsCarName(params) {
  return request({
    url: '/carport/oss/car/listGoodsCarName',
    method: 'get',
    params
  })
}
/**
 * 搜索年代款
 * @param data
 */
export function searchCar(data) {
  return request({
    url: '/carport/oss/car/carNameList',
    method: 'get',
    params: data
  })
}
/**
 * 获取车系列表
 * @param data
 */
export function getSeriesList(params) {
  return request({
    url: '/carport/oss/brandseries/all/list',
    method: 'get',
    params
  })
}
/**
 * 文章下推送
 * @param data
 */
export function pushForum(data) {
  return request({
    url: '/uic/oss/message/pushForum',
    method: 'post',
    data
  })
}
/**
 * 添加优质置顶
 * @param data
 */
export function saveRecommend(data) {
  return request({
    url: '/forum/oss/remmdController/saveRecommend',
    method: 'post',
    data
  })
}
/**
 * 取消优质，置顶
 * @param data
 */
export function closeRecommend(data) {
  return request({
    url: '/forum/oss/remmdController/closeRecommend',
    method: 'post',
    data
  })
}
/**
 * 文章推荐搜索
 * @param data
 */
export function findRecommand(data) {
  return request({
    url: '/forum/oss/remmdController/findRecommand',
    method: 'post',
    data
  })
}
/**
 * 批量修改文章状态
 * @param query
 */
export function updateEssayStatus(data) {
  return request({
    url: '/forum/oss/businessEssayController/updateBusinessEssayStatus',
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    data,
    timeout: 10000
  })
}
/**
 * 保存发布者标签映射关系
 * @param data
 */
export function saveOssUserLabel(data) {
  return request({
    url: '/forum/oss/otherController/saveOssUserLabel',
    method: 'post',
    data
  })
}
/**
 * 删除发布者标签关系
 * @param data
 */
export function delOssUserLabel(data) {
  return request({
    url: '/forum/oss/otherController/delOssUserLabel',
    method: 'post',
    data
  })
}

/**
 * 搜索用户模糊搜索
 * @param data
 */
export function searchVagueUser(data) {
  return request({
    url: '/operate/baseCode/searchAuther.do',
    method: 'post',
    data
  })
}

/**
 * 搜索用户模糊搜索
 * @param data
 */
export function establishLabel(data) {
  return request({
    url: '/forum/oss/otherController/addLabel',
    method: 'post',
    data
  })
}

/**
 * 更新用户个性标签
 * @param data
 */
export function updataUserPersonalityLabel(data) {
  return request({
    url: '/forum/oss/homeModuleRelationController/updatePersonLabel',
    method: 'post',
    beforeAlterTwo: false,
    data
  })
}

/**
 * 非模块下话题列表
 * @param data
 */
export function topicListUnderNoModule(data) {
  return request({
    url: '/forum/oss/homeModuleRelationController/listHomeModuleRelationNotExist',
    method: 'post',
    data
  })
}

/**
 * 内容同步百家号
 * @param data
 */
export function publishEssayToBJH(data) {
  return request({
    url: `/forum/oss/businessEssayController/publishEssayToBJH`,
    method: 'post',
    beforeAlterFirst: false,
    menu: '********',
    data
  })
}

/**
 * 小视频集添加
 * @param data
 */
export function saveMomentsModule(data) {
  return request({
    url: `/forum/oss/homeModuleRelationController/saveMomentsModule`,
    method: 'post',
    data
  })
}

/**
 * 小视频集删除
 * @param data
 */
export function delMomentsModule(data) {
  return request({
    url: `/forum/oss/homeModuleRelationController/delMomentsModule`,
    method: 'post',
    data
  })
}

/**
 * 添加动态审核优质置顶
 * @param data
 */
export function saveDynamicRecommend(data) {
  return request({
    url: '/forum/oss/recommendController/saveBuesinessRecommendDetail',
    method: 'post',
    data
  })
}

/**
 * 圈子列表
 * @param data
 */
export function getselectTopics(params) {
  return request({
    url: '/forum/oss/shortTopicController/selectTopics',
    method: 'get',
    params
  })
}

/**
 * 小组件圈子增加推荐圈子
 * @param data
 */
export function saveRelation(data) {
  return request({
    url: `/forum/oss/homeModuleRelationController/saveRelation`,
    method: 'post',
    data
  })
}

/**
 * 推荐圈子列表
 * @param data
 */
export function getHomeShortTopicList(params) {
  return request({
    url: '/forum/oss/shortTopicController/homeShortTopicList',
    method: 'get',
    params
  })
}

/**
 * 添加首页推荐圈子
 * @param data
 */
export function addHomeShortTopic(data) {
  return request({
    url: `/forum/oss/shortTopicController/addHomeShortTopic`,
    method: 'post',
    data
  })
}

/**
 * 删除首页推荐圈子
 * @param data
 */
export function delHomeShortTopic(data) {
  return request({
    url: `/forum/oss/shortTopicController/delHomeShortTopic`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010206',
    data
  })
}

/**
 * 圈子集合下的子圈子
 * @param data
 */
export function getSelectTopics(params) {
  return request({
    url: `forum/oss/homeModuleController/selectTopics`,
    method: 'get',
    params
  })
}

/**
 * 修改圈子排序
 * @param data
 */
export function updateSort(data) {
  return request({
    url: `/forum/oss/shortTopicController/updateSort`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010205',
    data
  })
}

/**
 * 修改圈子状态
 * @param data
 */
export function updateStatus(data) {
  return request({
    url: `/forum/oss/shortTopicController/updateStatus`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010205',
    data
  })
}

/**
 * 圈子fans
 * @param data
 */
export function getShortTopicFans(data) {
  return request({
    url: `/forum/oss/shortTopicController/getShortTopicFans`,
    method: 'post',
    data
  })
}

/**
 * 获取圈子详情
 * @param data
 */
export function getShortTopicDetailById(params) {
  return request({
    url: `/forum/oss/shortTopicController/getShortTopicDetailById`,
    method: 'get',
    params
  })
}

/**
 * 创建或修改圈子详情
 * @param data
 */
export function saveShortTopic(data) {
  return request({
    url: `/forum/oss/shortTopicController/saveShortTopic`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010205',
    data
  })
}
/**
 * 话题置顶/取消置顶
 * @param data
 */
export function topOperate(data) {
  return request({
    url: `/forum/oss/shortTopicController/topOperate`,
    method: 'post',
    data
  })
}
/**
 * 移除话题
 * @param data
 */
export function removeContentFromTopic(data) {
  return request({
    url: `/forum/oss/shortTopicController/removeContentFromTopic`,
    method: 'post',
    data
  })
}

/**
 * 新随机发布者搜索
 * @param data
 */
export function getSearchVest(params) {
  return request({
    url: `uic/operate/vest/searchVest`,
    method: 'get',
    params
  })
}

/**
 * 评论列表
 * @param params
 */
export function getReplyList(params) {
  return request({
    url: `/reply/oss/auth/replyList`,
    method: 'get',
    params
  })
}

/**
 * 评论审核列表
 * @param params
 */
export function getReplyAuditList(params) {
  return request({
    url: `/reply/oss/auth/waitAuditReplyList`,
    method: 'get',
    params
  })
}

/**
 * 评论拉取数据
 * @param params
 */
export function getReplyPullData(data) {
  return request({
    url: `/reply/oss/auth/pullData`,
    method: 'post',
    data
  })
}

/**
 * 评论审核统计
 * @param params
 */
export function getReplyAuditStatistics(params) {
  return request({
    url: `/reply/oss/auth/statistics`,
    method: 'get',
    params
  })
}

/**
 * 评论待审核数
 * @param params
 */
export function getReplyAuditCount(params) {
  return request({
    url: `/reply/oss/auth/poolWaitAuditDataCount`,
    method: 'get',
    params
  })
}

/**
 * 评论批量审核
 * @param data
 */
export function batchUpdateReplyStatus(data) {
  return request({
    url: `/reply/oss/auth/batchUpdate`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010108',
    data
  })
}

/**
 * 收益权益管理列表
 * @param params
 */
export function getPermissionList(params) {
  return request({
    url: `/uic/oss/user/essay/income/permissionList`,
    method: 'get',
    params
  })
}

/**
 * 收益权益状态更改
 * @param data
 */
export function updateStatusPermission(data) {
  return request({
    url: `/uic/oss/user/essay/income/updatePermissionStatus`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010801',
    data
  })
}

/**
 * 查询收益文章明细
 * @param params
 */
export function getIncomeEssayList(params) {
  return request({
    url: `/uic/oss/user/essay/income/essayList`,
    method: 'get',
    params
  })
}

/**
 * 修改文章异常状态
 * @param data
 */
export function updateActiveStatus(data) {
  return request({
    url: `/uic/oss/user/essay/income/updateActiveStatus`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010802',
    data
  })
}

/**
 * 提现明细列表
 * @param params
 */
export function getWithdrawList(params) {
  return request({
    url: `/uic/oss/user/essay/income/withdrawList`,
    method: 'get',
    params
  })
}

/**
 * 批量更新提现状态
 * @param data
 */
export function updateBatchUpdateWithdrawStatus(data) {
  return request({
    url: `/uic/oss/user/essay/income/batchUpdateWithdrawStatus`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010803',
    data
  })
}

/**
 * 更新税后金额
 * @param data
 */
export function updateWithdrawRealAmount(data) {
  return request({
    url: `/uic/oss/user/essay/income/updateWithdrawRealAmount`,
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010803',
    data
  })
}

/**
 * 更改签约状态
 * @param params
 */
export function updateIncomeStatus(params) {
  return request({
    url: `/uic/oss/user/essay/income/updateSignStatus`,
    method: 'post',
    params
  })
}

/**
 * 获取入库规则
 * @param params
 */
export function getStoreRule(params) {
  return request({
    url: `/forum/oss/essayPushRepo/auto/configList`,
    method: 'get',
    params
  })
}

/**
 * 添加入库规则配置
 * itemId(用户id/车辆id), type(1=用户 2=车辆)
 * @param params
 */
export function saveStoreRule(query) {
  return request({
    url: `/forum/oss/essayPushRepo/auto/saveConf`,
    method: 'post',
    params: query
  })
}

/**
 * 删除不入库规则
 * id（不是itemId）
 * @param params
 */
export function deleteStoreRule(query) {
  return request({
    url: `/forum/oss/essayPushRepo/auto/deleteConf`,
    method: 'post',
    params: query
  })
}

/**
 * oss置顶评论
 * @param params
 */
export function topReply(data) {
  return request({
    url: `/reply/oss/auth/topReply`,
    method: 'post',
    data
  })
}

/**
 * 创作者提现明细
 * @param params
 */
export function withdrawDetail(params) {
  return request({
    url: `/uic/oss/user/essay/income/withdraw/detail`,
    method: 'get',
    params
  })
}
/**
 * 新创作者提现明细
 * @param params
 */
export function newWithdrawDetail(params) {
  return request({
    url: `/pirate/user/credit/oss/creatorWallet/orders/getList`,
    method: 'get',
    params
  })
}

/**
 * 新创作者明细
 * @param params
 */
export function newBalanceDetail(data) {
  return request({
    url: `/pirate/user/credit/oss/creatorWallet/serials/getList`,
    method: 'post',
    data
  })
}

/**
 * 创作者明细
 * @param params
 */
export function balanceDetail(params) {
  return request({
    url: `/uic/oss/user/essay/income/balance/detail`,
    method: 'get',
    params
  })
}

/**
 * 确认提现成功
 * @param data
 */
export function withdrawSucceed(data) {
  return request({
    url: `/uic/oss/user/essay/income/withdraw/success`,
    method: 'post',
    data
  })
}

/**
 * 新确认提现成功
 * @param data
 */
export function newWithdrawSucceed(data) {
  return request({
    url: `/pirate/user/credit/oss/creatorWallet/withdraw/complete`,
    method: 'post',
    data
  })
}

/**
 * 重试提现
 * @param data
 */
export function withdrawTautology(data) {
  return request({
    url: `/uic/oss/user/essay/income/withdraw/again`,
    method: 'post',
    data
  })
}

/**
 * 文章审核记录
 * @param params
 */
export function essayAuthRecord(params) {
  return request({
    url: `/forum/oss/essay/auth/record`,
    method: 'get',
    params
  })
}
/**
 * 爬虫文章图片失效列表
 * @param params
 */
export function getEssayFailList(params) {
  return request({
    url: `/forum/oss/essay/crawler/list`,
    method: 'get',
    params
  })
}
/**
 * 修改文章状态
 * @param params
 */
export function updateSingleEssayStatus(data) {
  return request({
    url: `/forum/oss/businessEssayController/updateEssayStatus`,
    method: 'post',
    data
  })
}

/**
 * 文章审核记录
 * @param params
 */
export function getQueryEassyOperateLog(params) {
  return request({
    url: `/forum/oss/businessEssayController/queryEassyOperateLog`,
    method: 'get',
    params
  })
}

/**
 * 获取详情数据
 * @param params
 */
export function getQueryEassyDetail(params) {
  return request({
    url: `${LIVEURL}/forum/wap/public/businessEssayController.do`,
    method: 'get',
    params
  })
}
/**
 * 特邀作者活动列表页
 * @param params
 */
export function getAuthorActivityList(params) {
  return request({
    url: `/forum/oss/author/activity/activityList`,
    method: 'get',
    params
  })
}
/**
 * 特邀作者打款
 * @param params
 */
export function postAuthorSettleBonus(data) {
  return request({
    url: `/forum/oss/author/activity/settleBonus`,
    method: 'post',
    data
  })
}
/**
 *   特邀作者列表
 * @param params
 */
export function getAuthorList(params) {
  return request({
    url: `/forum/oss/author/activity/authorList`,
    method: 'get',
    params
  })
}
/**
 * 特邀作者新增活动
 * @param params
 */
export function postAuthorBatchAdd(data) {
  return request({
    url: `/forum/oss/author/activity/batchAdd`,
    method: 'post',
    data
  })
}
/**
 * 特邀作者更新活动
 * @param params
 */
export function postAuthorBatchUpdate(data) {
  return request({
    url: `/forum/oss/author/activity/batchUpd`,
    method: 'post',
    data
  })
}
/**
 *   特邀作者日志
 * @param params
 */
export function getAuthorLog(params) {
  return request({
    url: `/forum/oss/author/activity/handle/log`,
    method: 'get',
    params
  })
}

/**
 *   舆情监测状态更新
 * @param data
 */
export function postOpinionMonitor(data) {
  return request({
    url: `/forum/oss/businessEssayController/updatePublicOpinionMonitor`,
    method: 'post',
    data
  })
}

/**
 *   舆情监测状态更新 （内容宝活动）
 * @param data
 */
export function postOpinionMonitorAct(data) {
  return request({
    url: `/forum/oss/essay/activity/updateActPublicOpinionMonitor`,
    method: 'post',
    data
  })
}

/**
 *  获取评论列表
 * @param data
 */
export function getReplyControllerList(params) {
  return request({
    url: `/reply/oss/replyController/list`,
    method: 'get',
    params
  })
}

/**
 *  发布评论
 * @param data
 */
export function postAddReply(data) {
  return request({
    url: `/reply/oss/replyController/addReply`,
    method: 'post',
    data
  })
}

/**
 *  增加点赞
 * @param data
 */
export function postAddPraise(data) {
  return request({
    url: `/forum/oss/replyController/addPraise`,
    method: 'post',
    data
  })
}
/**
 *  获取评论列表
 * @param data
 */
export function getCommentDetails(params) {
  return request({
    url: `/reply/oss/replyController/detail`,
    method: 'get',
    params
  })
}

/**
 *  oss取消屏蔽评论
 * @param data
 */
export function postCancelBlockReply(data) {
  return request({
    url: `/reply/oss/auth/cancelBlockReply`,
    method: 'post',
    data
  })
}

/**
 *  oss屏蔽评论
 * @param data
 */
export function postBlockReply(data) {
  return request({
    url: `/reply/oss/auth/blockReply`,
    method: 'post',
    data
  })
}

/**
 *  获取创作活动评论列表
 * @param data
 */
export function actReplyList(params) {
  return request({
    url: `/reply/oss/auth/actReplyList`,
    method: 'get',
    params
  })
}

/**
 *  车库资讯/动态管理  保存/编辑
 * @param data
 */
export function garageInfoSaveOrUpdate(data) {
  return request({
    url: `/forum/oss/essayGoodsTopRel/saveOrUpdate`,
    method: 'post',
    data
  })
}

/**
 *  车库资讯/动态管理  取消置顶
 * @param data
 */
export function garageInfoCancelTop(data) {
  return request({
    url: `/forum/oss/essayGoodsTopRel/cancelTop`,
    method: 'post',
    data
  })
}

/**
 *  车库资讯/动态管理  列表
 * @param params
 */
export function listTopPage(params) {
  return request({
    url: `/forum/oss/essayGoodsTopRel/listTopPage`,
    method: 'get',
    params
  })
}

/**
 *  车库资讯/动态管理  排序
 * @param data
 */
export function garageInfoSortTop(data) {
  return request({
    url: `/forum/oss/essayGoodsTopRel/sortTop`,
    method: 'post',
    data
  })
}

/**
 *  进行中的活动
 * @param data
 */
export function getOngoingActList(params) {
  return request({
    url: `/forum/oss/essay/activity/ongoingActList`,
    method: 'get',
    params
  })
}

/**
 *  文章阅读时长系数趋势
 * @param params
 */
export function getEssayReadFactor(params) {
  return request({
    url: `/uic/oss/user/essay/income/getEssayReadFactor`,
    method: 'get',
    params
  })
}

/**
 *  指定用户发布内容关联专题活动 保存编辑
 * @param data
 */
export function postSaveOrUpdateUser(data) {
  return request({
    url: `/forum/oss/authorActRel/saveOrUpdate`,
    method: 'post',
    data
  })
}

/**
 *  查询活动关联作者信息
 * @param data
 */
export function getByActId(params) {
  return request({
    url: `/forum/oss/authorActRel/getByActId`,
    method: 'get',
    params
  })
}

/**
 *  「内容」问答后台管理
 * @param data
 */
export function postAcceptAnswer(data) {
  return request({
    url: `/forum/oss/businessEssayController/acceptAnswer`,
    method: 'post',
    data
  })
}
/**
 *  内容池导入文章
 * @param data
 */
export function importPlacingEssayList(data) {
  return request({
    url: `/forum/oss/placingTreasuresController/importPlacingEssayList`,
    method: 'post',
    data
  })
}
