import request from '@/utils/request'

// 首页小组件列表
export function listHomeBlockSet(query) {
  return request({
    url: '/pirate/feed/oss/feed/listHomeBlockSet',
    method: 'get',
    params: query,
  })
}

// 首页小组件保存生效/生效
export function saveHomeBlockSet(data) {
  return request({
    url: '/pirate/feed/oss/feed/saveHomeBlockSet',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S1010602',
    data,
  })
}
