/* eslint-disable */
import { saveAs } from 'file-saver'
import XLSX from 'xlsx-js-style'

function generateArray(table) {
  var out = []
  var rows = table.querySelectorAll('tr')
  var ranges = []
  for (var R = 0; R < rows.length; ++R) {
    var outRow = []
    var row = rows[R]
    var columns = row.querySelectorAll('td')
    for (var C = 0; C < columns.length; ++C) {
      var cell = columns[C]
      var colspan = cell.getAttribute('colspan')
      var rowspan = cell.getAttribute('rowspan')
      var cellValue = cell.innerText
      if (cellValue !== '' && cellValue == +cellValue) cellValue = +cellValue

      //Skip ranges
      ranges.forEach(function (range) {
        if (
          R >= range.s.r &&
          R <= range.e.r &&
          outRow.length >= range.s.c &&
          outRow.length <= range.e.c
        ) {
          for (var i = 0; i <= range.e.c - range.s.c; ++i) outRow.push(null)
        }
      })

      //Handle Row Span
      if (rowspan || colspan) {
        rowspan = rowspan || 1
        colspan = colspan || 1
        ranges.push({
          s: {
            r: R,
            c: outRow.length
          },
          e: {
            r: R + rowspan - 1,
            c: outRow.length + colspan - 1
          }
        })
      }

      //Handle Value
      outRow.push(cellValue !== '' ? cellValue : null)

      //Handle Colspan
      if (colspan) for (var k = 0; k < colspan - 1; ++k) outRow.push(null)
    }
    out.push(outRow)
  }
  return [out, ranges]
}

function datenum(v, date1904) {
  if (date1904) v += 1462
  var epoch = Date.parse(v)
  return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000)
}

function sheet_from_array_of_arrays(data, opts) {
  var ws = {}
  var range = {
    s: {
      c: 10000000,
      r: 10000000
    },
    e: {
      c: 0,
      r: 0
    }
  }
  for (var R = 0; R != data.length; ++R) {
    for (var C = 0; C != data[R].length; ++C) {
      if (range.s.r > R) range.s.r = R
      if (range.s.c > C) range.s.c = C
      if (range.e.r < R) range.e.r = R
      if (range.e.c < C) range.e.c = C
      var cell = {
        v: data[R][C]
      }
      if (cell.v == null) continue
      var cell_ref = XLSX.utils.encode_cell({
        c: C,
        r: R
      })

      if (typeof cell.v === 'number') cell.t = 'n'
      else if (typeof cell.v === 'boolean') cell.t = 'b'
      else if (cell.v instanceof Date) {
        cell.t = 'n'
        cell.z = XLSX.SSF._table[14]
        cell.v = datenum(cell.v)
      } else cell.t = 's'

      ws[cell_ref] = cell
    }
  }
  if (range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range)
  return ws
}

function Workbook() {
  if (!(this instanceof Workbook)) return new Workbook()
  this.SheetNames = []
  this.Sheets = {}
}

function s2ab(s) {
  var buf = new ArrayBuffer(s.length)
  var view = new Uint8Array(buf)
  for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
  return buf
}

export function export_table_to_excel(id) {
  var theTable = document.getElementById(id)
  var oo = generateArray(theTable)
  var ranges = oo[1]

  /* original data */
  var data = oo[0]
  var ws_name = 'SheetJS'

  var wb = new Workbook(),
    ws = sheet_from_array_of_arrays(data)

  /* add ranges to worksheet */
  // ws['!cols'] = ['apple', 'banan'];
  ws['!merges'] = ranges

  /* add worksheet to workbook */
  wb.SheetNames.push(ws_name)
  wb.Sheets[ws_name] = ws

  var wbout = XLSX.write(wb, {
    bookType: 'xlsx',
    bookSST: false,
    type: 'binary'
  })

  saveAs(
    new Blob([s2ab(wbout)], {
      type: 'application/octet-stream'
    }),
    'test.xlsx'
  )
}

export function export_json_to_excel(
  header,
  data,
  filename,
  sheetname, //需要多填写一个sheet名
  autoWidth = true,
  bookType = 'xlsx'
) {
  sheetname && sheetname.length
    ? export_json_to_excel_sheet({
        header,
        data,
        filename,
        sheetname,
        autoWidth,
        bookType
      })
    : export_json_to_excel_new({
        header,
        data,
        filename,
        autoWidth,
        bookType
      })
}

export function export_json_to_excel_new({
  title = '',
  header,
  data,
  filename,
  isSheetname,
  autoWidth = true,
  bookType = 'xlsx',
  multiHeader = [],
  merges = [],
  link = '',
  linkTitle = '',
  link_X = ''
} = {}) {
  /* original data */
  filename = filename || 'excel-list'
  data = [...data]
  data.unshift(header)

  for (let i = multiHeader.length - 1; i > -1; i--) {
    data.unshift(multiHeader[i])
  }

  const AZ = [
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z'
  ]
  if (title) {
    data.unshift([title])
    const num = header.length
    if (num > 1) {
      let i1 = Math.floor(num / 26)
      let i2 = num % 26
      if (i1 && !i2) {
        i1 = i1 - 1
        i2 = 26
      }
      let end = `${i1 ? AZ[i1 - 1] : ''}${AZ[i2 - 1]}1`
      const always = `A1:${end}`
      merges.unshift(always)
    }
  }

  var ws_name = 'SheetJS'
  var wb = new Workbook(),
    ws = sheet_from_array_of_arrays(data)

  if (merges.length > 0) {
    if (!ws['!merges']) ws['!merges'] = []
    merges.forEach((item) => {
      ws['!merges'].push(XLSX.utils.decode_range(item))
    })
  }

  if (autoWidth) {
    /*设置worksheet每列的最大宽度*/
    const colWidth = data.map((row) =>
      row.map((val) => {
        /*先判断是否为null/undefined*/
        if (val == null) {
          return {
            wch: 10
          }
        } else {
          const isTitle = val === title
          const value = val.toString()
          let length = 0
          const flag = value.indexOf('\n') !== -1
          if (flag) {
            value.split('\n').map((_) => {
              const num = _.replace(/[^\x00-\xff]/g, '**').length
              length = num > length ? num : length
            })
          } else {
            length = value.replace(/[^\x00-\xff]/g, '**').length
          }
          const interval = Math.ceil(length / 10)
          const colw = length + interval
          return {
            wch: colw > 80 && !isTitle ? 80 : colw
          }
        }
      })
    )
    /*以第一行为初始值, 有title以第二行为初始值*/
    let result = title ? colWidth[1] : colWidth[0]
    for (let i = 1; i < colWidth.length; i++) {
      for (let j = 0; j < colWidth[i].length; j++) {
        if (result[j]['wch'] < colWidth[i][j]['wch']) {
          result[j]['wch'] = colWidth[i][j]['wch']
        }
      }
    }
    if (title) {
      const title_w = colWidth[0][0]['wch']
      let all_w = 0
      result.forEach((_) => {
        all_w += _.wch
      })
      if (all_w < title_w) {
        const addNum = Math.ceil((title_w - all_w) / result.length)
        result.forEach((_) => {
          _.wch = _.wch + addNum
        })
      }
    }
    ws['!cols'] = result
  }

  if (title) {
    const rowCount = title.split('\n').length
    ws['!rows'] = [{ hpx: rowCount * 16 }]
  }

  const dataType = (data) => {
    const type = typeof data
    if (type !== 'object') {
      return type
    }
    return Object.prototype.toString
      .call(data)
      .replace(/^\[object (\S+)\]$/, '$1')
  }

  for (const key in ws) {
    if (Object.hasOwnProperty.call(ws, key)) {
      if (dataType(ws[key]) === 'Object') {
        ws[key].s = {
          alignment: {
            wrapText: 1 // 自动换行
          }
        }
        if (
          link &&
          linkTitle &&
          link_X &&
          key.replace(/[^a-zA-Z]/g, '') === link_X
        ) {
          if (ws[key].v && ws[key].v !== linkTitle) {
            ws[key].l = {
              Target: link + ws[key].v,
              Tooltip: link + ws[key].v
            }
            ws[key].s = {
              alignment: {
                wrapText: 1 // 自动换行
              },
              font: {
                underline: true,
                color: { rgb: '0000FF' }
              }
            }
          }
        }
      }
    }
  }

  /* add worksheet to workbook */
  wb.SheetNames.push(ws_name)
  wb.Sheets[ws_name] = ws

  if (isSheetname) {
    return ws
  }

  var wbout = XLSX.write(wb, {
    bookType: bookType,
    bookSST: false,
    type: 'binary'
  })
  saveAs(
    new Blob([s2ab(wbout)], {
      type: 'application/octet-stream'
    }),
    `${filename}.${bookType}`
  )
}

export function export_json_to_excel_sheet({
  title = '',
  header,
  data,
  filename,
  sheetname,
  autoWidth = true,
  bookType = 'xlsx'
} = {}) {
  var wb = new Workbook()
  sheetname.map((item) => {
    wb.SheetNames.push(item.name)
    wb.Sheets[item.name] = export_json_to_excel_new({
      header: item.header,
      data: item.data,
      filename: item.name,
      isSheetname: true
    })
  })
  var wbout = XLSX.write(wb, {
    bookType: bookType,
    bookSST: false,
    type: 'binary'
  })
  saveAs(
    new Blob([s2ab(wbout)], {
      type: 'application/octet-stream'
    }),
    `${filename}.${bookType}`
  )
}
