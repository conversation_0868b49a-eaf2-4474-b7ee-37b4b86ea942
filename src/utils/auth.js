import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

export function getToken() {
  return Cookies.get(TokenKey) || JSON.parse(localStorage.userInfo || '{}').ossToken
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getUid() {
  return JSON.parse(localStorage.userInfo || '{}').userid
}

export function getUseName() {
  return JSON.parse(localStorage.userInfo || '{}').username
}
