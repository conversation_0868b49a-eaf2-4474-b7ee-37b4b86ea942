/** 格式化时间
 *  @param {string} date 需要格式化的时间 2017-11-11、2017/11/11、linux time 毫秒
 *  @param {string} fmt 想要格式化的格式
 *         YYYY-MM-DD HH:mm:ss    YYYY/MM/DD HH:mm:ss
 */
// 废弃，全部采用公用方法库
export function format(date, fmt = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) {
    return ''
  }
  if (typeof date === 'string') {
    date = new Date(date.replace(/-/g, '/'))
  }
  if (typeof date === 'number') {
    date = new Date(date)
  }
  var o = {
    'M+': date.getMonth() + 1,
    'D+': date.getDate(),
    'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
    'H+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds(),
  }
  var week = {
    0: '\u65e5',
    1: '\u4e00',
    2: '\u4e8c',
    3: '\u4e09',
    4: '\u56db',
    5: '\u4e94',
    6: '\u516d',
  }
  if (/(Y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    )
  }
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (RegExp.$1.length > 1
        ? RegExp.$1.length > 2
          ? '\u661f\u671f'
          : '\u5468'
        : '') + week[date.getDay() + '']
    )
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      )
    }
  }
  return fmt
}

export function formatNumber(n) {
  const str = n.toString()
  return str[1] ? str : `0${str}`
}

/** 获取倒计时剩余时间
 *  @param {date} endTime 截止时间
 *  @return {object} {dd: '天', hh: '时', mm: '分', ss: '秒'}
 */
export function remainTime(endTime) {
  const ts = endTime - new Date() // 计算剩余的毫秒数
  let dd = parseInt(ts / 1000 / 60 / 60 / 24, 10) // 计算剩余的天数
  let hh = parseInt((ts / 1000 / 60 / 60) % 24, 10) // 计算剩余的小时数
  let mm = parseInt((ts / 1000 / 60) % 60, 10) // 计算剩余的分钟数
  let ss = parseInt((ts / 1000) % 60, 10) // 计算剩余的秒数
  if (ts <= 0) {
    // dd = 0; hh = 0; mm = 0; ss = 0;
    return false
  }
  dd = checkTime(dd)
  hh = checkTime(hh)
  mm = checkTime(mm)
  ss = checkTime(ss)

  function checkTime(i) {
    if (i < 10) {
      i = '0' + i
    }
    return i
  }

  return { dd: dd, hh: hh, mm: mm, ss: ss }
}

/**
 * 废弃，建议使用：changeDate
 *   add(new Date(), 60)
 * @param {Date} date
 * @param {Number} second
 */
export function add(date, second) {
  let temp = null

  if (!date) {
    return ''
  }
  if (!second) {
    return date
  }
  if (typeof date === 'string') {
    temp = new Date(date.replace(/-/g, '/'))
  }
  if (typeof date === 'number') {
    temp = new Date(date)
  }
  temp = date

  date = new Date(temp.getTime() + second * 1000)

  return date
}

/**
 * 改变时间
 *   changeDate(new Date(), 7)
 * @param {Date} date
 * @param {Number} day 可以为负数
 * @param {Number} second 可以为负数
 */
export function changeDate({ date, day = 0, second = 0 }) {
  let temp = null

  if (!date) {
    return ''
  }
  if (!day && !second) {
    return date
  }
  if (typeof date === 'string') {
    temp = new Date(date.replace(/-/g, '/'))
  }
  if (typeof date === 'number') {
    temp = new Date(date)
  }
  temp = date

  date = new Date(temp.getTime() + day * 24 * 60 * 60 * 1000 + second * 1000)

  return date
}

/**
 * 转换为unix时间
 * @param {Date} date
 */
export function convertToUnixTime(date) {
  let temp = null
  if (!date) {
    return ''
  }
  if (typeof date === 'string') {
    temp = new Date(date.replace(/-/g, '/'))
  }
  if (typeof date === 'number') {
    temp = new Date(date)
  }
  return temp && temp.getTime() / 1000
}

/**
 * 格式化价格
 * 注意：安卓不支持
 * formatPrice(12305030388.9087); // "12,305,030,388.909"
 */
export function formatPrice(price) {
  if (!price) return price
  try {
    price = Number(price)
  } catch (e) {
    return price
  }

  return (
    Number(price)
      .toFixed(2)
      .toString()
      .replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
      .replace('.00', '')
      .replace('.0', '') || ''
  )
}

// 秒数转为时长
export function secondToFormatDuration(ts) {
  // ts = ts / 1000
  var day = Math.floor(ts / (60 * 60 * 24))
  var hour = Math.floor(ts / (60 * 60)) % 24
  var minute = Math.floor(ts / 60) % 60
  var second = Math.floor(ts) % 60
  if (day > 0) {
    return `${day}d ${hour}h:${minute}m:${second}s`
  } else if (hour > 0) {
    return `${hour}h:${minute}m:${second}s`
  } else if (minute > 0) {
    return `${minute}m:${second}s`
  } else {
    return `${second}s`
  }
}
