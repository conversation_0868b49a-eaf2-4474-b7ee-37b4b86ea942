import dayjs from 'dayjs'
export const gender = ['保密', '男', '女']

export const forwardPickerOptions = {
  shortcuts: [
    {
      text: '最近一周',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: '最近一个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    },
    {
      text: '最近二个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 60)
        return [start, end]
      }
    },
    {
      text: '最近三个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      }
    },
    {
      text: '最近一年',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
        return [start, end]
      }
    }
  ]
}

export const forwardOtherPickerOptions = {
  shortcuts: [
    {
      text: '最近一周',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
        return [start, end]
      }
    },
    {
      text: '最近一个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 29)
        return [start, end]
      }
    },
    {
      text: '最近二个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 59)
        return [start, end]
      }
    },
    {
      text: '最近三个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 89)
        return [start, end]
      }
    }
  ]
}

export const laterPickerOptions = {
  shortcuts: [
    {
      text: '一周',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        end.setTime(start.getTime() + 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: '一个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        end.setTime(start.getTime() + 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    },
    {
      text: '二个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        end.setTime(start.getTime() + 3600 * 1000 * 24 * 60)
        return [start, end]
      }
    },
    {
      text: '一年',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        end.setTime(start.getTime() + 3600 * 1000 * 24 * 365)
        return [start, end]
      }
    },
    {
      text: '永久',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        end.setTime(start.getTime() + 3600 * 1000 * 24 * 365 * 100)
        return [start, end]
      }
    }
  ]
}

export const pickerOptions = {
  shortcuts: [
    {
      text: '今天',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        return [start, end]
      }
    },
    {
      text: '最近一周',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: '最近一个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    },
    {
      text: '最近三个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      }
    }
  ]
}

export const pickerDayOptions = {
  shortcuts: [
    {
      text: '最近一天',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        return [start, end]
      }
    },
    {
      text: '最近三天',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
        return [start, end]
      }
    },
    {
      text: '最近七天',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: '最近一个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    }
  ]
}

export const wechatPickerOptions = {
  shortcuts: [
    {
      text: '一个月',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        end.setTime(start.getTime() + 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    },
    {
      text: '半年',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        end.setTime(start.getTime() + 3600 * 1000 * 24 * 180)
        return [start, end]
      }
    },
    {
      text: '一年',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        end.setTime(start.getTime() + 3600 * 1000 * 24 * 365)
        return [start, end]
      }
    }
  ]
}

export const usedCarspickerOptions = {
  shortcuts: [
    {
      text: '近七天',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: '近30天',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    },
    {
      text: '最近90天',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      }
    },
    {
      text: '近半年',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
        return [start, end]
      }
    },
    {
      text: '近一年',
      value() {
        const end = dayjs('23:59:59', 'hh:mm:ss').toDate()
        const start = dayjs('00:00:00', 'hh:mm:ss').toDate()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
        return [start, end]
      }
    }
  ]
}
