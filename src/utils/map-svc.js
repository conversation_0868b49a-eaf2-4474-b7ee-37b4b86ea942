/**
 * Created by <PERSON><PERSON><PERSON> on 2017-07-31.
 *
 * demo:
    import {getMap} from '@/assets/js/map-service'
    getMap().then(function(map){
        map.getLocation().then(function () {
          console.log('getLocation')
          console.log(localStorage.position)
        })
    })
 */
import { toPromise, loadJs } from '@/utils'
let map = null
let AMap = null

const api = {
  /**
   * 获取定位信息
   */
  getLocation(timeout) {
    return toPromise(function (resolve, reject) {
      let geolocation
      const mapObj = new AMap.Map('iCenter')
      mapObj.plugin('AMap.Geolocation', function () {
        geolocation = new AMap.Geolocation({
          useNative: true, // 是否使用高德定位sdk用来辅助优化定位效果，默认：false
          timeout: timeout || 10000 // 超过10秒后停止定位，默认：无穷大
        })
        mapObj.addControl(geolocation)
        geolocation.getCurrentPosition()
        AMap.event.addListener(geolocation, 'complete', onComplete) // 返回定位信息
        AMap.event.addListener(geolocation, 'error', onError) // 返回定位出错信息
      })
      // 解析定位结果
      function onComplete(data) {
        api
          .getAddressByLocation(data.position.getLng(), data.position.getLat())
          .then(function () {
            resolve()
          })
      }
      // 解析定位错误信息
      function onError(data) {
        console.log('定位失败')
        console.log(data)
        reject()
        // alert('定位失败')
      }
    })
  },
  /**
   * 根据经纬度获取地址详细信息
   */
  getAddressByLocation(lng, lat) {
    return toPromise(function (resolve, reject) {
      const geocoder = new AMap.Geocoder({
        radius: 1000,
        extensions: 'all' // 默认值：base，返回基本地址信息；all，返回地址信息及附近poi、道路、道路交叉口等信息
      })
      geocoder.getAddress([lng, lat], function (status, result) {
        if (status === 'complete' && result.info === 'OK') {
          // console.log('getAddressByLocation')
          // console.log(result)
          window.localStorage.setItem(
            'myPosition',
            JSON.stringify({
              lng: lng,
              lat: lat,
              address: result.regeocode.formattedAddress,
              aois:
                (result.regeocode.aois[0] && result.regeocode.aois[0].name) ||
                '',
              city: result.regeocode.addressComponent.city,
              adcode:
                (result.regeocode.aois[0] && result.regeocode.aois[0].adcode) ||
                ''
            })
          )
          resolve()
        } else {
          reject()
        }
      })
    })
  }
}

export function getMap() {
  let interval = null
  return toPromise(function (resolve, reject) {
    // loadJs(
    //   '//webapi.amap.com/maps?v=1.4.6&key=5c61691728bacc1467e91049fb9b8520'
    // ).then(function () {
    //   interval = setInterval(function () {
    //     if (window.AMap && window.AMap.Map) {
    //       clearInterval(interval)
    //       AMap = window.AMap
    //       // 加载地图，调用浏览器定位服务
    //       map = map || new AMap.Map('container')
    //       resolve(api)
    //     }
    //   }, 500) // maps 加载后，还需要时间进行初始化
    // })
  })
}
