import { toPromise } from '@/utils'
let map = null
let BMapGL = null

const api = {
  /**
   * 获取定位信息
   */
  getLocation() {
    return toPromise(function (resolve, reject) {
      let geolocation = new BMapGL.Geolocation()
      // 开启SDK辅助定位
      geolocation.enableSDKLocation()
      geolocation.getCurrentPosition(function (data) {
        if (geolocation.getStatus() === 0) {
          const bdLat = data.latitude
          const bdLng = data.longitude
          const [gcjLat, gcjLng] = api.bd09ToGcj02(bdLat, bdLng)
          api.getAddressByLocation(gcjLng, gcjLat).then(resolve)
        } else {
          reject('定位失败')
        }
      })
    })
  },
  /**
   * 根据经纬度获取地址详细信息
   */
  getAddressByLocation(gcjLng, gcjLat) {
    return toPromise(function (resolve, reject) {
      // COORDINATES_GCJ02 = 3  GCJ02坐标
      // COORDINATES_BD09 = 5  百度bd09经纬度坐标
      let ggPoint = new BMapGL.Point(gcjLng, gcjLat)

      function translateCallback(data) {
        if (data.status === 0) {
          const { lng, lat } = data.points[0]
          // 创建地理编码实例
          let myGeo = new BMapGL.Geocoder()
          // 根据坐标得到地址描述
          myGeo.getLocation(new BMapGL.Point(lng, lat), function (result) {
            if (result) {
              const position = {
                lng: gcjLng,
                lat: gcjLat,
                address: result.address,
                province: result.addressComponents.province,
                city: result.addressComponents.city,
                district: result.addressComponents.district,
                aois: result.content.poi_desc,
                adcode: result.content.address_detail.adcode
              }
              window.localStorage.setItem(
                'myPosition',
                JSON.stringify(position)
              )
              resolve(position)
            } else {
              reject('地址逆向解析失败')
            }
          })
        } else {
          reject('地址逆向解析失败')
        }
      }

      let convertor = new BMapGL.Convertor()
      let pointArr = []
      pointArr.push(ggPoint)
      convertor.translate(pointArr, 3, 5, translateCallback)
    })
  },
  /**
   * 百度地图坐标（BD-09）转换为国测局坐标（GCJ-02）
   */
  bd09ToGcj02(bdLat, bdLng) {
    const X_PI = (Math.PI * 3000.0) / 180.0
    const x = bdLng - 0.0065
    const y = bdLat - 0.006
    const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI)
    const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI)
    const gcjLng = z * Math.cos(theta)
    const gcjLat = z * Math.sin(theta)
    return [gcjLat, gcjLng]
  }
}

export function getMap() {
  let interval = null
  return toPromise(function (resolve) {
    const BMap_URL =
      'https://api.map.baidu.com/api?type=webgl&v=2.0&ak=syq97Wqch8s3TW4SpNZGQWKpO4TZbZUG&callback=onBMapCallback'

    if (window.BMapGL && window.BMapGL.Map) {
      BMapGL = window.BMapGL
      map = map || new BMapGL.Map('container')
      resolve(api)
      return true
    }

    // 百度地图异步加载回调处理
    window.onBMapCallback = function () {
      interval = setInterval(function () {
        if (window.BMapGL && window.BMapGL.Map) {
          clearInterval(interval)

          BMapGL = window.BMapGL
          // 加载地图，调用浏览器定位服务
          map = map || new BMapGL.Map('container')
          resolve(api)
        }
      }, 500) // 加载后，还需要时间进行初始化
    }

    // 插入script脚本
    let container = document.createElement('div')
    container.setAttribute('id', 'container')
    container.style.opacity = 0
    document.body.appendChild(container)
    let scriptNode = document.createElement('script')
    scriptNode.setAttribute('type', 'text/javascript')
    scriptNode.setAttribute('src', BMap_URL)
    document.body.appendChild(scriptNode)
  })
}
