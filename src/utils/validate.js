/**
 * Created by ji<PERSON><PERSON><PERSON> on 16/11/18.
 */

export function isvalidUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/* 合法uri true 合法，false 非法*/
export function validateURL(textval) {
  const urlregex =
    /(?:(https?|ftp|file):)?\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/
  return urlregex.test(textval)
}

/* 小写字母*/
export function validateLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/* 大写字母*/
export function validateUpperCase(str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/* 大小写字母*/
export function validateAlphabets(str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * validate email
 * @param email
 * @returns {boolean}
 */
export function validateEmail(email) {
  const re =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return re.test(email)
}

/**
 * 身份证验证
 */
export function verificationCard(str) {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return reg.test(str)
}

/**
 * 手机号验证
 */
export function verificationPhone(str) {
  const reg = /^1[3|4|5|6|7|8|9][0-9]{9}$/
  return reg.test(str)
}

/**
 * 正数
 */
export function validatePositiveNumber(str) {
  const reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/
  return reg.test(str)
}

/**
 * 正数且最多保留以为小数
 */
export function validatePositiveNumberOne(str) {
  const reg = /^[+]?(0|([1-9]\d*))(\.\d{1})?$/
  return reg.test(str)
}

/**
 * 正整数
 */
export function alidatePositiveInteger(str) {
  const reg = /^[+]{0,1}(\d+)$/
  return reg.test(str)
}

/**
 * 匹配中英文及大小写
 */
export function matchChiEng(str) {
  console.log(str, 'strstr')
  const reg = /^[\u4e00-\u9fa5a-zA-Z]+$/
  return reg.test(str)
}
