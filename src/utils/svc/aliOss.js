/**
 * 阿里云图片上传
 * 文档：https://help.aliyun.com/document_detail/64041.html?spm=a2c4g.11186623.2.15.4c8e5d26hmxgzg#concept-64041-zh
 * 示例：
    :http-request="httpRequest"
    async httpRequest(option) {
      // option.imageType = 'forum'; // 图片类型：carport、forum、user，默认carport
      this.$oss.ossUploadImage(option)
    },
    async httpRequest(option) {
      this.$oss.ossUploadFile(option)
    },
 * 其他说明：
    1、传给接口时域名需要替换
   http://images.58moto.com/
   示例：
       https://images.58moto.com/carport/20191129/20191129174439_az5s.png!official
       https://images.58moto.com/carport/20191129/20191129174439_az5s.png!official300
    2、访问图片时，问号后面参数替换
    const suffixEnum = {
        'offical': '!official', // 车库等
        'forum': '!forum',
        'user': '!user',
    }
 */
import {
  darkWaterUploadImage,
  multiTransferImage,
  aliyunPersist
} from '@/api/garage'
import util from '@haluo/util'
import { uploadGIF, uploadConf, getAliyunSts } from '@/api/postApk'
import request from '@/utils/request'
import OSS from 'ali-oss'
import lrz from 'lrz'
let client = null
let clientUnProtect = null
let clientNoWater = null
let clientPrivate = null

const aliOss = {
  /**
   * 创建随机字符串
   * @param num
   * @returns {string}
   */
  randomString(num) {
    const chars = [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      'a',
      'b',
      'c',
      'd',
      'e',
      'f',
      'g',
      'h',
      'i',
      'j',
      'k',
      'l',
      'm',
      'n',
      'o',
      'p',
      'q',
      'r',
      's',
      't',
      'u',
      'v',
      'w',
      'x',
      'y',
      'z'
    ]
    let res = ''
    for (let i = 0; i < num; i++) {
      const id = Math.ceil(Math.random() * 35)
      res += chars[id]
    }
    return res
  },

  /**
   * 创建oss客户端对象
   * @returns {*}
   */
  createOssClient(type) {
    return new Promise(async (resolve, reject) => {
      // if (client) {
      //   return resolve(client)
      // }

      let clientParams = {}
      const res = await getAliyunSts({
        businessType: type === 'avatar' ? 1 : 1500
      })
      if (res.data.code === 0) {
        clientParams = res.data.data || {}
        clientParams.suffix = clientParams.style || ''
      }
      client = new OSS({
        region: 'oss-cn-beijing',
        ...clientParams
      })
      resolve(client)
    })
  },

  /**
   * 创建oss客户端对象 UnProtect
   * @returns {*}
   */
  createOssClientUnProtect(type) {
    return new Promise(async (resolve, reject) => {
      // if (clientUnProtect) {
      //   return resolve(clientUnProtect)
      // }
      let clientParams = {}
      const res = await getAliyunSts({ businessType: 8 })
      if (res.data.code === 0) {
        clientParams = res.data.data || {}
        clientParams.suffix = clientParams.style || ''
      }
      clientUnProtect = new OSS({
        region: 'oss-cn-beijing',
        ...clientParams
      })
      resolve(clientUnProtect)
    })
  },
  /**
   * 创建oss客户端对象 不打水印
   * @returns {*}
   */
  createOssClientNoWater() {
    return new Promise(async (resolve, reject) => {
      // if (clientNoWater) {
      //   return resolve(clientNoWater)
      // }
      let clientParams = {}
      const res = await getAliyunSts({ businessType: 0 })
      if (res.data.code === 0) {
        clientParams = res.data.data || {}
        clientParams.suffix = clientParams.style || ''
      }
      clientNoWater = new OSS({
        region: 'oss-cn-beijing',
        ...clientParams
      })
      resolve(clientNoWater)
    })
  },
  createOssClientPrivate() {
    return new Promise(async (resolve, reject) => {
      // if (clientPrivate) {
      //   return resolve(clientPrivate)
      // }
      let clientParams = {}
      const res = await getAliyunSts({ businessType: 1199 })
      if (res.data.code === 0) {
        clientParams = res.data.data || {}
        clientParams.suffix = clientParams.style || ''
      }
      clientPrivate = new OSS({
        region: 'oss-cn-beijing',
        ...clientParams
      })
      resolve(clientPrivate)
    })
  },
  // 视频上传（活动视频上传，不需要转吗）
  createOssClientVideo() {
    return new Promise(async (resolve, reject) => {
      // if (clientPrivate) {
      //   return resolve(clientPrivate)
      // }
      let clientParams = {}
      const res = await getAliyunSts({ businessType: 6 })
      if (res.data.code === 0) {
        clientParams = res.data.data || {}
        clientParams.suffix = clientParams.style || ''
      }
      clientPrivate = new OSS({
        region: 'oss-cn-beijing',
        ...clientParams
      })
      resolve(clientPrivate)
    })
  },
  // 文件上传，bucket为接口的数据
  createOssClientDocument(option) {
    return uploadConf(option.businessType).then((res) => {
      return new Promise(async (resolve, reject) => {
        // if (clientPrivate) {
        //   return resolve(clientPrivate)
        // }
        let clientParams = {}
        const res = await getAliyunSts({ businessType: 8 })
        if (res.data.code === 0) {
          clientParams = res.data.data || {}
          clientParams.suffix = clientParams.style || ''
        }
        clientPrivate = new OSS({
          region: 'oss-cn-beijing',
          ...clientParams
        })
        resolve(clientPrivate)
      })
    })
  },
  suffixEnum: {
    nowater: '!nowater', // 不打水印
    official: '!official', // 车库、经销商水印，默认
    panoram: '!panoram', // 全景图水印
    forum: '!forum',
    avatar: '!avatar', // 头像
    square: '!square',
    carport: '!carport' // 车库、经销商水印
  },
  // 加载图片
  imgUpload(file) {
    return new Promise((resolve, reject) => {
      let _URL = window.URL || window.webkitURL
      let image = new Image()
      image.src = _URL.createObjectURL(file)
      image.onload = function () {
        resolve(image)
      }
    })
  },
  /**
   * 图片上传
   * 参考csdn: https://blog.csdn.net/qq_27626333/article/details/81463139
   * @param option
   *   option.file 文件
   *   option.imageType 图片类型：nowater、official、panoram、forum、user，默认official
   * demo:
          :http-request="httpRequest"
          async httpRequest(option) {
              // option.imageType = 'forum';
              this.$oss.ossUploadImage(option);
          }
   */
  ossUploadImage(option) {
    // console.log(option);
    const self = aliOss
    const file = option.file
    option.onError = option.onError || function () {}
    option.onSuccess = option.onSuccess || function () {}
    option.onProgress = option.onProgress || function () {}
    console.log(file)
    if (
      !['image/png', 'image/gif', 'image/jpg', 'image/jpeg'].includes(file.type)
    ) {
      option.onError('')
      return window.$message.warning(
        '上传失败，请上传后缀为png、gif、jpg的文件'
      )
    }
    return new Promise((resolve, reject) => {
      let imageType = option.imageType || 'official' // 图片类型，默认official
      let quality = option.quality || 0.7 // 图片压缩比例，默认 0.7 车库平台使用时，使用0.8
      const suffix = self.suffixEnum[imageType] // 返回的图片路径后缀
      // 采用公用组件库方法:@haluo/util
      const year = util.date.format(new Date(), 'YYYY') // 年
      const date = util.date.format(new Date(), 'YYYYMMDD') // 当前时间
      const dateTime = util.date.format(new Date(), 'YYYYMMDDHHmmss') // 当前时间
      const randomStr = self.randomString(4) //  4位随机字符串
      const extensionName = `.${file.name.split('.').reverse()[0]}` // 文件扩展名
      const fileName =
        imageType +
        '/' +
        date +
        '/' +
        dateTime +
        '_' +
        randomStr +
        extensionName // 文件名字（相对于根目录的路径 + 文件名）
      const smallImgStatus = file.size
        ? Math.floor(file.size / 1024) <= 100
        : false // 判定是不是小图片（大小小雨）,是小图片，quality 取值 为1 (MAIN-2481)
      quality = smallImgStatus ? 1 : quality
      const isGif = file.type === 'image/gif'

      self.imgUpload(file).then((image) => {
        lrz(file, {
          quality: quality
        })
          .then(function (rst) {
            // console.log(rst)

            // 长图压缩有问题， isLong 判断是否是长图，是长图不使用压缩后的文件rst
            const w = image.width
            const h = image.height
            const b = w > h ? w : h
            const s = w < h ? w : h
            const isLong = b / s > 2

            let postFile = isLong ? file : rst.file
            // 压缩后图片的透明度会丢失，有些地方需要的话先上传不压缩的图片吧
            if (option.notCompress || isGif) {
              postFile = file
            }

            // 超出大小
            const imgSize = isLong ? file.size : rst.file.size
            // console.log(imgSize / 1024);
            if (imgSize / 1024 > 10 * 1024) {
              reject('图片不能超过10M')
              return option.onError('图片不能超过10M')
            }

            // 设置文件名
            if (!rst.file.name) {
              rst.file.name = file.name
            }

            // if (isGif) {
            //   let fd = new FormData()
            //   fd.append('file', file)
            //   return uploadGIF(fd)
            //     .then((_) => {
            //       console.log('88888888888888', _)
            //       if (_.data.code === 0) {
            //         self.loadImage({
            //           url: _.data.data,
            //           val: _.data,
            //           suffix,
            //           option,
            //           file: rst.origin,
            //           resolve,
            //           reject,
            //           isGif
            //         })
            //       } else {
            //         option.onError('上传失败')
            //         reject('上传失败')
            //         console.log('上传失败')
            //       }
            //     })
            //     .catch(() => {
            //       option.onError('上传失败')
            //       reject('上传失败')
            //       console.log('上传失败')
            //     })
            // }

            // square carport 需要打暗水印
            if (option.batchTransfer) {
              return multiTransferImage({
                file: postFile
              })
                .then((_) => {
                  // console.log(_)
                  if (_.data.code === 0) {
                    self.loadImage({
                      url: _.data.data,
                      val: _.data,
                      suffix,
                      option,
                      file: postFile,
                      resolve,
                      reject
                    })
                  } else {
                    option.onError('上传失败')
                    reject('上传失败')
                    console.log('上传失败')
                  }
                })
                .catch(() => {
                  option.onError('上传失败')
                  reject('上传失败')
                  console.log('上传失败')
                })
            }
            // carport 需要打暗水印
            if ([self.suffixEnum.carport].includes('!' + option.imageType)) {
              // self.suffixEnum.square,
              return darkWaterUploadImage({
                file: postFile
              })
                .then((_) => {
                  // console.log(_)
                  if (_.data.code === 0) {
                    self.loadImage({
                      url: _.data.data,
                      val: _.data,
                      suffix,
                      option,
                      file: postFile,
                      resolve,
                      reject
                    })
                  } else {
                    option.onError('上传失败')
                    reject('上传失败')
                    console.log('上传失败')
                  }
                })
                .catch(() => {
                  option.onError('上传失败')
                  reject('上传失败')
                  console.log('上传失败')
                })
            }
            const isNoWater =
              ['nowater', 'forum'].indexOf(option.imageType) > -1
            const idCard = option.idCard || false

            // 执行上传
            ;(isGif
              ? self.createOssClientUnProtect()
              : idCard
              ? self.createOssClientPrivate()
              : isNoWater
              ? self.createOssClientNoWater()
              : self.createOssClient(imageType)
            ).then((client) => {
              // console.log(client);
              // 上传处理
              // 分片上传文件  replace：兼容客户端BUG，改图片后缀名
              client
                .multipartUpload(fileName, postFile, {
                  progress: function (p) {
                    const e = {}
                    e.percent = Math.floor(p * 100)
                    option.onProgress(e)
                  },
                  meta: { year, people: 'x-oss-meta-motor' },
                  mime: isLong ? file.type : rst.file.type
                })
                .then(
                  (val) => {
                    console.info(val)
                    if (val.res.statusCode === 200) {
                      if (idCard) {
                        request({
                          url: '/expands/oss/img/generatePrePresignedUrl',
                          method: 'get',
                          params: {
                            objectId: val.name,
                            expireMils: 60000
                          }
                        }).then((res) => {
                          if (res.data.code === 0) {
                            const url = res.data.data
                            self.loadImageNew({
                              url,
                              val,
                              file: postFile,
                              option,
                              resolve,
                              reject
                            })
                          }
                        })
                      } else {
                        if (imageType === 'avatar') {
                          aliyunPersist({
                            object: `${val.bucket}://${val.name}`,
                            businessType: 1 // 业务类型：1头像，2文章图片，3文章HTML详情，4GIF
                          }).then((res) => {
                            if (res.data.code === 0) {
                              const url = res.data.data
                              self.loadImageNew({
                                url,
                                val,
                                file: postFile,
                                option,
                                resolve,
                                reject
                              })
                            }
                          })
                        } else {
                          const clientParams = client.options
                          const url = `${clientParams.realmName}${val.name}${
                            clientParams.suffix || ''
                          }`
                          self.loadImage({
                            url,
                            val,
                            suffix,
                            file: postFile,
                            option,
                            resolve,
                            reject,
                            isGif
                          })
                        }
                      }
                    } else {
                      option.onError('上传失败')
                      reject('上传失败')
                      console.log('上传失败')
                    }
                  },
                  (err) => {
                    option.onError('上传异常')
                    reject(err.message)
                    console.log(err.message)
                  }
                )
            })
          })
          .catch(function (e) {
            console.error(e)
          })
      })
    })
  },
  loadImage({ url, val, suffix, file, option, resolve, reject, isGif }) {
    let img = new Image()
    img.src = url
    // console.time()
    img.onload = function () {
      val.imgOrgUrl = `${url}?_${img.width}_${img.height}`
      // 全景图水印图"suffix === '!panoram'"缩略图后缀不带300，其他需要
      // gif 图片不需要300
      val.imgUrl =
        suffix === '!panoram' || isGif
          ? `${url}?_${img.width}_${img.height}`
          : `${url}300?_${img.width}_${img.height}`
      val.name = file.name
      val.fileName = file.name
      // console.log(val);
      option.onSuccess(val)
      resolve(val)
      // console.timeEnd()
      img = null
    }
    img.onerror = function () {
      option.onError('图片加载失败')
      reject('图片加载失败')
      console.log('图片加载失败')
      img = null
    }
  },
  loadImageNew({ url, val, file, option, resolve, reject }) {
    let img = new Image()
    img.src = url
    img.onload = function () {
      val.imgOrgUrl = url
      val.imgUrl = val.name
      val.name = file.name
      val.fileName = file.name
      option.onSuccess(val)
      resolve(val)
      img = null
    }
    img.onerror = function () {
      option.onError('图片加载失败')
      reject('图片加载失败')
      console.log('图片加载失败')
      img = null
    }
  },
  /**
   * 文件上传
   * @param option
   * demo:
          :http-request="httpRequest"
          async httpRequest(option) {
              // option.isVideo = true
              // option.isUnProtect = true
              this.$oss.ossUploadFile(option);
          }
  */
  ossUploadFile(option) {
    // console.log(option);
    const self = this
    const file = option.file
    return new Promise((resolve, reject) => {
      // 采用公用组件库方法:@haluo/util
      const year = util.date.format(new Date(), 'YYYY') // 年
      const date = util.date.format(new Date(), 'YYYYMMDD') // 当前时间
      const dateTime = util.date.format(new Date(), 'YYYYMMDDHHmmss') // 当前时间
      const randomStr = self.randomString(4) //  4位随机字符串
      const extensionName = `.${file.name.split('.').reverse()[0]}` // 文件扩展名
      const fileType = extensionName === '.apk' ? 'apk' : 'file' // 文件类型，默认file
      const fileName =
        fileType + '/' + date + '/' + dateTime + '_' + randomStr + extensionName // 文件名字（相对于根目录的路径 + 文件名）
      const isVideo = !!option.isVideo
      const isDocument = !!option.isDocument // 文件上传
      const isUnProtect = !!option.isUnProtect
      const ossMethod = isUnProtect
        ? self.createOssClientUnProtect()
        : isVideo
        ? self.createOssClientVideo()
        : isDocument
        ? self.createOssClientDocument(option)
        : self.createOssClient()

      // 执行上传
      ossMethod.then((client) => {
        // 上传处理
        // 分片上传文件  replace：兼容客户端BUG，改图片后缀名
        client
          .multipartUpload(fileName, file, {
            progress: function (p) {
              const e = {}
              e.percent = Math.floor(p * 100)
              option.onProgress(e)
            },
            meta: { year, people: 'x-oss-meta-motor' },
            mime: file.type
          })
          .then(
            (val) => {
              console.info(val)
              if (val.res.statusCode === 200) {
                // const documentData = isDocument
                //   ? client.options.documentData
                //   : {}
                const clientParams = client.options
                const url = `${clientParams.realmName}${val.name}${
                  clientParams.suffix || ''
                }`
                // const url = `${
                //   isVideo
                //     ? VIDEOURL
                //     : isDocument
                //     ? documentData.realmName
                //     : ALIIMAGE
                // }${val.name}${isDocument ? documentData.style : ''}`
                val.url = url
                val.fileName = file.name
                // console.log(val);
                option.onSuccess(val)
                resolve(val)
              } else {
                option.onError('上传失败')
                reject('上传失败')
                console.log('上传失败')
              }
            },
            (err) => {
              option.onError('上传异常')
              reject(err)
            }
          )
      })
    })
  },
  /**
   * 商品详情图片上传
   *
   */
  shopDetailUpdate(option) {
    const file = option.file
    option.onError = option.onError || function () {}
    option.onSuccess = option.onSuccess || function () {}
    option.onProgress = option.onProgress || function () {}
    if (
      !['image/png', 'image/gif', 'image/jpg', 'image/jpeg'].includes(file.type)
    ) {
      option.onError()
      return window.$message.warning(
        '上传失败，请上传后缀为png、gif、jpg的文件'
      )
    }
    return new Promise((resolve, reject) => {
      if (file.size / 1024 > 10 * 1024) {
        reject('图片不能超过10M')
        return option.onError('图片不能超过10M')
      }
      const self = aliOss
      //读取图片数据
      const reader = new FileReader()
      reader.onload = function (e) {
        const data = e.target.result
        //加载图片获取图片真实宽度和高度
        const image = new Image()
        image.onload = function () {
          const imageType = option.imageType || 'official' // 图片类型，默认official
          const suffix = self.suffixEnum[imageType] // 返回的图片路径后缀
          const year = util.date.format(new Date(), 'YYYY') // 年
          const date = util.date.format(new Date(), 'YYYYMMDD') // 当前时间
          const dateTime = util.date.format(new Date(), 'YYYYMMDDHHmmss') // 当前时间
          const randomStr = self.randomString(4) //  4位随机字符串
          const extensionName = `.${file.name.split('.').reverse()[0]}` // 文件扩展名
          const fileName =
            imageType +
            '/' +
            date +
            '/' +
            dateTime +
            '_' +
            randomStr +
            extensionName // 文件名字（相对于根目录的路径 + 文件名）
          self.createOssClientNoWater().then((client) => {
            // 上传处理
            // 分片上传文件  replace：兼容客户端BUG，改图片后缀名
            client
              .multipartUpload(fileName, file, {
                progress: function (p) {
                  const e = {}
                  e.percent = Math.floor(p * 100)
                  option.onProgress(e)
                },
                meta: { year, people: 'x-oss-meta-motor' },
                mime: file.type
              })
              .then(
                (val) => {
                  console.info(val)
                  if (val.res.statusCode === 200) {
                    const clientParams = client.options
                    const url = `${clientParams.realmName}${val.name}${
                      clientParams.suffix || ''
                    }`
                    self.loadImage({
                      url,
                      val,
                      suffix,
                      file: file,
                      option,
                      resolve,
                      reject
                    })
                  } else {
                    option.onError('上传失败')
                    reject('上传失败')
                    console.log('上传失败')
                  }
                },
                (err) => {
                  option.onError('上传异常')
                  reject(err.message)
                  console.log(err.message)
                }
              )
          })
        }
        image.src = data
      }
      reader.readAsDataURL(file)
    })
  }
}

export default {
  install(app) {
    app.config.globalProperties.$oss = aliOss
  }
}
export const $aliOss = aliOss
