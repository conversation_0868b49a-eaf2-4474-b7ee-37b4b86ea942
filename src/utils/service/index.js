import { getUserCertify } from '@/api/user'
// 获取用户认证
export function getUserCertifyInfo(data) {
  let uids = ''
  const allData = JSON.parse(JSON.stringify(data) || '[]')
  if (data) {
    uids = data
      .map((_) => {
        return _.autherId || _.uid || _.authorId
      })
      .join(',')
  }
  return getUserCertify({
    uids
  })
    .then((res) => {
      const respon = res.data
      if (respon.code === 0) {
        allData.forEach((item) => {
          if (respon.data) {
            const certify =
              respon.data.find(
                (value) =>
                  value.uid == (item.autherId || item.uid || item.authorId)
              ) || {}
            item.certifyNames =
              JSON.parse(JSON.stringify(certify.certifyVos)) || []
            if (certify.ifFactoty === 1) {
              item.certifyNames.unshift({ certifyName: '厂家成员' })
            }
            if (certify.hoopMaster === 1) {
              item.certifyNames.unshift({ certifyName: '圈主' })
            }
          }
        })
        // console.log(allData)
        return allData
      }
    })
    .catch((e) => {
      console.log('error', e)
      return allData
    })
}
