import util from '@haluo/util'
import store from '@/store'
import { SMARTSERVICEURL } from '@/utils/configData/config'

window.addEventListener('focus', () => {
  if (window.isSmartService || SMARTSERVICEURL.includes(location.host)) {
    return
  }

  // fix: 登录互踢
  const cUserInfo = JSON.parse(util.cookie.getCookie('userInfo') || '{}')
  const lUserInfo = JSON.parse(localStorage.userInfo || '{}')
  if (cUserInfo.ossToken && cUserInfo.ossToken !== lUserInfo.ossToken) {
    localStorage.removeItem('token')
    localStorage.removeItem('ossToken')
    localStorage['userInfo'] = JSON.stringify(cUserInfo)
    store.commit('SET_NAME', cUserInfo.username)
    store.commit('SET_UID', cUserInfo.userid)
    store.commit('SET_CODE', cUserInfo.usercode)
    store.commit('SET_TOKEN', cUserInfo.ossToken)
  }
})
