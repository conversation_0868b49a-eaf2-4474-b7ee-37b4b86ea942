import util from '@haluo/util'

/**
 * 重置组件数据 data
 * 示例：resetData(this)
 * @param {object} current this
 */
export function resetData(me) {
  if (me && me.$data) {
    Object.assign(me.$data, me.$options.data())
  }
}

export function clearLogin() {
  localStorage.removeItem('userInfo')
  let domain = ''
  if (
    location.hostname === 'localhost' ||
    util.match.checkType(location.hostname, 'ip')
  ) {
    domain = location.hostname
  } else {
    domain = location.host.replace(/\w+\./, '') // cros cookie
  }
  util.cookie.clearCookie({
    name: 'userInfo',
    domain
  })
  util.cookie.clearCookie({
    name: 'userInfo',
    domain: 'mddmoto.com' // cros cookie
  })
  // fix: 登录互踢
  util.cookie.clearCookie({
    name: 'token',
    domain
  })
  util.cookie.clearCookie({
    name: 'token',
    domain: 'mddmoto.com' // cros cookie
  })
  util.cookie.clearCookie({
    name: 'ossToken',
    domain
  })
  util.cookie.clearCookie({
    name: 'ossToken',
    domain: 'mddmoto.com' // cros cookie
  })
}

export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (('' + time).length === 10) time = parseInt(time) * 1000
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

/** 格式化时间
 *  @param {string} date 需要格式化的时间 2017-11-11、2017/11/11、linux time
 *  @param {string} fmt 想要格式化的格式
 */
// 废弃，全部采用公用方法库
export function format(date, fmt = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) {
    return ''
  }
  if (typeof date === 'string') {
    date = new Date(date.replace(/-/g, '/'))
  }
  if (typeof date === 'number') {
    date = new Date(date)
  }
  var o = {
    'M+': date.getMonth() + 1,
    'D+': date.getDate(),
    'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
    'H+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds()
  }
  var week = {
    0: '\u65e5',
    1: '\u4e00',
    2: '\u4e8c',
    3: '\u4e09',
    4: '\u56db',
    5: '\u4e94',
    6: '\u516d'
  }
  if (/(Y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    )
  }
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (RegExp.$1.length > 1
        ? RegExp.$1.length > 2
          ? '\u661f\u671f'
          : '\u5468'
        : '') + week[date.getDay() + '']
    )
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      )
    }
  }
  return fmt
}

export function formatTime(time, option) {
  time = +time * 1000
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

// 格式化时间
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 *get getByteLen
 * @param {Sting} val input value
 * @returns {number} output value
 */
export function getByteLen(val) {
  let len = 0
  for (let i = 0; i < val.length; i++) {
    if (val[i].match(/[^\x00-\xff]/gi) != null) {
      len += 1
    } else {
      len += 0.5
    }
  }
  return Math.floor(len)
}

export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map((key) => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    })
  ).join('&')
}

export function param2Obj(url) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(
    '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"') +
      '"}'
  )
}

export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

export function objectMerge(target, source) {
  /* Merges two  objects,
     giving the last one precedence */

  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

export function scrollTo(element, to, duration) {
  if (duration <= 0) return
  const difference = to - element.scrollTop
  const perTick = (difference / duration) * 10
  setTimeout(() => {
    element.scrollTop = element.scrollTop + perTick
    if (element.scrollTop === to) return
    scrollTo(element, to, duration - 10)
  }, 10)
}

export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

const loaded = {}
/**
 * function 转换为 loadJs
 */
export function loadJs(url) {
  const name = url && url.split('?')[0].split('/').reverse()[0]
  const id = 'js_' + name
  return new Promise((resolve, reject) => {
    if (loaded[id]) {
      return resolve()
    }
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.async = true
    script.src = url
    script.id = id
    script.onload = () => {
      loaded[id] = true
      resolve()
    }
    script.onerror = () => {}
    document.body.appendChild(script)
  })
}

/**
 * 动态加载样式
 */
export function loadCss(url) {
  const name = url && url.split('?')[0].split('/').reverse()[0]
  const id = 'css_' + name
  return new Promise((resolve, reject) => {
    if (loaded[id]) {
      return resolve()
    }
    var link = document.createElement('link')
    link.type = 'text/css'
    link.rel = 'stylesheet'
    link.href = url
    link.id = id
    link.onload = () => {
      loaded[id] = true
      resolve()
    }
    link.onerror = () => {
      reject()
    }
    document.head.appendChild(link)
  })
}

/**
 * 深拷贝对象
 * 将 source 拷贝到 dest 并返回
 * @param {object} source
 * @param {object} dest
 * @returns new object
 */
export function deepCopy(source, dest) {
  const result = dest || {}
  for (const i in source) {
    // eslint-disable-next-line no-prototype-builtins
    if (source.hasOwnProperty(i)) {
      if (typeof source[i] === 'object' && source[i] !== null) {
        result[i] = source[i].constructor === Array ? [] : {}
        deepCopy(source[i], result[i])
      } else {
        result[i] = source[i]
      }
    }
  }
  return result
}

/**
 * 数组元素交换位置
 * @param {array} array 数组
 * @param {number} index1 添加项目的位置
 * @param {number} index2 删除项目的位置
 * index1和index2分别是两个数组的索引值，即是两个要交换元素位置的索引值，如1，5就是数组中下标为1和5的两个元素交换位置
 */
export function swapArray(array, index1, index2) {
  ;[array[index1], array[index2]] = [array[index2], array[index1]]
  return array
}

/**
 * function 转换为 promise
 */
export function toPromise(fn) {
  return new Promise((resolve, reject) => {
    fn(resolve, reject)
  })
}

/**
 * 改变url，图片的大小
 */
// export function changeImageSize(src, size = 300) {
//   return src && src.replace(/(\.(jpg)|\.(png)|\.(jpeg)|\.(gif)|.(bmp))/, `_${size}$1`) || '';
// }

/** 获取倒计时剩余时间
 *  @param {date} endTime 截止时间
 *  @return {object} {dd: '天', hh: '时', mm: '分', ss: '秒'}
 */
export function remainTime(endTime) {
  const ts = (endTime - new Date()) / 1000 // 计算剩余的总秒数
  const dd = parseInt(ts / 60 / 60 / 24, 10) // 计算剩余的天数
  const hh = parseInt((ts / 60 / 60) % 24, 10) // 计算剩余的小时数
  const mm = parseInt((ts / 60) % 60, 10) // 计算剩余的分钟数
  const ss = parseInt(ts % 60, 10) // 计算剩余的秒数
  if (ts <= 0) {
    return false
  }
  function checkTime(i) {
    if (i < 10) {
      i = '0' + i
    }
    return i
  }

  return {
    dd: checkTime(dd),
    hh: checkTime(hh),
    mm: checkTime(mm),
    ss: checkTime(ss)
  }
}

/**
 * 获取 linkName ,不同type值返回的name值不同
 * getLinkName(value);
 */
export const getLinkName = (type) => {
  let result = ''
  switch (type) {
    case 'brand_detail': // 品牌详情
      result = '品牌'
      break
    case 'riding_detail': // 骑行详情
      result = '轨迹'
      break
    case 'moment_detail': // 动态详情
      result = '动态'
      break
    case 'essay_detail': // 文章详情
      result = '文章'
      break
    case 'car_detail': // 车辆详情
      result = '车辆'
      break
    case 'opinion_detail': // 观点详情
      result = '观点'
      break
    case 'topic_detail': // 问答详情
      result = '提问'
      break
    case 'shop_detail': // 商家详情
      result = '商家'
      break
    case 'activity': // 活动
      result = '活动'
      break
    case 'thread_detail': // 帖子
      result = '帖子'
      break
  }
  return result
}

/** 获取倒计时剩余时间
 *  @param {date} seconds 秒数
 *  @return {string} xxh:xxmin:xxs
 */
export function dealRemainTime(seconds) {
  if (seconds <= 0) {
    return ''
  }
  let hh = Math.floor((seconds / 60 / 60) % 24) // 计算剩余的小时数
  let mm = Math.floor((seconds / 60) % 60) // 计算剩余的分钟数
  let ss = Math.floor(seconds % 60) // 计算剩余的秒数
  let time = 0
  if (hh) {
    time = hh + 'h'
    if (mm) {
      time = time + mm + 'min'
    } else {
      if (ss) {
        time = time + ss + 's'
      }
    }
  } else {
    if (mm) {
      time = time + mm + 'min'
    }
    if (ss) {
      time = time + ss + 's'
    }
  }
  return time
}

/**
 * 电话加密
 * @param {*} type
 */
export function maskPhoneNumber(phoneNumber) {
  if (!phoneNumber) return ''
  // 使用正则表达式匹配手机号中的数字
  const regex = /(\d{3})\d{4}(\d{4})/

  // 替换中间四位数字为星号
  const maskedPhoneNumber = phoneNumber.replace(regex, '$1****$2')

  return maskedPhoneNumber
}

/**
 * 传入小时返回**天**小时
 * @param {*} type
 */
export function filterDay(data) {
  if (!data) return '-'
  const day = Math.floor(data / 24)
  const time = data - 24 * day
  if (
    data.toString().indexOf('天') > -1 ||
    data.toString().indexOf('小时') > -1 ||
    data === '/'
  ) {
    return data
  }
  return day && time ? `${day}天${time}小时` : day ? `${day}天` : `${time}小时`
}

/**
 * 判断是否是本地环境
 * @returns true | false
 */
export function isLocal() {
  const isLocal =
    import.meta.env.MODE === 'development' ||
    ['localhost', '0.0.0.0', '127.0.0.1', '172.'].some((_) =>
      location.hostname.includes(_)
    )
  return isLocal
}
