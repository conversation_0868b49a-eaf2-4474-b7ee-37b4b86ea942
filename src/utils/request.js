import axios from 'axios'
import qs from 'qs'
import { ElMessageBox, ElMessage } from 'element-plus'
import store from '@/store'
import {
  MANAGERURL,
  CRMURL,
  SMARTSERVICEURL,
  SMARTMANAGERURL
} from '@/utils/configData/config'
import { clearLogin } from '@/utils'
import util from '@haluo/util'

axios.defaults.headers = {
  'X-Requested-With': 'XMLHttpRequest',
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Method': 'POST,GET'
}
const beforeAlterList = [
  'beforeAlterFirst',
  'beforeAlterTwo',
  'beforeAlterThree'
] // 存储的列表字段名，在数组中，才会记录日志

const baseURL = SMARTSERVICEURL.includes(location.hostname)
  ? SMARTMANAGERURL
  : MANAGERURL

// create an axios instance
const service = axios.create({
  baseURL, // api 的 base_url
  timeout: 60000, // request timeout
  transformRequest: [
    function (data) {
      // `transformRequest` 允许在向服务器发送前，修改请求数据
      // 只能用在 'PUT', 'POST' 和 'PATCH' 这几个请求方法
      // if (data) { data['platform'] = platformList2['OSS']; }
      return qs.stringify(data)
    }
  ]
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    // crm服务的替换
    if (/\/crm/.test(config.url)) {
      config.baseURL = CRMURL
      config.url = config.url.replace('/crm', '')
    }
    const isManager = config.url.includes('.com/')
      ? config.url.includes(MANAGERURL)
      : (config.baseURL + '/').includes(MANAGERURL)

    // contentType默认true，默认application/x-www-form-urlencoded
    // contentType默认false 取自定义
    if (config.contentType !== false) {
      config.headers = {
        'Content-type': 'application/x-www-form-urlencoded'
      }
    }

    let cUserInfo = {}

    if (!store.getters.token) {
      if (window.isSmartService || SMARTSERVICEURL.includes(location.host)) {
        cUserInfo = JSON.parse(localStorage.userInfo || '{}')
      } else {
        cUserInfo = JSON.parse(util.cookie.getCookie('userInfo') || '{}')
      }
    }

    const sessionStorageUser = JSON.parse(
      sessionStorage.getItem('user') || '{}'
    )

    if (config.method === 'get') {
      config.params = config.params || {}
      config.params['ossUserId'] =
        config.params['ossUserId'] ||
        store.getters.uid ||
        cUserInfo.userid ||
        sessionStorageUser.userid
      config.params['ossToken'] =
        config.params['ossToken'] ||
        store.getters.token ||
        cUserInfo.ossToken ||
        sessionStorageUser.ossToken
      config.params['ossUserName'] =
        config.params['ossUserName'] ||
        store.getters.name ||
        cUserInfo.username ||
        sessionStorageUser.username
      // console.log(
      //   config.params,
      //   store.getters.token,
      //   '--------------------------------测试登出问题'
      // )
      // headers
      if (isManager) {
        config.headers['ossUserId'] = config.params['ossUserId']
        config.headers['ossToken'] = config.params['ossToken']
      }
    }
    if (config.method === 'post') {
      const sendName = beforeAlterList.find((_) => {
        return config[_] === false
      })
      const sendData = sessionStorage.getItem(sendName) || '{}'
      const objId = sessionStorage.getItem('objId') || ''
      const menu = sessionStorage.getItem('menu') || ''
      config.data = config.data || {}
      config.data['ossUserId'] =
        config.data['ossUserId'] ||
        store.getters.uid ||
        cUserInfo.userid ||
        sessionStorageUser.userid
      config.data['ossToken'] =
        config.data['ossToken'] ||
        store.getters.token ||
        cUserInfo.ossToken ||
        sessionStorageUser.ossToken
      config.data['ossUserName'] =
        config.data['ossUserName'] ||
        store.getters.name ||
        cUserInfo.username ||
        sessionStorageUser.username

      // headers
      if (isManager) {
        config.headers['ossUserId'] = config.data['ossUserId']
        config.headers['ossToken'] = config.data['ossToken']
      }
      if (!config.data.newLog) {
        config.data.beforeAlter = sendData // 必须
        config.data.menu = sendName ? config.menu || menu || '' : '' // 哪个页面
        config.data.objId = objId || '' // 哪条数据
      }

      // if (config.data['Content-type']) {
      //   config.headers['Content-type'] = config.data['Content-type']
      // }
    }

    // console.log(config.url, config.headers['ossUserId'], config.headers['ossToken'])
    return config
  },
  (error) => {
    // Do something with request error
    console.log(error) // for debug
    ElMessage({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  (response) => {
    const findSuccess = beforeAlterList.find((_) => {
      return response.config[_] === true
    })
    if (findSuccess) {
      const data = response.data.data || {}
      const ext = response.data.ext || {}
      const storageData = data.list && data.list.length ? data.list[0] : data
      sessionStorage.setItem(findSuccess, JSON.stringify(storageData))
      console.log(ext)
      sessionStorage.setItem('objId', (ext && ext.objId) || '')
    } // 有上字段时，取出字段
    const code = response.data.code
    if ([0, 200, 1001, undefined].indexOf(code) > -1) {
      // 返回正常
      return response
    } else if (code === 1003) {
      ElMessage({
        message: response.data.data || response.data.msg || '登录过期',
        type: 'error',
        duration: 5 * 1000
      })

      // // fix: 登录互踢
      // const cUserInfo = JSON.parse(util.cookie.getCookie('userInfo') || '{}')
      // const lUserInfo = JSON.parse(localStorage.userInfo || '{}')
      // if (cUserInfo && cUserInfo.ossToken !== lUserInfo.ossToken) {
      //   localStorage.removeItem('token')
      //   localStorage.removeItem('ossToken')
      //   localStorage['userInfo'] = JSON.stringify(cUserInfo)
      //   return setTimeout(() => {
      //     location.reload()
      //   }, 1000)
      // }

      clearLogin()

      location.hash = '#/login'
      setTimeout(() => {
        location.reload()
      }, 100)
    } else if (code === 90001) {
      // 用户没有权限
      ElMessage({
        message: '您没有操作权限, 请刷新页面重试',
        type: 'error',
        duration: 5 * 1000
      })
    } else {
      console.log(9999)
      response.data = response.data || {}
      const BannedRemindStatus =
        response.data.msg &&
        response.data.msg.includes('102') &&
        response.data.code === -1 // 禁言提醒
      if (BannedRemindStatus) {
        const time = response.data.msg.includes('102,')
          ? response.data.msg.split('.')[0].split('102,')[1]
          : time
        ElMessageBox({
          message: `当前用户已被禁言，到期时间${time}，继续操作请先解除禁言`
        })
      }
      // 是否隐藏错误提示
      if (
        (!response.config.params ||
          (response.config.params && !response.config.params.hideErrorMsg)) &&
        !BannedRemindStatus
      ) {
        ElMessage({
          message: response.data.msg || '接口响应信息异常',
          type: 'error',
          duration: 5 * 1000
        })
      }
    }
    return Promise.reject({
      message: response.data.msg,
      response
    })
  },
  (error) => {
    console.log(error.message)
    const message =
      error.response && error.response.data && error.response.data.message
    ElMessage({
      message: message || '网络超时',
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
