function appendAll(obj) {
  return {
    全部: '',
    ...obj
  }
}

export const commonBoolEnum = { 全部: '', 是: 1, 否: 0 }
export const shopResponseStatus = { 已处理: 1, 未处理: 0 } // 商家响应状态
export const userTrackingStatus = {
  已购车: 1,
  无购车需求: 2,
  有购车需求: 3,
  已添加微信: 4
} // 用户跟踪状态
export const shopLevelList = {
  无: 0,
  旗舰店: 6,
  品牌店: 7,
  综合店: 8,
  摩配店: 9
} // 经销商等级列表
export const storeAttributes = { 无: 0, 集团公司: 1, 连锁店: 2, 独立店: 3 } // 商家响应状态
export const storeCategory = {
  展示经销商: 1,
  入驻经销商: 2,
  合作经销商: 3,
  试用期: 4
} // 店铺类别
// export const paymentType = { 白银会员: 10, 黄金会员: 11, 白金会员: 12, 钻石会员: 13, 黑金会员: 14, 二手车会员: 15 } // 会员等级
export const paymentType = {
  白银会员: 10,
  黄金会员: 11,
  白金会员: 12,
  钻石会员: 13,
  黑金会员: 14
} // 会员等级
export const memberTypeList = { 全部: '', 新车: 1, 二手车: 2 } // 付费业务
export const memberTypeEnum = { 1: '新车会员', 2: '二手车会员' } // 会员类型
export const memberButtonEnum = { 1: '新车会员配置', 2: '二手车商缴费' } // 会员类型
export const paymentStatus = {
  已支付: 3,
  待支付: 1,
  支付失败: 2,
  已退款: 0,
  作废: 5,
  已生效: 6,
  已失效: 7,
  已过期: 8
} // 支付状态

export const shopPaymentStatus = {
  已取消: -4,
  已退款: 0,
  待支付: 1,
  支付失败: 2,
  支付完成: 3,
  正在支付: 4,
  已作废: 5,
  已生效: 6,
  已失效: 7,
  已过期: 8
} // 商家订单-支付状态

export const shopPaymentAuitStatus = {
  待审核中: 17,
  审核成功: 18,
  审核失败: 19,
  部分退款: 20
} // 商家订单-支付状态-审核状态

export const shopAuditStatusNew = {
  无需审核: 0,
  待审核: 1,
  拒绝: 2,
  通过: 3
} // 商家订单-审核状态

export const shopServiceType = {
  新车会员: 1,
  二手车会员: 2,
  询价线索: 3,
  试驾线索: 4,
  租车线索: 5,
  驾考线索: 6,
  微信线索: 7,
  年包线索: 8,
  收车线索: 9,
  二手车询价: 11
  // 收车线索: 9
} // 商家订单-服务类型

export const shopPayGoldType = {
  微信支付: 1,
  支付宝: 2,
  转账: 3,
  ApplePay: 5,
  金币支付: 6,
  余额支付: 7,
  '微信(收钱吧)': 61,
  '支付宝(收钱吧)': 62
} // 商家订单-支付类型

export const shopMainOrderSource = {
  0: '创建订单',
  1: '升级订单',
  2: '转移订单'
} // 商家订单-主订单来源

export const shopInvoiceType = {
  '-1': '未开票',
  0: '开票中',
  1: '已开票（待邮寄）',
  2: '已邮寄',
  3: '开票失败',
  5: '部分开票'
} // 商家订单-发票状态

export const feeTypes = {
  '50条询价线索包': 1,
  '100条询价线索包': 2,
  '200条询价线索包': 3,
  '500条询价线索包': 4,
  '1000条询价线索包': 5,
  年包线索包: 11
} // 线索付费类型
export const sendStatus = { 异地询价: 2, 手机空号: 1, 非售车型: 3, 自主赠送: 4 } // 赠送原因
export const payCategory = {
  微信支付: 1,
  转账: 2,
  支付宝: 3,
  '微信(收钱吧)': 61,
  '支付宝(收钱吧)': 62,
  账户余额: 7
} // 付费方式
export const payType = { 全部: '', ...payCategory } // 付费方式
export const sendGoldStatus = {
  平台赠送: 2,
  充值赠送: 1,
  新人赠送: 3,
  邀请赠送: 4,
  会员赠送: 5
} // 赠送原因
export const payGoldType = {
  全部: '',
  微信支付: 1,
  转账: 3,
  支付宝: 2,
  账户余额: 7,
  ApplePay: 5,
  '微信(收钱吧)': 61,
  '支付宝(收钱吧)': 62
} // 付费方式
export const abnormalState = {
  无异常: 0,
  空号错号: 1,
  异地线索: 2,
  非售车型: 3,
  其他异常: 4,
  重复询价: 5,
  未开启定位: 6,
  无法接通: 7
} // 异常状态
export const isLock = { 解锁: 1, 未解锁: 0 } // 是否解锁
export const isFree = {
  基础: 1,
  免费: 2,
  付费: 0,
  年包线索: 3,
  自主赠送: 4,
  厂家: 5
} // 是否免费
export const sourceEnum = {
  主包APP: 0,
  PC站: 1,
  M站: 2,
  经销商广告: 3,
  厂家活动客资: 4,
  微信小程序: 5,
  百度小程序: 6,
  头条小程序: 7
} // 来源
export const enquirysourceEnum = {
  主包APP: 0,
  PC站: 1,
  M站: 2,
  厂家活动客资: 4,
  经销商广告: 3,
  微信小程序: 5,
  百度小程序: 6,
  头条小程序: 7
} // 询价线索来源
export const packageType = { 全部: '', 按时间收费: 1, 按条数收费: 2 } // 付费方式
export const clueType = {
  全部: '',
  询价线索: 1,
  试驾线索: 2,
  询价年包: 3,
  微信线索: 5,
  驾考线索: 6,
  租车线索: 8
} // 线索类型
export const followUpStatus = { 未跟进: 1, 已备注: 2, 已回拨: 3 }
export const handleStatus = {
  未跟进: 1,
  跟进中: 2,
  已到店: 3,
  已成交: 4,
  战败: 5
}
export const wechatSettingsType = {
  配置数量: 1,
  配置时间: 2,
  关联询价: 3,
  关联二手会员: 4,
  关联询价及二手车会员: 5
}
export const wechatSettingsTypeToLabel = [
  { label: '配置数量', id: 1 },
  { label: '配置时间', id: 2 },
  { label: '关联询价', id: 3 },
  { label: '关联二手会员', id: 4 },
  { label: '关联询价及二手车会员', id: 5 }
]
// 标签
export const labelTitle = [
  {
    name: '售新车',
    code: 2
  },
  {
    name: '二手车',
    code: 14
  },
  {
    name: '租车商家',
    code: 17
  },
  {
    name: '驾考',
    code: 18
  },
  {
    name: '直播商家',
    code: 19
  },
  // {
  //   name: '俱乐部',
  //   code: 5
  // },
  // {
  //   name: '加油站',
  //   code: 6
  // },
  {
    name: '维修救援',
    code: 20
  }
]

// 角色标签
export const businessRoleLabel = [
  {
    name: '售新车商家',
    code: 2, // 角色id
    status: false, // 角色是否开启
    businessCode: [1, 2, 3, 4, 5, 7, 10, 11, 13, 14, 15, 22, 25, 26], // 角色对应的业务code
    operationCode: [1, 2, 3, 4, 5, 7, 10, 11, 14, 22, 25, 26] // 角色对应可以控制的业务开关code
  },
  {
    name: '二手车商家',
    code: 14,
    status: false,
    businessCode: [28, 6, 10, 11, 20, 23, 24, 27],
    operationCode: [28, 6, 10, 11, 20, 23, 24, 27] // 角色对应可以控制的业务开关code
  },
  {
    name: '租车商家',
    code: 17,
    status: false,
    businessCode: [9],
    operationCode: [9] // 角色对应可以控制的业务开关code
  },
  {
    name: '驾考商家',
    code: 18,
    status: false,
    businessCode: [8, 21],
    operationCode: [8, 21] // 角色对应可以控制的业务开关code
  },
  // {
  //   name: '俱乐部',
  //   code: 5,
  //   status: false
  // },
  // {
  //   name: '加油站',
  //   code: 6,
  //   status: false
  // },
  {
    name: '直播商家',
    code: 19,
    status: false,
    businessCode: [16],
    operationCode: [16] // 角色对应可以控制的业务开关code
  },
  // {
  //   name: '维修救援',
  //   code: 20,
  //   status: false,
  //   businessCode: [10, 11],
  //   operationCode: [10, 11] // 角色对应可以控制的业务开关code
  // },
  {
    name: '加油站',
    code: 99,
    status: false,
    businessCode: [12],
    operationCode: [12] // 角色对应可以控制的业务开关code
  }
  // {
  //   name: '公共业务', // 不属于业务角色，只是做为控制使用
  //   code: 10000,
  //   status: false,
  //   businessCode: [3, 7, 14],
  //   operationCode: [3, 7, 14] // 角色对应可以控制的业务开关code
  // }
]
// 业务标签

export const businessRoleLabel2 = [
  {
    businessName: '询价客户',
    businessType: 1, // 业务id
    openStatus: true, // 业务是否开启
    penaltyDueTime: '', // 处罚时间
    isOperate: true // 业务对应是否有相关操作按钮
  },
  {
    businessName: '试驾客户',
    businessType: 2,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: true
  },
  {
    businessName: '电话线索',
    businessType: 3,
    openStatus: false,
    penaltyDueTime: '2022-1-10',
    isOperate: true
  },
  {
    businessName: '直通车',
    businessType: 4,
    openStatus: false,
    penaltyDueTime: '2022-1-10',
    isOperate: true
  },
  {
    businessName: '一口价',
    businessType: 5,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: true
  },
  {
    businessName: '二手车',
    businessType: 6,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: true
  },
  {
    businessName: '店铺活动',
    businessType: 7,
    openStatus: true,
    penaltyDueTime: '',
    isOperate: true
  },
  {
    businessName: '驾考报名',
    businessType: 8,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: true
  },
  {
    businessName: '租车服务',
    businessType: 9,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: true
  },
  {
    businessName: '维修',
    businessType: 10,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '救援',
    businessType: 11,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '加油站',
    businessType: 12,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: true
  },
  {
    businessName: '在售车型',
    businessType: 13,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: true
  },
  {
    businessName: '微信线索',
    businessType: 14,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: true
  },
  {
    businessName: '广告营销',
    businessType: 15,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '直播售车',
    businessType: 16,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '投诉管理',
    businessType: 17,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '电话线索',
    businessType: 18,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '微信线索',
    businessType: 19,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '店铺活动',
    businessType: 20,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '店铺活动',
    businessType: 21,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '分期(售新车)',
    businessType: 22,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '分期(二手车)',
    businessType: 23,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '收车&估价',
    businessType: 24,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  },
  {
    businessName: '联系销售',
    businessType: 25,
    openStatus: false,
    penaltyDueTime: '',
    isOperate: false
  }
]
export const platformEnum = {
  APP: -1, // 不存在的类型，查询时转换为 '1,2'，临时兼容，后期不再支持
  OSS: 5,
  PC: 4,
  Android: 1,
  iOS: 2
  // 'WAP': 3
}
export const platformAllEnum = appendAll(platformEnum)

export const whetherPrime = {
  全部: '',
  优质: 1,
  非优质: 0
}

// 来源列表
export const platformList = {
  1: 'Android',
  2: 'iOS',
  3: 'WAP',
  4: 'PC',
  5: 'OSS',
  6: 'PC车库',
  7: '经销商小程序',
  8: 'wx_garage',
  9: 'wx_motorfans'
}
export const platformList2 = {
  Android: 1,
  iOS: 2,
  WAP: 3,
  PC: 4,
  OSS: 5,
  PC车库: 6,
  经销商小程序: 7,
  wx_garage: 8,
  wx_motorfans: 9
  // '摩托范商家版': 10,
}
export const articleStatusEnum = {
  审核通过: 1, // 接口文档对应 显示
  待审核: 2,
  审核不通过: 3,
  仅自己可见: 4,
  永久删除: 0,
  个人删除: -1
}

export const dynamicClassification = appendAll({
  普通动态: 1,
  外链: 2,
  小视频: 3,
  长视频: 4
})
export const articleStatusAllEnum = appendAll(articleStatusEnum)

export const articleListStatusEnum = {
  '-1': '个人删除',
  0: '系统删除',
  1: '显示',
  2: '待审核',
  3: '审核不通过'
}
export const articleListStatusColorEnum = {
  '-1': 'red',
  0: 'red',
  1: '#606266',
  2: '#DAA520',
  3: 'red'
}
// 推送文章的消息类型
export const pushMsgType = {
  文章: 'essay_detail',
  动态: 'moment_detail',
  链接: 'link'
}
// 热搜推荐的标识类型
export const searchDentify = {
  无: 0,
  新: 6,
  热: 2
}
// 首页推荐类型
export const homeRecommendType = {
  热门推荐: 'essay_detail',
  // '智能推荐': 'xianjian',
  车文: 'follow_car',
  // '浏览车文': 'view_car',
  // '关注人文': 'follow_user',
  广告: 'banner_detail',
  // '附近动态': 'location',
  优质动态: 'moment_detail',
  普通集合: 'normal_block',
  热搜: 'search_block'
}
// 首页小组件
export const homeBlockType = {
  车辆集合: 'car_detail_block',
  用户集合: 'user_block',
  摩友圈集合: 'hoop_block',
  小视频集合: 'micro_video_block'
}
// 首页普通集合推荐类型
export const homeRecommendList = {
  小视频集合: 'micro_video_block',
  车辆集合: 'car_detail_block',
  话题集合: 'short_topic_block',
  用户集合: 'user_block',
  摩友圈集合: 'hoop_block'
}

export const approveType = {
  // 认证类别
  8: '女摩头',
  1: '行家',
  2: '达人',
  4: '地域骑士',
  5: '企业',
  6: '自媒体',
  7: '官方媒体',
  9: '实名认证',
  14: '实人认证'
}
export const onSaleEnum = {
  // 在售状态
  停售: 0,
  在售: 1,
  即将上市: 2,
  大陆未引进: 3,
  未上市: 6,
  未知: 5
}
export const produceWay = {
  // 生产方式
  进口: '3',
  合资: '2',
  国产: '1'
}
export const onSaleEnumAll = appendAll(onSaleEnum)
export const goodAbsEnum = {
  // ABS
  无: '0',
  有: '1'
}
export const activityType = {
  // 活动类型
  新建活动: '1',
  修改活动: '2'
}
export const activityTypeAll = appendAll(activityType)
export const activityStatusEnum = {
  // 活动状态
  待审核: '1', // 接口文档对应 显示
  审核通过: '2',
  审核不通过: '3',
  已失效: '4'
}
export const activityStatusEnumAll = appendAll(activityStatusEnum)
export const activityInfoType = {
  // 活动详情类型
  优惠: 7, // 接口文档对应 显示
  试驾: 5,
  开业: 8,
  骑行: 1,
  展会: 4,
  其他: 6
}
export const activeStatus = {
  // 活动状态类型
  全部: '',
  审核中: 4,
  进行中: 2,
  审核未通过: 5,
  已结束: 3,
  已删除: 6,
  已撤销: 0
}
export const vehicleInfoType = {
  // 车辆类型
  机动车: 1, // 接口文档对应 显示
  新能源: 2
}
export const activityAdvertise = {
  // 活动详情宣传范围
  本市: 1, // 接口文档对应 显示
  本省置顶: 2,
  全国置顶: 3
}
export const serviceList = [
  {
    values: '摩托车经销商',
    selected: false,
    code: 2
  },
  {
    values: '维修',
    selected: false,
    code: 5
  },
  {
    values: '住宿',
    selected: false,
    code: 6
  },
  {
    values: '加油站',
    selected: false,
    code: 4
  },
  {
    values: '俱乐部',
    selected: false,
    code: 3
  },
  {
    values: '救援',
    selected: false,
    code: 1
  },
  {
    values: '销售公司',
    selected: false,
    code: 13
  },
  {
    values: '二手车商',
    selected: false,
    code: 14
  },
  {
    values: '租车商家',
    selected: false,
    code: 17
  }
]

export const serviceListV2 = [
  {
    values: '经销商',
    selected: false,
    code: '2'
  },
  {
    values: '二手车商',
    selected: false,
    code: '14'
  },
  {
    values: '维修',
    selected: false,
    code: '5'
  },
  {
    values: '救援',
    selected: false,
    code: '1'
  }
]

export const dataSources = {
  // 车辆认证数据来源
  车库: '1', // 接口文档对应 显示
  手动录入: '2'
}
export const auditStatus = {
  // 车辆认证审核状态
  拒绝: '0',
  通过: '1', // 接口文档对应 显示
  待审核: '2',
  取消: '3'
}
export const brandProductSource = {
  1: '国产',
  2: '合资',
  3: '进口',
  4: '中国台湾',
  5: '中国香港',
  6: '中国澳门'
}
export const brandEnergyType = {
  1: '燃油',
  2: '纯电动',
  3: '混合'
}
export const statusObjectList = {
  已通过: '1',
  不通过: '2',
  已删除: '3',
  修改待审核: '4'
}

export const bizCategoriesType = {
  售新车: '-1',
  二手车: '6',
  驾考: '8',
  租车: '9',
  维修: '10',
  救援: '11',
  加油站: '12'
}
export const dynamicAuditStatusEnum = {
  // 动态审核状态
  // '全部': '1,2,3',
  已审核: '1', // 接口文档对应 显示
  未审核: '2',
  未通过: '3'
}
export const authorizationStatus = {
  // 授权状态
  已通过: '1', // 接口文档对应 显示
  未通过: '2'
}
export const authorizationStatus2 = {
  // 授权状态
  已通过: '1', // 接口文档对应 显示
  未通过: '2',
  待审核: '0'
}
export const localAuthorizationList = {
  // 地方授权状态
  省级授权: 2,
  市级授权: 1,
  '区/县级授权': 0,
  '乡/镇级授权': 3
}

export const qCellCore = [
  // 归属地
  '京',
  '津',
  '冀',
  '晋',
  '蒙',
  '辽',
  '吉',
  '黑',
  '沪',
  '苏',
  '浙',
  '皖',
  '闽',
  '赣',
  '鲁',
  '豫',
  '鄂',
  '湘',
  '粤',
  '桂',
  '琼',
  '渝',
  '川',
  '黔',
  '滇',
  '藏',
  '陕',
  '甘',
  '青',
  '宁',
  '新',
  '台',
  '港',
  '澳'
]

export const usedVehiclesType = {
  // 二手车权限状态
  全部: '',
  待审核: '1', // 接口文档对应 显示
  审核通过: '2',
  审核不通过: '3'
}

export const bannerLocation = {
  // 展示位置
  Banner: '0',
  广告: '1',
  启动页: '2',
  活动: '3',
  短话题: '4',
  车库首页: '5',
  活动列表: '6',
  天气: '7',
  能量活动: '8',
  骑行首页: '9',
  首页其他页签banner: '10',
  首页弹出广告: '11',
  文章详情广告: '12',
  我的签到处banner: '13',
  附近的人广告: '14',
  电动车: '15',
  首页引导: '16',
  选车页面弹窗: '18',
  用车页面弹窗: '19'
}

export const frequencyList = {
  // 展示频次
  当日打开一次: '2',
  仅打开一次: '3'
}

export const topicTypeList = {
  0: '兴趣',
  1: '车型',
  2: '品牌'
}

// 能源类型
export const energyList = [
  {
    value: '',
    name: '全部'
  },
  {
    value: '1',
    name: '燃油'
  },
  {
    value: '2',
    name: '纯电动'
  },
  {
    value: '4',
    name: '混动'
  },
  {
    value: '3',
    name: '柴油'
  }
]
// 上市国家及上市价格
export const appearMarket = {
  0: {
    country: '请选择',
    value: '',
    currency: ''
  },
  1: {
    country: '加拿大',
    value: 1,
    currency: '加拿大元C$'
  },
  2: {
    country: '美国',
    value: 2,
    currency: '美元$'
  },
  3: {
    country: '德国',
    value: 3,
    currency: '欧元€'
  },
  4: {
    country: '意大利',
    value: 4,
    currency: '欧元€'
  },
  5: {
    country: '俄罗斯',
    value: 5,
    currency: '俄罗斯卢布руб'
  },
  6: {
    country: '泰国',
    value: 6,
    currency: '泰铢฿'
  },
  7: {
    country: '日本',
    value: 7,
    currency: '日元J￥'
  },
  8: {
    country: '墨西哥',
    value: 8,
    currency: '墨西哥比索Peso'
  },
  9: {
    country: '巴西',
    value: 9,
    currency: '雷亚尔R$'
  },
  10: {
    country: '阿根廷',
    value: 10,
    currency: '阿根廷比索ARS$'
  },
  11: {
    country: '西班牙',
    value: 11,
    currency: '欧元€'
  },
  12: {
    country: '荷兰',
    value: 12,
    currency: '欧元€'
  },
  13: {
    country: '希腊',
    value: 13,
    currency: '欧元€'
  },
  14: {
    country: '卢森堡',
    value: 14,
    currency: '欧元€'
  },
  15: {
    country: '芬兰',
    value: 15,
    currency: '欧元€'
  },
  16: {
    country: '法国',
    value: 16,
    currency: '欧元€'
  },
  17: {
    country: '葡萄牙',
    value: 17,
    currency: '欧元€'
  },
  18: {
    country: '比利时',
    value: 18,
    currency: '欧元€'
  },
  19: {
    country: '瑞典',
    value: 19,
    currency: '瑞典克朗Kr'
  },
  20: {
    country: '丹麦',
    value: 20,
    currency: '丹麦克朗DKK'
  },
  21: {
    country: '捷克',
    value: 21,
    currency: '捷克克朗Kcs'
  },
  22: {
    country: '波兰',
    value: 22,
    currency: '兹罗提PLZ'
  },
  23: {
    country: '英国',
    value: 23,
    currency: '英镑￡'
  },
  24: {
    country: '越南',
    value: 24,
    currency: '越南盾₫'
  },
  25: {
    country: '印度',
    value: 25,
    currency: '印度卢比₹'
  },
  26: {
    country: '印尼',
    value: 26,
    currency: '印度尼西亚盾Rp'
  },
  27: {
    country: '韩国',
    value: 27,
    currency: '韩币₩'
  },
  28: {
    country: '缅甸',
    value: 28,
    currency: '缅甸元MMK'
  },
  29: {
    country: '沙特',
    value: 29,
    currency: '里亚尔SAR'
  },
  30: {
    country: '土耳其',
    value: 30,
    currency: '里拉₺'
  },
  31: {
    country: '中国台湾',
    value: 31,
    currency: '台币NT$'
  },
  32: {
    country: '中国香港',
    value: 32,
    currency: '港币HK$'
  },
  33: {
    country: '欧洲',
    value: 33,
    currency: '欧元€'
  }
}

// 圈子推荐/置顶/精华枚举
export const hoopRelEnum = {
  全部: '',
  是: 1,
  否: 0
}

// 举报类型
export const whistleblowersList = {
  1: '拉黑',
  2: '禁言',
  3: '封号'
}

// 举报状态
export const whistleblowersStatus = {
  0: '永久',
  1: '1天',
  3: '3天',
  7: '7天'
}

// 封号处理原因
export const whistleblowersReasonList = [
  '垃圾广告',
  '低俗色情',
  '人身攻击',
  '违法信息',
  '政治敏感',
  '信息不实',
  '冒用账号',
  '虚假欺骗',
  '抄袭',
  '内容引战',
  '其他'
]

// 封号处理原因(二手车)
export const whistleblowersCarReasonList = [
  '买家诈骗（短信诈骗）',
  '卖家诈骗',
  '发布违法违规车源',
  '发布车源P图',
  '其他'
]

// 封号处理原因(二手车)枚举
export const whistleblowersCarReasonEnum = {
  '买家诈骗（短信诈骗）': 1,
  卖家诈骗: 2,
  发布违法违规车源: 3,
  发布车源P图: 4,
  其他: 5
}

// 内容列表显示列名称
export const articleDisplayList = [
  'ID',
  '作者',
  '列表标题/内容',
  '预览',
  '分类',
  '分级',
  '是否推送',
  '状态',
  '推荐',
  '百家号',
  'CTR',
  '平台',
  '真实浏览量',
  '曝光量',
  '审核人',
  '回复',
  '审核时间',
  '发布时间',
  '修改时间'
]

// 内容修改审核类型
export const circleInfoStatus = {
  0: '未发起',
  1: '已通过',
  2: '未通过',
  3: '待审核'
}

// 推送发送状态
export const pushStatusEnum = {
  已发送: '1',
  未发送: '0',
  发送中: '2',
  已撤回: '3'
}
export const pushStatusAllEnum = appendAll(pushStatusEnum)

// 推送用户类型
export const userCategoryEnum = {
  新用户过滤: '1',
  老用户过滤: '2',
  全量过滤: '3',
  新用户非过滤: '4',
  老用户非过滤: '5',
  全量非过滤: '6',
  单一用户: '7',
  画像系统用户: '8',
  画像系统用户过滤: '9',
  指定用户: '10',
  指定用户过滤: '11'
}
export const userCategoryAllEnum = appendAll(userCategoryEnum)

// 推送类型
export const pushCategoryEnum = {
  文章: 'essay_detail',
  车辆: 'car_detail',
  能量商城首页: 'my_energy',
  活动: 'activity',
  用车页面: 'use_motor_index',
  我的页面: 'user_index',
  首页: 'home_index',
  选车页面: 'select_motor_index',
  摩友圈首页: 'index_hoop',
  某个摩友圈详情页: 'hoop_home',
  新车主页: 'new_car_main',
  二手车主页: 'transaction_home',
  二手车搜索结果页: 'second_hand_car_search_result',
  商城首页: 'mall_index',
  '商城-选品库': 'mall_choopseProductDepotId',
  '商城-品牌库': 'mall_brandId',
  '商城-一级分类': 'mall_firstCategory',
  '商城-二级分类': 'mall_secondCategory',
  单个京东商品: 'mall_jd_detail',
  单个淘宝商品: 'mall_tb_detail',
  单个自营商品: 'mall_zy_detail'
}
export const pushCategoryAllEnum = appendAll(pushCategoryEnum)

// 商家店铺类型
export const shopTypeEnum = {
  1: '新车会员',
  2: '新车过期未续费会员',
  3: '二手车会员',
  4: '二手车过期未续费会员',
  5: '驾考会员',
  6: '驾考过期未续费会员',
  7: '租车会员',
  8: '租车过期未续费会员',
  9: '未开通会员'
}

// 经销商详情-会员角色信息
export const shopDetailTypeEnum = {
  1: '新车会员',
  2: '二手车会员',
  3: '驾校会员',
  4: '租车会员'
}

// 商家用户角色
export const roleTypeEnum = {
  1: '管理员',
  2: '店长',
  3: '店员'
}

// 商家推送渠道
export const channelTypeEnum = {
  ios: '苹果',
  android: '安卓'
}

// 商家端推送落地页
export const pushBusinessSceneEnum = {
  // 询价客户: '1',
  // 在售车型: '13',
  // 试驾: '2',
  // 电话: '3',
  // 直通车: '4',
  // 一口价: '5',
  // 二手车: '6',
  // 驾考报名: '8',
  询价客户: 'AskPriceListScene',
  在售车型: 'OnsaleTypeScene',
  试驾: 'DriveTestListScene',
  电话: 'TelCustomerListScene',
  直通车: 'CouponMainScene',
  一口价: 'OnePriceListScene',
  二手车: 'MySecondHandScene',
  驾考报名: 'DrivingTestHome',
  首页: 'Tab',
  'H5链接（支持内链和外链）': 'Web',
  无: ''
}

// 推送渠道
export const pushPlatformEnum = {
  所有: '',
  全部: 'all',
  苹果: 'ios',
  华为: 'hw',
  小米: 'xm',
  vivo: 'vi',
  魅族: 'mz',
  oppo: 'op',
  极光: 'jpush',
  荣耀: 'ry'
}

// 订单状态
export const orderStatusEnum = {
  已完成: '4',
  待支付: '0',
  支付失败: '-5',
  已支付待核销: '1',
  订单取消: '-4',
  已退款: '10',
  已核销待上传发票: '301',
  发票审核中: '201'
}
export const orderStatusAllEnum = appendAll(orderStatusEnum)

// 补贴状态
export const isReceiveEnum = {
  未领取: '0',
  已领取: '1'
}
export const isReceiveAllEnum = appendAll(isReceiveEnum)

// 支付方式
export const payTypeEnum = {
  微信: '1',
  支付宝: '2',
  花呗: '4'
}
export const payTypeAllEnum = appendAll(payTypeEnum)

// 是否发票状态
export const invoiceTypeEnum = {
  未开: '0',
  已开: '1'
}
export const invoiceTypeAllEnum = appendAll(invoiceTypeEnum)

export const giveFlagEnum = {
  非赠送: '0',
  已赠送: '1'
}

export const giveFlagAllEnum = appendAll(giveFlagEnum)

// 发票状态
export const invoiceStatusSpecialEnum = {
  申请中: 0,
  已开票: 1,
  已邮寄: 2,
  拒绝: 3
}

export const invoiceStatusSpecialAllEnum = appendAll(invoiceStatusSpecialEnum)

// 发票状态
export const invoiceStatusEnum = {
  申请中: 0,
  已开票: 1,
  已邮寄: 2,
  拒绝: 3,
  作废: 4
}
export const invoiceStatusAllEnum = appendAll(invoiceStatusEnum)

// 发票类型
export const invoiceType = {
  企业增值税专用发票: 1,
  企业增值税普通发票: 2,
  个人增值税普通发票: 3,
  增值税电子普通发票: 4
}

// 提现状态
export const withDrawStatusEnum = {
  全部: '',
  '发起提现-待业务审核': 6,
  '发起提现-待财务审核': 14,
  '审核成功-待打款': 10,
  提现成功: 7,
  提现失败: 8,
  财务审核失败: 11,
  业务审核失败: 15,
  打款中: 17
}
export const withDrawStatusAllEnum = appendAll(withDrawStatusEnum)

// 结款状态
export const settlementStatus = {
  未结款: 0,
  结款成功: 1,
  结款失败: 2,
  第三方平台异常: 5,
  平台异常: 6
}
export const settlementStatusAllEnum = appendAll(settlementStatus)
// 结款状态

export const wsettlementStatusAllEnum = appendAll(withDrawStatusEnum)
// 二手车订单状态
export const usedCarOrderStatusEnum = {
  0: '等待买家付款',
  101: '等待卖家处理',
  102: '等待买家确认',
  4: '交易成功',
  '-4': '订单取消',
  103: '申请退款'
}

// 二手车退款状态
export const usedCarRefundStatusEnum = {
  1: '买家申请退款',
  2: '卖家同意退款',
  3: '卖家拒绝退款',
  4: '平台审核中',
  5: '平台拒绝退款',
  6: '平台同意退款',
  7: '退款成功',
  8: '买家撤销退款申诉',
  9: '买家撤销退款'
}
export const usedCarRefundStatusEnumDetail = {
  1: '买家申请退款',
  2: '卖家同意退款',
  3: '卖家拒绝退款',
  4: '买家申诉退款',
  5: '平台拒绝退款',
  6: '平台同意退款',
  7: '退款成功',
  8: '买家撤销退款申诉',
  9: '买家撤销退款'
}

// 直通车业务订单类型
export const orderTypeEnum = {
  商家充值: 6,
  体验金扣除: 7,
  体验金充值: 8
}

// 直通车业务支付状态
export const paymentStatusEnum = { 完成: 4, 待支付: 0, 支付失败: 601 }

// 金币充值支付状态
export const goldStatusEnum = {
  已支付: 4,
  待支付: 0,
  已退款: 10,
  已作废: 13,
  已取消: -4
}

// 线索包支付状态
export const clueStatusEnum = {
  完成: 4,
  待支付: 0,
  订单取消: -4,
  退款成功: 10,
  作废: -3
}

// 优惠券订单--返现领取状态
export const remitStatusEnum = {
  未领取: 0,
  已领取: 1
}
export const remitStatusAllEnum = appendAll(remitStatusEnum)

// 优惠券订单支付方式
export const couponPayTypeEnum = {
  微信: '1',
  支付宝: '2'
}
export const couponPayTypeAllEnum = appendAll(couponPayTypeEnum)

// 优惠券订单--订单状态
export const couponOrderStatusEnum = {
  待支付: 0,
  已付款: 1,
  已完成: 4,
  订单取消: -4,
  发票审核中: 201
}
export const couponOrderStatusAllEnum = appendAll(couponOrderStatusEnum)

// 优惠券订单--退款状态
export const couponRefundStatusEnum = {
  已完成: 1,
  失败: 2
}
export const couponRefundStatusAllEnum = appendAll(couponRefundStatusEnum)

// 优惠券订单--订单类型
export const couponOrderTypeEnum = {
  优惠券订单: 4,
  优惠券赠送订单: 8
}
export const couponOrderTypeAllEnum = appendAll(couponOrderTypeEnum)

// 优惠券返现结款状态
export const counterStatusEnum = { 未结款: 1, 已结款: 2 }

// 优惠券发票审核状态
export const couponInvoiceAuditStatus = {
  全部: '',
  待审核: 0,
  通过: 1,
  不通过: 2
}

// 商家审核状态
export const shopAuditStatus = {
  全部: '',
  未审核: 0,
  通过: 1,
  驳回: 2
}

// 实名认证状态
export const certificationStatus = {
  全部: '',
  审核中: 0,
  已认证: 1,
  审核未通过: 2
}

// 优惠券发票审核--发票订单类型
export const couponInvoiceTypeEnum = {
  摩托范补贴: 0,
  一口价: 1
}
export const couponInvoiceTypeAllEnum = appendAll(couponInvoiceTypeEnum)

// 经销商可提现账户明细收支类型
export const accountTypeEnum = {
  商家提现: 1,
  新车销售定金结算: 2,
  新车销售全款结算: 3,
  直通车账户转入: 7
}
export const accountTypeAllEnum = appendAll(accountTypeEnum)

// 直通车余额账户明细收支类型
export const directTrainTypeEnum = {
  商家充值: 4,
  '优惠券成单（老数据）': 5,
  优惠券售出: 6,
  优惠券退单: 8,
  商家转出: 9,
  优惠券成单解冻: 11,
  优惠券成单扣款: 12
}
export const directTrainTypeAllEnum = appendAll(directTrainTypeEnum)

// 直通车体验金账户明细收支类型
export const experienceGoldTypeEnum = {
  平台赠送体验金: 1,
  平台扣除体验金: 2,
  '优惠券成单（老数据）': 3,
  优惠券售出: 4,
  优惠券退单: 5,
  优惠券成单解冻: 6,
  优惠券成单扣款: 7
}
export const experienceGoldTypeAllEnum = appendAll(experienceGoldTypeEnum)

// 金币账户收支类型
export const inComeTypeEnum = {
  商家充值: 1,
  二手车置顶: 2,
  购买询价线索包: 3,
  购买试驾线索包: 4,
  充值赠送: 5,
  平台赠送: 6,
  驾考报名: 7,
  商家活动投放: 8,
  商家活动置顶: 9,
  活动未通过: 10,
  活动置顶撤销: 11,
  新人赠送: 12,
  邀请赠送: 13,
  会员赠送: 14,
  投放广告: 15,
  二手车会员赠送: 16,
  二手车会员作废: 17,
  二手车会员退单: 18,
  二手车会员升级: 19,
  商城订单消耗: 20,
  商城订单退款: 21,
  批发订单返现: 22
}

// 创建优惠券---排量
export const volumeEnum = {
  '150cc以下': '1',
  '150-250cc': '2',
  '250-400cc': '3',
  '400cc以上': '4'
}
export const volumeAllEnum = appendAll(volumeEnum)

// 创建优惠券---车辆类型
// 车型（1=跨骑,2=弯梁,3=踏板,4=巡航太子,5=越野,6=电动,7=新车,8=街车,9=跑车,10=旅行,11=拉力,12=三轮,13=复古,14=其他,15=mini），多个值按英文逗号分割
export const goodTypeEnum = {
  跨骑: 1,
  弯梁: 2,
  踏板: 3,
  巡航太子: 4,
  越野: 5,
  街车: 8,
  跑车: 9,
  旅行: 10,
  拉力: 11,
  三轮: 12,
  复古: 13,
  其他: 14,
  MINI: 15
}
export const goodTypeEnum2 = {
  弯梁: '弯梁',
  踏板: '踏板',
  巡航太子: '巡航太子',
  越野: '越野',
  街车: '街车',
  跑车: '跑车',
  旅行: '旅行',
  拉力: '拉力',
  三轮: '三轮',
  复古: '复古',
  其他: '其他',
  MINI: 'MINI'
}
export const goodTypeAllEnum = appendAll(goodTypeEnum)

// 经销商管理----价格折扣
export const discountEnum = {
  无: '',
  '4折': 0.4,
  '4.5折': 0.45,
  '4.8折': 0.48,
  '5折': 0.5,
  '5.5折': 0.55,
  '5.6折': 0.56,
  '6折': 0.6,
  '6.4折': 0.64,
  '6.5折': 0.65,
  '7折': 0.7,
  '7.2折': 0.72,
  '7.5折': 0.75,
  '8折': 0.8,
  '8.5折': 0.85,
  '9折': 0.9,
  '9.5折': 0.95,
  '10折': 1
}
// 二手车商折扣----价格折扣
export const secondMemberDiscountEnum = {
  无: '',
  '5折': 0.5,
  '5.5折': 0.55,
  '6折': 0.6,
  '6.5折': 0.65,
  '7折': 0.7,
  '7.5折': 0.75,
  '8折': 0.8,
  '8.5折': 0.85,
  '9折': 0.9,
  '9.5折': 0.95
}

// 经销商权益表----会员级别
export const memberLevelEnum = {
  白银会员: '1',
  黄金会员: '2',
  白金会员: '3',
  钻石会员: '4',
  黑金会员: '5'
}

// 活动管理---付费等级
export const payLevelEnum = {
  未付费: '0',
  白银会员: '1',
  黄金会员: '2',
  白金会员: '3',
  钻石会员: '4',
  黑金会员: '5'
}

//会员级别-人员限制
export const memberLevelPeople = [
  {
    level: 1,
    levelName: '白银会员',
    administrator: 2, //管理员
    shopManager: 3, // 店长
    salesperson: 5 // 销售人员
  },
  {
    level: 2,
    levelName: '黄金会员',
    administrator: 2,
    shopManager: 3,
    salesperson: 5
  },
  {
    level: 3,
    levelName: '白金会员',
    administrator: 2,
    shopManager: 3,
    salesperson: 5
  },
  {
    level: 4,
    levelName: '钻石会员',
    administrator: 4,
    shopManager: 5,
    salesperson: 10
  },
  {
    level: 5,
    levelName: '黑金会员',
    administrator: 5,
    shopManager: 7,
    salesperson: 20
  }
]

export const memberPayLevelEnum = appendAll(payLevelEnum)
export const memberLevelAllEnum = appendAll(memberLevelEnum)

export const alephAll = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z'
] // 首字母列表

// 二手车主隐私风控 审核状态
export const ownerPrivacyStatus = {
  全部: '',
  待审核: 0,
  已封停: 1,
  已解封: 2
}

// 二手车主隐私风控 异常来源
export const ownerPrivacySourceStatus = {
  全部: '',
  '微信获取异常（1日）': 0,
  '微信获取异常（7日）': 1,
  '手机获取异常（1日）': 2,
  '手机获取异常（7日）': 3
}
export const reviewStatus = {
  0: '审核中',
  1: '', // 审核通过不展示
  2: '审核不通过'
}

export const machineReviewStatus = [
  [-1, '全部'],
  [1, '通过'],
  [2, '未通过']
]

export const cluePackageType = {
  1: '询价',
  6: '驾考',
  8: '租车'
}
export const orderCluePackageType = {
  1: '新车询价',
  6: '驾考',
  8: '租车',
  2: '试驾',
  9: '收车',
  11: '二手车询价'
}

export const auditAfterStatus = [
  [-1, '全部'],
  [0, '待审核'],
  [1, '审核通过'],
  [2, '审核未通过']
]
export const carOnSaleEnum = {
  0: '停售',
  1: '在售',
  2: '即将上市',
  3: '未引进',
  4: '延期上市',
  5: '未知',
  6: '未上市'
}
export const carLiceseTypeList = {
  全部: '',
  D证: 1,
  E证: 2,
  F证: 3,
  安驾培训: 4
}
export const carLiceseTypeList2 = {
  全部: '',
  D证: 1,
  E证: 2,
  F证: 3,
  安驾培训: 4
}
export const clueTypes = { 全部: '', 领劵: 1, 学车咨询: 2, 电话: 3, 微信: 4 }
export const clueTypesArr = ['领劵', '学车咨询', '电话', '微信']
export const growOfficerStatusList = {
  '': '全部',
  0: '正式身份',
  1: '试用中',
  2: '试用失败',
  3: '勋章回收'
}
export const commentorStatusList = {
  '': '全部',
  0: '正式身份',
  1: '试用中',
  2: '试用失败',
  3: '勋章回收'
}
export const patrolOfficerStatusList = {
  '': '全部',
  3: '失效',
  0: '生效'
}
export const couponTypeList = {
  '': '全部',
  1: '全场券',
  2: '品类券',
  3: '指定商品券'
}
export const goodsSalesModel = {
  1: '全款',
  2: '预付款',
  3: '定金',
  4: '预留客资'
}
export const inventoryEnum = {
  全部: '',
  是: 1,
  否: 0
}

export const violationTypeList = {
  // 1: {
  //   1: '虚假广告宣传',
  //   2: '威胁、辱骂、骚扰',
  //   3: '卖家拒绝平台交易',
  //   4: '钓鱼网站',
  //   5: '卖家无相关资质',
  //   6: '涉嫌刷单',
  //   7: '恶意改价'
  // }, // 交易欺诈与纠纷举报
  // 2: {
  //   101: '销售车辆与实际车型不符',
  //   102: '拒绝平台交易',
  //   103: '低价引流（实际售价更高）',
  //   104: '不是卖车的，在平台打广告的',
  //   105: '卖家无车或车子已经卖掉了',
  //   106: '故意不卖',
  //   107: '图片、内容引起不适',
  //   108: '无法与卖家取得联系'
  // }, // 虚假车源与滥发举报
  // 3: {
  //   201: '水车',
  //   202: '套牌车',
  //   203: '盗抢车',
  //   204: '盗用他人信息'
  // } // 违规车源举报
  1: {
    9: '短信链接诈骗',
    10: '平台订单纠纷',
    11: '私下交易纠纷',
    12: '其他交易纠纷'
  },
  2: {
    101: '销售车型与实际不符',
    109: '销售款型与实际不符',
    106: '卖家故意不卖',
    105: '卖家无车或车子已经卖掉了',
    108: '无法与卖家取得联系',
    204: '盗用他人信息'
  }, // 虚假车源举报
  4: {
    102: '拒绝平台交易',
    301: '重复铺货、打广告',
    103: '低价引流（实际售价更高）'
  }, // 滥发信息举报
  3: {
    201: '水车'
  } // 违法违规举报
}

export const reportTypeList = {
  1: '诈骗及交易纠纷举报',
  2: '虚假车源举报',
  4: '滥发信息举报',
  3: '违法违规举报'
}

export const reportReasonTypeList = {
  1: '车辆不可上路',
  2: '商家车出租出去了',
  3: '商家没有这台车',
  4: '商家额外收租金',
  5: '商家地址不对',
  6: '商家联系不上',
  7: '虚假车源/违规车源',
  8: '商家是假的/盗用他人店铺信息',
  9: '其它'
}

export const fieldName = [
  {
    title: '举报类型',
    value: 'reportType'
  },
  {
    title: '问题类型',
    value: 'dealIssue'
  },
  {
    title: '相关订单',
    value: 'relatedItem'
  },
  {
    title: '交易平台',
    value: 'tradingDesk'
  },
  {
    title: '收货状态',
    value: 'whetherGoods'
  },
  {
    title: '损失金额',
    value: 'lossAmount'
  },
  {
    title: '被侵害人',
    value: 'isSelf'
  },
  {
    title: '姓名',
    value: 'tortUserName'
  },
  {
    title: '联系方式',
    value: 'myContactWay'
  },
  {
    title: '事件描述',
    value: 'eventProcedure'
  }
]

export const capitalsLoss = {
  1: '有资金损失，通过平台订单交易',
  2: '有资金损失，未通过平台订单交易',
  3: '无资金损失'
}

export const thirdPlatform = {
  1: '支付宝',
  2: '微信',
  3: 'QQ',
  4: '银行卡转账',
  5: '其他'
}

export const goodsReceived = {
  1: '已收到/已退货',
  2: '没有收到/收到空包裹'
}
export const associatedObject = {
  1: '用户',
  2: '商家',
  3: '厂家',
  4: '平台'
}
export const workerOrderStatus = {
  1: '待分配',
  2: '处理中',
  3: '待确认',
  4: '已处理',
  5: '已关闭'
}
export const workerOrderOptStatus = {
  1: '创建',
  2: '编辑',
  3: '分配',
  4: '处理',
  5: '驳回',
  6: '完成',
  7: '关闭',
  8: '发送消息',
  9: '添加记录'
}

// 商品模式
export const goodsSellMOdel = {
  '': '全部',
  1: '全款模式',
  2: '预付款模式',
  4: '预留客资模式'
}
// 商品状态
export const goodsSellStatus = {
  '': '全部',
  1: '售卖中',
  0: '已下架'
}
export const energeRange = {
  1: { max: 10, min: 0.1 },
  2: { max: 90, min: 10.1 }
}

export const reportType = [
  {
    value: '',
    label: '全部'
  },
  {
    value: 1,
    label: '诈骗及交易纠纷举报',
    children: [
      {
        value: '',
        label: '全部'
      },
      {
        value: 9,
        label: '短信链接诈骗'
      },
      {
        value: 10,
        label: '平台订单纠纷'
      },
      {
        value: 11,
        label: '私下交易纠纷'
      },
      {
        value: 12,
        label: '其他交易纠纷'
      }
    ]
  },
  {
    value: 2,
    label: '虚假车源举报',
    children: [
      {
        value: '',
        label: '全部'
      },
      {
        value: 101,
        label: '销售车型与实际不符'
      },
      {
        value: 109,
        label: '销售款型与实际不符'
      },
      {
        value: 105,
        label: '卖家无车或车子已经卖掉了'
      },
      {
        value: 106,
        label: '卖家故意不卖'
      },
      {
        value: 108,
        label: '无法与卖家取得联系'
      },
      {
        value: 204,
        label: '盗用他人信息'
      },
      {
        value: 8,
        label: '其他虚假车源举报'
      }
    ]
  },
  {
    value: 4,
    label: '滥发信息举报',
    children: [
      {
        value: '',
        label: '全部'
      },
      {
        value: 102,
        label: '拒绝平台交易'
      },
      {
        value: 103,
        label: '低价引流（实际售价更高）'
      },
      {
        value: 301,
        label: '重复铺货、打广告'
      },
      {
        value: 8,
        label: '其他滥发信息举报'
      }
    ]
  },
  {
    value: 3,
    label: '违法违规举报',
    children: [
      {
        value: '',
        label: '全部'
      },
      {
        value: 201,
        label: '水车'
      },
      {
        value: 8,
        label: '其他违法违规举报'
      }
    ]
  }
]
// 采购订单状态
export const purchaseOrderStatus = {
  待支付: '0',
  待发货: '1',
  已发货: '2',
  已完成: '4',
  已关闭: '-4'
}
export const purchaseOrderStatusAllEnum = appendAll(purchaseOrderStatus)

// 采购订单售后状态
export const purchaseAfterSalesStatus = {
  退款审核中: 0,
  '同意退款,退款成功': 1,
  '拒绝退款,售后完成': 2
}
export const purchaseAfterSalesStatusAllEnum = appendAll(
  purchaseAfterSalesStatus
)

// 采购订单付款方式
export const purchasePayStatus = {
  微信在线支付: 1,
  支付宝在线支付: 2
}
export const purchasePayStatusAllEnum = appendAll(purchasePayStatus)
// 开票状态
export const purchaseInvoiceStatus = {
  待开票: 0,
  已开票: 1
}

// 售卖渠道
export const channelList = {
  Q网: 1,
  E网: 2,
  'Q网&E网': 3
}

// 业务类型
export const businessTypeList = {
  经销商会员: 11,
  二手车会员: 12,
  询价线索包: 21,
  试驾线索包: 22,
  租车线索包: 23,
  驾考线索包: 24,
  微信线索包: 25,
  年包线索包: 26,
  收车线索包: 27,
  二手车询价线索包: 28,
  金币充值: 31,
  广告订单: 41
}

export const businessTypeVipList = {
  经销商会员: 11,
  二手车会员: 12
}

export const businessTypeClueList = {
  询价线索包: 21,
  试驾线索包: 22,
  租车线索包: 23,
  驾考线索包: 24,
  微信线索包: 25,
  年包线索包: 26,
  收车线索包: 27,
  二手车询价线索包: 28,
  金币充值: 31,
  广告订单: 41
}

// 流水类型
export const serialsTypeList = {
  1: '支付',
  2: '退款'
}

// 交易状态
export const transactionStatus = {
  0: '失败',
  1: '完成'
}

// 支付渠道
export const channelCodeList = {
  微信: 'WECHAT',
  支付宝: 'ALI',
  苹果: 'APPLE',
  收钱吧: 'SQB',
  线下转账: 'OFFLINE',
  钱包: 'WALLET'
}

// 支付方式
export const wayCodeList = {
  微信: 'WX',
  支付宝: 'ALI',
  线下转账: 'OFFLINE',
  钱包: 'WALLET'
}

// 流水-支付状态
export const paymentStatusNew = {
  支付成功: 1,
  退款成功: 2
}

// 流水-支付状态
export const payOrderStatusNew = {
  支付成功: 4,
  退款成功: 10
}

// 流水-动账账户
export const transactionAccountList = {
  微信6781: '微信6781',
  微信1784: '微信1784',
  微信5458: '微信5458',
  支付宝moto: '支付宝moto',
  支付宝moto2: '支付宝moto2',
  支付宝moto3: '支付宝moto3',
  线下转账: '线下转账'
}

// 厂家业务状态
export const manufacturerBusinessType = {
  舆情服务: '1',
  评论置顶服务: '2'
}

export const officerTypesList = {
  0: '增长官',
  1: '城市官',
  2: '安全官',
  3: '巡查官',
  4: '点评官'
}

export const refundOnlyStatusType = {
  订单权益删除: 0,
  订单权益保留: 1
}

export const drivingTestVipEnum = {
  1: '科一VIP',
  2: '科二VIP',
  3: '科三VIP',
  4: '科四VIP',
  5: '科一科四VIP',
  6: '科二科三VIP',
  7: '全科VIP',
  8: '科一直播课',
  9: '科四直播课',
  10: '基础VIP',
  11: '尊享VIP'
}

export const driverExamQuestionTypeEnum = {
  1: '科一精选100题',
  2: '科一考前密卷1',
  3: '科一考前密卷2',
  4: '科一真实考场模拟',
  5: '科一2小时精炼课',
  6: '科二考试项目讲解视频',
  7: '科三考试项目讲解视频',
  8: '科四精选100题',
  9: '科四考前密卷1',
  10: '科四考前密卷2',
  11: '科四真实考场模拟',
  12: '科四1小时精炼课',
  13: '满分学习精选100题',
  14: '满分学习考前密卷1',
  15: '满分学习考前密卷2',
  16: '满分学习真实考场模拟'
}

export const carModelLabels = {
  3619: '适合新手',
  144: '竞速超跑',
  146: '长途摩旅',
  145: '上班代步',
  1706: '适合女性',
  7879: '侉子风范'
}

export const TagType = {
  CONfIG: 1, // 款型 配置选项
  PACKAGE: 2 // 款型 选装包
}

export const payPriceStatusEnum = {
  全部: '-1',
  大于0元: 1,
  '0元': 0
}
