// 日志记录使用文件
import { deepCopy } from '@/utils'
// 将旧数据存储在session storage中，数据变更时，跟随接口将数据发送
// ['beforeAlterFirst', 'beforeAlterTwo', 'beforeAlterThree'] // sessionStorage存储的列表字段名
// 在request.js 中定义拦截发送请求和接收请求，值为true 时拦截请求将旧数据存储，为false时，将数据从session storage 中取出

// 第一个recordOldData 用于记录列表或详情数据
// recordBeforeAlter 存储变更数据，一般跟第一个recordOldData 配合使用
// batchRecordBeforeAlter 直接存储数，可用于详情存储
// updataRecordOneData 存储列表变更数据

// 存储list页面展示数据 数组格式(多用于列表页面)， name 字段用于存储记录值
// 传参值为数据及对应的存储名称，默认使用oldPageData，还有oldTwoPageData（一个页面存在两个list使用）
export function recordOldData(data, name) {
  const oldData = []
  data?.map((_) => {
    oldData.push(deepCopy(_))
  })
  const sName = name || 'oldPageData'
  sessionStorage.setItem(sName, JSON.stringify(oldData))
}

// 存储即将变更数据
// data 传参数据
// idName 数据中key值（用于判定更改数据）
// name 整体值 使用oldPageData，还有oldTwoPageData
// sName sessionStorage 中存储的名称,['beforeAlterFirst', 'beforeAlterTwo', 'beforeAlterThree']
// isJoin 是否需要jsin便利化
export function recordBeforeAlter(data, idName, name, sName, isJoin) {
  const pName = name || 'oldPageData'
  const oldData = JSON.parse(sessionStorage[pName] || '[]')
  const isJoinStatus = isJoin || false
  const beforeAlterFirst =
    oldData.find((_) => {
      return !isJoinStatus
        ? _[idName] === data[idName]
        : _[idName].join() === data[idName].join()
    }) || {}
  const sessName = sName || 'beforeAlterFirst'
  clearSessionData()
  sessionStorage.setItem(sessName, JSON.stringify(beforeAlterFirst))
  sessionStorage.setItem('objId', data[idName])
}

// 批量存储即将变更数据
// data 存储数据
// id 存储的objId 用于保存接口中提供的变更id
// sName 同上
export function batchRecordBeforeAlter(data, id, sName) {
  const name = sName || 'beforeAlterFirst'
  clearSessionData()
  sessionStorage.setItem(name, JSON.stringify(data))
  sessionStorage.setItem('objId', id || '')
}

// 更新存储list数据（其中一条）
// 变更的数据
// 变更的数据索引值
// sName 同上
export function updataRecordOneData(data, id, sName) {
  const name = sName || 'oldPageData'
  const oldData = JSON.parse(sessionStorage[name] || '[]')
  const index = oldData.findIndex((_) => {
    return _.id === id
  })
  oldData[index] = data
  sessionStorage.setItem(name, JSON.stringify(oldData))
}

// 清除数据，用于保存时，数据不混淆
export function clearSessionData() {
  const beforeAlterList = [
    'beforeAlterFirst',
    'beforeAlterTwo',
    'beforeAlterThree'
  ] // 存储的列表字段名
  beforeAlterList.map((_) => {
    sessionStorage.removeItem(_)
  })
  sessionStorage.removeItem('objId')
}
