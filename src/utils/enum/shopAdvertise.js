function getArr(nums) {
  return Array.from(new Array(nums), (_, i) => ++i)
}
const partialPositions = getArr(7) // [1-7]位置
const partialLargeSize = '690*260' // 部分稍大尺寸

function appendAll(obj) {
  return {
    全部: '',
    ...obj,
  }
}

// OSS一级页面(商家广告)
export const shopFirstLevelPage = appendAll({
  首页: '1',
  商城首页: '2',
  商城品牌页: '3',
})

// OSS二级页面(商家广告)
export const shopSecondaryPage = appendAll({
  弹窗广告: '1',
  焦点位banner: '2',
  商城焦点图: '3',
  商城运营位: '4',
  商城品牌banner位: '5',
  商城活动专区: '6',
})

// 样式(商家广告)
export const shopAdvertStyle = {
  弹窗: '1',
  banner焦点图: '3',
}

const secondaryPage = {
  modal: {
    // 1 ==> secondaryPage['弹窗广告'] 取value
    name: '弹窗广告', //  '弹窗广告' ==> secondaryPage['弹窗广告'] 取key
    position: partialPositions, // [1-7]位置
    style: shopAdvertStyle, // 部分尺寸
    size: '540*640', // 部分稍大尺寸
  },
  banner: {
    name: '焦点图banner',
    position: partialPositions,
    style: shopAdvertStyle,
    size: partialLargeSize,
  },
  operations: {
    name: '运营位',
    position: [1],
    style: shopAdvertStyle,
    size: partialLargeSize,
  },
  activity: {
    name: '活动专区',
    position: partialPositions,
    style: {
      banner焦点图: '3',
    },
    size: partialLargeSize,
  },
}

// 商家广告配置
export const shopConfiguration = {
  1: {
    // 2 ==> firstLevelPage['首页'] 取value
    name: '商家版-首页', // 首页 ==> firstLevelPage['首页'] 取key
    value: 1, // 2 ==> firstLevelPage['首页'] 取value
    show: false, // 貌似没用到
    secondary: {
      1: {
        ...secondaryPage.modal,
        value: 1, // 1 ==> secondaryPage['弹窗广告'] 取value
        clientPage: 1, // 1 ==> clientPage['首页弹窗广告'] 取value
      },
      2: {
        ...secondaryPage.banner,
        value: 2,
        clientPage: 2,
      },
    },
  },
  2: {
    name: '商家版-商城首页',
    value: 2,
    show: false, // 貌似没用到
    secondary: {
      3: {
        ...secondaryPage.banner,
        value: 3,
        clientPage: 3,
      },
      4: {
        ...secondaryPage.operations,
        value: 4,
        clientPage: 4,
      },
      6: {
        ...secondaryPage.activity,
        value: 6,
        clientPage: 6,
      },
    },
  },
  3: {
    name: '商家版-商城品牌banner位',
    value: 3,
    show: false, // 貌似没用到
    secondary: {
      5: {
        ...secondaryPage.banner,
        value: 5,
        clientPage: 5,
      },
    },
  },
}
