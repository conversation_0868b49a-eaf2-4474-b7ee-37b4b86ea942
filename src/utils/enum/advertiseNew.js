function appendAll(obj) {
  return {
    全部: '',
    ...obj
  }
}

export const effective = appendAll({ 有效: 1, 无效: 0 }) // 是否有效

// APP页面类型，给客户端使用
// http://wiki.corp.mddmoto.com/pages/viewpage.action?pageId=12583279
export const clientPage = {
  首页焦点图banner: 1,
  首页信息流: 2,
  '首页文章/话题详情': 3,
  首页弹窗广告: 4,
  首页底部提示框: 5,
  首页悬浮图: 6,
  选车页焦点图banner: 7,
  选车页弹窗广告: 8,
  选车页经销商列表: 9,
  选车页人气排行全部: 10,
  选车页人气排行街车: 11,
  选车页人气排行跑车: 12,
  选车页人气排行踏板: 13,
  选车页人气排行太子: 14,
  选车页人气排行拉力: 15,
  选车页人气排行复古: 16,
  选车页人气排行旅行: 17,
  选车页人气排行三轮: 18,
  选车页人气排行弯梁: 19,
  选车页口碑榜全部: 21,
  选车页口碑榜街车: 22,
  选车页口碑榜行跑车: 23,
  选车页口碑榜踏板: 24,
  选车页口碑榜太子: 25,
  选车页口碑榜拉力: 26,
  选车页口碑榜复古: 27,
  选车页口碑榜旅行: 28,
  选车页口碑榜三轮: 29,
  选车页口碑榜弯梁: 30,
  选车页品牌进口: 33,
  选车页品牌合资: 34,
  选车页品牌国产: 35,
  选车新能源焦点图banner: 36,
  用车页天气: 37,
  用车页附近摩友: 38,
  用车页附近活动: 39,
  用车页焦点图banner: 40,
  用车页弹窗广告: 41,
  用车页骑行: 42,
  搜索页搜索结果综合: 43,
  搜索页搜索结果车辆: 44,
  搜索页搜索结果商家: 45,
  我的页能量签到: 46,
  我的页弹窗广告: 47,
  我的页能量商城首页: 48,
  选车页新车上市弹窗广告: 49,
  售车页焦点图banner: 52,
  二手车首页banner: 53,
  售车运营位1: 54,
  售车运营位2: 55,
  首页视频: 56,
  小视频: 57,
  车型详情相关车型: 58,
  车型详情同级新车: 59,
  首页附近: 60,
  '同城-附近经销商': 61,
  '首页摩友圈-热门动态': 62,
  选车页推荐品牌: 67,
  新能源页推荐车型: 68,
  新能源页推荐品牌: 69,
  选车页推荐车型: 63,
  商城焦点图banner: 65,
  我的页商城入口: 66,
  首页视频信息流: 70,
  首页附近信息流: 71,
  '首页摩友圈-热门动态信息流': 72,
  '选车车型详情tab-动态': 73,
  '选车车型详情tab-评测': 74,
  '选车车型详情tab-视频': 75,
  '选车车型详情tab-资讯': 76,
  文章详情页信息流: 77,
  视频详情页信息流: 78,
  '首页摩友圈-我的圈子动态信息流': 79,
  '首页摩友圈-圈子内部动态信息流': 80,
  评论列表信息流: 81,
  二手车列表信息流: 82,
  '车型详情-经销商': 83,
  首页推送焦点图banner: 84,
  首页推送信息流: 85,
  '首页推送文章/话题详情': 86,
  首页推送弹窗广告: 87,
  首页推送底部提示框: 88,
  首页推送悬浮图: 89,
  首页最新焦点图banner: 90,
  首页最新信息流: 91,
  '首页最新文章/话题详情': 92,
  首页最新弹窗广告: 93,
  首页最新底部提示框: 94,
  首页最新悬浮图: 95,
  '首页视频文章/话题详情': 96,
  首页视频弹窗广告: 97,
  首页视频底部提示框: 98,
  首页视频悬浮图: 99,
  首页女骑焦点图banner: 100,
  首页女骑信息流: 101,
  '首页女骑文章/话题详情': 102,
  首页女骑弹窗广告: 103,
  首页女骑底部提示框: 104,
  首页女骑悬浮图: 105,
  首页维修改装焦点图banner: 106,
  首页维修改装信息流: 107,
  '首页维修改装文章/话题详情': 108,
  首页维修改装弹窗广告: 109,
  首页维修改装底部提示框: 110,
  首页维修改装悬浮图: 111,
  首页测评焦点图banner: 112,
  首页测评信息流: 113,
  '首页测评文章/话题详情': 114,
  首页测评弹窗广告: 115,
  首页测评底部提示框: 116,
  首页测评悬浮图: 117,
  '车型详情-底部banner': 118,
  '选车车型详情tab-口碑': 119,
  '新车特卖-推荐品牌': 120,
  '选车-车型图片经销商': 121,
  '首页同城-优质经销商': 123,
  '首页关注-信息流': 124,
  商城焦点图banner2: 125,
  商城运营位1: 126,
  '选车-品牌详情': 127,
  车辆推荐: 128,
  激励视频: 129,
  贴片视频: 130,
  金刚位: 131,
  运营位新: 132,
  弹窗广告: 133, // 商城-弹窗广告
  '搜索默认页-推荐车型': 134,
  '首页摩友圈-圈子内部动态焦点图banner': 135,
  '搜索默认页-摩托范热搜': 136,
  '商城导航1-焦点图banner': 137,
  '商城导航2-焦点图banner': 138,
  '商城导航3-焦点图banner': 139,
  '商城导航4-焦点图banner': 140,
  '商城导航5-焦点图banner': 141,
  '商城导航6-焦点图banner': 142,
  '商城导航7-焦点图banner': 143,
  '商城导航8-焦点图banner': 144,
  '商城导航9-焦点图banner': 145,
  '商城导航10-焦点图banner': 146,
  '商城精选-活动专区': 147,
  首页直播: 148,
  选车金刚位: 149,
  条件选车: 150,
  '驾校报名-焦点图banner': 151,
  '商城精选-自营商品': 152,
  插屏: 153,
  '驾校报名-驾校列表': 154,
  '首页发现-金刚位': 155,
  '首页发现-全部功能-看车频道': 156,
  '首页发现-全部功能-买车频道': 157,
  '首页发现-全部功能-玩车频道': 158,
  '首页发现-全部功能-用车频道': 159,
  '商城-摩托范自营页面-焦点图banner': 160,
  '商城精选-淘宝精选': 161,
  '商城精选-京东精选': 162,
  // '商城精选-优惠券': 166,
  支付结果: 163,
  订单详情: 164
}

// OSS一级页面
export const firstLevelPage = appendAll({
  启动屏: 1,
  '首页-首页发现': 2,
  '首页-首页关注': 3,
  选车: 5,
  选车新能源: 6,
  用车: 7,
  搜索: 11,
  我的: 12,
  新车售卖: 13,
  二手车: 14,
  首页视频: 15,
  小视频: 16,
  首页附近: 17,
  '首页摩友圈-热门动态': 18,
  商城精选: 19,
  文章详情页: 20,
  视频详情页: 21,
  '首页摩友圈-我的圈子动态': 22,
  '首页摩友圈-圈子内部动态': 23,
  评论列表: 24,
  二手车列表: 25,
  首页推送: 26,
  首页最新: 27,
  首页女骑: 28,
  首页维修改装: 29,
  首页测评: 30,
  新车特卖: 31,
  '首页-同城': 32,
  '商家版-首页': 33,
  激励视频: 34,
  贴片视频: 35,
  '商城导航-1': 36,
  '商城导航-2': 37,
  '商城导航-3': 38,
  '商城导航-4': 39,
  '商城导航-5': 40,
  '商城导航-6': 41,
  '商城导航-7': 42,
  '商城导航-8': 43,
  '商城导航-9': 44,
  '商城导航-10': 45,
  首页直播: 46,
  驾校报名: 47,
  插屏: 48
})

// OSS二级页面，注意：跟clientPage不对应
export const secondaryPage = appendAll({
  无: '-1',
  焦点图banner: '1',
  信息流: '2', // 找对应关系key：clientPage: 2
  '文章/话题详情': '3',
  弹窗广告: '4',
  附近状态: '5',
  车辆详情: '6',
  款型详情页: '7',
  人气榜: '8',
  口碑榜: '9',
  品牌榜: '10',
  摩托范热搜: '11',
  搜索结果综合: '12',
  搜索结果车辆: '13',
  搜索结果商家: '14',
  人气排行全部: '15',
  人气排行街车: '16',
  人气排行跑车: '17',
  人气排行踏板: '18',
  人气排行太子: '19',
  人气排行拉力: '20',
  人气排行复古: '21',
  人气排行旅行: '22',
  人气排行三轮: '23',
  人气排行弯梁: '24',
  口碑榜全部: '26',
  口碑榜街车: '27',
  口碑榜行跑车: '28',
  口碑榜踏板: '29',
  口碑榜太子: '30',
  口碑榜拉力: '31',
  口碑榜复古: '32',
  口碑榜旅行: '33',
  口碑榜三轮: '34',
  口碑榜弯梁: '35',
  悬浮图: '37',
  瀑布流: '38',
  底部提示框: '39',
  品牌上方运营位: '40',
  经销商列表: '41',
  附近活动: '42',
  品牌进口: '44',
  品牌合资: '45',
  品牌国产: '46',
  天气: '47',
  骑行: '48',
  附近摩友: '49',
  能量签到: '50',
  能量商城首页: '51',
  新车上市弹窗广告: '52',
  运营位1: '53',
  运营位2: '54',
  车型详情相关车型: '55',
  车型详情同级新车: '56',
  '同城-附近经销商': '57',
  推荐品牌: '59',
  推荐车型: '63',
  商城入口: '64',
  '车型详情tab-动态': '67',
  '车型详情tab-评测': '65',
  '车型详情tab-视频': '66',
  '车型详情tab-资讯': '68',
  '车型详情-经销商': '69',
  '车型详情-底部banner': '118',
  '车型详情tab-口碑': '70',
  '车型图片-经销商': '71',
  优质经销商: '72',
  '首页关注-信息流': '73',
  商城焦点图banner2: '74',
  商城运营位1: '75',
  品牌详情: '76',
  车辆推荐: '77',
  金刚位: '78',
  运营位新: '79',
  活动专区: '80',
  条件选车: '81',
  自营商品: '82',
  驾校列表: '83',
  '全部功能-看车频道': '84',
  '全部功能-买车频道': '85',
  '全部功能-玩车频道': '86',
  '全部功能-用车频道': '87',
  摩托范自营: '88',
  淘宝精选: '89',
  京东精选: '90',
  // 优惠券: '94',
  支付结果: '91',
  订单详情: '92'
})

function getArr(nums) {
  return Array.from(new Array(nums), (_, i) => ++i)
}
const allPositions = getArr(20) // [1-20]位置
const fiftyPositions = getArr(50) // [1-50]位置
const partialPositions = getArr(7) // [1-7]位置
const bannerPositions = getArr(10) // [1-10]位置
const specialPositions = getArr(6) // [1-6]位置
const recommdCarPositions = getArr(8) // [1-8]位置
const refreshCountAll = getArr(30) // [1-30]位置

const partialCarAndBrandStyle = { 车辆: '16' } // 人气口碑样式
const partialCarStyle = { 车辆: '7', 第三方广告: '11' } // 人气口碑样式
const partialBrandStyle = { 品牌: '9', 第三方广告: '11' } // 品牌样式

const partialBannerUpStyle = { banner焦点图: '3', 第三方广告: '11' } // banner焦点图样式
const partialBannerPopStyle = { 弹窗: '1', 第三方广告: '11' } // banner弹窗样式
const salesStyle = { 经销商: '8', 第三方广告: '11' } // 经销商样式
const partialBannerBrandAndCarStyle = {
  banner可投放品牌: '12',
  第三方广告: '11'
} // banner焦点图带投放品牌和投放车型

const partialSize = '690*160' // 部分尺寸
const partialLargeSize = '690*260' // 部分稍大尺寸
const partiaKingSize = '70*70' // 部分稍大尺寸
/**
 * 广告位置，根据一级、二级页面来判断
 * 参数说明：
  2: { // 2 ==> firstLevelPage['首页'] 取value
    name: '首页', // 首页 ==> firstLevelPage['首页'] 取key
    value: 2, // 2 ==> firstLevelPage['首页'] 取value
    show: false, // 貌似没用到
    secondary: {
      1: { // 1 ==> secondaryPage['焦点图banner'] 取value
        name: '焦点图banner', //  '焦点图banner' ==> secondaryPage['焦点图banner'] 取key
        value: 1, // 1 ==> secondaryPage['焦点图banner'] 取value
        position: partialPositions, // [1-7]位置
        style: partialBannerUpStyle, // 部分尺寸
        size: partialLargeSize, // 部分稍大尺寸
        clientPage: 1, // 1 ==> clientPage['首页焦点图banner'] 取value
      },
    }
  }
 */
export const configuration = {
  1: {
    name: '启动屏',
    value: 1,
    show: false,
    secondary: {
      '-1': {
        name: '无',
        value: -1,
        position: [1],
        refreshCount: refreshCountAll,
        style: { banner启动屏: '10', 第三方广告: '11' },
        size: '750*1126'
      }
    }
  },
  2: {
    // 2 ==> firstLevelPage['首页'] 取value
    name: '首页发现', // 首页 ==> firstLevelPage['首页'] 取key
    value: 2, // 2 ==> firstLevelPage['首页'] 取value
    show: false, // 貌似没用到
    secondary: {
      1: {
        // 1 ==> secondaryPage['焦点图banner'] 取value
        name: '焦点图banner', //  '焦点图banner' ==> secondaryPage['焦点图banner'] 取key
        value: 1, // 1 ==> secondaryPage['焦点图banner'] 取value
        position: bannerPositions, // [1-7]位置
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle, // 部分尺寸
        size: partialLargeSize, // 部分稍大尺寸
        clientPage: 1 // 1 ==> clientPage['首页焦点图banner'] 取value
      },
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: { banner焦点图: '3', 第三方广告: '11', 文章原样式: '14' },
        size: partialLargeSize,
        clientPage: 2
      },
      3: {
        name: '文章/话题详情',
        value: 3,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 3
      },
      4: {
        name: '弹窗广告',
        value: 4,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 4
      },
      39: {
        name: '底部提示框',
        value: 39,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 底部提示框: '2' },
        size: '120*120',
        clientPage: 5
      },
      37: {
        name: '悬浮图',
        value: 37,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 悬浮图: '12', 第三方广告: '11' },
        size: '120*120',
        clientPage: 6
      },
      // 57: {
      //   name: '同城-附近经销商', value: 57, position: [1, 2], style: { '经销商': '8' }, size: '120*120', clientPage: 61
      // },
      77: {
        name: '车辆推荐',
        value: 77,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 指定车型: '7' },
        size: '120*120',
        clientPage: 128
      },
      78: {
        name: '金刚位',
        value: 78,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: { 金刚位: '15' },
        size: partiaKingSize,
        clientPage: 155
      },
      84: {
        name: '全部功能-看车频道',
        value: 84,
        position: fiftyPositions,
        refreshCount: refreshCountAll,
        style: { 金刚位: '15' },
        size: partiaKingSize,
        clientPage: 156
      },
      85: {
        name: '全部功能-买车频道',
        value: 85,
        position: fiftyPositions,
        refreshCount: refreshCountAll,
        style: { 金刚位: '15' },
        size: partiaKingSize,
        clientPage: 157
      },
      86: {
        name: '全部功能-玩车频道',
        value: 86,
        position: fiftyPositions,
        refreshCount: refreshCountAll,
        style: { 金刚位: '15' },
        size: partiaKingSize,
        clientPage: 158
      },
      87: {
        name: '全部功能-用车频道',
        value: 87,
        position: fiftyPositions,
        refreshCount: refreshCountAll,
        style: { 金刚位: '15' },
        size: partiaKingSize,
        clientPage: 159
      }
    }
  },
  26: {
    name: '首页推送',
    value: 26,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 84
      },
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 85
      },
      3: {
        name: '文章/话题详情',
        value: 3,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 86
      },
      4: {
        name: '弹窗广告',
        value: 4,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 87
      },
      39: {
        name: '底部提示框',
        value: 39,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 底部提示框: '2' },
        size: '120*120',
        clientPage: 88
      },
      37: {
        name: '悬浮图',
        value: 37,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 悬浮图: '12', 第三方广告: '11' },
        size: '120*120',
        clientPage: 89
      }
    }
  },
  27: {
    name: '首页最新',
    value: 27,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 90
      },
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 91
      },
      3: {
        name: '文章/话题详情',
        value: 3,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 92
      },
      4: {
        name: '弹窗广告',
        value: 4,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 93
      },
      39: {
        name: '底部提示框',
        value: 39,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 底部提示框: '2' },
        size: '120*120',
        clientPage: 94
      },
      37: {
        name: '悬浮图',
        value: 37,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 悬浮图: '12', 第三方广告: '11' },
        size: '120*120',
        clientPage: 95
      }
    }
  },
  28: {
    name: '首页女骑',
    value: 28,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 100
      },
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 101
      },
      3: {
        name: '文章/话题详情',
        value: 3,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 102
      },
      4: {
        name: '弹窗广告',
        value: 4,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 103
      },
      39: {
        name: '底部提示框',
        value: 39,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 底部提示框: '2' },
        size: '120*120',
        clientPage: 104
      },
      37: {
        name: '悬浮图',
        value: 37,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 悬浮图: '12', 第三方广告: '11' },
        size: '120*120',
        clientPage: 105
      }
    }
  },
  29: {
    name: '首页维修改装',
    value: 29,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 106
      },
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 107
      },
      3: {
        name: '文章/话题详情',
        value: 3,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 108
      },
      4: {
        name: '弹窗广告',
        value: 4,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 109
      },
      39: {
        name: '底部提示框',
        value: 39,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 底部提示框: '2' },
        size: '120*120',
        clientPage: 110
      },
      37: {
        name: '悬浮图',
        value: 37,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 悬浮图: '12', 第三方广告: '11' },
        size: '120*120',
        clientPage: 111
      }
    }
  },
  30: {
    name: '首页测评',
    value: 30,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 112
      },
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 113
      },
      3: {
        name: '文章/话题详情',
        value: 3,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 114
      },
      4: {
        name: '弹窗广告',
        value: 4,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 115
      },
      39: {
        name: '底部提示框',
        value: 39,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 底部提示框: '2' },
        size: '120*120',
        clientPage: 116
      },
      37: {
        name: '悬浮图',
        value: 37,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 悬浮图: '12', 第三方广告: '11' },
        size: '120*120',
        clientPage: 117
      }
    }
  },
  3: {
    name: '首页关注',
    value: 3,
    show: false,
    secondary: {
      73: {
        name: '信息流',
        value: 73,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 124
      }
    }
  },
  // 4: {
  //   name: '首页圈子', value: '4', show: false,
  //   secondary: {
  //     2: {
  //       name: '信息流', value: 2, position: [3, 7, 11, 15, 19]
  //     },
  //     3: {
  //       name: '文章/状态详情', value: 3, position: [1]
  //     },
  //     4: {
  //       name: '弹窗广告', value: 4, position: [1]
  //     },
  //     39: {
  //       name: '底部提示框', value: 39, position: [1]
  //     },
  //     37: {
  //       name: '悬浮图', value: 37, position: [1]
  //     }
  //   }
  // },
  5: {
    name: '选车',
    value: 5,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 7
      },
      4: {
        name: '弹窗广告',
        value: 4,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 8
      },
      // 40: {
      //   name: '品牌上方运营位', value: 40, position: [1]
      // },
      // 4: {
      //   name: '弹窗广告', value: 4, position: [1]
      // },
      41: {
        name: '经销商列表',
        value: 41,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: salesStyle,
        size: '690*230',
        clientPage: 9
      },
      15: {
        name: '人气榜-全部',
        value: 15,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 10
      },
      16: {
        name: '人气榜-街车',
        value: 16,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 11
      },
      17: {
        name: '人气榜-跑车',
        value: 17,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 12
      },
      18: {
        name: '人气榜-踏板',
        value: 18,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 13
      },
      19: {
        name: '人气榜-太子',
        value: 19,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 14
      },
      20: {
        name: '人气榜-拉力',
        value: 20,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 15
      },
      21: {
        name: '人气榜-复古',
        value: 21,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 16
      },
      22: {
        name: '人气榜-旅行',
        value: 22,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 17
      },
      23: {
        name: '人气榜-三轮',
        value: 23,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 18
      },
      24: {
        name: '人气榜-弯梁',
        value: 24,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 19
      },
      26: {
        name: '口碑榜-全部',
        value: 26,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 21
      },
      27: {
        name: '口碑榜-街车',
        value: 27,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 22
      },
      28: {
        name: '口碑榜-跑车',
        value: 28,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 23
      },
      29: {
        name: '口碑榜-踏板',
        value: 29,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 24
      },
      30: {
        name: '口碑榜-太子',
        value: 30,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 25
      },
      31: {
        name: '口碑榜-拉力',
        value: 31,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 26
      },
      32: {
        name: '口碑榜-复古',
        value: 32,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 27
      },
      33: {
        name: '口碑榜-旅行',
        value: 33,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 28
      },
      34: {
        name: '口碑榜-三轮',
        value: 34,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 29
      },
      35: {
        name: '口碑榜-弯梁',
        value: 35,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 30
      },
      44: {
        name: '品牌榜-进口',
        value: 44,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBrandStyle,
        size: partialSize,
        clientPage: 33
      },
      45: {
        name: '品牌榜-合资',
        value: 45,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBrandStyle,
        size: partialSize,
        clientPage: 34
      },
      46: {
        name: '品牌榜-国产',
        value: 46,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBrandStyle,
        size: partialSize,
        clientPage: 35
      },
      52: {
        name: '新车上市弹窗广告',
        value: 52,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 49
      },
      55: {
        name: '车型详情相关车型',
        value: 55,
        position: specialPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 58
      },
      56: {
        name: '车型详情同级新车',
        value: 56,
        position: specialPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: '540*640',
        clientPage: 59
      },
      59: {
        name: '推荐品牌',
        value: 59,
        position: [1, 2, 3, 4, 5],
        refreshCount: refreshCountAll,
        style: partialBrandStyle,
        size: partialSize,
        clientPage: 67
      },
      63: {
        name: '推荐车型',
        value: 63,
        position: recommdCarPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: '540*640',
        clientPage: 63
      },
      67: {
        name: '车型详情tab-动态',
        value: 67,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 73
      },
      65: {
        name: '车型详情tab-评测',
        value: 65,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 74
      },
      66: {
        name: '车型详情tab-视频',
        value: 66,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 75
      },
      68: {
        name: '车型详情tab-资讯',
        value: 68,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 76
      },
      69: {
        name: '车型详情-经销商',
        value: 69,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: salesStyle,
        size: '690*230',
        clientPage: 83
      },
      118: {
        name: '车型详情-底部banner',
        value: 118,
        position: [1, 2, 3, 4, 5, 6],
        refreshCount: refreshCountAll,
        style: partialBannerBrandAndCarStyle,
        size: partialLargeSize,
        clientPage: 118
      },
      70: {
        name: '车型详情tab-口碑',
        value: 70,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 119
      },
      71: {
        name: '车型图片-经销商',
        value: 71,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: salesStyle,
        size: '690*230',
        clientPage: 121
      },
      76: {
        name: '品牌详情',
        value: 76,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarAndBrandStyle,
        size: '540*640',
        clientPage: 127
      },
      78: {
        name: '金刚位',
        value: 78,
        position: [1, 2, 3, 4, 5],
        refreshCount: refreshCountAll,
        style: { 金刚位: '15' },
        size: partiaKingSize,
        clientPage: 149
      },
      81: {
        name: '条件选车',
        value: 81,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: partialSize,
        clientPage: 150
      }
    }
  },
  6: {
    name: '选车新能源',
    value: 6,
    show: false,
    secondary: {
      // 40: {
      //     name: '品牌上方运营位', value: 40, position: [1]
      // },
      // 4: {
      //     name: '弹窗广告', value: 4, position: [1]
      // },
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 36
      },
      63: {
        name: '推荐车型',
        value: 63,
        position: recommdCarPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: '540*640',
        clientPage: 68
      },
      59: {
        name: '推荐品牌',
        value: 59,
        position: [1, 2, 3, 4, 5],
        refreshCount: refreshCountAll,
        style: partialBrandStyle,
        size: partialSize,
        clientPage: 69
      }
    }
  },
  7: {
    name: '用车',
    value: 7,
    show: false,
    secondary: {
      // 5: {
      //   name: '附近状态', value: 5, position: [3, 7, 11, 15, 19]
      // },
      // 3: {
      //   name: '文章/状态详情', value: 3, position: [1]
      // },
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 40
      },
      47: {
        name: '天气',
        value: 47,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialSize,
        clientPage: 37
      },
      48: {
        name: '骑行',
        value: 48,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 42
      },
      42: {
        name: '附近活动',
        value: 42,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 39
      },
      49: {
        name: '附近摩友',
        value: 49,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialSize,
        clientPage: 38
      },
      4: {
        name: '弹窗广告',
        value: 4,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 41
      }
    }
  },
  11: {
    name: '搜索',
    value: 11,
    show: false,
    secondary: {
      11: {
        name: '摩托范热搜',
        value: 11,
        position: specialPositions,
        refreshCount: refreshCountAll,
        style: { 热搜标题: '17' },
        size: partialLargeSize,
        clientPage: 136
      },
      12: {
        name: '搜索结果综合',
        value: 12,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 43
      },
      13: {
        name: '搜索结果车辆',
        value: 13,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 44
      },
      14: {
        name: '搜索结果商家',
        value: 14,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 45
      },
      63: {
        name: '推荐车型',
        value: 63,
        position: recommdCarPositions,
        refreshCount: refreshCountAll,
        style: partialCarStyle,
        size: '540*640',
        clientPage: 134
      }
    }
  },
  // 10: {
  //   name: '有赞商城', value: '10', show: false,
  //   secondary: {
  //     42: {
  //       name: '焦点图banner', value: 42, position: [1, 2, 3, 4, 5, 6, 7]
  //     },
  //   }
  // },
  12: {
    name: '我的',
    value: 12,
    show: false,
    secondary: {
      // 11: {
      //   name: '摩托范热搜', value: 11, position: [1, 2, 3, 4, 5, 6, 7]
      // },
      50: {
        name: '能量签到',
        value: 50,
        refreshCount: refreshCountAll,
        position: partialPositions,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 46
      },
      4: {
        name: '弹窗广告',
        value: 4,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 47
      },
      51: {
        name: '能量商城首页',
        value: 51,
        position: partialPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 48
      },
      64: {
        name: '商城入口',
        value: 64,
        position: partialPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 66
      }
    }
  },
  13: {
    name: '新车售卖',
    value: 13,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: { banner焦点图: '3' },
        size: partialLargeSize,
        clientPage: 52
      },
      53: {
        name: '运营位1',
        value: 53,
        position: partialPositions,
        refreshCount: refreshCountAll,
        style: { banner焦点图: '3' },
        size: partialLargeSize,
        clientPage: 54
      },
      54: {
        name: '运营位2',
        value: 54,
        position: partialPositions,
        refreshCount: refreshCountAll,
        style: { banner焦点图: '3' },
        size: partialLargeSize,
        clientPage: 55
      }
    }
  },
  14: {
    name: '二手车',
    value: 14,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: { banner焦点图: '3' },
        size: partialLargeSize,
        clientPage: 53
      }
    }
  },
  15: {
    name: '首页视频',
    value: 15,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        style: partialBannerUpStyle,
        refreshCount: refreshCountAll,
        size: partialLargeSize,
        clientPage: 56
      },
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        style: partialBannerUpStyle,
        refreshCount: refreshCountAll,
        size: partialLargeSize,
        clientPage: 70
      },
      3: {
        name: '文章/话题详情',
        value: 3,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 96
      },
      4: {
        name: '弹窗广告',
        value: 4,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 97
      },
      39: {
        name: '底部提示框',
        value: 39,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 底部提示框: '2' },
        size: '120*120',
        clientPage: 98
      },
      37: {
        name: '悬浮图',
        value: 37,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 悬浮图: '12', 第三方广告: '11' },
        size: '120*120',
        clientPage: 99
      }
    }
  },
  16: {
    name: '小视频',
    value: 16,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: allPositions,
        style: partialBannerUpStyle,
        refreshCount: refreshCountAll,
        size: partialLargeSize,
        clientPage: 57
      }
    }
  },
  17: {
    name: '首页附近',
    value: 17,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 60
      },
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 71
      }
    }
  },
  18: {
    name: '首页摩友圈-热门动态',
    value: 18,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 62
      },
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 72
      }
    }
  },
  19: {
    name: '商城精选',
    value: 19,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner1',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 65
      },
      74: {
        name: '焦点图banner2',
        value: 74,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 125
      },
      75: {
        name: '运营位1',
        value: 75,
        position: [1, 2, 3, 4],
        refreshCount: [1],
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 126
      },
      79: {
        name: '运营位新',
        value: 79,
        position: [1, 2, 3, 4],
        refreshCount: [1],
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 132
      },
      4: {
        name: '弹窗广告',
        value: 4,
        position: partialPositions,
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: partialLargeSize,
        clientPage: 133
      },
      80: {
        name: '活动专区',
        value: 80,
        position: [1, 2, 3, 4],
        refreshCount: [1],
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 147
      },
      82: {
        name: '自营商品',
        value: 82,
        position: [1, 2, 3, 4],
        refreshCount: [1],
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 152
      },
      88: {
        name: '摩托范自营',
        value: 88,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: { banner焦点图: '3' },
        size: partialLargeSize,
        clientPage: 160
      },
      89: {
        name: '淘宝精选',
        value: 89,
        position: [1, 2, 3, 4],
        refreshCount: [1],
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 161
      },
      90: {
        name: '京东精选',
        value: 90,
        position: [1, 2, 3, 4],
        refreshCount: [1],
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 162
      },
      // 94: {
      //   name: '优惠券',
      //   value: 94,
      //   position: bannerPositions,
      //   refreshCount: [1],
      //   style: { banner焦点图: '3' },
      //   size: partialLargeSize,
      //   clientPage: 166
      // },
      91: {
        name: '支付结果',
        value: 91,
        position: [1],
        refreshCount: [1],
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 163
      },
      92: {
        name: '订单详情',
        value: 92,
        position: [1],
        refreshCount: [1],
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 164
      }
    }
  },
  20: {
    name: '文章详情页',
    value: 20,
    show: false,
    secondary: {
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 77
      }
    }
  },
  21: {
    name: '视频详情页',
    value: 21,
    show: false,
    secondary: {
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 78
      }
    }
  },
  22: {
    name: '首页摩友圈-我的圈子动态',
    value: 22,
    show: false,
    secondary: {
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 79
      }
    }
  },
  23: {
    name: '首页摩友圈-圈子内部动态',
    value: 23,
    show: false,
    secondary: {
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 80
      },
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: { banner焦点图: '3' },
        size: partialLargeSize,
        clientPage: 135
      }
    }
  },
  24: {
    name: '评论列表',
    value: 24,
    show: false,
    secondary: {
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 81
      }
    }
  },
  25: {
    name: '二手车列表',
    value: 25,
    show: false,
    secondary: {
      2: {
        name: '信息流',
        value: 2,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 82
      }
    }
  },
  31: {
    name: '新车特卖',
    value: 31,
    show: false,
    secondary: {
      59: {
        name: '推荐品牌',
        value: 59,
        position: [1, 2, 3, 4, 5],
        refreshCount: refreshCountAll,
        style: partialBrandStyle,
        size: partialSize,
        clientPage: 120
      },
      78: {
        name: '金刚位',
        value: 78,
        position: partialPositions,
        refreshCount: refreshCountAll,
        style: { 金刚位: '15' },
        size: partiaKingSize,
        clientPage: 131
      }
    }
  },
  32: {
    name: '首页-同城',
    value: 32,
    show: false,
    secondary: {
      72: {
        name: '优质经销商',
        value: 72,
        position: allPositions,
        refreshCount: refreshCountAll,
        style: salesStyle,
        size: '690*230',
        clientPage: 123
      }
    }
  },
  33: {
    name: '商家版-首页',
    value: 33,
    show: false,
    secondary: {
      4: {
        name: '弹窗广告',
        value: 4,
        position: [1],
        refreshCount: refreshCountAll,
        style: partialBannerPopStyle,
        size: '540*640',
        clientPage: 4
      }
    }
  },
  34: {
    name: '激励视频',
    value: 34,
    show: false,
    secondary: {
      '-1': {
        name: '无',
        value: -1,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 第三方广告: '11' },
        size: '750*1126',
        clientPage: 129
      }
    }
  },
  35: {
    name: '贴片视频',
    value: 35,
    show: false,
    secondary: {
      '-1': {
        name: '无',
        value: -1,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 第三方广告: '11' },
        size: '750*1126',
        clientPage: 130
      }
    }
  },
  36: {
    name: '商城导航-1',
    value: 36,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 137
      }
    }
  },
  37: {
    name: '商城导航-2',
    value: 37,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 138
      }
    }
  },
  38: {
    name: '商城导航-3',
    value: 38,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 139
      }
    }
  },
  39: {
    name: '商城导航-4',
    value: 39,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 140
      }
    }
  },
  40: {
    name: '商城导航-5',
    value: 40,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 141
      }
    }
  },
  41: {
    name: '商城导航-6',
    value: 41,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 142
      }
    }
  },
  42: {
    name: '商城导航-7',
    value: 42,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 143
      }
    }
  },
  43: {
    name: '商城导航-8',
    value: 43,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 144
      }
    }
  },
  44: {
    name: '商城导航-9',
    value: 44,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 145
      }
    }
  },
  45: {
    name: '商城导航-10',
    value: 45,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: partialBannerUpStyle,
        size: partialLargeSize,
        clientPage: 146
      }
    }
  },
  46: {
    name: '首页直播',
    value: 46,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: { banner焦点图: '3' },
        size: partialLargeSize,
        clientPage: 148
      }
    }
  },
  47: {
    name: '驾校报名',
    value: 47,
    show: false,
    secondary: {
      1: {
        name: '焦点图banner',
        value: 1,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: { banner焦点图: '3' },
        size: partialLargeSize,
        clientPage: 151
      },
      83: {
        name: '驾校列表',
        value: 83,
        position: bannerPositions,
        refreshCount: refreshCountAll,
        style: { 驾校卡片: '18' },
        size: partialLargeSize,
        clientPage: 154
      }
    }
  },
  48: {
    name: '插屏',
    value: 48,
    show: false,
    secondary: {
      '-1': {
        name: '无',
        value: -1,
        position: [1],
        refreshCount: refreshCountAll,
        style: { 第三方广告: '11' },
        size: '750*1126',
        clientPage: 153
      }
    }
  }
}

// 广告类型
export const advertisementType = { 内部运营广告: '3', 外部广告: '4' }

//广告方类型
export const advertisingType = {
  // 全部: -1,
  厂商: 1,
  经销商: 2,
  非车企: 3,
  第三方: 4,
  摩托范: 5
}

//广告类型
export const advertiserType = {
  '启动屏(开屏)': 1,
  信息流: 2,
  焦点图: 3,
  弹窗: 4,
  热搜标题: 5,
  经销商: 6,
  品牌: 7,
  车辆: 8,
  文章原样式: 9,
  运营位新: 10,
  金刚位: 11,
  小视频: 12,
  贴片视频: 13,
  激励视频: 14
}

//广告刷数、位置
export const advertisingRange = [
  1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22,
  23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41,
  42, 43, 44, 45, 46, 47, 48, 49, 50
]

//当前广告计划状态
export const currentStatus = {
  全部: '',
  未开始: 1,
  进行中: 2,
  已完成: 3,
  已失效: 4
}

// 样式,详情页面 被动废弃了
export const advertStyle = {
  // '请选择': '',
  启动屏: '10',
  banner焦点图: '3',
  弹窗: '1',
  底部提示框: '2',
  悬浮图: '12',
  商品链接: '4',
  // '小组件': '5',
  // '热搜词': '6',
  指定车型: '7',
  指定经销商: '8',
  指定品牌: '9',
  第三方广告: '11',
  banner可投放品牌: '12',
  文章原样式: '14',
  车辆: '16',
  金刚位: '15',
  热搜标题: '17',
  驾校卡片: '18'
}

// 计划列表 广告计划类型
export const campaignType = {
  1: '启动屏',
  2: '信息流',
  3: 'Banner（CPM计费）',
  13: 'Banner（CPT计费）',
  4: '弹窗',
  5: '热搜标题',
  7: '品牌推荐',
  8: '车型推荐',
  9: '内容推荐',
  11: '搜索站',
  12: '投放内容池',
  0: '不限'
}
