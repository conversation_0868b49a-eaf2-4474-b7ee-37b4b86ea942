// 投放渠道
// export const channelEnum = {
//   // 全部: -1,
//   Android: '1',
//   iOS: '2',
//   发布平台: '3'
// }
export const channelEnum = [
  {
    value: '1',
    label: 'Android',
    children: [
      {
        value: 'xiaomi',
        label: '小米'
      },
      {
        value: 'oppo',
        label: 'OPPO'
      },
      {
        value: 'vivo',
        label: 'VIVO'
      },
      {
        value: 'meizu',
        label: '魅族'
      },
      {
        value: 'baidu',
        label: '百度'
      },
      {
        value: 'huawei',
        label: '华为'
      },
      {
        value: 'qihu360',
        label: '360'
      },
      {
        value: 'wandoujia',
        label: '豌豆荚'
      },
      {
        value: 'yingyongbao',
        label: '应用宝'
      },
      {
        value: 'lenovo',
        label: '联想应用'
      },
      {
        value: 'samsung',
        label: '三星渠道'
      },
      {
        value: 'rongyao',
        label: '荣耀'
      },
      {
        value: '0',
        label: '其他'
      }
    ]
  },
  {
    value: 2,
    label: 'IOS'
  },
  {
    value: 3,
    label: '发布平台'
  },
  {
    value: 31,
    label: '鸿蒙'
  }
]

function getArr(nums) {
  return Array.from(new Array(nums), (_, i) => ++i)
}

// 是否是新用户
export const newUserEnum = {
  全部: '',
  新用户: 1,
  老用户: 0
}

// 图片样式
export const pictureStyleEnum = {
  大图: 1,
  小图: 2
}

// 广告位置
export const positionEnum = getArr(20) // [1-20]位置

// 广告刷数
export const refreshCountEnum = getArr(20) // [1-20]刷数

// 按钮位置
export const buttonPositionEnum = {
  下方: 1,
  右上角: 2
}

// 按钮大小
export const buttonSizeEnum = {
  标准: 1,
  小: 2,
  偏小: 3,
  偏大: 4,
  大: 5
}

// 配置角标
export const badgeEnum = {
  无: '',
  上新: '上新',
  热门: '热门',
  推荐: '推荐',
  广告: '广告',
  降价: '降价',
  hot: 'hot'
}
function appendAll(obj) {
  return {
    全部: '',
    ...obj
  }
}

export const effective = appendAll({ 有效: 1, 无效: 0 }) // 是否有效

// 广告类型
export const advertisementType = { 内部运营广告: '3', 外部广告: '4' }

//广告方类型
export const advertisingType = {
  // 全部: -1,
  厂商: 1,
  经销商: 2,
  非车企: 3,
  第三方: 4,
  摩托范: 5
}

//广告刷数、位置
export const advertisingRange = getArr(50)

//当前广告计划状态
export const currentStatus = {
  全部: '',
  未开始: 1,
  进行中: 2,
  已完成: 3,
  已失效: 4
}

// 计划列表 广告计划类型
export const campaignType = {
  1: '启动屏',
  2: '信息流',
  3: 'Banner（CPM计费）',
  13: 'Banner（CPT计费）',
  4: '弹窗',
  5: '热搜标题',
  7: '品牌推荐',
  8: '车型推荐',
  14: '车型卡片（CPT计费）',
  9: '内容推荐',
  11: '搜索站',
  12: '投放内容池',
  0: '不限'
}
