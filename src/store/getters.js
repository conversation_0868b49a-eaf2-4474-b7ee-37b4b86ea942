const getters = {
  sidebar: (state) => state.app.sidebar,
  language: (state) => state.app.language,
  size: (state) => state.app.size,
  device: (state) => state.app.device,
  visitedViews: (state) => state.tagsView.visitedViews,
  cachedViews: (state) => state.tagsView.cachedViews,
  uid: (state) => state.user.uid,
  token: (state) => state.user.token,
  avatar: (state) => state.user.avatar,
  name: (state) => state.user.name,
  introduction: (state) => state.user.introduction,
  status: (state) => state.user.status,
  roles: (state) => state.user.roles,
  favcRoles: (state) => state.user.favcRoles,
  menuList: (state) => state.user.menuList,
  setting: (state) => state.user.setting,
  permission_routers: (state) => state.permission.routers,
  favorite_routers: (state) => state.permission.favoriteRouters,
  addRouters: (state) => state.permission.addRouters,
  pageChangedStatus: (state) => state.tagsView.pageChangedStatus,
  errorLogs: (state) => state.errorLog.logs,
  pageChangedCallback: (state) => state.tagsView.pageChangedCallback,
  pageChangedErrorCallback: (state) => state.tagsView.pageChangedErrorCallback,
  cityList: (state) => state.cityList.cityList
}
export default getters
