import { createStore } from 'vuex'
import app from './modules/app'
import errorLog from './modules/errorLog'
import permission from './modules/permission'
import tagsView from './modules/tagsView'
import user from './modules/user'
import getters from './getters'
import adConfig from './modules/adConfig'
import cityList from './modules/cityList'
const store = createStore({
  modules: {
    app,
    errorLog,
    permission,
    tagsView,
    user,
    adConfig,
    cityList
  },
  getters
})

export default store
