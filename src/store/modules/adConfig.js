const adConfig = {
  state: {
    adDetailShow: false,
    adProjectType: '', // 广告方类型
    adDetailData: {
      clientPage: '', // 广告位
      siteSetFirstId: '', // 广告位-一级
      siteSetSecondId: '', // 广告位-二级
      siteSetThirdId: '', // 广告位-三级
      orgType: '', // 所属广告方类型
      adTypeId: '', // 广告类型ID
      // orgId: '', // 对象id
      // campaignId: '', // 计划id
      adName: '', // 广告名称
      linkType: '', // 跳转类型ID
      linkUrl: '', // 跳转链接或id
      coverImage: '', // 广告封面图片
      platform: [], // 投放渠道
      beginVersion: '', // 版本开始
      versionEnd: '', // 版本截止
      beginTime: '', // 有效开始时间 日期或13位时间戳
      endTime: '', // 有效结束时间 日期或13位时间戳
      timeSeries: [], // 投放时间段，生效时间段
      newUser: '', // 分用户
      userProfileId: '', // 用户画像ID
      controlType: '', // 控量方式
      controlNumber: '', // 控量的总数量
      controlDayNumber: '', // 控量的天数量
      controlHours: '', // 投放天数
      controlHoursPredictDay: '', // 计划天数
      controlHoursPredictHour: '', // 计划小时
      uvControl: 0, // 用户控量方式
      uvControlNum: '', // 曝光频控次数
      uvControlDayNum: '', // 曝光频控天次数
      regionListStr: [], // 投放地区
      relationListStr: [], // 关联业务及id  业务类型 1文章 2话题分类 3品牌 4车型 5 摩友圈  数据结构 relationListStr:[{businessType: 1,relationIdList:[关联业务id数组]}]
      pictureStyle: '', // 图片样式 1:大图 2:小图
      position: '', // 广告位置 1-20
      positionSortNum: '',
      refreshCount: '', // 广告刷数 1-20
      exclusiveFlag: false, // 是否独占
      buttonPosition: '', // 按钮位置  1:下方 2:右上角
      buttonSize: '', // 按钮大小  1:标准 2:小 3:偏小 4:偏大 5:大
      isRepeat: false, // 是否轮巡
      isRemind: false, // 是否提醒
      userLableFilter: false, // 是否展示用户标签
      badge: '', // 角标
      badgeColor: '#f43530', // 角标颜色-默认颜色-红色
      badgeBeginTime: '', // 角标开始时间 日期或13位时间戳
      badgeEndTime: '', // 角标结束时间 日期或13位时间戳
      pageSelect: '', // 选择页面
      adAliasName: '', // 信息流标题
      linkUrlName: '',
      buttonName: '',
      buttonUrl: '',
      title: '', // 主标题
      subTitle: '', // 副标题
      remindUserName: '', // 提醒人
      remindUserId: '', // 提醒人id
      channel: [], // 渠道
      displayRelationList: [], // 车型小组件、关键词
      clickRatio: '', // 点击计算系数
      orderNum: ''
    },
    campaignType: ''
  },
  mutations: {
    CHANGE_AD_DETAILSHOW: (state, falg) => {
      state.adDetailShow = falg
    },
    CHANGE_AD_TYPE: (state, type) => {
      state.adProjectType = type
    },
    CHANGE_AD_DETAIL: (state, data) => {
      state.adDetailData = {
        ...state.adDetailData,
        ...data
      }
    },
    // 重置
    RESET_AD_DETAIL: (state) => {
      state.adDetailShow = false
      state.adProjectType = ''
      state.adDetailData = {
        clientPage: '', // 广告位
        siteSetFirstId: '', // 广告位-一级
        siteSetSecondId: '', // 广告位-二级
        siteSetThirdId: '', // 广告位-三级
        orgType: '', // 所属广告方类型
        adTypeId: '', // 广告类型ID
        orgId: '', // 对象id
        campaignId: '', // 计划id
        adName: '', // 广告名称
        linkType: '', // 跳转类型ID
        linkUrl: '', // 跳转链接或id
        coverImage: '', // 广告封面图片
        platform: [], // 投放渠道
        beginVersion: '', // 版本开始
        versionEnd: '', // 版本截止
        beginTime: '', // 有效开始时间 日期或13位时间戳
        endTime: '', // 有效结束时间 日期或13位时间戳
        timeSeries: [], // 投放时间段，生效时间段
        newUser: '', // 分用户
        userProfileId: '', // 用户画像ID
        controlType: '', // 控量方式
        controlNumber: '', // 控量的总数量
        controlDayNumber: '', // 控量的天数量
        controlHours: '', // 投放天数
        controlHoursPredictDay: '', // 计划天数
        controlHoursPredictHour: '', // 计划小时
        uvControl: 0, // 用户控量方式
        uvControlNum: '', // 曝光频控次数
        uvControlDayNum: '', // 曝光频控天次数
        regionListStr: [], // 投放地区
        relationListStr: [], // 关联业务及id  业务类型 1文章 2话题分类 3品牌 4车型 5 摩友圈  数据结构 relationListStr:[{businessType: 1,relationIdList:[关联业务id数组]}]
        pictureStyle: '', // 图片样式 1:大图 2:小图
        position: '', // 广告位置 1-20
        positionSortNum: '',
        refreshCount: '', // 广告刷数 1-20
        exclusiveFlag: false, // 是否独占
        buttonPosition: '', // 按钮位置  1:下方 2:右上角
        buttonSize: '', // 按钮大小  1:标准 2:小 3:偏小 4:偏大 5:大
        isRepeat: false, // 是否轮巡
        isRemind: false, // 是否提醒
        userLableFilter: false, // 是否展示用户标签
        badge: '', // 角标
        badgeColor: '#f43530', // 角标颜色
        badgeBeginTime: '', // 角标开始时间 日期或13位时间戳
        badgeEndTime: '', // 角标结束时间 日期或13位时间戳
        pageSelect: '', // 选择页面
        adAliasName: '', // 信息流标题
        linkUrlName: '',
        buttonName: '',
        buttonUrl: '',
        title: '', // 主标题
        subTitle: '', // 副标题
        remindUserName: '', // 提醒人
        remindUserId: '', // 提醒人id
        channel: [], // 渠道
        displayRelationList: [], // 车型小组件、关键词
        clickRatio: '', // 点击计算系数
        orderNum: ''
      }
    },
    // 重置非公告数据的
    RESET_AD_DETAIL_LIST: (state) => {
      state.adDetailShow = false
      // state.adProjectType = ''
      const oldData = state.adDetailData || {}
      state.adDetailData = {
        clientPage: '', // 广告位
        siteSetFirstId: '', // 广告位-一级
        siteSetSecondId: '', // 广告位-二级
        siteSetThirdId: '', // 广告位-三级
        adTypeId: '', // 广告类型ID
        // orgId: '', // 对象id
        // campaignId: '', // 计划id
        adName: '', // 广告名称
        linkType: '', // 跳转类型ID
        linkUrl: '', // 跳转链接或id
        coverImage: '', // 广告封面图片
        platform: oldData.platform || [], // 投放渠道
        beginVersion: oldData.beginVersion || '', // 版本开始
        versionEnd: oldData.versionEnd || '', // 版本截止
        beginTime: oldData.beginTime || '', // 有效开始时间 日期或13位时间戳
        endTime: oldData.endTime || '', // 有效结束时间 日期或13位时间戳
        timeSeries: oldData.timeSeries || [], // 投放时间段，生效时间段
        newUser: oldData.newUser || null, // 分用户
        userProfileId: oldData.userProfileId || '', // 用户画像ID
        controlType: oldData.controlType || '', // 控量方式
        controlNumber: oldData.controlNumber || '', // 控量的总数量
        controlDayNumber: oldData.controlDayNumber || '', // 控量的天数量
        controlHours: oldData.controlDayNumber || '', // 投放天数
        controlHoursPredictDay: oldData.controlHoursPredictDay || '', // 计划天数
        controlHoursPredictHour: oldData.controlHoursPredictHour || '', // 计划小时
        uvControl: oldData.uvControl || 0, // 用户控量方式
        uvControlNum: oldData.uvControlNum || '', // 曝光频控次数
        uvControlDayNum: oldData.uvControlDayNum || '', // 曝光频控天次数
        regionListStr: oldData.regionListStr || [], // 投放地区
        relationListStr: [], // 关联业务及id  业务类型 1文章 2话题分类 3品牌 4车型 5 摩友圈  数据结构 relationListStr:[{businessType: 1,relationIdList:[关联业务id数组]}]
        pictureStyle: '', // 图片样式 1:大图 2:小图
        position: '', // 广告位置 1-20
        positionSortNum: '',
        refreshCount: '', // 广告刷数 1-20
        exclusiveFlag: false, // 是否独占
        buttonPosition: '', // 按钮位置  1:下方 2:右上角
        buttonSize: '', // 按钮大小  1:标准 2:小 3:偏小 4:偏大 5:大
        isRepeat: false, // 是否轮巡
        isRemind: oldData.isRemind || false, // 是否提醒
        userLableFilter: oldData.userLableFilter || false, // 是否展示用户标签
        badge: '', // 角标
        badgeColor: '#f43530', // 角标颜色
        badgeBeginTime: '', // 角标开始时间 日期或13位时间戳
        badgeEndTime: '', // 角标结束时间 日期或13位时间戳
        pageSelect: '', // 选择页面
        adAliasName: oldData.adAliasName || '', // 信息流标题
        linkUrlName: '',
        buttonName: '',
        buttonUrl: '',
        title: '', // 主标题
        subTitle: '', // 副标题
        remindUserName: oldData.remindUserName || '', // 提醒人
        remindUserId: oldData.remindUserId || '', // 提醒人id
        channel: oldData.channel || [], // 渠道: [] // 渠道
        displayRelationList: oldData.displayRelationList || [], // 车型小组件、关键词
        clickRatio: oldData.clickRatio, // 点击计算系数
        orderNum: oldData.orderNum
      }
    },
    CHANGE_CAMPAIGN_TYPE(state, type) {
      state.campaignType = type
    }
  },
  actions: {
    // 控制配置广告内容显示隐藏 true/false
    changeAdDetailShow({ commit }, falg) {
      commit('CHANGE_AD_DETAILSHOW', falg)
    },
    changeAdType({ commit }, type) {
      commit('CHANGE_AD_TYPE', type)
    },
    changeAdDetail({ commit }, data) {
      commit('CHANGE_AD_DETAIL', data)
    },
    resetAdDetail({ commit }) {
      commit('RESET_AD_DETAIL')
    },
    resetAdDetailList({ commit }) {
      commit('RESET_AD_DETAIL_LIST')
    },
    changeCampaignType({ commit }, type) {
      commit('CHANGE_CAMPAIGN_TYPE', type)
    }
  }
}

export default adConfig
