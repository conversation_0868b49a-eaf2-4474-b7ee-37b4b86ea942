import {
  loginByUsername,
  getUserInfo,
  listAllMenu,
  listFavcMenu,
  addFavcMenu,
  deleteFavcMenu
} from '@/api/login'
import util from '@haluo/util' // import { date } from '@haluo/util'
import { clearLogin } from '@/utils'
import { getToken, removeToken, getUid, getUseName } from '@/utils/auth'
import { SMARTSERVICEURL } from '@/utils/configData/config'

const user = {
  state: {
    user: '',
    status: '',
    code: '',
    token: getToken(),
    name: getUseName(),
    avatar: '',
    introduction: '',
    roles: [],
    favcRoles: [],
    menuList: [],
    uid: getUid(),
    setting: {
      articlePlatform: []
    },
    pageReloadStatus: false // 页面重新进入加载
  },

  mutations: {
    SET_CODE: (state, code) => {
      state.code = code
    },
    SET_UID: (state, uid) => {
      state.uid = uid
    },
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_INTRODUCTION: (state, introduction) => {
      state.introduction = introduction
    },
    SET_SETTING: (state, setting) => {
      state.setting = setting
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_MENULIST: (state, menus) => {
      // console.log(menus)
      state.menuList = menus
    },
    SET_FAVC_ROLES: (state, roles) => {
      state.favcRoles = roles
    },
    SET_PAGE_RELOAD_STATUS: (state, status) => {
      state.pageReloadStatus = status
    }
  },

  actions: {
    // 用户名登录
    LoginByUsername({ commit }, userInfo) {
      const username = userInfo.username.trim()
      return new Promise((resolve, reject) => {
        loginByUsername(username, userInfo.password, userInfo.captchaCode)
          .then((response) => {
            if (response.data.success && response.data.data.passwordQualified) {
              const data = response.data.data
              commit('SET_NAME', data.username)
              commit('SET_UID', data.userid)
              commit('SET_CODE', data.usercode)
              commit('SET_TOKEN', data.ossToken)
              delete userInfo.password

              const _userInfo = JSON.stringify({
                ...userInfo,
                ...data
              })
              // 前端保留登录状态
              localStorage['userInfo'] = _userInfo

              let domain = ''
              if (
                location.hostname === 'localhost' ||
                util.match.checkType(location.hostname, 'ip')
              ) {
                domain = location.hostname
              } else {
                domain = location.host.replace(/\w+\./, '') // cros cookie
              }
              if (
                !(
                  window.isSmartService ||
                  SMARTSERVICEURL.includes(location.host)
                )
              ) {
                util.cookie.setCookie({
                  name: 'userInfo',
                  value: _userInfo,
                  exdays: 7,
                  domain
                })
                sessionStorage.removeItem('user')
                sessionStorage.removeItem('userInfo')
              }
            } else {
              const data = response.data.data
              sessionStorage.setItem('user', JSON.stringify(data))
              sessionStorage.setItem('userInfo', JSON.stringify(userInfo))
            }
            resolve(response)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    getAllMenuList({ commit }, data) {
      function recursionMenu(menus = [], res) {
        res &&
          res.length &&
          res.map((_) => {
            // _.param1 ? menus.push(_.param1) : menus.push(_.text) // 添加菜单Id 或 菜单名称
            _.param1 && menus.push(_.param1) // 添加菜单Id
            _.children && recursionMenu(menus, _.children) // 递归遍历子菜单
          })
      }
      function goLogin() {
        if (location.hash === '#/login') return

        clearLogin()

        location.hash = '#/login'
        setTimeout(() => {
          location.reload()
        }, 100)
      }

      return new Promise((resolve, reject) => {
        listAllMenu(data)
          .then((response) => {
            if (!response.data.success) {
              console.log('fail login', response.data)
              if (location.hash === '#/login') return resolve('success')

              // 因一些原因，登录会出现异常情况，需重新登录
              window.$vueApp.config.globalProperties.$message({
                message: response.data.data || '登录失效，请重新登录',
                type: 'error'
              })

              return goLogin()
            }
            let menus = ['noLimit', 'S123', 'S12301', 'S1230101'] // 不限
            recursionMenu(menus, response.data.data)
            menus = [...new Set(menus)]
            commit('SET_MENULIST', menus)
            // commit('SET_ROLES', response.data.data)
            resolve()
          })
          .catch((error) => {
            console.log('error login', error)
            reject(error)
            goLogin()
          })
      })
    },
    getFavcMenuList({ commit }, data) {
      return new Promise((resolve, reject) => {
        listFavcMenu(data)
          .then((response) => {
            commit('SET_FAVC_ROLES', response.data.data)
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    addFavcMenu({ commit, state }, data) {
      return new Promise((resolve, reject) => {
        addFavcMenu(data)
          .then((response) => {
            if (response.data.success) {
              commit('SET_FAVC_ROLES', [
                ...state.favcRoles,
                { menuId: data.menuId }
              ])
            }
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    deleteFavcMenu({ commit, state }, data) {
      return new Promise((resolve, reject) => {
        deleteFavcMenu(data)
          .then((response) => {
            if (response.data.success) {
              const roles = state.favcRoles.filter(
                (_) => _.menuId !== data.menuId
              )
              commit('SET_FAVC_ROLES', roles)
            }
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    // 获取用户信息
    GetUserInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getUserInfo(state.token)
          .then((response) => {
            if (!response.data) {
              // 由于mockjs 不支持自定义状态码只能这样hack
              reject('error')
            }
            const data = response.data

            if (data.roles && data.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              commit('SET_ROLES', data.roles)
            } else {
              reject('getInfo: roles must be a non-null array !')
            }

            commit('SET_NAME', data.name)
            commit('SET_AVATAR', data.avatar)
            commit('SET_INTRODUCTION', data.introduction)
            resolve(response)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    // 第三方验证登录
    // LoginByThirdparty({ commit, state }, code) {
    //   return new Promise((resolve, reject) => {
    //     commit('SET_CODE', code)
    //     loginByThirdparty(state.status, state.email, state.code).then(response => {
    //       commit('SET_TOKEN', response.data.token)
    //       setToken(response.data.token)
    //       resolve()
    //     }).catch(error => {
    //       reject(error)
    //     })
    //   })
    // },

    // 登出
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        clearLogin()
        resolve()
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    },
    setPageReloadStatus({ commit }, status) {
      commit('SET_PAGE_RELOAD_STATUS', status)
    }

    // 动态修改权限
    // ChangeRoles({ commit, dispatch }, role) {
    //   return new Promise(resolve => {
    //     commit('SET_TOKEN', role)
    //     setToken(role)
    //     getUserInfo(role).then(response => {
    //       const data = response.data
    //       commit('SET_ROLES', data.roles)
    //       commit('SET_NAME', data.name)
    //       commit('SET_AVATAR', data.avatar)
    //       commit('SET_INTRODUCTION', data.introduction)
    //       // dispatch('GenerateRoutes', data) // 动态修改权限后 重绘侧边菜单
    //       resolve()
    //     })
    //   })
    // }
  }
}

export default user
