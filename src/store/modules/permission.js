import { isLocal } from '@/utils'
import { asyncRouterMap } from '@/router'
import { constantRouterMap } from '@/router/constRoutes'

import { favoriteRouter } from '@/router/modules/favorite'

/**
 * 判断菜单权限
 * @param menuList
 * @param route
 */
function hasPermission(menuList = {}, route) {
  // 本地环境不做授权校验
  if (isLocal()) {
    return true
  }
  if (route.meta && route.meta.menuId) {
    // return true
    return menuList.menuList && menuList.menuList.includes(route.meta.menuId)
  } else {
    return false
  }
}

/**
 * 递归过滤异步路由表，返回符合用户角色权限的路由表
 * @param routes asyncRouterMap
 * @param menuList
 */
function filterAsyncRouter(routes, menuList) {
  const res = []

  routes.forEach((route) => {
    const tmp = { ...route }
    if (hasPermission(menuList, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRouter(tmp.children, menuList)
      }
      res.push(tmp)
    }
  })

  return res
}

function filterFavoriteRouters(routers, rolesList) {
  const favList = [JSON.parse(JSON.stringify(favoriteRouter))]
  routers.forEach((router) => {
    if (router.children) {
      router.children.forEach((_router) => {
        if (_router.children) {
          _router.children.forEach((router_) => {
            if (hasPermission(rolesList, router_)) {
              favList[0].children.push({
                meta: router_.meta,
                path: `${router.path}/${_router.path}/${router_.path}`
              })
            }
          })
        }
      })
    }
  })

  return favList
}

const permission = {
  state: {
    routers: constantRouterMap,
    addRouters: [],
    favoriteRouters: [],
    favoriteRouterGeted: false
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = routers
      state.routers = constantRouterMap.concat(routers)
    },
    SET_FAVORITE: (state, routers) => {
      state.favoriteRouters = routers
    },
    SET_FAVORITE_STATUS: (state, status) => {
      state.favoriteRouterGeted = status
    }
  },
  actions: {
    GenerateRoutes({ commit }, menuList) {
      return new Promise((resolve) => {
        const accessedRouters = filterAsyncRouter(asyncRouterMap, menuList)
        commit('SET_ROUTERS', accessedRouters)
        resolve(accessedRouters)
      })
    },
    GenerateFavoriteList({ commit }, data) {
      const { roles } = data
      const favList = filterFavoriteRouters(asyncRouterMap, roles)
      commit('SET_FAVORITE', favList)
      commit('SET_FAVORITE_STATUS', true)
    }
  }
}

export default permission
