/**
 * 城市所有列表
 */
import { getListAllMap } from '@/api/advertModule'
const city = {
  state: {
    cityList: []
  },
  mutations: {
    GET_LIST: (state, list) => {
      state.cityList = list
    }
  },
  actions: {
    getCityList({ commit }) {
      const cityList = city.state.cityList || []
      console.log(cityList, '221')
      if (cityList.length) return
      getListAllMap()
        .then((response) => {
          const data = response.data || []
          commit('GET_LIST', data)
        })
        .catch((error) => {
          console.log(error)
        })
    }
  }
}

export default city
