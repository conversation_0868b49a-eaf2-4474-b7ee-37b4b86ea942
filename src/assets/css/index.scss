// 消息确认提示，确认在左侧，取消在右侧
.el-form-item__label{
  font-weight: 700;
}
.el-message-box__wrapper {
  .el-message-box {
    .el-message-box__btns {
      .el-button--small {
        float: right;
      }
      .el-button--primary {
        margin-right: 10px;
      }
    }
  }
}
.el-table__body-wrapper{
  .el-scrollbar__bar.is-horizontal{
    height: 15px;
  }
  .el-scrollbar__bar.is-vertical{
    width: 15px;
  }
}
// 选择下拉框 审核
.is-choice-examine{
  .el-input--medium{
    .el-input__inner{
      border: 1px solid #ffea00 !important;
      background-color: #ffea00;
    }
    .el-input__inner:hover{
      border: 1px solid #ffea00 !important;
      background-color: #ffea00;
    }
  }
}
// 选择下拉框 删除
.is-choice-delete{
  .el-input--medium{
    .el-input__inner{
      border: 1px solid red !important;
      background-color: red;
      color: #fff;
    }
    .el-input__inner:hover{
      border: 1px solid red !important;
      background-color: red;
      color: #fff;
    }
  }
}
// 选择下拉框 审核不通过
.is-choice-examine-fail{
  .el-input--medium{
    .el-input__inner{
      border: 1px solid #FF9933 !important;
      background-color: #FF9933;
    }
    .el-input__inner:hover{
      border: 1px solid #FF9933 !important;
      background-color: #FF9933;
    }
  }
}

// 文本框 审核
.is-text-examine {
  background-color: #ffea00;
  padding: 5px;
  border-radius: 5px;
}
.is-text-grey{
  background-color: grey;
  padding: 5px;
  border-radius: 5px;
}
.is-text-delete{
  background-color: red;
  padding: 5px 2px;
}
// 文本框状态   字体颜色区分
.is-text-audit {
  color: green;
}
.is-text-pass{
  color: blue;
}
.is-text-fail{
  color: red;
}
.is-text-high{
  input {
    color: red !important;
    }
}
// 圈子使用的申请状态样式
.circle-not-launched{
  color: #999;
}
.circle-have-launched{
  color: #009900;
}
.circle-not-pass{
  color: red;
  border-bottom: 1px solid red;
}
.circle-be-audited{
  color: #FFCC00;
  border-bottom: 1px solid #FFCC00;
}
// 分页页码居中对齐
.el-pagination-center{
  // text-align: center;
  justify-content: center;
  margin-top: 20px;
}
// 给标签中文字加下划线
.lable-border{
  .el-form-item__label{
    text-decoration: underline;
  }
}
.el-table::before {
  height: 0;
}
.red {
  color: red;
  .el-form-item__label {
    color: red;
  }
}
// 针对文本框
.textarea-content{
  .el-textarea__inner{
    min-height: 66px !important;
  }
}
.footer-content{
  margin-top: 10px;
  text-align: center;
}
// 针对 el-form-item 设置宽度
.inner-item{
  width: 400px;
  display: inline-block;
}
