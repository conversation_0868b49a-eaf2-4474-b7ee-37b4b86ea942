<template>
  <div>
    <!-- <el-dialog
      v-if="mapStatus"
      v-model="mapStatus"
      title="地图"
      class="dialog-content"
      width="600px"
      append-to-body
      @close="closeDialog"
    >
      <form @submit.prevent="change()">
        <div class="search">
          <div style="margin-left: 16px">
            经纬度查询地址：<a
              target="_blank"
              href="https://lbs.amap.com/tools/picker"
              >https://lbs.amap.com/tools/picker</a
            >
          </div>
          <div class="flex">
            <input
              v-model="searchKey"
              type="search"
              placeholder="请输入地址"
              autofocus
              class="fl-left mint-searchbar-core"
              style="width: 45%; margin-right: 20px"
              @focus="showResult = true"
            />
            <el-input
              v-model="searchKey"
              type="text"
              placeholder="请输入地址"
              autofocus
              style="width: 240px; margin-right: 20px"
              clearable
              @click="showResult = true"
            />
            <el-input
              v-model="searchLngLat"
              type="text"
              placeholder="请输入经纬度"
              autofocus
              clearable
              style="width: 240px; margin-right: 20px"
            />-->
    <!-- <span class="fl-right btn-search" @click="change()">搜索</span> -->
    <!--  <el-button type="primary" link @click="change()">搜索</el-button>
          </div>
          <div v-show="searchKey && showResult" class="search-result">
            <ul>
              <template v-for="(result, index) in searchResult" :key="index">
                <li
                  v-if="typeof result.location === 'string'"
                  @click="selectedTab(result, index)"
                >
                  <span
                    :class="{ 'selected-text': selectedIndex === index }"
                    class="text dotdotdot1"
                    >{{ getAddress(result) }}</span
                  >
                </li>
              </template>
            </ul>
          </div>
        </div>
        <div class="btn-complate" @click="setStorage()">完成</div>
      </form>
      <div id="container" />
    </el-dialog> -->
  </div>
</template>

<script type="es6">
// import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer';
// import { loadJs } from '@/utils'
// import { searchAddress } from '@/api/searchMap.js'
// let AMap = null
// export default {name: 'CMPosition',
// components: {},
// data() {
//   return {
//     markers: [],
//     lng: 0,
//     lat: 0,
//     pickerPosition: null, // 地图选址，可传
//     address: '', // 当前地址
//     searchKey: '',
//     city: '', // 当前城市
//     aois: '', // 大概位置
//     adcode: '', // 行政区编码
//     selectedIndex: -1,
//     searchResult: [],
//     showResult: false,
//     mapStatus: false, // 是否显示地图
//     positionPicker: null, // 实例化 地图选址组件
//     selectedPosition: '', // 记录当前选中的位置
//     map: null,
//     searchLngLat:''
//   };
// },
// watch: {
//   searchKey(val) {
//     if (!val) {
//       // 清空搜索结果
//       this.searchResult = []
//     }
//   }
// },
// mounted() {
// },
// methods: {
//   init() {
//     const me = this
//     loadJs('//webapi.amap.com/maps?v=1.4.6&key=5c61691728bacc1467e91049fb9b8520').then(function () {
//       let interval = null
//       interval = setInterval(function () {
//         if (window.AMap && window.AMap.Map) {
//           clearInterval(interval)
//           AMap = window.AMap
//           me.map = new AMap.Map('container', {
//             resizeEnable: true
//           })
//           // window.map = me.map
//           me.map.setMapStyle('amap://styles/683ade8ce3312333a81bf86146cecfb3')
//           AMap.plugin('AMap.ToolBar', function () { // 异步加载ToolBar插件
//             const toolbar = new AMap.ToolBar();
//             me.map.addControl(toolbar);
//           });
//           me.getPosition()
//           me.zoomOn()
//         }
//       }, 500) // 500ms 时间  maps 加载后，还需要时间进行初始化
//     })
//   },
//   /* 传值选址
//    * [121.245806, 31.299379]
//    */
//   initPostion(pickerPosition, searchKey = '') {
//     this.pickerPosition = pickerPosition && pickerPosition.filter(_ => _)
//     console.log(this.pickerPosition)
//     this.searchKey = searchKey
//     this.searchLngLat = ''
//     this.mapStatus = true
//     this.$nextTick(function () {
//       this.init()
//     })
//   },
//   getPosition() {
//     const me = this
//     let geolocation
//     me.map.plugin('AMap.Geolocation', function () {
//       geolocation = new AMap.Geolocation({
//         // enableHighAccuracy: true, //是否使用高精度定位，默认:true
//         timeout: 10000, // 超过10秒后停止定位，默认：无穷大
//         buttonOffset: new AMap.Pixel(10, 20), // 定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
//         zoomToAccuracy: false, // 定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
//         buttonPosition: 'RB',
//         showMarker: false // 定位成功时是否在定位位置显示一个Marker
//       })
//       me.map.addControl(geolocation)
//       geolocation.getCurrentPosition()
//       window.AMap.event.addListener(geolocation, 'complete', onComplete) // 返回定位信息
//       window.AMap.event.addListener(geolocation, 'error', onError) // 返回定位出错信息
//       // map.on('click', function (e) {
//       //   alert('您在[ ' + e.lnglat.getLng() + ',' + e.lnglat.getLat() + ' ]的位置点击了地图！')
//       // })
//     })
//     // 解析定位结果
//     function onComplete(data) {
//       me.lng = data.position.getLng()
//       me.lat = data.position.getLat()
//       me.address = data.formattedAddress
//       me.selectedIndex = -1
//       me.picker()
//     }
//     // 解析定位错误信息
//     function onError(data) {
//       me.picker()
//       // return me.$message.error('定位失败')
//     }
//   },
//   setCenterByInitPostion() {
//     const me = this;
//     if (me.pickerPosition && me.pickerPosition.length) {
//       me.updateCenter(me.pickerPosition)
//       me.pickerPosition = null
//     }
//   },
//   updateCenter(location) {
//     const position = location || this.pickerPosition || [this.lng, this.lat];
//     this.map && this.map.setCenter(position)
//   },
//   // 事件绑定
//   zoomOn() {
//     console.log('绑定事件')
//     this.map.on('zoomstart', this.mapZoomstart)
//     // this.map.on('zoomchange', this.mapZoom)
//     this.map.on('zoomend', this.mapZoomend)
//   },
//   // 解绑事件
//   zoomOff() {
//     console.log('解除事件绑定')
//     this.map.off('zoomstart', this.mapZoomstart)
//     // this.map.off('zoomchange', this.mapZoom)
//     this.map.off('zoomend', this.mapZoomend)
//   },
//   mapZoomstart() {
//     const center = this.map.getCenter();
//     this.currentPosition = [center.lng, center.lat];
//     // this.picker();
//   },
//   mapZoomend() {
//     // this.picker();
//     this.updateCenter(this.currentPosition)
//     this.currentPosition = null;
//   },
//   picker() {
//     const me = this

//     function mapUI() {
//       window.AMapUI.loadUI(['misc/PositionPicker'], function (PositionPicker) {
//         if (!me.positionPicker) {
//           me.positionPicker = new PositionPicker({
//             mode: 'dragMap', // 设定为拖拽地图模式，可选'dragMap'、'dragMarker'，默认为'dragMap'
//             map: me.map, // 依赖地图对象
//             iconStyle: {
//               url: '/static/img/<EMAIL>', // 图片地址
//               size: [54, 54], // 要显示的点大小，将缩放图片
//               ancher: [28, 34] // 要显示的点大小，将缩放图片
//             }
//           })
//           me.positionPicker.on('success', function (result) {
//             // console.log('me.positionPicker.on：' + JSON.stringify(result, null, ' '))
//             me.lng = result.position.lng
//             me.lat = result.position.lat
//             me.address = result.address
//             me.city = result.regeocode.addressComponent.city
//             me.aois = result.regeocode.aois[0] && result.regeocode.aois[0].name || ''
//             me.adcode = result.regeocode.aois[0] && result.regeocode.aois[0].adcode || ''
//             // 判断是否是拖拽定位
//             if (me.selectedPosition.indexOf(me.lng) === -1 && me.selectedPosition.indexOf(me.lat) === -1) {
//               me.selectedIndex = -1 // 拖拽定位取消选中状态
//             }
//           })
//           me.positionPicker.on('fail', function (result) {
//             console.log(result)
//           })
//         }

//         // 以originPos为初始位置开始拖拽选址，参数缺省值为地图中心点
//         me.positionPicker.start()
//       })
//     }

//     loadJs('//webapi.amap.com/ui/1.0/main.js?v=1.0.11').then(function () {
//       me.setCenterByInitPostion()
//       mapUI()
//     })
//   },
//   change() {
//     const me = this
//     if (me.searchLngLat.trim()) {
//       const location = me.searchLngLat.split(',')
//       if (!location[0] || !location[1]) {
//         return me.$message.error('请检查输入的经纬度是否正确!')
//       }
//       me.updateCenter(location)
//       me.picker()
//       return
//     }
//     if (!me.searchKey) {
//       return me.$message.error('请输入搜索内容')
//     }
//     searchAddress({
//       s: 'rsv3',
//       key: '5c61691728bacc1467e91049fb9b8520',
//       city: '中国',
//       citylimit: false,
//       platform: 'js',
//       logversion: '2.0',
//       sdkversion: '1.3',
//       appname: 'locationurl',
//       keywords: me.searchKey
//     }).then(response => {
//       // me.$http.get(`https://restapi.amap.com/v3/assistant/inputtips?s=rsv3&key=${key}&city=中国&citylimit=false&platform=JS&logversion=2.0&sdkversion=1.3&appname=locationurl&keywords=${me.searchKey}`).then(response => {
//       console.log(response)
//       if (response.status === 200 && response.data.tips.length > 0) {
//         me.selectedIndex = -1
//         me.searchResult = response.data.tips
//         me.showResult = true
//       } else {
//         me.$message.error('搜索无结果')
//       }
//     }).catch((err) => {
//       me.$message.error('搜索异常')
//       console.log('err:' + JSON.stringify(err))
//     })
//   },
//   getAddress(result) {
//     return result.district + result.name
//   },
//   selectedTab(result, index) {
//     const me = this
//     me.selectedIndex = index
//     // me.searchKey = result.address
//     console.log(result.location)
//     me.selectedPosition = result.location
//     const location = result.location.split(',')
//     me.lng = location[0]
//     me.lat = location[1]
//     me.address = me.getAddress(result)
//     me.showResult = false
//     me.updateCenter(location)
//     me.picker()
//   },
//   setStorage() {
//     const me = this
//     if (me.lng && me.lat) {
//       window.localStorage.setItem('position', JSON.stringify({
//         lng: me.lng,
//         lat: me.lat,
//         address: me.address,
//         city: me.city,
//         aois: me.aois,
//         adcode: me.adcode
//       }))
//     }
//     $emit(me, "mapData");
//     me.closeDialog();
//   },
//   closeDialog() {
//     const me = this;
//     me.mapStatus = false;
//     me.positionPicker && me.positionPicker.stop();
//     me.positionPicker = null;
//     me.zoomOff();
//   }
// },
//                 emits:['mapData',]
//             }
</script>

<style scoped>
.amap-page-container {
  height: 70vh;
}
.search {
  width: 100%;
  z-index: 10;
}
.flex {
  height: 33px;
  margin: 14px 14px 8px;
  border-radius: 5px;
  background-color: #fff;
}
input {
  height: 29px;
  line-height: 29px;
  padding: 0 8px;
  border-radius: 3px;
  background-color: #f2f2f2;
}
input[type='search'] {
  -webkit-appearance: block;
}
input::-webkit-search-cancel-button {
  display: block;
}
.btn-search {
  width: 40px;
  height: 29px;
  line-height: 29px;
  margin-left: 10px;
  font-size: 16px;
  text-align: center;
}
ul {
  max-height: 220px;
  margin: 0;
  padding: 0;
  position: absolute;
  left: 0;
  z-index: 2;
  width: 100%;
  border-radius: 5px;
  background-color: #fff;
  overflow: scroll;
}
li {
  display: block;
  height: 44px;
  line-height: 44px;
  margin: 0 14px;
  border-bottom: 1px solid #ddd;
}
.text {
  display: inline-block;
  width: 100%;
  padding-left: 3px;
}
li:last-child {
  border-bottom: 0;
}
.selected-text {
  width: 88%;
  color: #ffa727;
}
.btn-complate {
  position: absolute;
  height: 44px;
  line-height: 44px;
  left: 14px;
  right: 14px;
  bottom: 20px;
  font-size: 16px;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  background-color: #ffa727;
  z-index: 10;
}
#container {
  width: 100%;
  height: 60vh;
}
.amap-geolocation-con {
  left: 14px !important;
  bottom: 70px !important;
}
</style>
