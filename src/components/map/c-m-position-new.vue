<template>
  <div>
    <el-dialog
      v-if="mapStatus"
      v-model="mapStatus"
      title="地图"
      class="dialog-content"
      width="600px"
      append-to-body
      @close="closeDialog"
    >
      <form @submit.prevent="change()">
        <div class="search">
          <div style="margin-left: 16px">
            经纬度查询地址：<a
              target="_blank"
              href="https://api.map.baidu.com/lbsapi/getpoint/index.html"
              >https://api.map.baidu.com/lbsapi/getpoint/index.html</a
            >
          </div>
          <div class="flex">
            <el-input
              v-model="searchKey"
              type="text"
              placeholder="请输入地址"
              autofocus
              style="width: 240px; margin-right: 20px"
              clearable
              @click="showResult = true"
            />
            <el-input
              v-model="searchLngLat"
              type="text"
              placeholder="请输入经纬度"
              autofocus
              clearable
              style="width: 240px; margin-right: 20px"
            />
            <el-button type="primary" link @click="change()">搜索</el-button>
          </div>
          <div v-show="searchKey && showResult" class="search-result">
            <ul>
              <template v-for="(result, index) in searchResult" :key="index">
                <li @click="selectedTab(result, index)">
                  <span
                    :class="{ 'selected-text': selectedIndex === index }"
                    class="text dotdotdot1"
                    >{{ getAddress(result) }}</span
                  >
                </li>
              </template>
            </ul>
          </div>
        </div>
        <div class="btn-complate" @click.self="setStorage()">完成</div>
      </form>
      <div id="bdMapContainer"></div>
    </el-dialog>
  </div>
</template>

<script type="es6">
import { $emit } from '../../utils/gogocodeTransfer';
import imgSrc from '/static/img/<EMAIL>'

export default {
  name: 'CMPosition',
  data() {
    return {
      markers: [],
      lng: 0,
      lat: 0,
      pickerPosition: null, // 地图选址，可传
      address: '', // 当前地址
      searchKey: '',
      city: '', // 当前城市
      aois: '', // 大概位置
      adcode: '', // 行政区编码
      selectedIndex: -1,
      searchResult: [],
      showResult: false,
      mapStatus: false, // 是否显示地图
      positionPicker: null, // 实例化 地图选址组件
      selectedPosition: '', // 记录当前选中的位置
      map: null,
      searchLngLat:'',
      customOverlay: null, // 标记
      imgSrc
    }
  },
  watch: {
    searchKey(val) {
      if (!val) {
        // 清空搜索结果
        this.searchResult = []
      }
    }
  },
  methods: {
    init() {
      this.pickerPosition = null
      this.searchKey = ''
      this.searchLngLat = ''
      this.mapStatus = true
      this.$nextTick(function () {
        this.setMap()
      })
    },
    setMap() {
      const me = this
      let interval = null
      const BMap_URL = 'https://api.map.baidu.com/api?type=webgl&v=2.0&ak=syq97Wqch8s3TW4SpNZGQWKpO4TZbZUG&callback=onBMapCallback'

      if (window.BMapGL && window.BMapGL.Map) {
        me.createMap()
        return true
      }

      // 百度地图异步加载回调处理
      window.onBMapCallback = function () {
        interval = setInterval(function () {
          if (window.BMapGL && window.BMapGL.Map) {
            clearInterval(interval)
            me.createMap()
          }
        }, 500) // 加载后，还需要时间进行初始化
      }

      // 插入script脚本
      let scriptNode = document.createElement('script')
      scriptNode.setAttribute('type', 'text/javascript')
      scriptNode.setAttribute('src', BMap_URL)
      document.body.appendChild(scriptNode)
    },
    createMap() {
      const me = this
      const BMapGL = window.BMapGL
      // 创建Map实例
      me.map = new BMapGL.Map('bdMapContainer')
      // 初始化地图,设置中心点坐标和地图级别
      me.map.centerAndZoom(new BMapGL.Point(116.41036949371, 39.921336993524), 11)
      // 开启鼠标滚轮缩放
      me.map.enableScrollWheelZoom(true)
      // 添加缩放控件
      const zoomCtrl = new BMapGL.ZoomControl()
      me.map.addControl(zoomCtrl)
      // 创建定位控件
      const locationControl = new BMapGL.LocationControl({
        anchor: 'BMAP_ANCHOR_TOP_RIGHT',
        offset: new BMapGL.Size(20, 20)
      })
      me.map.addControl(locationControl)
      locationControl.addEventListener('locationSuccess', function(data) {
        if (data) {
          const {longitude, latitude} = data.point
          me.lng = longitude
          me.lat = latitude
          me.map.setCenter(new BMapGL.Point(me.lng, me.lat))
          me.getLookUpAddress()
          me.selectedIndex = -1
        }
      })
      locationControl.addEventListener('locationError', () => {
        me.$message.error('获取位置失败')
      })
      // 个性化样式
      me.map.setMapStyleV2({
        styleId: '512722b64745f0025f56c8672cbbc060'
      })

      me.getPosition()
      me.zoomOn()
    },
    /* 传值选址
    * [121.245806, 31.299379]
    */
    initPostion(pickerPosition, searchKey = '') {
      this.pickerPosition = pickerPosition && pickerPosition.filter(_ => _)
      this.searchKey = searchKey
      this.searchLngLat = ''
      this.mapStatus = true
      this.customOverlay = null
      this.$nextTick(function () {
        this.setMap()
      })
    },
    getPosition() {
      const me = this
      const BMapGL = window.BMapGL

      if (me.pickerPosition && me.pickerPosition.length) {
        const [gcjLng, gcjLat]= me.pickerPosition
        // COORDINATES_GCJ02 = 3  GCJ02坐标
        // COORDINATES_BD09 = 5  百度bd09经纬度坐标
        let ggPoint = new BMapGL.Point(gcjLng, gcjLat)
        const translateCallback = (data) => {
          if (data.status === 0) {
            const { lng, lat } = data.points[0]
            me.lng = lng
            me.lat = lat
            me.map.setCenter(new BMapGL.Point(me.lng, me.lat))
            me.getLookUpAddress()
            me.selectedIndex = -1
          }
          me.pickerPosition = null
        }
        let convertor = new BMapGL.Convertor()
        let pointArr = []
        pointArr.push(ggPoint)
        convertor.translate(pointArr, 3, 5, translateCallback)
      } else {
        let geolocation = new BMapGL.Geolocation()
        // 开启SDK辅助定位
        geolocation.enableSDKLocation()
        geolocation.getCurrentPosition(function (data) {
          if (geolocation.getStatus() === 0) {
            me.lng = data.longitude
            me.lat = data.latitude
            me.map.setCenter(new BMapGL.Point(me.lng, me.lat))
            me.getLookUpAddress()
            me.selectedIndex = -1
          }
        })
      }
      me.picker()
    },
    // 经纬度反查地址
    getLookUpAddress() {
      const me = this
      const BMapGL = window.BMapGL
      // 创建地理编码实例
      let myGeo = new BMapGL.Geocoder()
      // 根据坐标得到地址描述
      myGeo.getLocation(new BMapGL.Point(me.lng, me.lat), function (result) {
        if (result) {
          me.address = result.address || ''
          me.city = result.addressComponents.city || ''
          me.aois =  result.content.poi_desc || ''
          me.adcode = result.content.address_detail.adcode || ''
        }
        me.picker()
      })
    },
    updateCenter(location) {
      const me = this
      const BMapGL = window.BMapGL
      const [lng, lat] = location || this.pickerPosition || [this.lng, this.lat]
      me.map.setCenter(new BMapGL.Point(lng, lat))
    },
    // 事件绑定
    zoomOn() {
      this.map.addEventListener('zoomstart', this.mapZoomstart)
      this.map.addEventListener('zoomend', this.mapZoomend)
    },
    // 解绑事件
    zoomOff() {
      this.map.removeEventListener('zoomstart', this.mapZoomstart)
      this.map.removeEventListener('zoomend', this.mapZoomend)
    },
    mapZoomstart() {
      const center = this.map.getCenter()
      this.currentPosition = [center.lng, center.lat]
    },
    mapZoomend() {
      // this.updateCenter(this.currentPosition)
      this.currentPosition = null
    },
    picker() {
      const me = this
      const BMapGL = window.BMapGL
      console.log('me.map======', me.map)
      // console.log(' map.getOverlays();',  me.map.getOverlays())
      // for (var i = 0; i < me.map.getOverlays().length; i++){
      //   me.map.removeOverlay(me.map.getOverlays()[i]);
		  // }
      if (me.customOverlay) {
        me.customOverlay.setPoint(new BMapGL.Point(me.lng, me.lat))
      } else {
        // 创建自定义覆盖物DOM
        const createDOM = () => {
          const img = document.createElement('img')
          img.style.height = '54px'
          img.style.width = '54px'
          img.src = me.imgSrc
          img.draggable = false
          return img
        }
        // 创建自定义覆盖物
        me.customOverlay = new BMapGL.CustomOverlay(createDOM, {
          point: new BMapGL.Point(me.lng, me.lat),
          opacity: 1,
          map: me.map,
          enableDraggingMap: true
        })

        // 将自定义覆盖物添加到地图上
        me.map.addOverlay(me.customOverlay)

        // this.map.addEventListener('dragging', () => {
        //   const pos = me.map.getCenter()
        //   const lng = pos.lng
        //   const lat = pos.lat
        //   me.customOverlay.setPoint(new BMapGL.Point(lng, lat))
        // }, false)
        this.map.addEventListener('dragend',() => {
          const pos = me.map.getCenter()
          me.lng = pos.lng
          me.lat = pos.lat
          me.customOverlay.setPoint(new BMapGL.Point(me.lng, me.lat))
          me.getLookUpAddress()
          me.selectedIndex = -1
        }, false)
      }
    },
    change() {
      const me = this
      const BMapGL = window.BMapGL
      if (me.searchLngLat.trim()) {
        const location = me.searchLngLat.split(',')
        if (!location[0] || !location[1]) {
          return me.$message.error('请检查输入的经纬度是否正确!')
        }
        const [lng, lat] = location
        me.lng = lng
        me.lat = lat
        me.getLookUpAddress()
        me.updateCenter(location)
        return
      }
      if (!me.searchKey) {
        return me.$message.error('请输入搜索内容')
      }

      let options = {
        onSearchComplete: function(results){
          if (results) {
            me.selectedIndex = -1
            me.searchResult = results._pois
            me.showResult = true
          } else {
            me.$message.error('搜索无结果')
          }
        }
      }
      let local = new BMapGL.LocalSearch(me.map, options)
      local.search(me.searchKey)
    },
    getAddress(result) {
      return result.address
    },
    selectedTab(result, index) {
      const me = this
      me.selectedIndex = index
      me.selectedPosition = result.point
      me.lng = result.point.lng
      me.lat = result.point.lat
      me.address = me.getAddress(result)
      me.showResult = false
      me.updateCenter([me.lng, me.lat])
      me.picker()
    },
    bd09ToGcj02(bdLat, bdLng) {
      const X_PI = (Math.PI * 3000.0) / 180.0
      const x = bdLng - 0.0065
      const y = bdLat - 0.006
      const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI)
      const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI)
      const gcjLng = z * Math.cos(theta)
      const gcjLat = z * Math.sin(theta)
      return [gcjLat, gcjLng]
    },
    setStorage() {
      const me = this
      const [gcjLat, gcjLng] = me.bd09ToGcj02(me.lat, me.lng)
      if (gcjLng && gcjLat) {
        window.localStorage.setItem('position', JSON.stringify({
          lng: gcjLng,
          lat: gcjLat,
          address: me.address,
          city: me.city,
          aois: me.aois,
          adcode: me.adcode
        }))
      }
      $emit(me, 'mapData')
      me.closeDialog()
    },
    closeDialog() {
      const me = this
      me.mapStatus = false
      me.positionPicker && me.positionPicker.stop()
      me.positionPicker = null
      me.zoomOff()
    }
  },
  emits:['mapData']
}
</script>

<style scoped lang="scss">
.amap-page-container {
  height: 70vh;
}

.search {
  width: 100%;
  z-index: 10;
}

.flex {
  height: 33px;
  margin: 14px 14px 8px;
  border-radius: 5px;
  background-color: #fff;
}

input {
  height: 29px;
  line-height: 29px;
  padding: 0 8px;
  border-radius: 3px;
  background-color: #f2f2f2;
}

input[type='search'] {
  -webkit-appearance: block;
}

input::-webkit-search-cancel-button {
  display: block;
}

.btn-search {
  width: 40px;
  height: 29px;
  line-height: 29px;
  margin-left: 10px;
  font-size: 16px;
  text-align: center;
}

ul {
  max-height: 220px;
  margin: 0;
  padding: 0;
  position: absolute;
  left: 0;
  z-index: 100;
  width: 100%;
  border-radius: 5px;
  background-color: #fff;
  overflow: auto;
}

li {
  display: block;
  height: 44px;
  line-height: 44px;
  margin: 0 14px;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
}

.text {
  display: inline-block;
  width: 100%;
  padding-left: 3px;
}

li:last-child {
  border-bottom: 0;
}

.selected-text {
  width: 88%;
  color: #ffa727;
}

.btn-complate {
  position: absolute;
  height: 44px;
  line-height: 44px;
  left: 14px;
  right: 14px;
  bottom: 20px;
  font-size: 16px;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  background-color: #ffa727;
  z-index: 10;
}

#bdMapContainer {
  width: 100%;
  height: 60vh;
}

.amap-geolocation-con {
  left: 14px !important;
  bottom: 70px !important;
}
</style>
