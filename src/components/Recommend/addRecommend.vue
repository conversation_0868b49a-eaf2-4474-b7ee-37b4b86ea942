<template>
  <el-dialog
    v-model="dialogVisible"
    :before-close="handleClose"
    :title="title"
    center
    width="400px"
  >
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      status-icon
      label-width="50px"
    >
      <el-form-item label="标题" prop="name">
        <el-input
          v-focus
          v-if="dialogVisible"
          v-model="ruleForm.name"
          type="text"
          @change="changed = true"
        />
      </el-form-item>
      <el-form-item label="有效">
        <el-switch
          :model-value="status === '1'"
          active-color="#13ce66"
          inactive-color="#ff4949"
          @change="changeSwitch"
        />
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <span>
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { UpdateHomeModule, AddHomeModule } from '@/api/garage'
import { recordBeforeAlter, clearSessionData } from '@/utils/enum/logData'
export default {
  name: 'AddCarClassification',
  props: {
    type: {
      type: String,
      default: 'user_detail',
    },
  },
  data() {
    const checkName = (rule, value, callback) => {
      if (value.trim() === '') {
        return callback(new Error('标题不能为空'))
      }
      if (value.trim().length > 12) {
        return callback(new Error('标题长度不能超过12'))
      }
      callback()
    }
    return {
      editInfo: {},
      dialogVisible: false,
      id: '',
      status: '0',
      ruleForm: {
        name: '',
      },
      rules: {
        name: [{ validator: checkName, trigger: 'blur' }],
      },
      changed: false,
    }
  },
  computed: {
    title() {
      if (this.editInfo.operateType === 1) {
        return '插上'
      }
      if (this.editInfo.operateType === 2) {
        return '插下'
      }
      if (this.editInfo.operateType === 3) {
        return '新增'
      }
      return '编辑'
    },
  },
  methods: {
    // 修改状态
    changeSwitch() {
      this.changed = true
      this.status = this.status === '0' ? '1' : '0'
    },
    init(item) {
      this.changed = false
      this.id = item.id || ''
      this.status = item.status || '0'
      this.ruleForm.name = item.title || ''
      this.editInfo = item
      if (this.$refs['ruleForm']) {
        this.$nextTick(() => {
          this.$refs['ruleForm'].clearValidate()
        })
      }
    },
    // 关闭
    handleClose() {
      if (!this.changed) {
        this.dialogVisible = false
        return
      }
      this.$confirm('确认关闭？')
        .then((_) => {
          this.dialogVisible = false
        })
        .catch((_) => {})
    },
    // 确认
    confirm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.submitForm()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 添加
    submitForm() {
      if (this.editInfo.operateType) {
        clearSessionData()
        AddHomeModule({
          ...this.editInfo,
          title: this.ruleForm.name.trim(),
          status: this.status,
          relatedType: this.type,
        }).then((response) => {
          if (response.data.code === 0) {
            this.$message.success('操作成功')
            this.dialogVisible = false
            $emit(this, 'updateSuccess')
          } else {
            this.$message.error(response.data.msg)
          }
        })
      } else {
        recordBeforeAlter({ ...this.ruleForm, id: this.id }, 'id')
        UpdateHomeModule({
          id: this.id,
          title: this.ruleForm.name.trim(),
          status: this.status,
        }).then((response) => {
          if (response.data.code === 0) {
            this.$message.success('操作成功')
            this.dialogVisible = false
            $emit(this, 'updateSuccess')
          } else {
            this.$message.error(response.data.msg)
          }
        })
      }
    },
  },
  emits: ['updateSuccess'],
}
</script>
