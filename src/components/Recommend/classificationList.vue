<template>
  <div>
    <p>
      <el-button @click="addList({}, 3)">添加</el-button>
      <el-input
        v-if="classification !== 'user_detail'"
        v-model="searchValue"
        :placeholder="placeholder"
        style="width: 200px"
        clearable
        @change="searchUserList"
      />
      <el-button v-show="tableData.length !== 0 && !loading" @click="resetList"
        >全部删除</el-button
      >
    </p>
    <div style="max-height: 80vh">
      <el-table
        ref="articleList"
        :data="tableData"
        row-key="articleList"
        border
        style="width: 100%; overflow-y: auto; height: 75vh"
      >
        <el-table-column prop="id" align="center" label="操作">
          <template v-slot="scope">
            <el-button
              v-if="classification === 'user_detail'"
              type="primary"
              link
              size="small"
              @click="setDialogTable(scope.row)"
              >修改标签</el-button
            >
            <el-button type="primary" link size="small" @click="deleteList(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
        <template v-if="classification === 'user_detail'">
          <el-table-column prop="goodId" label="用户ID" align="center" />
          <el-table-column prop="goodName" label="用户名" align="center" />
          <el-table-column prop="brandName" label="个性标签" align="center" />
          <el-table-column prop="createTime" align="center" label="创建时间">
            <template v-slot="scope">{{
              $filters.timeFullS(scope.row.createTime * 1000)
            }}</template>
          </el-table-column>
        </template>
        <template v-else>
          <el-table-column prop="shortTopicId" label="话题ID" align="center" />
          <el-table-column prop="title" label="话题名称" align="center" />
          <el-table-column align="center" label="Logo">
            <template v-slot="scope">
              <img :src="scope.row.logo" style="height: 40px" alt="" />
            </template>
          </el-table-column>
          <el-table-column prop="view" label="浏览量" align="center" />
          <el-table-column prop="fans" label="关注量" align="center" />
          <el-table-column prop="dynamic" label="动态数" align="center" />
          <el-table-column prop="type" align="center" label="类型">
            <template v-slot="scope">{{ typeList[scope.row.type] }}</template>
          </el-table-column>
          <el-table-column prop="createDate" align="center" label="创建时间">
            <template v-slot="scope">{{
              $filters.timeFullS(scope.row.createTime)
            }}</template>
          </el-table-column>
        </template>
      </el-table>
    </div>
    <el-pagination
      v-model:current-page="page"
      :page-size="20"
      :page-sizes="[10, 20, 40, 60]"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      class="el-pagination-center"
      @size-change="getList"
      @current-change="getList"
    />
    <el-dialog
      v-model="dialogTableVisible"
      class="dialog-content"
      title="修改标签"
    >
      <el-form ref="ruleForm" :model="ruleForm" label-width="70px">
        <el-form-item label="用户名">
          <el-input v-focus v-model="ruleForm.goodName" type="text" />
        </el-form-item>
        <el-form-item label="个性标签">
          <el-input
            v-focus
            v-model="ruleForm.brandName"
            type="text"
            maxlength="'6'"
          />
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span>
          <el-button @click="dialogTableVisible = false">取 消</el-button>
          <el-button type="primary" @click="editUser">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <add-class-list
      ref="AddList"
      :classification="classification"
      :pagetype="pagetype"
      @updateSuccess="
        () => {
          getList()
          $emit('changeValue')
        }
      "
    />
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import draggable from 'vuedraggable'
import AddClassList from './addClassificationList.vue'
import {
  updataUserPersonalityLabel,
  getSelectTopics
} from '@/api/articleModule'
import {
  GetHomeModuleRelationList,
  DeleteHomeModuleRelationList,
  ResetHomeModuleRelationList
} from '@/api/garage'
import { topicTypeList } from '@/utils/enum' // 话题类型
import {
  recordOldData,
  recordBeforeAlter,
  batchRecordBeforeAlter
} from '@/utils/enum/logData'
export default {
  name: 'UserList',
  components: {
    draggable,
    AddClassList
  },
  props: {
    moduleId: {
      type: Number,
      default: 0
    },
    classification: {
      type: String,
      default: 'user_detail'
    },
    getUrl: {
      type: String,
      default: 'home-model'
    },
    pagetype: {
      type: String,
      default: 'saveRelation'
    }
  },
  data() {
    return {
      tableData: [],
      oldList: [],
      ruleForm: {},
      typeList: topicTypeList,
      searchValue: '',
      oldSearchValue: '',
      total: 0,
      placeholder: '回车查询用户名',
      page: 1,
      loading: false,
      dialogTableVisible: false
    }
  },
  computed: {
    options() {
      return {
        draggable: '.item',
        handle: '.el-icon-rank',
        chosenClass: 'active',
        disabled: this.searchValue !== ''
      }
    }
  },
  watch: {
    tableData: function (newValue, oldValue) {
      // 将旧数据缓存 接口获取失败时恢复旧数据
      if (JSON.stringify(oldValue) !== JSON.stringify(this.oldList)) {
        this.oldList = oldValue
      }
    }
  },
  methods: {
    // 全部删除
    resetList() {
      this.$confirm('是否清空已选的列表！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          batchRecordBeforeAlter(
            this.tableData,
            this.moduleId,
            'beforeAlterTwo'
          )
          ResetHomeModuleRelationList({
            id: this.moduleId
          }).then((response) => {
            if (response.data.code === 0) {
              this.$message.success('清除成功')
              $emit(this, 'changeValue')
              this.getList()
            } else {
              this.$message.error(response.data.msg)
            }
          })
        })
        .catch()
    },
    // 当前筛选
    searchUserList() {
      this.page = 1
      this.getList()
    },
    // 修改单个用户标签
    setDialogTable(item) {
      this.dialogTableVisible = true
      this.ruleForm = item
    },
    // 开始修改用户标签
    editUser() {
      if (!this.ruleForm.brandName || this.ruleForm.brandName.trim() === '') {
        return this.$message.error('个性标签不可为空')
      }
      recordBeforeAlter(
        this.ruleForm,
        'uid',
        'oldTwoPageData',
        'beforeAlterTwo'
      )
      updataUserPersonalityLabel({
        moduleId: this.moduleId,
        uid: this.ruleForm.goodId,
        labelName: this.ruleForm.brandName.trim()
      }).then((response) => {
        if (response.data.code === 0) {
          this.$message.success('操作成功')
          this.dialogTableVisible = false
          $emit(this, 'changeValue')
          this.getList()
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    // 删除列表
    deleteList(item) {
      this.$confirm('是否删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          recordBeforeAlter(
            item,
            item.id ? 'id' : 'relationId',
            'oldTwoPageData',
            'beforeAlterTwo'
          )
          DeleteHomeModuleRelationList({
            id: item.id || item.relationId
          }).then((response) => {
            if (response.data.code === 0) {
              this.getList()
              $emit(this, 'changeValue')
            } else {
              this.$message.error(response.data.msg)
            }
          })
        })
        .catch()
    },
    // 获取所有列表
    getList() {
      this.loading = true
      const url =
        this.getUrl === 'home-model'
          ? GetHomeModuleRelationList
          : getSelectTopics
      url({
        page: this.page || 1,
        limit: 20,
        relatedType: this.classification,
        moduleId: this.moduleId,
        title: this.searchValue
      })
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data || {}
            this.total = data.total || data.totalSize || 0
            this.tableData =
              this.classification === 'user_detail'
                ? data.listData
                : data.topicList
            this.tableData = this.tableData || []
            recordOldData(this.tableData, 'oldTwoPageData')
          } else {
            this.$message.error(response.data.msg)
          }
          this.oldSearchValue = this.searchValue
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 创建
    init(moduleId) {
      this.tableData = []
      this.searchValue = ''
      this.page = 1
      this.placeholder =
        this.classification === 'user_detail'
          ? '回车查询用户名'
          : '回车查询话题名称'
      this.getList()
    },
    // operateType 分类操作类型0编辑 1.插上 2.插下 3.新增
    addList(row, type) {
      const data = {
        operateType: type,
        moduleId: this.moduleId,
        list: this.tableData
      }
      if (type !== 3) {
        data.id = row.id
      }
      this.$refs['AddList'].init(data)
      this.$refs['AddList'].dialogVisible = true
    }
  },
  emits: ['changeValue']
}
</script>

<style lang="scss" scoped>
.item {
  color: #909399;
  font-size: 14px;
  text-align: center;
  height: 80px;
  border: 1px solid #ebeef5;
  border-width: 0 0px 1px 1px;
  .el-col {
    border-right: 1px solid #ebeef5;
    word-break: break-all;
    overflow: hidden;
    height: 100%;
    display: table;
    .box {
      height: 100%;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-pack: center;
    }
  }
  &.header {
    border-width: 1px 0 1px 1px;
    height: 40px;
    line-height: 38px;
    overflow-y: scroll;
  }
  &.active {
    background: #dcdcdc;
  }
}
.content {
  height: calc(100vh - 240px);
  overflow-y: scroll;
  width: 100%;
}
.noData {
  text-align: center;
  line-height: 50px;
}
.faceLogo {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
}
</style>
