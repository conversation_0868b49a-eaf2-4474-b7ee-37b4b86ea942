<template>
  <el-dialog
    v-model="dialogVisible"
    :before-close="handleClose"
    :title="title"
    class="add-topic"
    center
    width="1000px"
  >
    <div class="choosedCar">
      <p v-for="(item, index) in valueList" :key="index" class="box">
        {{ item.name || item.auther || item.title }}
        <span @click="toggleData(item)">删除</span>
      </p>
    </div>

    <p class="topicSearch">
      {{ searchName }}
      <el-input
        v-focus
        v-if="dialogVisible"
        v-model="searchValue"
        placeholder="回车搜索"
        type="text"
        style="width: 300px"
        clearable
        @change="
          () => {
            page = 1
            getList()
          }
        "
      />
      <span v-if="classification === 'user_detail'" style="margin-left: 15px"
        >个性标签</span
      >
      <el-input
        v-if="classification === 'user_detail'"
        v-model="labelValue"
        placeholder="必填"
        type="text"
        style="width: 300px"
        maxlength="'6'"
        clearable
      />
    </p>
    <div style="max-height: 70vh">
      <el-table
        ref="multipleTable"
        :data="dataList"
        row-key="multipleTable"
        border
        style="width: 100%; overflow-y: auto; height: 55vh"
        @row-click="toggleData"
        @select="toggleAllCar"
        @select-all="toggleAllCar"
      >
        <el-table-column type="selection" width="70" />
        <template v-if="classification === 'user_detail'">
          <el-table-column prop="autherid" label="用户ID" align="center" />
          <el-table-column prop="auther" label="用户名" align="center" />
          <el-table-column prop="dateline" align="center" label="创建时间">
            <template v-slot="scope">{{
              $filters.timeFullS(scope.row.createDate)
            }}</template>
          </el-table-column>
        </template>
        <template v-else>
          <el-table-column prop="shortTopicId" label=" 话题ID" align="center">
            <template v-slot="scope">
              <span>{{ scope.row.shortTopicId || scope.row.id }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="title" label="话题名称" align="center" />
          <el-table-column align="center" label="Logo">
            <template v-slot="scope">
              <img
                :src="scope.row.img || scope.row.logo"
                style="height: 40px"
                alt=""
              />
            </template>
          </el-table-column>
          <el-table-column prop="viewNum" label="浏览量" align="center">
            <template v-slot="scope">
              <span>{{ scope.row.viewNum || scope.row.view }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="fansNum" label="关注量" align="center">
            <template v-slot="scope">
              <span>{{ scope.row.fansNum || scope.row.fans }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="dynamicNum" label="动态数" align="center">
            <template v-slot="scope">
              <span>{{ scope.row.dynamicNum }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="type" align="center" label="类型">
            <template v-slot="scope">{{ typeList[scope.row.type] }}</template>
          </el-table-column>
          <el-table-column prop="createDate" align="center" label="创建时间">
            <template v-slot="scope">{{
              $filters.timeFullS(scope.row.createTime)
            }}</template>
          </el-table-column>
        </template>
      </el-table>
    </div>

    <el-pagination
      v-if="total"
      :total="total"
      :page-size="20"
      :current-page="page"
      align="center"
      layout="total, prev, pager, next, jumper"
      class="el-pagination-center"
      @current-change="currentChange"
    />
    <div v-if="classification === 'user_detail'" class="page-content">
      <span @click="setPage('up')">上一页</span>
      <span>当前第{{ page }}页</span>
      <span @click="setPage('down')">下一页</span>
    </div>
    <template v-slot:footer>
      <span>
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { $emit } from '../../utils/gogocodeTransfer'
import { AddHomeModuleRelation } from '@/api/garage'
import {
  getselectTopics,
  addHomeShortTopic,
  saveRelation
} from '@/api/articleModule'
import { getUserAccountCorrelationV2, createUser } from '@/api/user'
import { AddInstructor } from '@/api/motoCollege' // 摩托学院添加教官
import { topicTypeList } from '@/utils/enum' // 话题类型
export default {
  name: 'AddClassList',
  props: {
    classification: {
      type: String,
      default: 'user_detail'
    },
    pagetype: {
      // 页面类型标记不同页面，请求不同接口
      type: String,
      default: ''
    }
  },
  data() {
    return {
      editInfo: {},
      typeList: topicTypeList,
      page: 1,
      total: 0,
      dataList: [],
      valueList: [],
      title: '',
      searchName: '用户名',
      searchValue: '',
      labelValue: '',
      loading: false,
      loadStatus: false, // user 加载状态
      dialogVisible: false
    }
  },
  methods: {
    // 变更页码，上一页或下一页
    currentChange(page) {
      this.page = page
      this.getList()
    },
    // 变更页码，上一页或下一页 ,用户筛选独用
    setPage(type) {
      if (!this.searchValue) {
        return this.$message.error('请输入用户名,再进行搜索')
      }
      if (type === 'up' && this.page === 1) {
        return this.$message.error('当前是第一页')
      }
      if (!this.loadStatus && type === 'down' && this.dataList.length < 20) {
        return this.$message.error('已没有更多数据')
      }
      if (this.loadStatus) {
        return this.$message.error('正在请求数据，请稍后')
      }
      this.loadStatus = true
      type === 'up' ? this.page-- : this.page++
      this.getList()
    },
    // 点选或多选
    toggleAllCar(data, type) {
      const me = this
      if (type) {
        // 单选
        return me.toggleData(type)
      }
      let status = true // 根据状态，全部清除（false) ,全部选择（true)走不同 的逻辑
      if (data.length === 0) {
        // 全部取消勾选
        me.$refs.multipleTable.clearSelection()
        status = false
      }
      const content = data.length ? data : me.dataList
      content.map(function (value) {
        if (status) {
          me.classification === 'short_topic'
            ? me.addShortTipic(value, data.length > 0)
            : me.addUserList(value, data.length > 0)
        } else {
          me.classification === 'short_topic'
            ? me.setShortTipic(value, false)
            : me.seteUser(value, false)
        }
      })
    },
    // 新增或删除 用户或短话题
    toggleData(item) {
      this.classification === 'short_topic'
        ? this.addShortTipic(item, true)
        : this.addUserList(item, true)
    },
    // 确认逻辑，走添加或删除话题
    addShortTipic(item, type) {
      if (
        this.valueList.some(
          (_) =>
            (_.name && _.name === item.name) ||
            (_.title && _.title === item.title)
        )
      ) {
        return this.setShortTipic(item, false)
      }
      this.addList(item, type)
    },
    // 删除话题或添加话题列表勾选逻辑,type 为false 删除，true更新勾选
    setShortTipic(item, type) {
      const me = this
      me.valueList.map(function (value, index) {
        if (value.title === item.title) {
          type
            ? (me.valueList = [...me.valueList])
            : me.valueList.splice(index, 1)
          me.$refs.multipleTable.toggleRowSelection(item, type)
        }
      })
    },
    // 确认逻辑，添加或删除用户
    addUserList(item, type) {
      const me = this
      if (me.valueList.some((_) => _.autherid === item.autherid)) {
        return this.seteUser(item, false)
      }
      me.addList(item, type)
    },
    // 删除用户或添加用户列表勾选逻辑，type 为false 删除，true更新勾选
    seteUser(item, type) {
      const me = this
      me.valueList.map(function (value, index) {
        if (value.autherid === item.autherid) {
          type
            ? (me.valueList = [...me.valueList])
            : me.valueList.splice(index, 1)
          me.$refs.multipleTable.toggleRowSelection(item, type)
        }
      })
    },
    // 添加选中状态列表
    addList(item, type) {
      if (type) {
        this.$refs.multipleTable.toggleRowSelection(item, true)
      }
      this.valueList = [...this.valueList, item]
      this.$nextTick(() => {
        document.getElementsByClassName('choosedCar')[0].scrollTo(1000, 1000)
      })
    },
    setChecklist() {
      const me = this
      if (me.dataList && me.dataList.length) {
        setTimeout(() => {
          me.dataList.map(function (item) {
            me.classification === 'short_topic'
              ? me.setShortTipic(item, true)
              : me.seteUser(item, true)
          })
        }, 500)
      }
    },
    // 获取列表
    getList() {
      this.loading = true
      this.dataList = []
      if (this.classification === 'user_detail' && this.searchValue) {
        getUserAccountCorrelationV2({
          username: this.searchValue,
          page: this.page,
          limit: 20
        })
          .then((response) => {
            if (response.data.code === 0) {
              const data = response.data.data || {}
              const list = data.list || []
              list.map(function (value) {
                value.autherid = value.uid
                value.auther = value.username
              })
              this.dataList = list
              this.setChecklist()
              setTimeout(() => {
                this.loadStatus = false
              }, 300)
            } else {
              this.loadStatus = false
              this.$message.error(response.data.msg)
            }
          })
          .catch(() => {
            this.loadStatus = false
            this.loading = false
          })
      } else if (this.classification === 'short_topic') {
        let postData =
          this.pagetype === 'focusCircle'
            ? {}
            : { moduleId: this.editInfo.moduleId }
        postData = {
          title: this.searchValue,
          page: this.page,
          limit: 20,
          ...postData
        }
        getselectTopics(postData)
          .then((response) => {
            if (response.data.code === 0) {
              this.dataList =
                response.data.data.shortTopicListData ||
                response.data.data.topicList
              this.setChecklist()
              this.total =
                response.data.data.total || response.data.data.toltalSize
            } else {
              this.$message.error(response.data.msg)
            }
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    // 重置状态
    init(item) {
      this.dialogVisible = true
      this.page = 1
      this.total = 0
      this.editInfo = item
      this.searchValue = ''
      this.labelValue = ''
      this.dataList = []
      this.valueList = []
      this.searchName =
        this.classification === 'user_detail' ? '用户名' : '话题名称'
      this.title =
        this.classification === 'user_detail' ? '选择用户' : '选择话题'
      this.getList()
    },
    // 关闭
    handleClose() {
      if (this.valueList.length === 0) {
        this.dialogVisible = false
        return false
      }
      this.$confirm('确认关闭？')
        .then(() => {
          this.dialogVisible = false
        })
        .catch(() => {})
    },
    // 确认
    confirm() {
      const me = this
      if (
        me.classification === 'user_detail' &&
        (!me.labelValue || me.labelValue.trim() === '')
      ) {
        return me.$message.error('个性标签必填')
      }
      if (me.valueList.length === 0) {
        if (me.classification === 'user_detail') {
          return me.$message.error('您还没有选择任何用户')
        } else {
          return me.$message.error('您还没有选择任何话题')
        }
      }
      const data = {
        moduleId: me.editInfo.moduleId || '',
        operateType: me.editInfo.operateType,
        ids: me.valueList.map((_) => _.shortTopicId || _.autherid).join(','),
        personLabel: me.labelValue.trim()
      }
      data.id = me.editInfo.id ? me.editInfo.id : ''
      let postUrl = null
      let postData = {}
      if (me.pagetype === 'motoCollege') {
        // 摩托学院教官添加用户时，点击确定只调用添加教官接口
        postUrl = AddInstructor
        postData = {
          uids: data.ids,
          label: me.labelValue.trim()
        }
      } else if (me.pagetype === 'focusUser') {
        // 新关注用户推荐
        postUrl = createUser
        postData = data
        delete postData.moduleId
        delete postData.operateType
      } else if (me.pagetype === 'focusCircle') {
        // 新关注话题推荐
        postUrl = addHomeShortTopic
        const topics = me.valueList.map((_) => {
          return { id: _.id, type: _.type }
        })
        postData.jsonStr = JSON.stringify(topics)
      } else if (me.pagetype === 'saveRelation') {
        // 小组件话题增加推荐话题
        postUrl = saveRelation
        const topics = me.valueList.map((_) => {
          return { id: _.id, type: _.type }
        })
        const jsonData = {
          topics: topics,
          moduleId: me.editInfo.moduleId
        }
        postData.json = JSON.stringify(jsonData)
      } else {
        postUrl = AddHomeModuleRelation
        const topics = me.valueList.map((_) => {
          return { id: _.shortTopicId, type: _.type }
        })
        postData = {
          topics: topics,
          ...data
        }
      }
      postUrl(postData).then((response) => {
        if (response.data.code === 0) {
          this.$message.success('操作成功')
          this.dialogVisible = false
          $emit(this, 'updateSuccess')
        } else {
          this.$message.error(response.data.msg)
        }
      })
    }
  },
  emits: ['updateSuccess']
}
</script>

<style>
.add-topic .el-dialog--center .el-dialog__body {
  padding: 15px 15px 0px;
}
.add-topic .el-dialog.el-dialog--center {
  margin-top: 10vh !important;
}
</style>

<style lang="scss" scoped>
.list-box {
  height: 450px;
  width: 100%;
  overflow-y: scroll;
}
.choosedCar {
  border-bottom: 1px solid #ddd;
  max-height: 100px;
  overflow-y: auto;
  .box {
    margin: 0 10px 10px 0;
    line-height: 24px;
    height: 24px;
    border: 1px solid #ddd;
    padding-left: 5px;
    display: inline-block;
    color: #ffffff;
    background: #9a7b49;
    span {
      text-decoration: underline;
      cursor: pointer;
      border-left: 1px solid #ddd;
      padding: 0 3px;
      display: inline-block;
      height: 100%;
      background: #f56c6c;
      // float: right;
      margin-left: 5px;
    }
  }
}
.noData {
  line-height: 50px;
  text-align: center;
}
.item {
  color: #909399;
  font-size: 14px;
  text-align: center;
  height: 80px;
  border: 1px solid #ebeef5;
  border-width: 0 0px 1px 1px;
  .el-col {
    border-right: 1px solid #ebeef5;
    overflow: hidden;
    word-wrap: break-word;
    height: 100%;
    display: table;
    word-break: break-all;
    .box {
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-pack: center;
      height: 100%;
    }
  }
  &.header {
    border-width: 1px 0 1px 1px;
    height: 40px;
    overflow-y: scroll;
    line-height: 38px;
    ::-webkit-scrollbar {
      opacity: 0;
    }
  }
}
.faceLogo {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
}
</style>
