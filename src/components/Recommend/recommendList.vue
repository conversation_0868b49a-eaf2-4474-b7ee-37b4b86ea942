<template>
  <div>
    <p>
      <el-button @click="addClass({}, 3)">新建</el-button>
      <el-input
        v-model="searchValue"
        style="width: 200px"
        placeholder="回车查询标题"
        clearable
        @change="getListBusinesCategory"
      />
      <el-button type="success" @click="effective">生效</el-button>
    </p>
    <div style="max-height: 80vh">
      <el-table
        ref="articleList"
        :data="tableData"
        highlight-current-row
        row-key="articleList"
        border
        style="width: 100%; overflow-y: auto; height: 75vh"
        @row-dblclick="rowDoubleClick"
      >
        <el-table-column prop="id" align="center" label="操作">
          <template v-slot="scope">
            <el-button type="primary" link size="small" @click="editClass(scope.row)"
              >编辑</el-button
            >
            <el-button type="primary" link size="small" @click="deletleClass(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="title" label="标题" align="center" />
        <el-table-column prop="prime" label="是否有效" align="center">
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.isEffective"
              @change="changeSwitch(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createDate" align="center" label="创建时间">
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.createTime * 1000)
          }}</template>
        </el-table-column>
        <el-table-column prop="updateTime" align="center" label="修改时间">
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.updateTime * 1000)
          }}</template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      v-model:current-page="page"
      :page-size="20"
      :page-sizes="[10, 20, 40, 60]"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      class="el-pagination-center"
      @size-change="getListBusinesCategory"
      @current-change="getListBusinesCategory"
    />
    <add-recommend
      ref="AddRecommend"
      :type="classification"
      @updateSuccess="
        () => {
          getListBusinesCategory()
          $emit('changeValue')
        }
      "
    />
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import draggable from 'vuedraggable'
import addRecommend from './addRecommend.vue'
import {
  GetHomeModuleList,
  DeleteHomeModule,
  EnableHomeModule
} from '@/api/garage'
import { recordOldData, recordBeforeAlter } from '@/utils/enum/logData'
export default {
  name: 'RecommendList',
  components: {
    draggable,
    addRecommend
  },
  props: {
    classification: {
      type: String,
      default: 'user_detail'
    }
  },
  data() {
    return {
      tableData: [],
      oldList: [],
      searchValue: '',
      id: '',
      oldSearchValue: '',
      total: 0,
      page: 1
    }
  },
  computed: {
    options() {
      return {
        draggable: '.item',
        handle: '.el-icon-rank',
        chosenClass: 'active',
        disabled: this.searchValue !== ''
      }
    }
  },
  watch: {
    tableData: function (newValue, oldValue) {
      // 将旧数据缓存 接口获取失败时恢复旧数据
      if (JSON.stringify(oldValue) !== JSON.stringify(this.oldList)) {
        this.oldList = oldValue
      }
    }
  },
  created() {
    this.getListBusinesCategory()
  },
  methods: {
    // 生效
    effective() {
      EnableHomeModule().then((response) => {
        if (response.data.code === 0) {
          this.$message.success('成功')
          this.$store.commit('CHANGE_PAGE_CHANGED_STATUS', {
            name: this.$route.name,
            text: ''
          })
        } else {
          this.$message.error('失败')
        }
      })
    },
    // 删除
    deletleClass(row) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          recordBeforeAlter(row, 'id')
          DeleteHomeModule({
            id: row.id
          }).then((response) => {
            if (response.data.code === 0) {
              this.$message.success('删除成功')
              this.getListBusinesCategory()
              $emit(this, 'changeValue')
            } else {
              this.$message.error('删除失败')
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 获取列表数据
    getListBusinesCategory() {
      GetHomeModuleList({
        page: this.page,
        limit: 20,
        relatedType: this.classification,
        title: this.searchValue
      }).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data.listData
          this.total = response.data.data.total
          recordOldData(data)
          data.map(function (value) {
            value.isEffective = value.status === '1'
          })
          this.tableData = data
        }
        this.oldSearchValue = this.searchValue
      })
    },
    // 快捷激活开关
    changeSwitch(row) {
      this.$refs['AddRecommend'].init({
        ...row,
        status: row.isEffective ? '1' : '0'
      })
      this.$refs['AddRecommend'].submitForm()
    },
    // 编辑当前分类
    editClass(row) {
      this.$refs['AddRecommend'].init(row)
      this.$refs['AddRecommend'].dialogVisible = true
    },
    // operateType 分类操作类型 1.插上 2.插下 4.新增
    addClass(row, type) {
      const data = { operateType: type, status: '1' }
      if (type !== 4) {
        data.id = row.id || ''
      }
      this.$refs['AddRecommend'].init(data)
      this.$refs['AddRecommend'].dialogVisible = true
    },
    // 双击查看子模块详情
    rowDoubleClick(row) {
      this.id = row.id
      $emit(this, 'chooseRow', row)
    }
  },
  emits: ['changeValue', 'chooseRow']
}
</script>

<style lang="scss" scoped>
.item {
  color: #909399;
  font-size: 14px;
  text-align: center;
  height: 80px;
  border: 1px solid #ebeef5;
  border-width: 0 0px 1px 1px;
  .el-col {
    border-right: 1px solid #ebeef5;
    word-break: break-all;
    overflow: hidden;
    height: 100%;
    display: table;
    .box {
      height: 100%;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-pack: center;
    }
  }
  &.header {
    border-width: 1px 0 1px 1px;
    height: 40px;
    line-height: 38px;
    overflow-y: scroll;
  }
  &.active {
    background: #dcdcdc;
  }
  .el-button + .el-button {
    margin-left: 5px;
  }
}
.content {
  height: calc(100vh - 240px);
  overflow-y: scroll;
  width: 100%;
}
.noData {
  text-align: center;
  line-height: 50px;
}
</style>
