<template>
  <div class="upload">
    <el-upload
      :show-file-list="false"
      :http-request="httpRequest"
      :on-success="onSuccess"
      multiple
      action
    >
      <el-button>添加图片</el-button>
    </el-upload>
  </div>
</template>

<script>
export default {
  name: 'ALiUpload',
  props: {
    value: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  methods: {
    /* @description [httpRequest 覆盖默认的上传行为，实现自定义上传]
     * @param    {object}   option [上传选项]
     * @return   {null}   [没有返回]
     */
    async httpRequest(option) {
      this.$oss.ossUploadImage(option)
    },
    // 上传成功后
    onSuccess(res, file, fileList) {
      console.log(res)
      console.log(file)
      console.log(fileList)
    },
  },
  emits: ['update:value'],
}
</script>

<style lang="scss" scoped>
.upload {
  display: inline-block;
}
</style>
