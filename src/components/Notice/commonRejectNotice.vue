<template>
  <div>
    <el-dialog
      v-model="showStatus"
      :close-on-click-modal="false"
      :title="title"
      class="dialog-content"
      append-to-body
      width="30%"
    >
      <el-radio-group
        v-if="rejectList && rejectList.length"
        v-model="radioRejectReason"
        class="radio-content"
        @change="onChange"
      >
        <el-radio
          v-for="(reject, index) in rejectList"
          :key="index"
          :label="reject"
          >{{ reject }}</el-radio
        >
      </el-radio-group>
      <textarea
        v-if="showRejectReason"
        v-model="rejectReason"
        :placeholder="placeholder"
        class="content"
        cols="30"
        rows="10"
        @keyup="onKeyup"
      />
      <div class="food">
        <el-button type="danger" @click="confirm(false)">取消</el-button>
        <el-button type="success" @click="confirm(true)">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
export default {
  name: 'RejectNotice',
  props: {
    showRejectReason: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      radioRejectReason: '', // 选择的拒绝原因
      rejectReason: '', // 填写拒绝原因
      title: '选择原因',
      placeholder: '请填写原因',
      showStatus: false, // 是否显示
      rejectList: [
        '行驶证与认证车辆不符',
        '车辆照片务必保证车牌号清晰完整',
        '请按要求上传行驶证和车辆图片',
        '车架号必须保留首位和末四位清晰'
      ],
      extendInfo: {} // 扩展信息，当前处理的信息
    }
  },
  watch: {},
  mounted() {},
  methods: {
    init(rejectList, extendInfo = {}) {
      console.log(rejectList, 888)
      this.showStatus = true
      this.rejectReason = ''
      this.radioRejectReason = ''
      this.rejectList = rejectList || this.rejectList
      this.extendInfo = extendInfo
    },
    onChange(data) {
      this.radioRejectReason = data
      this.rejectReason = ''
    },
    onKeyup() {
      this.radioRejectReason = ''
    },
    confirm(confirm) {
      if (!confirm) {
        return (this.showStatus = false)
      }
      if (!this.radioRejectReason && !this.rejectReason.length) {
        this.$message.error('请选择类型或填写原因')
        return
      }
      if (this.rejectReason.length > 255) {
        this.$message.error('不能超过255个字符')
        return
      }
      const content =
        (this.radioRejectReason ? this.radioRejectReason : this.rejectReason) ||
        ''
      this.showStatus = false
      $emit(this, 'confirmRejection', {
        content,
        auditFailReason: content,
        extendInfo: this.extendInfo
      })
    }
  },
  emits: ['confirmRejection']
}
</script>

<style scoped>
.content {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 10px;
  display: block;
  width: 100%;
  resize: none;
}
.radio-content {
  line-height: 20px;
  margin-bottom: 10px;
}
</style>
