<template>
  <div>
    <el-dialog
      @close="handleClose"
      v-model="showStatus"
      :close-on-click-modal="clickModeStatus"
      :title="title"
      class="dialog-content"
      append-to-body
      width="20%"
    >
      <template v-if="type === 'authorizationAudit'">
        <el-radio-group
          v-model="authorizationAuditIndex"
          class="radio-content"
          @change="setData"
        >
          <el-radio :label="0">内容不正确</el-radio>
          <el-radio :label="1">图片不清晰</el-radio>
          <el-radio :label="2">其他</el-radio>
        </el-radio-group>
      </template>
      <template v-if="type == 'creator'">
        <el-radio-group
          v-model="creatorIndex"
          class="radio"
          @change="setDataCreator"
        >
          <el-radio :label="0" class="radio-content"
            >上传图片与要求不符</el-radio
          >
          <el-radio :label="1" class="radio-content">内容优质度不足</el-radio>
          <el-radio :label="2" class="radio-content">粉丝数未达要求</el-radio>
          <el-radio :label="3" class="radio-content"
            >其他平台历史发布量不符要求</el-radio
          >
          <el-radio :label="4" class="radio-content"
            >其他平台内容与摩托车相关度不高</el-radio
          >
          <el-radio :label="5" class="radio-content"
            >其他平台账号未查询到</el-radio
          >
        </el-radio-group>
      </template>
      <textarea
        v-if="type !== 'creator'"
        v-model="data"
        :placeholder="placeholder"
        class="content"
        cols="30"
        rows="10"
      />
      <div class="food">
        <el-button type="success" @click="confirm(true)">{{
          confirmTitle
        }}</el-button>
        <el-button type="danger" @click="confirm(false)">{{
          cancelTitle
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
export default {
  name: 'RejectNotice',
  props: {
    title: {
      type: String,
      default: '拒绝通知'
    },
    clickModeStatus: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请填写拒绝原因'
    },
    confirmTitle: {
      type: String,
      default: '确认'
    },
    cancelTitle: {
      type: String,
      default: '取消'
    }
  },
  data() {
    return {
      type: '',
      data: '', // 拒绝内容
      authorizationAuditIndex: 0, // 品牌授权审核更新索引
      creatorIndex: 0,
      showStatus: false, // 是否显示
      dataList: [
        {
          type: 'city',
          content: '由于你所在城市名额已满，本次城市官申请未能通过'
        },
        {
          type: 'shop',
          content: `亲爱的商家：
你本次的商家审核未通过，请对照以下原因修改：
1、上传的认证图片不清晰
2、上传的认证图片不符合认证要求
3、填写的认证信息不正确

如果你有任何疑问，欢迎致电“经销商客服电话18120063682”或者添加经销商客服微信：motofan888

摩托范`
        },
        {
          type: 'invalidation',
          content: `亲爱的商家，你好！
你本次的活动【活动名称】已失效。
若活动还有剩余置顶天数，【活动置顶】剩余天数的金币将会在24小时内退还到您的账户；

如果你有任何疑问，欢迎联系区域经理咨询。`
        },
        {
          type: 'authentication',
          content: `亲爱的骑士，
你本次的XX认证审核未通过，请对照以下原因修改：
1、上传的认证图片不清晰；
2、上传的认证图片不符合认证要求；
3、填写的认证信息不正确；
如果你有任何疑问，欢迎在APP内私信“摩托范内容小编”或者添加客服QQ2927706039。`
        },
        {
          type: 'girlfriend',
          content: `您本次申请的女摩友认证，由于“上传图片与要求不符”原因审核不通过。 如有问题，欢迎在APP内私信“摩托范官方团队”或者添加微信：18020255629，备注女摩友认证。摩托范`
        },
        {
          type: 'activityunagree',
          content: `亲爱的商家，你好！

你本次的活动【活动名称】审核未通过，请对照以下原因修改：
1、上传的活动图片不清晰；
2、填写的活动信息违规，涉及到真实联系方式、水车等；

活动支付的金币将会在24小时内退还到您的账户，如果你有任何疑问，欢迎联系区域经理咨询。`
        }
      ]
    }
  },
  watch: {},
  mounted() {},
  methods: {
    init(type, ext = {}) {
      this.showStatus = true
      this.type = type
      this.creatorIndex = 0
      if (type === 'creator') return
      const data = this.dataList.filter((_) => _.type === type)
      this.data = (data[0] && data[0].content) || ''
      this.data = type === 'authorizationAudit' ? '内容不正确' : this.data
      this.authorizationAuditIndex = 0
      // 文字是否动态替换
      if (
        !['authentication', 'invalidation', 'activityunagree'].includes(type)
      ) {
        return
      }
      this.replaceData(type, ext)
    },
    replaceData(type, ext = {}) {
      // 替换xx文案。用户认证时走此方法
      const me = this
      let name = ''
      if (me.$route.name === 'femalefriend' || me.$route.name === 'expert') {
        name = me.$route.name === 'femalefriend' ? '女摩头' : '行家'
      } else if (
        me.$route.name === 'gotTalent' ||
        me.$route.name === 'regionalKnight'
      ) {
        name = me.$route.name === 'gotTalent' ? '达人' : '地域骑士'
      } else if (
        me.$route.name === 'enterpriseCertification' ||
        me.$route.name === 'selfMedia'
      ) {
        name = me.$route.name === 'enterpriseCertification' ? '企业' : '自媒体'
      } else if (me.$route.name === 'officialMedia') {
        name = '官方媒体'
      }
      me.data = me.data.replace('XX', name)

      if (
        ['invalidation', 'activityunagree'].includes(type) &&
        ext.activityName
      ) {
        me.data = me.data.replace('活动名称', ext.activityName)
      }
    },
    setData(index) {
      this.data = index === 2 ? '' : ['内容不正确', '图片不清晰', '其他'][index]
    },
    setDataCreator(index) {
      this.data = [
        '上传图片与要求不符',
        '内容优质度不足',
        '粉丝数未达要求',
        '其他平台历史发布量不符要求',
        '其他平台内容与摩托车相关度不高',
        '其他平台账号未查询到'
      ][index]
    },
    handleClose() {
      $emit(this, 'cancel')
    },
    confirm(type) {
      if (type && !this.data.length) {
        this.$message.error(this.placeholder)
        return
      }
      this.showStatus = false
      if (!type) {
        $emit(this, 'cancel')
        return
      }
      if (this.data.length > 255) {
        this.$message.error('不能超过255个字符')
        return
      }
      if (type) {
        $emit(this, 'confirmRejection', this.data)
      }
    }
  },
  emits: ['confirmRejection']
}
</script>

<style scoped>
.content {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 10px;
  display: block;
  width: 100%;
  resize: none;
}
.radio-content {
  margin-bottom: 10px;
}
.radio {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
