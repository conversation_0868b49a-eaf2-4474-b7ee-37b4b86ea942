<template>
  <div class="c-feed-list-ctr">
    <el-tag
      v-if="item.prime && showPrime"
      size="small"
      type="warning"
      effect="dark"
    >
      {{ '优质' }}
    </el-tag>
    <c-feedList :card="item" />
    <div class="circle-box">
      <div v-for="hoop in item.esMotorHoopList" :key="hoop.id" class="box">
        <el-icon class="icon"><IconHelp /></el-icon>{{ hoop.name }}
      </div>
    </div>
    <div class="content-foot">
      <div class="content-foot-box">
        <span>CTR {{ (item.ctr * 100).toFixed(2) }}%</span>
        <span>阅读完成率 {{ item.readPercent || '0.00%' }}</span>
        <span>曝光量 {{ item.exposureTimes || 0 }}</span>
        <span>评论数 {{ item.replycnt || 0 }} </span>
      </div>
      <div class="content-foot-box">
        <span>浏览量 {{ item.viewNum || 0 }} </span>
        <span
          >发布时间：
          {{
            $filters.timeFullS((item.dateLine || item.createTime) * 1000)
          }}</span
        >
      </div>
    </div>
  </div>
</template>
<script>
import { Help as IconHelp, Sort as IconSort } from '@element-plus/icons-vue'
import CFeedList from './index.vue'
export default {
  name: 'CFeedListCtr',
  components: {
    CFeedList,
    IconHelp
  },
  props: {
    item: {
      type: Object,
      default: () => {
        return {}
      }
    },
    showPrime: {
      typeof: Boolean,
      default: false
    },
    showExpire: {
      typeof: Boolean,
      default: false
    }
  }
}
</script>
<style lang="scss" scoped>
.c-feed-list-ctr {
  .circle-box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .box {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin: 5px;
      .icon {
        width: 15px;
        height: 15px;
      }
    }
  }

  .content-foot {
    border: 1px solid #ebeef5;
    border-radius: 5px;
    margin: 5px;
    padding: 5px;
    &-box {
      display: flex;
      justify-content: space-between;
      padding: 2px;
      color: #909399;
    }
  }
}
</style>
