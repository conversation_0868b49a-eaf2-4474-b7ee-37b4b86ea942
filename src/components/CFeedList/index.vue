<template>
  <section class="c-feed-list">
    <div
      v-if="card.type === 'essay_detail'"
      :key="card && card.essayId"
      class="feed-content"
    >
      <!-- 大图显示(单张)(上下布局) -->
      <div v-if="card.bigOrSmall === '1'" class="tip-con1">
        <!-- 标题样式 -->
        <div v-if="getContent(card)" class="sub-subject">
          <pre v-html="getContent(card)" />
        </div>
        <div v-if="getMediaImgNum(card) > 0" class="img-wrap">
          <img
            :src="changeImgGIF(getFirstMediaImgs(card)[0].imgUrl)"
            class="img-wrap-img"
            alt
          />
          <!-- 是视频，显示播放时长 -->
          <div
            v-if="getType(card) === '6' && getFirstMediaInfo(card).duration"
            class="time-num"
          >
            <span class="num">{{
              formatSeconds(getFirstMediaInfo(card).duration)
            }}</span>
          </div>
          <div v-if="getType(card) === '6'" class="play" />
        </div>
      </div>
      <!-- 小图显示 -->
      <div v-else class="tip-con2">
        <!-- 小图(单张)(左右布局) -->
        <div v-if="getMediaImgNum(card) < 3" class="con2-one">
          <div class="left">
            <!-- 标题样式（有标题放标题，无标题放内容） -->
            <div v-if="getContent(card)" class="sub-subject">
              <!-- card.digest===5(优，无icon图  不显示) -->
              <pre v-html="getContent(card)" />
            </div>
          </div>
          <div v-if="getMediaImgNum(card) > 0" class="right">
            <img :src="changeImgGIF(getFirstMediaImgs(card)[0].imgUrl)" alt />
            <!-- 非视频、并且图片数量大于1时，才显示图片数量 -->
            <div
              v-if="getMediaImgNum(card) > 1 && getType(card) === '2'"
              class="img-num"
            >
              <span class="icon" />
              <span class="num">{{ card.imgCount }}</span>
            </div>
            <div
              v-if="getType(card) === '6' && getFirstMediaInfo(card).duration"
              class="time-num"
            >
              <span class="num">{{
                formatSeconds(getFirstMediaInfo(card).duration)
              }}</span>
            </div>
            <div v-if="getType(card) === '6'" class="play" />
          </div>
        </div>
        <!-- 三图(上下布局) -->
        <div v-else class="con2-two">
          <!-- 标题样式 -->
          <div
            v-if="getMediaImgNum(card) >= 3 && getContent(card)"
            class="sub-subject"
          >
            <pre v-html="getContent(card)" />
          </div>
          <div v-if="getMediaImgNum(card) >= 3" class="three-img-wrap">
            <template v-for="(i, index) in card.mediaInfo" :key="index">
              <div v-if="index < 3" class="wrap">
                <img
                  :src="
                    changeImgGIF(
                      i && i.images && i.images[0] && i.images[0].imgUrl
                    )
                  "
                  class="three-img-wrap-img"
                  alt
                />
              </div>
            </template>
            <div v-if="getType(card) === '2'" class="img-num">
              <span class="icon" />
              <span class="num">{{ card.imgCount }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="moment">
      <p class="dotdotdot1" v-html="card.content || card.title"></p>
      <br />
      <div class="wrap">
        <div
          v-for="(media, mIndex) in card.mediaInfo"
          :key="mIndex"
          class="images-wrap"
        >
          <a
            v-for="(img, index) in media.images"
            :key="index"
            :href="img.imgOrgUrl"
            target="_black"
          >
            <img :src="changeImgGIF(img.imgUrl)" alt />
          </a>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'CFeedList',

  // cards: 数据
  props: {
    card: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  watch: {
    card(val, oldVal) {
      if (val !== oldVal) {
        this.setContentData(this.card)
      }
    }
  },
  created() {
    // console.log(this.card)
    this.setContentData(this.card)
  },
  mounted() {},
  methods: {
    // 格式化秒，转化为02:25（时分）、01:02:48（时分秒）,格式
    formatSeconds(value) {
      let result = ''
      if (value > 0) {
        const hour = Math.floor(value / 3600)
        const min = Math.floor(value / 60) % 60
        const sec = value % 60
        if (hour === 0) {
          result = ''
        } else if (hour < 10) {
          result = '0' + hour + ':'
        } else {
          result = hour + ':'
        }
        if (min < 10) {
          result += '0'
        }
        result += min + ':'
        if (sec < 10) {
          result += '0'
        }
        result += sec
      }
      return result
    },
    getContent(card) {
      return card.operateTitle || card.title || card.content
    },
    getFirstMediaInfo(card) {
      return (card.mediaInfo && card.mediaInfo[0] && card.mediaInfo[0]) || {}
    },
    getFirstMediaImgs(card) {
      return (
        (card.mediaInfo && card.mediaInfo[0] && card.mediaInfo[0].images) || []
      )
    },
    getMediaImgNum(card) {
      let length = 0
      card.mediaInfo &&
        card.mediaInfo.map((value) => {
          if (value && value.images) {
            length += value.images.length
          }
        })
      return length
    },
    getType(card) {
      const type =
        (card &&
          card.mediaInfo &&
          card.mediaInfo[0] &&
          card.mediaInfo[0].type) ||
        ''
      return type
    },
    // 重置content内容（有##解析为短话题）
    setContentData(card) {
      if (card.bigOrSmall === '1' && this.getType(card) === '2') {
        card.mediaInfo &&
          card.mediaInfo[0] &&
          card.mediaInfo[0].images &&
          card.mediaInfo[0].images.map(function (v) {
            if (
              v.imgOrgUrl &&
              (v.imgOrgUrl.indexOf('//file.jddmoto.com/') > -1 ||
                v.imgOrgUrl.indexOf('//file.58moto.com/') > -1)
            ) {
              v.imgUrl = v.imgOrgUrl.replace(
                /(\.(jpg)|\.(png)|\.(jpeg)|\.(gif)|.(bmp))/,
                '_600$1'
              )
            } else {
              v.imgUrl = v.imgOrgUrl
            }
          })
      }
      if (card.content && card.content.indexOf('<strong') > -1) {
        return
      }
      // value.content = value.content.replace(/[\r\n]/g, ''); // 换行符不做处理
      card.content =
        card.content &&
        card.content.replace(
          /#(.*?)#/g,
          '<strong class="short-topic">#$1# </strong>'
        )
    },
    // 修改动图
    changeImgGIF(url) {
      const isGif = url.indexOf('.gif') > -1
      if (isGif) {
        url = url.substring(0, url.indexOf('.gif')) + '.gif!nowater'
      }
      return url
    }
  }
}
</script>

<style lang="scss">
.c-feed-list {
  position: relative;
  max-width: 100%;
  .feed-content {
    .feed-tip {
      padding: 15px 0;
      margin: 0 14px;
      border-bottom: 1px solid #e5e5e5;
    }
    .img-wrap {
      position: relative;
      margin-bottom: 7px;
      display: inline-block;
      width: 100%;
      height: 196px;
      background-color: #f4f4f4;
      .img-wrap-img {
        display: inline-block;
        width: 100%;
        height: 196px;
        object-fit: cover;
      }
    }
    .con2-one {
      display: flex;
    }
    .left {
      position: relative;
      flex: 2;
      margin-right: 5px;
    }
    .right {
      position: relative;
      flex: 1;
      background-color: #f4f4f4;
      img {
        display: inline-block;
        width: 100%;
        height: 85px;
        object-fit: cover;
        vertical-align: top;
      }
    }
    .three-img-wrap {
      position: relative;
      margin-bottom: 7px;
      display: flex;
      .wrap {
        position: relative; // .flex-num(1);
        flex: 1; // height: 22.586vw;
        margin-right: 3.5px;
        background-color: #f4f4f4;
        img {
          display: inline-block;
          width: 100%;
          height: 85px;
          object-fit: cover;
        }
      }
      .wrap:last-of-type {
        margin-right: 0;
      }
      .img-num {
        right: 6px;
        bottom: 10px;
      }
    }
    .sub-subject {
      position: relative;
      font-size: 14px;
      color: #333;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    .short-topic {
      font-size: 14px;
      color: #2eaee9;
      line-height: 20px;
    }
    pre {
      font-size: 14px;
      word-wrap: break-word;
      white-space: pre-wrap;
      white-space: -moz-pre-wrap;
      white-space: -pre-wrap;
      white-space: -o-pre-wrap;
      word-wrap: break-word;
      overflow: hidden;
      text-align: left;
    }
    .time-num {
      position: absolute;
      right: 3px;
      bottom: 5px;
      padding: 1px 3px;
      border-radius: (20px 20px 20px 20px);
      background-color: rgba(0, 0, 0, 0.5);
      .num {
        font-size: 11px;
        color: #fff;
        line-height: 18px;
      }
    }
    .img-num {
      position: absolute;
      height: 20px;
      right: 3px;
      bottom: 5px;
      padding: 1px 3px;
      border-radius: (10px 10px 10px 10px);
      background-color: rgba(0, 0, 0, 0.5);
      .icon {
        display: inline-block;
        width: 18px;
        height: 18px;
        background: url('../../assets/<EMAIL>');
        background-position-x: center;
        background-position-y: center;
        background-size: contain;
        margin-right: 3px;
        vertical-align: top;
      }
      .num {
        font-size: 13px;
        color: #fff;
      }
    }
    .play {
      position: absolute;
      width: 30px;
      height: 30px;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      z-index: 100;
      background: url('../../assets/<EMAIL>');
      background-position-x: center;
      background-position-y: center;
      background-size: contain;
    }
  }

  .moment {
    .images-wrap {
      display: inline-block;
      width: 33%;
      img {
        display: inline-block;
        width: 100%;
        padding-right: 2px;
      }
    }
  }
}
</style>
