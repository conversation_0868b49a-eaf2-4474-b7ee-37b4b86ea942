<template>
  <div class="upload-excel">
    <el-upload
      :multiple="false"
      name="upfile"
      action
      :http-request="httpRequest"
      :show-file-list="false"
      accept=".xls, .xlsx"
    >
      <el-button type="primary" :loading="loading || state">{{
        buttonName
      }}</el-button>
    </el-upload>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import XLSX from 'xlsx-js-style'

export default {
  props: {
    buttonName: {
      type: String,
      default: '导入Excel'
    },
    loading: {
      type: Boolean,
      default: false
    },
    range: {
      // 从第几行读取
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      state: false
    }
  },
  methods: {
    httpRequest(option) {
      this.state = true
      try {
        //获取上传对象
        const fileReader = new FileReader()
        fileReader.onload = (event) => {
          const fileData = event.target.result
          //读取excel文件
          const workboot = XLSX.read(fileData, {
            type: 'binary'
          })
          let sheel0 = workboot.SheetNames[0]
          let blockArr = XLSX.utils.sheet_to_json(workboot.Sheets[sheel0], {
            range: this.range
          })
          $emit(this, 'success', blockArr, option.file)
          this.state = false
        }
        fileReader.readAsBinaryString(option.file)
      } catch (e) {
        console.log('e :>> ', e)
        this.$message.error('获取文件失败')
        this.state = false
      }
    }
  },
  emits: ['success']
}
</script>

<style lang="scss" scoped>
.upload-excel {
  display: inline-block;
}
</style>
