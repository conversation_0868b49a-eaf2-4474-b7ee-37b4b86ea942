<template>
  <div>
    <el-popover
      popper-class="brand-car"
      v-model:visible="visible"
      trigger="click"
      @show="show"
      placement="bottom-start"
      :width="brandId ? 352 : 177"
      :hide-after="0"
    >
      <div class="box">
        <div :class="{ 'box-left': true, 'box-border': brandId }">
          <div class="ml10">
            <el-input
              v-model="brandName"
              style="width: 150px"
              placeholder="请输入品牌"
            ></el-input>
          </div>
          <div class="scroll-box">
            <div
              v-show="!brandName && unlimited"
              @click="selectBrand()"
              :class="[
                'item-car pl10 el-select-dropdown__item',
                !brandId ? 'selected' : ''
              ]"
            >
              不限
            </div>
            <div
              @click="selectBrand(brand)"
              :class="[
                'item-car pl10 el-select-dropdown__item',
                brandId === brand.brandId ? 'selected' : ''
              ]"
              v-for="(brand, index) in realBrandList"
              :key="index"
            >
              {{ brand.brandName }}
            </div>
          </div>
        </div>
        <div v-if="brandId">
          <div class="ml10">
            <el-input
              v-model="carName"
              style="width: 150px"
              placeholder="请输入车型"
            ></el-input>
          </div>
          <div class="scroll-box" @scroll="scrollCarList">
            <div
              v-show="!carName && unlimited"
              @click="selectCar()"
              :class="[
                'item-car pl10 el-select-dropdown__item',
                !goodId ? 'selected' : ''
              ]"
            >
              不限
            </div>
            <div
              @click="selectCar(car)"
              :class="[
                'item-car pl10 el-select-dropdown__item',
                goodId === car.goodId ? 'selected' : ''
              ]"
              v-for="(car, index) in carList"
              :key="index"
            >
              {{ car.goodName }}
            </div>
            <div v-if="loading" class="loading-box">
              <el-icon class="is-loading">
                <IconLoading />
              </el-icon>
              <span>加载中...</span>
            </div>
          </div>
        </div>
      </div>
      <template v-slot:reference>
        <div>
          <slot :brandCarName="name"></slot>
        </div>
      </template>
    </el-popover>
  </div>
</template>

<script>
import { debounce } from 'lodash-es'
import { $emit } from '../../utils/gogocodeTransfer'
import {
  searchBrandList // 车型属性列表
} from '@/api/brand'
import {
  searchCarList // 车型属性列表
} from '@/api/garage'
import { Loading as IconLoading } from '@element-plus/icons-vue'
export default {
  components: {
    IconLoading
  },
  props: {
    unlimited: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      visible: false,
      brandName: '',
      brandId: '',
      goodId: '',
      brandList: [],
      carList: [],
      carName: '',
      checkBrandName: '',
      checkCarName: '',
      loading: false,
      LoadOver: false,
      page: 1,
      title: ''
    }
  },
  computed: {
    realBrandList() {
      if (!this.brandName) return this.brandList
      return this.brandList.filter(
        (item) =>
          item.brandName.toUpperCase().includes(this.brandName.toUpperCase()) ||
          item.brandName.toLowerCase().includes(this.brandName.toLowerCase())
      )
    },
    // realCarList() {
    //   if (!this.carName) return this.carList
    //   return this.carList.filter(
    //     (item) =>
    //       item.goodName.toUpperCase().includes(this.carName.toUpperCase()) ||
    //       item.goodName.toLowerCase().includes(this.carName.toLowerCase())
    //   )
    // },
    name() {
      const text = this.unlimited ? '不限' : ''
      if (!this.checkBrandName && !this.checkCarName) return text || this.title
      if (this.checkBrandName && !this.checkCarName) {
        return text ? `${this.checkBrandName} - ${text}` : this.checkBrandName
      }
      return `${this.checkBrandName} - ${this.checkCarName}`
    }
  },
  watch: {
    carName() {
      this.clearData()
      if (this.fn) {
        this.fn()
        return
      }
      this.fn = debounce(this.getCarList, 1000)
      this.fn()
    }
  },
  mounted() {
    this.getBrandList()
    // this.getCarList()
  },
  methods: {
    init(brandId, goodsId, carName) {
      this.reset()
      this.brandId = brandId || ''
      this.goodId = goodsId || ''
      this.title = carName || ''
      this.getCarList()
    },
    reset() {
      this.brandName = ''
      this.brandId = ''
      this.goodId = ''
      this.carList = []
      this.carName = ''
      this.checkBrandName = ''
      this.checkCarName = ''
      this.loading = false
      this.LoadOver = false
      this.page = 1
      this.title = ''
    },
    show() {
      this.brandName = ''
      this.carName = ''
    },
    clearData() {
      this.carList = []
      this.LoadOver = false
      this.page = 1
    },
    scrollCarList(e) {
      const scrollNum = e.target.scrollHeight - e.target.clientHeight
      const scrollTop = e.target.scrollTop
      if (scrollNum - scrollTop <= 3 && !this.loading && !this.LoadOver) {
        this.getCarList()
      }
    },
    async getCarList() {
      this.loading = true
      const res = await searchCarList({
        name: this.carName,
        brand: this.checkBrandName,
        brandId: this.brandId,
        page: this.page,
        limit: 20,
        isOnStatus: 1
      })
      if (res.data.code === 0) {
        const list = res.data.data.list || []
        if (list.length < 20) {
          this.LoadOver = true
        } else {
          this.page++
        }
        this.carList = [...this.carList, ...list]
      }
      this.loading = false
    },
    async getBrandList() {
      const res = await searchBrandList({
        brandName: this.brandName,
        isShow: 1
      })
      if (res.data.code === 0) {
        this.brandList = res.data.data.list
      }
    },
    selectBrand(brand) {
      if (!brand) {
        this.checkBrandName = ''
        this.checkCarName = ''
        this.brandId = ''
        this.goodId = ''
        $emit(this, 'select', {
          goodId: '',
          brandId: ''
        })
        this.visible = false
        return
      }
      this.checkBrandName = brand.brandName
      this.checkCarName = ''
      this.brandId = brand.brandId
      this.goodId = ''
      $emit(this, 'select', {
        goodId: '',
        brandId: brand.brandId
      })
      this.clearData()
      this.getCarList()
    },
    selectCar(car) {
      if (!car) {
        this.checkCarName = ''
        this.goodId = ''
        $emit(this, 'select', {
          goodId: '',
          brandId: this.brandId
        })
        this.visible = false
        return
      }
      this.checkCarName = car.goodName
      this.goodId = car.goodId
      $emit(this, 'select', {
        goodId: car.goodId,
        brandId: this.brandId
      })
      this.visible = false
    }
  },
  emits: ['select']
}
</script>

<style lang="scss">
.brand-car {
  .box {
    display: flex;
    .scroll-box {
      width: 170px;
      height: 200px;
      overflow-y: auto;
      margin-top: 10px;
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #e0e0e0;
        border-radius: 6px;
      }
      .loading-box {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
      }
    }
    .box-left {
      margin-left: -10px;
      padding-right: 3px;
    }
    .box-border {
      border-right: 1px solid gainsboro;
    }
    .item-car {
      overflow: hidden;
      cursor: pointer;
      text-overflow: ellipsis;
      overflow: hidden;
      &.active {
        background: #7f7f7f;
        color: white;
      }
    }
  }
}
</style>
