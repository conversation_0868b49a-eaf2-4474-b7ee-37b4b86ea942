<template>
  <div class="border-content">
    <el-button type="primary" @click="openDialog">+ 添加文章</el-button>
    <div class="table-content">
      <el-row class="item header">
        <el-col :span="2">序号</el-col>
        <el-col :span="3">内容ID</el-col>
        <el-col :span="12">标题内容</el-col>
        <el-col :span="4"> 文章状态 </el-col>
        <el-col :span="3">操作</el-col>
      </el-row>
      <draggable
        v-if="articleList.length > 0"
        v-model="articleList"
        class="item-container"
        itemKey="id"
      >
        <template #item="{ element, index }">
          <el-row class="item">
            <el-col :span="2"> {{ index + 1 }} </el-col>
            <el-col :span="3"> {{ element.id }} </el-col>
            <el-col :span="12">
              <CFeedList :card="element" />
            </el-col>
            <el-col :span="4"> {{ options[element.status] }} </el-col>
            <el-col :span="3">
              <el-button
                type="primary"
                link
                size="small"
                @click.stop="deleteItem(element, index)"
                >删除</el-button
              >
              <el-button
                type="primary"
                link
                size="small"
                @click.stop="setTop(element, index)"
                >{{ element.topStatus ? '取消置顶' : '置顶' }}</el-button
              >
            </el-col>
          </el-row>
        </template>
      </draggable>
    </div>
  </div>
  <el-dialog title="添加文章" v-model="dialogVisible" width="40%">
    <el-form :model="form" inline>
      <el-form-item label="文章/视频ID">
        <el-input v-model="form.id" placeholder="请输入ID"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchArticle">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="currentList"
      style="width: 100%; height: 500px"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="id" label="内容ID" align="center" width="120" />
      <el-table-column prop="title" label="标题内容" align="center">
        <template #default="scope">
          <CFeedList :card="scope.row" />
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        label="文章状态"
        width="120"
        align="center"
      >
        <template #default="scope">
          {{ options[scope.row.status] }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-size="limit"
      :current-page="page"
      :total="total"
      align="center"
      background
      layout="total, prev, pager, next, jumper"
      class="el-pagination-center"
      @current-change="handleCurrentChange"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="save">保 存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import draggable from 'vuedraggable'
import { ElMessageBox, ElMessage } from 'element-plus'
import { searchArticleList } from '@/api/articleModule'
import CFeedList from '@/components/CFeedList/index.vue'
const options = {
  2: '审核中',
  1: '审核通过',
  3: '审核不通过',
  '-1': '个人删除',
  0: '永久删除',
  4: '仅自己可见',
  9: '编辑审核中'
}
const dialogVisible = ref(false)
const emits = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  uid: {
    type: String,
    default: ''
  }
})

const selectArticle = ref([])
const currentList = ref([])
const page = ref(1)
const limit = ref(10)
const total = ref(0)

const articleList = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emits('update:modelValue', value)
  }
})
const form = ref({
  id: ''
})

const openDialog = () => {
  dialogVisible.value = true
  searchArticle()
}
const closeDialog = () => {
  dialogVisible.value = false
  selectArticle.value = []
  currentList.value = []
  page.value = 1
  total.value = 0
}
const save = () => {
  const ids = selectArticle.value.map((item) => item.id)
  if (
    ids.length === 1 &&
    articleList.value.some((item) => item.id === ids[0])
  ) {
    console.log(selectArticle.value)
    ElMessage.error(`文章${ids[0]}已存在`)
    return
  }

  const repeatIds = []
  let lastTopIndex = 0
  for (let i = 0; i < articleList.value.length; i++) {
    const item = articleList.value[i]
    if (ids.includes(item.id)) {
      repeatIds.push(item.id)
    }
    if (item.topStatus) {
      lastTopIndex = i + 1
    }
  }

  if (repeatIds.length > 0) {
    ElMessage.warning(
      `文章${repeatIds.join(',')}已存在，如需覆盖请删除后重新添加`
    )
  }

  selectArticle.value = selectArticle.value.filter(
    (item) => !repeatIds.includes(item.id)
  )
  articleList.value.splice(lastTopIndex, 0, ...selectArticle.value)
  closeDialog()

  // ids.forEach((id) => {
  //   searchArticleList({ id: id })
  //     .then((res) => {
  //       if (articleList.value.some((item) => item.essayId === id)) return
  //       if (res.data.code === 0) {
  //         const data = res.data.data.listData || []
  //         articleList.value.push({
  //           ...data[0]
  //         })
  //         form.value.id = ''
  //       } else {
  //         ElMessage.error('操作失败')
  //       }
  //       closeDialog()
  //     })
  //     .catch(() => {})
  // })
}

const handleSelectionChange = (val) => {
  selectArticle.value = val
}

const handleCurrentChange = (val) => {
  page.value = val
  searchArticle()
}

const searchArticle = () => {
  searchArticleList({
    autherid: props.uid,
    id: form.value.id,
    limit: limit.value,
    page: page.value
  })
    .then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data.listData || []
        currentList.value = data
        total.value = res.data.data.total
      } else {
        ElMessage.error('操作失败')
      }
    })
    .catch(() => {})
}

const deleteItem = (item, index) => {
  ElMessageBox.confirm('确认是否删除该文章?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage({
      type: 'success',
      message: '删除成功!'
    })
    articleList.value.splice(index, 1)
  })
}

const setTop = (item, index) => {
  ElMessageBox.confirm(
    `${item.topStatus == 0 ? '取消' : ''}置顶后保存才能生效，确认是否${
      item.topStatus == 0 ? '取消' : ''
    }置顶该文章?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    articleList.value.splice(index, 1)
    articleList.value.unshift({
      ...item,
      topStatus: item.topStatus == 1 ? 0 : 1
    })
  })
}
</script>

<style lang="scss" scoped>
.border-content {
  border: 1px solid #ccc;
  padding: 10px;
  margin: 10px;
  width: 100%;
}
.dialog-footer {
  text-align: center;
}

.table-content {
  margin: 20px 0;
  text-align: center;
}
.item-container {
  text-align: center;
}
.noData {
  text-align: center;
  line-height: 50px;
}
.item {
  color: #909399;
  font-size: 14px;
  text-align: center;
  line-height: 40px;
  border: 1px solid #ebeef5;

  border-width: 0 0px 1px 1px;
  .el-col {
    word-break: break-all;
    border-right: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px;
  }

  &.header {
    border-width: 1px 0 1px 1px;
  }
  &.active {
    background: #dcdcdc;
  }
}
</style>
