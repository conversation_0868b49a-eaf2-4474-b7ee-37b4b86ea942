<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="可供复制的文章"
      center
      append-to-body
    >
      <p class="copy-tip">
        已选中其中文章，复制即可粘贴到其他地址,如出现未选中情况，需要手动复制
      </p>
      <div contenteditable="true" class="choose-div" v-html="content"></div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ChooseIframeContent',
  components: {},
  data() {
    return {
      dialogVisible: false,
      content: '',
    }
  },
  methods: {
    init(mes) {
      if (!mes.length) {
        return this.$message.error('还没有数据')
      }
      let html = ''
      mes.map((_) => {
        let content = ''
        content =
          _.link && _.img
            ? `${content}<video src="${_.link}" controls="controls"></video><p>${_.content}</p>`
            : content
        content =
          !_.link && _.img
            ? `${content}<img src="${_.img}"><p>${_.content}</p>`
            : content
        content =
          _.link && !_.img
            ? `${content}<a href="${_.link}">${_.content}</a>`
            : content
        content =
          _.type === '4' && _.content
            ? `${content}<h2>${_.content}</h2>`
            : content
        content =
          _.type === '1' && _.content
            ? `${content}<p>${_.content}</p>`
            : content
        html = `${html}${content}`
      })
      this.content = html
      this.dialogVisible = true
      setTimeout(() => {
        const dom = document.querySelector('.choose-div')
        const selection = window.getSelection()
        const range = document.createRange()
        range.selectNodeContents(dom)
        selection.removeAllRanges()
        selection.addRange(range)
      }, 150)
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
    },
  },
}
</script>

<style lang="scss">
.choose-div {
  width: 400px;
  height: 600px;
  border: 1px solid #eee;
  border-radius: 10px;
  padding: 5px;
  margin: 0 auto;
  display: block;
  overflow-y: scroll;
  img {
    display: inline-block;
    width: 100%;
  }
  video {
    display: inline-block;
    width: 100%;
  }
}
</style>
