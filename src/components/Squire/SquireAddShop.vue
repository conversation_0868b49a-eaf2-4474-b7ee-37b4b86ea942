<template>
  <div class="choose-shop-div">
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="选择商品"
      center
      append-to-body
    >
      <p class="copy-tip">选中的商品：{{ showData.goodsName }}</p>
      <el-form-item label="筛选商品类别">
        <el-select
          v-model="type"
          filterable
          class="search-user-content"
          @change="
            () => {
              goodsName = ''
              getList()
            }
          "
        >
          <el-option
            v-for="(item, index) in typeList"
            :key="index"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="商品ID">
        <el-input
          v-model="id"
          placeholder="请输入商品ID"
          clearable
          style="width: 200px"
        />&ensp;<el-button
          @click="
            () => {
              goodsName = ''
              getList({ id: id })
            }
          "
          >查询</el-button
        >
      </el-form-item>
      <el-form-item label="选择商品">
        <el-select
          ref="selectdGoodName"
          v-model="goodsName"
          :remote-method="getShopList"
          :loading="loading"
          popper-class="select-good-list"
          placeholder="请输入商品名称"
          filterable
          remote
          clearable
          @change="addLabel"
        >
          <el-option
            v-for="item in shopList"
            :key="item.labelId"
            :value="item.goodsName"
          >
            <img
              :src="item.firstImg"
              alt
              style="
                display: inline-block;
                width: 50px;
                height: 50px;
                margin-right: 10px;
              "
            />
            {{ item.goodsName }}</el-option
          >
        </el-select>
      </el-form-item>
      <div class="fooder center">
        <el-button type="success" @click="confirm(true)">确认</el-button>
        <el-button type="danger" @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $emit } from '../../utils/gogocodeTransfer'
import { GetListGoods, getJdSpuidList, getSelfGoodsList } from '@/api/shop'
export default {
  name: 'SquireAddShop',
  components: {},
  data() {
    return {
      dialogVisible: false,
      loading: false,
      typeList: ['淘宝商品', '京东商品', '自营商品'],
      shopList: [],
      showData: {},
      id: '',
      goodsName: '',
      content: '',
      type: '淘宝商品'
    }
  },
  methods: {
    init() {
      this.dialogVisible = true
      this.id = ''
      this.goodsName = ''
      this.type = '淘宝商品'
      this.showData = {}
      this.getList()
    },
    getShopList(query) {
      this.$tools.debounce(() => this.getList({}, query), 300)()
    },
    getList(num = {}, query = '') {
      const me = this
      const postData = {
        ...num, // 商城商品或京东商品
        goodsName: num && num.id ? '' : query || me.goodsName,
        limit: 100, // 数量
        page: 1,
        goodsStatus: me.type === '自营商品' ? 1 : 0
      }
      const querList = {
        淘宝商品: GetListGoods,
        京东商品: getJdSpuidList,
        自营商品: getSelfGoodsList
      }
      const query2 = querList[me.type]
      query2(postData)
        .then((response) => {
          me.shopList = []
          if (response.data.code === 0) {
            me.shopList = response.data.data.listData
            me.showData = me.shopList.length === 1 ? me.shopList[0] : {}
          }
        })
        .finally((_) => {
          this.loading = false
        })
    },
    addLabel(val) {
      if (!val) return
      this.showData = this.shopList.find((_) => {
        return _.goodsName === val
      })
      this.id = ''
    },
    confirm() {
      if (!this.showData.id) return
      $emit(this, 'addShop', { ...this.showData, labelName: this.type })
      this.handleClose()
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
    }
  },
  emits: ['addShop']
}
</script>

<style lang="scss">
.select-good-list {
  li {
    height: 55px;
    line-height: 25px;
    display: flex;
    white-space: inherit;
  }
}
</style>
