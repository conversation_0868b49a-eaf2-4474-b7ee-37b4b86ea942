<template>
  <div class="choose-shop-div">
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="插入文章"
      center
      width="700px"
    >
      <el-form-item label="文章标题筛选" style="margin-bottom: 10px">
        <el-input
          v-model="detailName"
          placeholder="请输入文章标题"
          clearable
          style="width: 200px"
        />&ensp;<el-button @click="getList()">查询</el-button>
      </el-form-item>
      <el-table
        ref="articleList"
        :data="articleList"
        border
        style="height: 60vh; width: 100%"
        max-height="60vh"
        highlight-current-row
        @current-change="handleCurrentChange"
      >
        <el-table-column label="列表标题/内容" prop="type" align="center">
          <template v-slot="scope">
            <c-feedList :card="scope.row" />
          </template>
        </el-table-column>
      </el-table>
      <div class="fooder center">
        <el-button type="success" @click="confirm(true)">确认</el-button>
        <el-button type="danger" @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { searchArticleList } from '@/api/articleModule'
import { getEssayDetail } from '@/api/article'
import CFeedList from '@/components/CFeedList/index.vue'
export default {
  name: 'SquireAddDetail',
  components: {
    CFeedList,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      articleList: [],
      selectedList: [],
      detailName: '',
      content: '',
    }
  },
  methods: {
    init() {
      this.dialogVisible = true
      this.detailName = ''
      this.selectedList = []
      this.articleList = []
      this.showData = {}
    },
    getList() {
      const me = this
      if (!me.detailName) {
        return me.$message.error('请输入文章标题')
      }
      const postData = {
        limit: 100, // 数量
        page: 1,
        status: 1,
        title: me.detailName,
      }
      searchArticleList(postData)
        .then((response) => {
          const res = response.data.data.listData
          me.articleList = res
        })
        .finally((_) => {
          this.loading = false
        })
    },
    handleCurrentChange(val) {
      this.selectedList = val && val.id ? [val] : []
    },
    confirm() {
      const me = this
      if (me.selectedList.length !== 1)
        return me.$message.error('请选择一条数据')
      const data = me.selectedList[0]
      const article = {
        img:
          data.mediaInfo && data.mediaInfo.length ? data.mediaInfo[0].img : '',
        title: data.title || '',
        author: data.author,
        id: data.id,
        viewNum: data.viewNum || 0,
        replyCnt: data.replyCnt || 0,
      }
      $emit(me, 'addDetail', article)
      me.handleClose()
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
    },
  },
  emits: ['addDetail'],
}
</script>

<style lang="scss">
.select-good-list {
  li {
    height: 55px;
    line-height: 25px;
    display: flex;
    white-space: inherit;
  }
}
</style>
