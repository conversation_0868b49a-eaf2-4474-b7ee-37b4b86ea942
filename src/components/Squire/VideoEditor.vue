<template>
  <div
    v-loading.fullscreen="loading"
    :element-loading-text="loadingText"
    class="video-editor"
  >
    <input
      v-if="!isAcitve"
      type="file"
      name="file"
      class="hide upload-video"
      @change="fileSelectedVideo"
    />
    <el-upload
      v-else
      :show-file-list="false"
      :http-request="httpRequest1"
      :on-success="updateFile"
      name="upfile"
      class="cover-uploader"
      action
    >
      <el-button type="primary" class="fl-left">选择视频</el-button>
    </el-upload>
    <el-button v-if="!isAcitve" class="add-video" round @click="chooseVideo"
      >添加或更改视频</el-button
    >
    <div v-if="pgcContent.link" class="video-box flex">
      <video
        :src="pgcContent.link"
        controls="controls"
        style="height: 400px; min-width: 450px"
        class="pgc-video"
      ></video>
      <div class="rate flex" style="align-items: flex-end; width: 40px">
        <el-dropdown>
          <el-tag class="el-dropdown-link">
            <span>{{ playbackRate + '倍' }}</span>
          </el-tag>
          <template v-slot:dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="changeRate(2)">2倍</el-dropdown-item>
              <el-dropdown-item @click="changeRate(1.5)"
                >1.5倍</el-dropdown-item
              >
              <el-dropdown-item @click="changeRate(1)">1倍</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <slot v-if="slots.content" name="content"></slot>
    <div v-if="reviewInfo" class="additional">
      <h3 class="mb8">
        {{
          reviewInfo.afterDays === 0 ? `1天内` : `${reviewInfo.afterDays}天后`
        }}追评：
      </h3>
      <div class="content">
        <div class="content-item">{{ reviewInfo.content }}</div>
        <div
          class="image-item"
          v-for="(item, index) in reviewInfo.imageInfo"
          :key="index"
        >
          <img :src="item.imgUrl" alt="" />
        </div>
      </div>
    </div>
    <div class="video-cover-box">
      <h3>视频封面</h3>
      <el-upload
        :show-file-list="false"
        :http-request="httpRequest"
        :on-success="onSuccess"
        :on-error="onError"
        :disabled="disable"
        name="upfile"
        class="avatar-uploader"
        action
      >
        <img
          v-if="pgcContent.img"
          :src="pgcContent.img"
          alt
          style="height: 300px"
        />
        <el-icon v-else style="font-size: 65px; color: #606266"
          ><IconPicture
        /></el-icon>
      </el-upload>
    </div>
  </div>
</template>

<script>
import { Picture as IconPicture } from '@element-plus/icons-vue'
import { $emit } from '../../utils/gogocodeTransfer'
import { mapGetters } from 'vuex'
import { createUploader } from '@/api/video'
export default {
  components: {
    IconPicture
  },
  props: {
    fromSource: {
      // 视频转码来源
      type: String,
      default: ''
    },
    disable: {
      type: Boolean,
      default: false
    },
    isAcitve: {
      type: Boolean,
      default: false
    } // 是否是活动
  },
  data() {
    return {
      loading: false,
      loadingText: '',
      id: '',
      img: '',
      link: '',
      vodName: '',
      duration: '', // 播放时长
      vodSize: '', // 视频大小
      vodType: '', // 图片尺寸
      pgcContent: {},
      playbackRate: 1, // 视频播放速率 1 1.5 2
      ruleForm: {},
      reviewInfo: null
    }
  },
  computed: {
    ...mapGetters(['uid']),
    slots() {
      return this.$slots
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    init(data = {}) {
      if (!data) return
      const me = this
      me.pgcContent = JSON.parse(JSON.stringify(data))
      this.reviewInfo = null
      if (data.carScoreFollowVO && data.carScoreFollowVO.createTime) {
        this.reviewInfo = data.carScoreFollowVO
      }
    },
    async httpRequest(option = {}) {
      if (this.disable) return
      this.loading = true
      option.imageType = 'forum'
      return this.$oss.ossUploadImage(option)
    },
    onSuccess(res) {
      const me = this
      if (!res) return
      me.loading = false
      if (res.name) {
        me.pgcContent.img = res.imgOrgUrl // imgOrgUrl
      } else {
        return me.$notify.error({
          title: '上传错误'
        })
      }
      $emit(me, 'updatePGCContent', me.pgcContent)
    },
    onError(res) {
      console.log(res)
      this.loading = false
      this.$message.error(res)
    },
    // js 把base64转file文件
    dataURLtoFile(dataurl, filename) {
      var arr = dataurl.split(',')
      var mime = arr[0].match(/:(.*?);/)[1]
      var bstr = atob(arr[1])
      var n = bstr.length
      var u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], filename, { type: mime })
    },
    changeRate(rate = 1) {
      const video = document.querySelector('.pgc-video')
      video && (video.playbackRate = rate)
      this.playbackRate = rate
    },
    // 上传视频 触发
    chooseVideo() {
      if (this.disable) return
      const chooseVideo = document.querySelector('.upload-video')
      if (chooseVideo) {
        // 解决文件不能重复上传问题
        chooseVideo.setAttribute('type', 'text')
        chooseVideo.setAttribute('type', 'file')
        chooseVideo.click()
      }
    },
    // 上传视频 前校验
    fileSelectedVideo(e) {
      const me = this
      const file = e.target.files[0]
      const fileName = ((file && file.name) || '').toLowerCase()
      if (!file) return
      if (file.size > 2 * 1024 * 1024 * 1024) {
        return me.setToast('视频大小不能超过2GB')
      }
      if (
        fileName.indexOf('.mp4') === -1 &&
        fileName.indexOf('.mov') === -1 &&
        fileName.indexOf('.ts') === -1
      ) {
        return me.setToast('视频格式不是mp4、mov、ts')
      }

      me.uploadVideo(file)
      me.cuteVideo(file)
    },
    // 剪切视频封面图
    cuteVideo(file) {
      const me = this

      createVideo()
      // 创建视频
      function createVideo() {
        const videoUrl = window.URL.createObjectURL(file)
        const video = document.createElement('video') // 创建video
        video.controls = false
        video.useCORS = true // 解决跨域
        video.crossOrigin = 'Anonymous' // 这两个参数必填 解决跨域
        video.autoplay = false
        video.setAttribute('src', videoUrl)
        setMedia({ video, videoUrl })
      }
      // 截图、上传
      function setMedia({ video, videoUrl, scale = 1 }) {
        // 非本地视频资源会有跨域截图问题 loadedmetadata
        video.addEventListener('loadedmetadata', function () {
          console.log('loadedmetadata')
          this.currentTime = 0
        })
        video.addEventListener('timeupdate', function () {
          console.log('timeupdate')
          // 拿到图片
          const canvas = document.createElement('canvas')
          canvas.width = video.videoWidth * scale
          canvas.height = video.videoHeight * scale
          const ctx = canvas.getContext('2d')
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
          const src = canvas.toDataURL('image/jpeg', 0.7) || ''
          var imageData = ctx.getImageData(
            canvas.width / 2,
            canvas.height / 2,
            1,
            1
          )
          var data = imageData.data
          // 判断中间像素的颜色是否接近黑色（R, G, B都接近0，A为255代表不透明）
          var isBlack =
            data[0] < 30 && data[1] < 30 && data[2] < 30 && data[3] === 255
          if (isBlack && video.currentTime < 5) {
            video.currentTime++
            return
          }
          const name = 'setMedia_' + Date.now() + '.jpeg'
          const image = me.dataURLtoFile(src || '', name)

          if (image) {
            me.httpRequest({
              file: image
            })
              .then(
                (_) => {
                  if (_.imgOrgUrl) {
                    me.pgcContent.img = _.imgOrgUrl
                  } else {
                    me.pgcContent.img = ''
                    me.setToast('封面图上传失败，请手动上传')
                  }
                  $emit(me, 'updatePGCContent', me.pgcContent)
                },
                (_) => {
                  console.log(_)
                  me.setToast('封面图上传失败，请手动上传')
                }
              )
              .finally(() => {
                video = null
                URL.revokeObjectURL(videoUrl)
              })
          } else {
            me.setToast('封面图获取失败，请手动上传')
            console.log(image)
          }
        })
      }
    },
    // 上传视频
    uploadVideo(file) {
      const me = this
      // const fileName = file.name
      me.loading = true
      return createUploader
        .call(me, file)
        .then((result = {}) => {
          me.loading = false
          me.writeMark = true
          me.loadingText = ''
          me.pgcContent.id = result.videoId
          me.pgcContent.img = result.coverUrl || me.pgcContent.img || ''
          me.pgcContent.link = result.videoUrl || ''
          me.pgcContent.vodName = result.file.name.split('.')[0]
          me.pgcContent.duration = result.duration || '' // 播放时长
          me.pgcContent.vodSize = result.vodSize || '' // 视频大小
          me.pgcContent.vodType = result.vodType || '' // 图片尺寸
          $emit(me, 'updatePGCContent', me.pgcContent, me.pgcContent.id)
        })
        .catch((_) => {
          me.loading = false
          me.setMessageBoxNoCancel(_)
        })
    },
    retsetVideo() {
      const me = this
      me.pgcContent.id = '' // 视频任务id
      me.pgcContent.img = '' // 视频封面图
      me.pgcContent.link = '' // 视频地址
      me.pgcContent.vodName = '' // 视频名称
      me.pgcContent.duration = '' // 播放时长
      me.pgcContent.vodSize = '' // 视频大小
      me.pgcContent.vodType = '' // 图片尺寸
      $emit(me, 'updatePGCContent', me.pgcContent)
    },
    // 上传视频
    async httpRequest1(option) {
      const file = option.file
      const fileName = ((file && file.name) || '').toLowerCase()
      if (!file) return
      if (file.size > 2 * 1024 * 1024 * 1024) {
        return this.setToast('视频大小不能超过2GB')
      }
      if (
        fileName.indexOf('.mp4') === -1 &&
        fileName.indexOf('.mov') === -1 &&
        fileName.indexOf('.ts') === -1
      ) {
        return this.setToast('视频格式不是mp4、mov、ts')
      }
      option.isVideo = true // 是视频
      this.$oss.ossUploadFile(option)
    },
    updateFile(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        this.pgcContent['link'] = res.url
        $emit(this, 'updatePGCContent', this.pgcContent)
        return
      } else {
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    // 弹框提示
    setToast(content) {
      this.$notify.error(content)
    },
    setMessageBoxNoCancel(content) {
      this.$confirm(content, '提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'warning',
        center: true
      })
    }
  },
  emits: ['updatePGCContent']
}
</script>

<style lang="scss">
@import '../../styles/util.scss';
.video-editor {
  min-width: 500px;
  min-height: 800px;
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e7e7e7;
  overflow: hidden;
  .add-video {
    margin: 0px auto 20px;
    display: block;
  }

  .video-box {
    max-width: 500px;
  }

  .additional {
    .content {
      background-color: #f2f2f2;
      border-radius: 2px;
      padding: 5px;
      font-size: 0;
      .content-item {
        white-space: pre-wrap;
        font-size: 16px;
        margin: 10px 5px;
        font-weight: bold;
      }
      .image-item {
        width: 200px;
        height: 170px;
        display: inline-block;
        margin: 5px;
        > img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}
</style>
