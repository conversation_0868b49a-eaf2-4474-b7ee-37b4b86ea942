<template>
  <div class="squire">
    <ToolBar :isOss="true" :editor="editor" :hiddenTools="hiddenTools" />
    <Editor
      :importEssay="importEssay"
      @insertImgs="insertImgs"
      @insertVideo="insertVideo"
      @updateAddVideo="updateAddVideo"
      :uploadImageByOther="uploadImageByOther"
      :getEassyDetail="getEassyDetail"
      :request="{}"
      :isOss="true"
      :disabled="disable"
      ref="editor"
      :getArticleList="getArticleList"
    />
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { Editor, ToolBar } from '@haluo/biz'
import { getArticleList, importEssay, uploadImageByOther } from '@/api/article'
import { getQueryEassyDetail } from '@/api/articleModule'
import { createUploader } from '@/api/video'

export default {
  components: {
    Editor,
    ToolBar
  },
  props: ['disable', 'hiddenTools'],
  data() {
    return {
      getArticleList,
      getQueryEassyDetail,
      importEssay,
      uploadImageByOther,
      editor: {}
    }
  },
  mounted() {
    this.editor = this.$refs['editor']
  },
  methods: {
    updateAddVideo(add) {
      $emit(this, 'updateAddVideo', add)
    },
    async insertImgs(files, type, done) {
      for (let file of files) {
        const option = {
          imageType: 'forum',
          filename: 'upfile',
          file
        }
        const res = await this.$oss.ossUploadImage(option)
        if (res.name) {
          done(type, res.imgOrgUrl)
        } else {
          this.$notify.error({
            title: '上传错误'
          })
        }
      }
    },
    getVideoFirstFrameAndUpload(videoSrc, name) {
      const me = this
      return new Promise((resolve, reject) => {
        const video = document.createElement('video')
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        video.setAttribute('crossOrigin', 'anonymous') //处理跨域
        video.setAttribute('src', videoSrc)
        video.setAttribute('autoplay', 'autoplay')
        video.addEventListener('canplaythrough', () => {
          canvas.width = video.videoWidth
          canvas.height = video.videoHeight
          context.drawImage(video, 0, 0, canvas.width, canvas.height)
          let dataurl = canvas.toDataURL('image/png')
          let arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n)
          while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
          }
          let file = new File([u8arr], name, { type: mime })
          const option = {
            imageType: 'nowater',
            quality: 1,
            file
          }
          me.$oss
            .ossUploadImage(option)
            .then((res) => {
              const imgUrl = res.imgOrgUrl
              resolve(imgUrl)
            })
            .catch((err) => {
              console.log(err)
              reject(err)
            })
        })
      })
    },
    postVideo(editor, data, suc, err, final) {
      return createUploader
        .call(editor, data[0])
        .then(async (result = {}) => {
          const res = await this.getVideoFirstFrameAndUpload(
            result.videoUrl,
            'cover'
          )
          result.customCover = res
          suc(result)
        })
        .catch((_) => {
          console.log('error')
          err(_)
        })
        .finally(() => {
          console.log('final')
          final()
        })
    },
    insertVideo(editor, files, suc, err, final) {
      this.postVideo(editor, files, suc, err, final)
    },
    getEassyDetail(essayId, done) {
      const me = this
      me.loading = true
      return getQueryEassyDetail({
        id: essayId,
        action: 22004
      })
        .then((response) => {
          if (response.data.code === 0) {
            const result = response.data.data
            done({
              title: result.title,
              author: result.auther,
              id: essayId,
              viewNum: result.viewcnt,
              replycnt: result.replycnt
            })
          } else {
            me.$message.error(response.data.msg || '文章详情获取失败')
          }
        })
        .catch((err) => {
          console.log(err)
          me.$message.error(
            (err && err.data && err.data.msg) || '文章详情获取失败'
          )
        })
        .finally((_) => {
          me.loading = false
        })
    },
    initData(data, arr) {
      this.$refs['editor'].initData(data, arr)
    },
    getData() {
      return this.$refs['editor'].getEditorData()
    },
    saveData() {
      const data = this.$refs['editor'].getHtml()
      $emit(this, 'squire', data)
    }
  },
  emits: ['squire', 'updateAddVideo']
}
</script>

<style lang="scss" scoped>
.squire:deep(#editor-content) {
  resize: vertical;
  padding: 14px;
  outline: 0;
  height: 70vh;
  overflow: hidden;
  overflow-y: scroll;
  border: 1px solid #e5e5e5;
  p {
    margin: 0;
    padding: 0;
  }
}
.squire {
  :deep(.placeholder) {
    left: 14px;
  }
}
</style>
<style lang="scss">
.tools-font {
  padding: 0;
  margin: 0;
  list-style: none;
}
</style>
