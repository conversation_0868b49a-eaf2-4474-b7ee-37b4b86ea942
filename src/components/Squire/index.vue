<template>
  <div class="squire-editing">
    <div class="editing-function flex">
      <el-popover
        v-if="showUndo"
        placement="top-start"
        trigger="hover"
        content="撤回"
        class="icon-section"
      >
        <template v-slot:reference>
          <img
            id="undo"
            class="icon"
            src="../../assets/image/<EMAIL>"
            alt
            @click="setContent($event)"
          />
        </template>
      </el-popover>
      <el-popover
        v-if="showRedo"
        placement="top-start"
        trigger="hover"
        content="恢复"
        class="icon-section"
      >
        <template v-slot:reference>
          <img
            id="redo"
            class="icon"
            src="../../assets/image/<EMAIL>"
            alt
            @click="setContent($event)"
          />
        </template>
      </el-popover>
      <el-popover
        v-if="showHeader"
        placement="top-start"
        trigger="hover"
        content="段落标题"
        class="icon-section"
      >
        <template v-slot:reference>
          <img
            id="makeHeader"
            class="icon"
            src="../../assets/image/<EMAIL>"
            alt
            @click="setContent($event)"
          />
        </template>
      </el-popover>
      <el-popover
        v-if="showTextAlign"
        trigger="hover"
        content="对齐方式"
        :disabled="textAlignDisable"
      >
        <div
          id="setTextAlignment"
          v-for="(text, index) in textAlignList"
          :key="index"
          style="margin-bottom: 5px; line-height: 25px"
          @click="setContent($event, text.value)"
        >
          <img
            style="
              display: inline-block;
              width: 25px;
              height: 25px;
              position: absolute;
            "
            :src="text.img"
            alt
          />
          &ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;{{ text.name }}
        </div>
        <template v-slot:reference>
          <img
            class="icon text-icon"
            src="../../assets/image/icon-text-default.png"
            alt
          />
        </template>
      </el-popover>
      <el-popover
        v-if="showImg"
        placement="top-start"
        trigger="hover"
        class="icon-section"
      >
        <div class="open-view-img-button">
          <div @click="openView('img')">上传图片</div>
          <div @click="openView('img', true)">上传无缝图片</div>
        </div>
        <template v-slot:reference>
          <img
            class="icon"
            src="../../assets/image/<EMAIL>"
            alt
          />
        </template>
      </el-popover>
      <el-popover
        v-if="showImportDetail"
        placement="top-start"
        trigger="hover"
        content="插入文章"
        class="icon-section"
      >
        <template v-slot:reference>
          <img
            class="icon import-icon"
            src="../../assets/image/icon-import.png"
            alt
            @click="addDetailDialog()"
          />
        </template>
      </el-popover>
      <el-popover
        v-if="showVideo"
        placement="top-start"
        trigger="hover"
        content="插入视频"
        class="icon-section"
      >
        <template v-slot:reference>
          <img
            class="icon"
            src="../../assets/image/<EMAIL>"
            alt
            @click="openView('video')"
          />
        </template>
      </el-popover>
      <el-popover
        v-if="showImport"
        placement="top-start"
        trigger="hover"
        content="一键导入"
        class="icon-section"
      >
        <template v-slot:reference>
          <img
            class="icon"
            src="../../assets/image/yijiandaoru_40.png"
            alt
            @click="addLink('import')"
          />
        </template>
      </el-popover>
      <!-- <el-popover v-if="showImport" placement="top-start" trigger="hover" content="复制文章" class="icon-section">
                      <img class="icon copy-icon" src="../../assets/image/copy.png" alt slot="reference" @click="saveData('copeContent')" />
                    </el-popover> -->
      <el-popover
        v-if="showAddShop"
        placement="top-start"
        trigger="hover"
        content="添加商品"
        class="icon-section"
      >
        <template v-slot:reference>
          <img
            class="icon copy-icon"
            src="../../assets/image/shop.png"
            alt
            @click="addShop('copeContent')"
          />
        </template>
      </el-popover>
      <el-popover
        v-if="showLink"
        placement="top-start"
        trigger="hover"
        content="插入连接"
        class="icon-section"
      >
        <template v-slot:reference>
          <img
            id="insertLink"
            class="icon"
            src="../../assets/image/<EMAIL>"
            alt
            @click="addLink('link')"
          />
        </template>
      </el-popover>
    </div>
    <div
      :id="id"
      class="squire-editor-content"
      style="pointer-events: auto"
      @keyup="setBack($event)"
      @createRange="setFouces($event)"
      @getCursorPosition="setBlur($event)"
      @mouseup="getLastChangeDom()"
      :contenteditable="!disable"
    ></div>
    <el-dialog
      :title="dialogTitle"
      v-model="viewStatus"
      :before-close="handleClose"
      class="squire-dialog-content"
      append-to-body
    >
      <div v-if="addImgStatus" class="add-img">
        <el-col v-if="showLink">
          <el-form-item label="链接地址">
            <el-input
              v-model="linkAddress"
              :disabled="disabledLink"
              placeholder="请输入链接地址"
            />
          </el-form-item>
        </el-col>
        <el-col>
          <div class="imgs-container">
            <span
              v-for="(img, index) in imgList"
              :key="index"
              class="image-content"
            >
              <img :src="img.url" alt class="imgs" />
              <img
                src="/static/img/<EMAIL>"
                alt
                class="imgs-del"
                @click="delImg(index)"
              />
            </span>
          </div>
          <el-upload
            :show-file-list="false"
            :http-request="httpRequest"
            :on-success="onSuccess"
            :on-error="onError"
            name="upfile"
            style="display: inline-block"
            class="avatar-uploader"
            multiple
            action
          >
            <el-button class="button" type="primary" link>选择图片</el-button>
          </el-upload>
        </el-col>
      </div>
      <div v-else-if="addVideoStatus">
        <input
          type="file"
          name="file"
          class="imageFile"
          accept="video/*"
          @change="postVideo($event)"
        />
      </div>
      <div v-else class="add-link">
        <el-row :gutter="20">
          <el-col>
            <el-form-item :label="linkTitleName" class="link-title">
              <el-input
                v-model="linkAddress"
                :disabled="disabledLink"
                :placeholder="linkPlaceholder"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="addLinkStatus">
            <el-form-item label="链接文案">
              <el-input
                v-model="linkWriting"
                type="textarea"
                autosize
                placeholder="请输入链接文案"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="viewStatus = false">取 消</el-button>
          <el-button type="primary" @click="setShowDisplay()">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <choose-iframe ref="ChooseIframe" />
    <squire-add-shop ref="SquireAddShop" @addShop="addShopDetail" />
    <squire-add-detail ref="SquireAddDetail" @addDetail="addDetail" />
    <div v-if="videoTips && videoTips.length" class="video-tips">
      <p v-for="tips in videoTips" :key="tips.time" class="video-tip">
        {{ tips.content }}
      </p>
    </div>
    <el-button
      v-clipboard:copy="copyTextareaValue"
      v-clipboard:success="clipboardSuccess"
      type="primary"
      link
      size="small"
      class="copy-data"
    ></el-button>
  </div>
</template>

<script>
import { $emit } from '../../utils/gogocodeTransfer'
const imgs = ['file', 'images', 'imgs', 'imgs2', 'imgs3', 'pkg']
const imgStr = new RegExp(
  imgs
    .map((_) => _ + '.jddmoto.com')
    .concat(imgs.map((_) => _ + '.58moto.com'))
    .join('|')
)
/**
 * 图片、链接、视频、导入、复制文章、自动保存，图文编辑、格式转存
 */
// displayData saveData setParagraph
import { ElLoading as Loading } from 'element-plus'
import Sortable from 'sortablejs'
import { mapGetters } from 'vuex'
import { deepCopy, swapArray } from '@/utils'
import { createUploader } from '@/api/video'
import { importEssay } from '@/api/article'
import { searchArticleList } from '@/api/articleModule'
import clipboard from '@/directive/clipboard/index.js'
import '@/assets/css/squire.css'
import ChooseIframe from './ChooseIframeSquire.vue'
import SquireAddShop from './SquireAddShop.vue'
import SquireAddDetail from './SquireAddDetail.vue'
export default {
  directives: {
    clipboard
  },
  components: {
    ChooseIframe,
    SquireAddShop,
    SquireAddDetail
  },
  props: {
    showUndo: {
      type: Boolean,
      default: true
    },
    showRedo: {
      type: Boolean,
      default: true
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    showImg: {
      type: Boolean,
      default: true
    },
    showLink: {
      type: Boolean,
      default: true
    },
    showVideo: {
      type: Boolean,
      default: true
    },
    showTextAlign: {
      type: Boolean,
      default: false
    },
    showImport: {
      type: Boolean,
      default: true
    },
    showAddShop: {
      type: Boolean,
      default: false
    },
    showImportDetail: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: 'editor-content'
    }, // 富文本框根据此id确认编辑框位置
    disable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      linkAddress: '', // 连接地址
      dialogTitle: '', // 标题
      linkWriting: '', // 连接文案
      preservationNum: '', // 自动加载个数
      copyTextareaValue: '', // 复制的textarea 内容
      editorDom: null, // 富文本的dom节点
      linkTitleName: '添加链接', // 插入链接 或者导入链接时标题
      linkPlaceholder: '请输入链接地址', // 插入链接 或者导入链接时 占位文字
      viewStatus: false, // 小窗口展示状态
      seamlessImg: false, // 无缝图片
      addImgStatus: false, // 增加图片
      addVideoStatus: false, // 添加视频
      disabledLink: false, // 链接地址是否可增加
      addLinkStatus: false, // 增加连接地址
      writeMark: false, // 编辑状态
      disableSubmit: false, // 获取导入链接状态
      isNoLocal: false, // 是否不是本地
      textAlignDisable: true, // 是否可选择对齐方式
      imgList: [], // 添加的img地址
      videoTips: [], // 视频提示
      videoAll: [], // 视频集合
      articleList: [], // 添加的文章集合
      saveTemData: [], // 保存的数据
      textCalssName: [
        'align-center',
        'align-right',
        'align-left',
        'align-justify'
      ], // 对齐方式的 className
      textAlignList: [
        {
          name: '左对齐',
          img: 'static/img/icon-text-left.png',
          value: 'left'
        },
        {
          name: '居中对齐',
          img: 'static/img/icon-text-center.png',
          value: 'center'
        },
        {
          name: '右对齐',
          img: 'static/img/icon-text-right.png',
          value: 'right'
        },
        {
          name: '两端对齐',
          img: 'static/img/icon-text-default.png',
          value: 'justify'
        }
      ],
      videoContent: {
        id: '', // 视频id
        name: '', // 视频名称
        coverUrl: '', // 视频封面
        duration: '', // 视频时长
        sourceVideoUrl: '', // 视频播放地址
        time: '', // 时间戳
        vodTaskId: '', // 任务id
        vodTaskStatus: '' // 任务是否结束
      }
    }
  },
  computed: {
    ...mapGetters(['uid'])
  },
  watch: {
    imgList() {
      this.linkAddress = this.imgList.length === 1 ? this.linkAddress : ''
      this.disabledLink = this.imgList.length > 1
      this.imgDrop()
    },
    viewStatus(value) {
      if (!value) {
        this.linkWriting = ''
        this.linkAddress = ''
        this.dialogTitle = ''
        this.imgList = []
        this.addImgStatus = false
        this.addVideoStatus = false
        this.disabledLink = false
        this.addLinkStatus = false
        this.seamlessImg = false
      }
    },
    // 编辑状态
    writeMark() {
      if (this.writeMark) {
        this.setPreservation()
      }
    },
    // 编辑个数
    preservationNum(val, oldVal) {
      if (val !== oldVal) {
        this.setPreservation()
      }
    }
  },
  created() {
    this.readySquire()
  },
  mounted() {
    if (!window.jQuery) {
      const url = this.isNoLocal
        ? '/oss/static/js/jquery.min.js'
        : '/static/js/jquery.min.js'
      window.loadJs(url)
    }
  },
  methods: {
    readySquire() {
      const me = this
      me.isNoLocal =
        window.location.origin.indexOf('jddmoto') > -1 ||
        window.location.origin.indexOf('58moto') > -1
      me.$nextTick(function () {
        if (!window.Squire) {
          const url = me.isNoLocal
            ? '/oss/static/js/squire-raw.js'
            : '/static/js/squire-raw.js'
          window.loadJs(url).then(function () {
            me.setSquire()
          })
        } else {
          me.setSquire()
        }
        me.videoTips = []
      })
    },
    // 图片拖拽
    imgDrop() {
      const imgContainer = document.querySelector('.imgs-container')
      const _this = this
      if (imgContainer) {
        // 不再建议使用
        Sortable.create(imgContainer, {
          onEnd({ newIndex, oldIndex }) {
            swapArray(_this.imgList, newIndex, oldIndex)
          }
        })
      }
    },
    // 设置富文本组件
    setSquire() {
      const me = this
      me.editorDom = document.getElementById(me.id)
      if (!me.editorDom) return
      // Squire 定义 paragraph 段落标签、paragraph-title 段落标题
      me.editor = new window.Squire(me.editorDom, {
        blockTag: 'p',
        blockAttributes: {
          class: 'squire-paragraph'
        }
      })
      me.editor.addEventListener('willPaste', function (type, handler) {
        type.defaultPrevented = true // 禁止粘贴
        me.getSticker(type, handler)
      })
      me.editor.addEventListener('paste', function (event) {
        const clipboardData =
          event.clipboardData || event.originEvent.clipboardData
        const items = clipboardData.items
        const types = clipboardData.types
        if (items && types) {
          const i = types.findIndex((_) => _ === 'Files')
          if (i !== -1) {
            const item = items[i]
            if (item && item.kind === 'file' && item.type.match(/^image\//i)) {
              const blob = item.getAsFile()
              const reader = new FileReader()
              reader.readAsDataURL(blob)
              reader.onload = function (e) {
                const src = e.target.result
                me.editor.insertImages(src)
                me.pictureKeyup()
                me.getDelImg()
              }
            }
          }
        }
      })
      window.Squire.prototype.makeParagraph = function (
        content,
        position,
        textAlign
      ) {
        let className = 'squire-paragraph'
        let style = ''
        if (textAlign) {
          className = `squire-paragraph align-${textAlign}`
          style = `text-align: ${textAlign}`
        }
        let p = this.createElement('p', {
          class: className,
          style: style
        })
        if (position) {
          p = this.createElement('DIV', {
            class: className,
            contenteditable: 'false',
            'data-position': position,
            style: style
          })
        }
        this.createElement('DIV', {
          class: className,
          contenteditable: 'false',
          'data-position': position
        })
        p.innerHTML = content
        this.insertElement(p)
      }
      window.Squire.prototype.makeHeader = function () {
        return this.modifyBlocks(function (frag) {
          const output = this._doc.createDocumentFragment()
          let block = frag
          const dom =
            block.children &&
            block.children[0] &&
            block.children[0].nodeName === 'H2'
              ? 'p'
              : 'h2'
          const cla =
            dom === 'h2' ? 'squire-paragraph-title' : 'squire-paragraph'
          while ((block = window.Squire.getNextBlock(block))) {
            output.appendChild(
              this.createElement(
                dom,
                {
                  class: cla
                },
                [window.Squire.empty(block)]
              )
            )
          }
          return output
        })
      }
      window.Squire.prototype.insertVideo = function (src, vId, content, img) {
        const video = this.createElement('VIDEO', {
          src: src,
          class: 'squire-video-area',
          controls: 'controls',
          'data-id': vId,
          poster: img || ''
        })
        const textarea = this.createElement('TEXTAREA', {
          placeholder: '告诉在人们视频背后的故事吧...(最多140个字)',
          maxlength: '140',
          contenteditable: 'true',
          class: 'squire-video-title squire-picture-title hide'
        })
        textarea.innerText = content || ''
        const p = this.createElement(
          'DIV',
          {
            class: 'squire-video-content',
            contenteditable: 'false'
          },
          [video, textarea]
        )
        this.insertElement(p)
      }
      window.Squire.prototype.insertImages = function (
        src = '',
        imgLink = '',
        content,
        seamlessImg = false
      ) {
        const delImg = `<img class="squire-del-img" src='/static/img/<EMAIL>'>`
        let className = 'squire-picture-area'
        if (seamlessImg) {
          className = className + ' seamless-img'
        }
        const img = this.createElement('IMG', {
          src: src,
          class: className
        })
        if (!me.showImport && !me.showVideo) {
          const p = this.createElement(
            'DIV',
            {
              class: imgStr.test(src)
                ? 'squire-img-content'
                : 'squire-img-content red-border',
              contenteditable: 'false'
            },
            [img]
          )
          p.innerHTML = p.innerHTML + delImg
          this.insertElement(p)
          if (seamlessImg) {
            //移除插入图片默认增加的p标签
            me.removeDefault(p, 'nextElementSibling')
            me.removeDefault(p, 'previousElementSibling')
          }
          return
        }
        const textarea = this.createElement('TEXTAREA', {
          placeholder: '告诉在人们这张照片背后的故事吧...(最多140个字)',
          maxlength: '140',
          contenteditable: 'true',
          class: 'squire-picture-title hide'
        })
        textarea.innerText = content || ''
        let a = null
        if (imgLink) {
          a = this.createElement('A', {
            class: 'squire-insert-link'
          })
          a.innerHTML = imgLink
        }
        const addDom = imgLink ? [img, a, textarea] : [img, textarea]
        const p = this.createElement(
          'DIV',
          {
            class: imgStr.test(src)
              ? 'squire-img-content'
              : 'squire-img-content red-border',
            contenteditable: 'false'
          },
          addDom
        )
        p.innerHTML = p.innerHTML + delImg
        this.insertElement(p)
        if (seamlessImg) {
          //移除插入图片默认增加的p标签
          me.removeDefault(p, 'nextElementSibling')
          me.removeDefault(p, 'previousElementSibling')
        }
      }
      window.Squire.prototype.insertLink = function (link, content, origin) {
        const delImg = `<img class="squire-del-img" src='/static/img/<EMAIL>'>`
        const img = `<img class='squire-link-img' src='/static/img/<EMAIL>'>`
        const a = `<a class='squire-link-mes' data-url='${link}' target='_blank'>${
          content || link
        }</a>`
        const p = this.createElement('DIV', {
          class: 'squire-link-content',
          'data-origin': origin || '',
          contenteditable: 'false'
        })
        p.innerHTML = `${img}${a}${delImg}`
        this.insertElement(p)
      }
      window.Squire.prototype.insertShopDetail = function (
        id,
        content,
        src,
        labelName
      ) {
        const type = { 淘宝商品: 9, 京东商品: 10, 自营商品: 12 }[labelName]
        const delImg = `<p class="squire-shop-del-img-content"><i class="squire-shop-del-img" /></p>`
        const mes = `<img class="squire-shop-img" src='${src}'><p class='squire-shop-mes' data-id='${id}' data-type='${type}'>${content}</p><p class='squire-shop-mes squire-shop-tip'>${labelName}</p>`
        const p = this.createElement('DIV', {
          class: 'squire-shop-content',
          contenteditable: 'false'
        })
        p.innerHTML = `${mes}${delImg}`
        this.insertElement(p)
      }
      window.Squire.prototype.insertArticle = function (article) {
        const delImg = `<img class="squire-del-img" src='/static/img/<EMAIL>'>`
        let img = article.img
          ? `<img class="squire-article-img" src='${article.img}'>`
          : ''
        const mes = `<div class='squire-article-mes ${
          article.img ? 'squire-article-mes-padding' : ''
        }'>
      <p class='squire-article-title dotdotdot1'>${article.title}</p>
      <p class='squire-article-tip'>${article.viewNum}浏览 · ${
          article.replyCnt
        }评论<span class="squire-article-tag">文章</span></p>
      <p class='squire-article-tip'>作者：${article.author}</p>
      </div>`
        const p = this.createElement('DIV', {
          class: 'squire-article-content',
          contenteditable: 'false',
          'data-id': article.id
        })
        p.innerHTML = `${img ? img : ''}${mes}${delImg}`
        this.insertElement(p)
      }
    },
    removeDefault(next, key) {
      const me = this
      const cur = next[key]
      if (!cur || cur.innerText !== '\n') return
      //全部移除p标签会使rang的值变化导致替换图片时位置不对，所以保留图片前后各一个隐藏的p标签，用来确定位置
      const blockDefault = cur.classList.contains('squire-paragraph') || false
      const isLasterBlock = next.classList.contains('squire-paragraph') || false
      if (blockDefault && isLasterBlock) {
        cur.remove()
        me.removeDefault(cur, key)
      } else {
        if (blockDefault) {
          cur.style.display = 'none'
          me.removeDefault(cur, key)
        }
      }
    },
    // 点击功能区，触发增加图片或其他
    setContent(e, type) {
      const me = this
      const id = e.target.id
      const value = type || ''
      me.writeMark = true
      if (id && me.editor && me.editor[id]) {
        me.editor[id](value)
        me.getLastChangeDom()
      }
    },
    // 确认增加图片或、外链、导入
    setShowDisplay() {
      const me = this
      me.writeMark = true
      if (!me.addLinkStatus && !me.addVideoStatus && !me.addImgStatus) {
        return me.getImportLinkData() // 一键导入功能单独处理
      }
      if (me.addLinkStatus && !me.linkAddress) {
        return me.$message.error('请输入添加的地址')
      }
      me.linkAddress = (me.linkAddress && me.linkAddress.trim()) || ''
      const objExp =
        /^(((ht|f)tp(s?)):\/\/)?(www.|[a-zA-Z].)[a-zA-Z0-9-.]+.(com|edu|gov|mil|net|org|biz|info|name|museum|us|ca|uk|cn|im)(:[0-9]+)*(\/($|[a-zA-Z0-9.,;?'&%$#=~_-]+))*$/
      if (me.linkAddress && !objExp.test(me.linkAddress)) {
        return me.$message.error('URL无效')
      }
      if (me.addImgStatus && me.imgList.length > 1) {
        const lengthAll = me.imgList.length
        me.imgList.map(function (value, index) {
          const isLast = lengthAll === index + 1
          const seamlessImg = me.seamlessImg && !isLast
          me.editor['insertImages'](value.url, me.linkAddress, '', seamlessImg)
        })
      } else if (me.addImgStatus && me.imgList.length) {
        me.editor['insertImages'](me.imgList[0].url, me.linkAddress)
      } else if (me.linkAddress) {
        me.editor['insertLink'](me.linkAddress, me.linkWriting)
      }
      me.close()
      me.getDelImg()
    },
    // 增加link
    addLink(type) {
      const me = this
      me.linkTitleName = type === 'link' ? '添加链接' : '导入链接地址'
      me.linkPlaceholder =
        type === 'link' ? '请输入链接地址' : '导入链接地址仅支持微信'
      me.addLinkStatus = type === 'link'
      me.viewStatus = true
      me.linkStatus = true
    },
    // 增加商品
    addShop() {
      this.$refs.SquireAddShop.init()
    },
    // 弹出上传图片或视频
    openView(name, flag = false) {
      const me = this
      me.dialogTitle = name === 'img' ? '添加图片' : '添加视频'
      me.viewStatus = true
      name === 'img' ? (me.addImgStatus = true) : (me.addVideoStatus = true)
      me.seamlessImg = flag
    },
    // 关闭蒙层页面
    close() {
      this.viewStatus = false
    },
    // 关闭弹框
    handleClose() {
      if (!this.linkAddress && !this.linkWriting && !this.imgList.length) {
        this.viewStatus = false
        return
      }
      this.$confirm('确认关闭？')
        .then(() => {
          this.viewStatus = false
        })
        .catch(() => {})
    },
    // 侦测回退或删除或者撤回时
    setBack(e, type) {
      console.log(e, type)
      const me = this
      me.writeMark = true
      if (
        (e.ctrlKey || e.metaKey) &&
        e.keyCode === 67 &&
        e.target.getAttribute('class') &&
        e.target.getAttribute('class').indexOf('picture-title') > -1
      ) {
        // 复制描述中的文字
        me.copyTextareaValue = e.target.value
        setTimeout(() => {
          document.querySelector('.copy-data').click()
        }, 50)
        setTimeout(() => {
          document.querySelector('.copy-data').click()
        }, 200)
        return
      }
      const arrowArr = [37, 38, 39, 40] // 键盘，上下所有
      if (arrowArr.indexOf(e.keyCode) > -1) {
        me.getLastChangeDom(true)
      }
      if (!(e.keyCode === 8 || e.keyCode === 46)) {
        // 8 返回键（backspace), 46 删除键（delete)
        return
      }
      const endContainer = me.editor.getSelection().endContainer
      let dom =
        e.keyCode === 8
          ? endContainer.previousSibling ||
            endContainer.parentNode.previousSibling ||
            endContainer.parentNode
          : endContainer.nextSibling ||
            endContainer.parentNode.nextSibling ||
            endContainer.parentNode // 上一个节点或下一个节点或自己节点
      if (!dom) {
        // 没有拿到dom节点时
        return
      }
      if (
        endContainer &&
        endContainer.getAttribute &&
        endContainer.getAttribute('class').indexOf('picture-title') > -1
      ) {
        // 特殊处理，删除视频
        dom = endContainer.parentNode
      }
      function delMediaDom(dom, type) {
        if (
          (dom.querySelector('.squire-video-area') &&
            dom.querySelector('.squire-video-title') &&
            type === 'video') ||
          (dom.querySelector('.squire-picture-area') &&
            dom.querySelector('.squire-picture-title') &&
            type === 'img')
        ) {
          return
        }
        me.editorDom.removeChild(dom)
      }
      function delLinkDom(dom) {
        if (
          !(
            dom.querySelector('.squire-link-img') &&
            dom.querySelector('.squire-link-mes')
          )
        ) {
          me.editorDom.removeChild(dom)
        }
      }
      if (
        dom.nodeName !== '#text' &&
        (dom.getAttribute('class') === 'squire-img-content' ||
          dom.getAttribute('class') === 'squire-video-content')
      ) {
        // 图片或视频
        dom.getAttribute('class') === 'squire-img-content'
          ? delMediaDom(dom, 'img')
          : delMediaDom(dom, 'video')
      } else if (
        dom.nodeName !== '#text' &&
        dom.getAttribute('class') === 'squire-link-content'
      ) {
        // 外链
        delLinkDom(dom)
      }
      // 重新侦测video标签是否有变化
      // const children = Array.from(me.editorDom.children)
      // children.map(function (value, index) {
      //   if (value.className !== 'squire-video-content') {
      //     return
      //   }
      //   const video = value.querySelector('.squire-video-area')
      //   if (!video) {
      //     return
      //   }
      //   const num =
      //     me.videoAll.findIndex(_ => {
      //       return _.time === parseInt(video && video.dataset && video.dataset.id)
      //     }) > -1
      //       ? 1
      //       : 0
      //   me.videoTips.map(function (tVideo, tIndex) {
      //     tVideo.status = tVideo.time === parseInt(video && video.dataset && video.dataset.id) ? true : tVideo.status
      //   })
      //   if (num !== 0) {
      //     return
      //   }
      //   me.delVideoTips(video.dataset.id)
      // })
      me.pictureKeyup()
      me.getDelImg()
    },
    // 获取最后一次光标停留位置
    getLastChangeDom(status) {
      const me = this
      setTimeout(() => {
        const lastDom = status
          ? me.editor.getSelection().endContainer.parentNode.localName || ''
          : me.editor.getSelection().endContainer.localName || ''
        me.textAlignDisable = lastDom === 'h2'
      }, 100)
    },
    // 自动保存并增加计数
    setPreservation() {
      const me = this
      if (!me.writeMark) {
        return
      }
      // 编辑、创建文章才会用到自动保存
      if (
        'OssReview EditArticle createArticle BusinessGrowthNewArticle'
          .toLowerCase()
          .indexOf(me.$route.name.toLowerCase()) > -1
      ) {
        console.log('编辑、创建文章才会用到自动保存')
        setTimeout(() => {
          me.preservationNum++
          me.saveData('', {
            autoSave: true
          })
        }, 5000)
      }
    },
    // 发布，保存dom节点数据(父组件有调用)
    saveData(type, extend = {}) {
      const me = this
      const mes = []
      if (!me.editorDom) return
      let children = me.editorDom.children
      children = Array.from(children)
      children.map(function (value) {
        // 根据不同dom节点存储不同类型保存
        if (value.tagName === 'H2' && value.innerText.length) {
          // 标题类型
          if (!extend.autoSave) {
            value.innerHTML = value.innerHTML.replace(/<br>/g, '')
          }
          pushContent('4', value.innerText)
        } else if (value.className.indexOf('squire-paragraph') > -1) {
          // 段落
          setParagraph(value)
        } else if (/squire-img-content/.test(value.className)) {
          // 图文
          if (
            value.querySelectorAll('.squire-paragraph') &&
            value.querySelectorAll('.squire-paragraph').length
          ) {
            let paragraphs = value.querySelectorAll('.squire-paragraph')
            paragraphs = Array.from(paragraphs)
            paragraphs &&
              paragraphs.map(function (p) {
                setParagraph(p)
              })
          }
          const img = value.querySelector('.squire-picture-area')
          if (!(img && img.src)) {
            return
          }
          const imgTitle = value.querySelector('.squire-picture-title')
          const imgLink = value.querySelector('.squire-insert-link')
          const src = img.dataset.id
            ? me.imgAll.find((_) => {
                _.id === parseInt(img.dataset.id)
              }).src
            : img.src
          const imgType = img.className.includes('seamless-img') ? 1 : ''
          pushContent(
            imgTitle && imgTitle.value ? '3' : '2',
            (imgTitle && imgTitle.value) || '',
            src,
            (imgLink && imgLink.innerText) || '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            imgType
          )
        } else if (value.className === 'squire-video-content') {
          // 视频 ，需要单独处理，保证视频正常上传完成后方可发送
          if (value.querySelectorAll('.squire-paragraph').length) {
            let paragraphs = value.querySelectorAll('.squire-paragraph')
            paragraphs = Array.from(paragraphs)
            paragraphs &&
              paragraphs.map(function (value) {
                setParagraph(value)
              })
          }
          const video = value.querySelector('.squire-video-area')
          if (!(video && video.src)) {
            return
          }
          let videoContent = {}
          me.videoAll.map(function (value) {
            if (value.time === parseInt(video.dataset.id)) {
              videoContent = value
            }
          })
          // if (video.src.indexOf('blob') > -1 && video.dataset.id) {
          //   if (videoContent.vodTaskStatus !== 'CompleteAllSucc') {
          //     if (!videoContent.vodTaskId) {
          //       return
          //     }
          //     getVideoTaskStatus(videoContent.vodTaskId)
          //       .then(function (res) {
          //         console.log(res)
          //         videoContent.vodTaskStatus = res.data.data
          //         if (res.data.data === 'CompleteAllSucc') {
          //           getVideoContent(videoContent)
          //         } else {
          //           me.addVideoTips(video.dataset.id, videoContent.name)
          //         }
          //       })
          //       .catch(err => {
          //         console.log(err)
          //         me.$message.error(err.message || '视频转码失败，请删除后重新上传')
          //       })
          //     return
          //   }
          //   if (videoContent.vodTaskStatus === 'CompleteAllSucc' && videoContent.coverUrl === '') {
          //     getVideoContent(videoContent)
          //   }
          // }
          const videoTitle = value.querySelector('.squire-video-title')
          if (videoTitle && videoTitle.value) {
            pushContent(
              '1',
              videoTitle && videoTitle.value,
              '',
              '',
              '',
              '',
              '',
              '',
              ''
            )
          }
          pushContent(
            '6',
            '',
            videoContent.coverUrl,
            videoContent.sourceVideoUrl || video.src,
            videoContent.id,
            videoContent.duration,
            videoContent.vodSize,
            videoContent.vodType
          )
        } else if (value.className === 'squire-link-content') {
          // 外链
          const link = value.querySelector('.squire-link-mes')
          if (!(link && link.innerText)) {
            return
          }
          // 文章发布，关联数据原样返回（type为5，才能获取到data-origin）
          const dataOrigin = value.getAttribute('data-origin')
          if (dataOrigin) {
            try {
              return mes.push(JSON.parse(dataOrigin))
            } catch (error) {
              this.$message.error(error.message)
            }
          } else {
            const content =
              link.dataset.url === link.innerText ? '' : link.innerText
            pushContent(
              content ? '1' : '8',
              content,
              '',
              link.dataset.url ? link.dataset.url : link.innerText
            )
          }
        } else if (value.className === 'squire-shop-content') {
          // 商品详情
          const mesContent = value.querySelector('.squire-shop-mes')
          if (!mesContent.dataset.id) return
          const img = value.querySelector('.squire-shop-img').src || ''
          const content = mesContent.innerText || ''
          const id = mesContent.dataset.id
          const type = mesContent.dataset.type || '9' // 可为9，10，12
          pushContent(type, content, img, '', id)
        } else if (value.className === 'squire-article-content') {
          if (!value.dataset.id) return
          const artId = Number(value.dataset.id)
          const findData =
            me.articleList.find((_) => {
              return _.id === artId
            }) || {}
          const article = {
            img:
              findData.mediaInfo && findData.mediaInfo.length
                ? findData.mediaInfo[0].img
                : findData.img || '',
            title: findData.title || '',
            author: findData.author,
            id: findData.id,
            viewNum: findData.viewNum || 0,
            replyCnt: findData.replyCnt || 0
          }
          pushContent('11', '', '', '', artId, '', '', '', '', '', article)
        }
      })
      // 针对段落做处理
      function setParagraph(paragraph) {
        if (paragraph.innerText.length < 2) {
          return
        }
        const value = paragraph.innerText.replace(/#/g, '＃')
        let findTextIndex = me.textCalssName.findIndex((item) => {
          if (paragraph.className.indexOf(item) > -1) {
            return item
          }
        })
        let textAlign = ''
        if (findTextIndex > -1) {
          textAlign = me.textCalssName[findTextIndex].split('-')[1]
        }
        pushContent(
          '1',
          value,
          '',
          '',
          '',
          '',
          '',
          '',
          paragraph.dataset.position,
          textAlign
        )
      }
      function pushContent(
        type,
        content,
        img,
        link,
        id,
        duration,
        vodSize,
        vodType,
        highlightPosition,
        textAlign,
        article,
        imgType
      ) {
        content = content && content.replace(/<br>/g, '')
        const addData = {
          type: type || '', // 类型
          content: content || '', // 内容
          img: img || '', // 视频图片或图片
          link: link || '', // 外链或视频播放地址
          id: id || '', // 视频id
          duration: duration || '', // 播放时长
          vodSize: vodSize || '', // 视频宽高
          vodType: vodType || '', // 视频大小
          highlightPosition: highlightPosition || '', // 关注@功能
          textAlign: textAlign || '',
          article: article && article.id ? article : '',
          imgType: imgType || ''
        }
        mes.push(addData)
      }
      // 获取视频
      // function getVideoContent(videoContent) {
      //   getVideoMes(videoContent.id)
      //     .then(function (data) {
      //       console.log(data)
      //       if (data.data.code === 0 && data.data.data !== null) {
      //         videoContent.duration = data.data.data.duration
      //         videoContent.sourceVideoUrl = data.data.data.sourceVideoUrl
      //         videoContent.coverUrl = data.data.data.coverUrl
      //         videoContent.vodSize = data.data.data.mSize
      //         videoContent.vodType = `${data.data.data.height}x${data.data.data.width}`
      //         me.delVideoTips(videoContent.time)
      //       } else {
      //         setTimeout(function () {
      //           getVideoMes(videoContent)
      //         }, 4000)
      //       }
      //     })
      //     .catch(err => {
      //       console.log(err)
      //       me.$message.error(err.message || '视频获取失败，请删除后重新上传')
      //     })
      // }
      // 自动保存或预览
      if (!type) {
        me.saveTemData = mes
        window.sessionStorage.setItem(
          'localData',
          JSON.stringify(me.saveTemData)
        )
        return
      }
      if (type === 'copeContent') {
        return me.$refs.ChooseIframe.init(mes)
      }
      // if (me.videoTips && me.videoTips.length) {
      //   return me.$message.error('还有视频任务未执行完成，请稍等')
      // }
      // 保存 或预览
      if (type) {
        $emit(me, 'squire', mes, type)
      }
    },
    // 防止定时器干扰，需先清除原dom(父组件有调用)
    clearEditor() {
      const me = this
      me.editorDom = null
      sessionStorage.removeItem('localData')
    },
    // 展示，对应数据(父组件有调用)，注意，需先调用：this.$refs.squire && this.$refs.squire.clearEditor()
    displayData(data, status) {
      // 重写，有问题
      const me = this
      me.editorDom = document.getElementById(me.id)
      if (!me.editorDom) return
      if (!status) {
        me.editorDom.innerHTML = ''
      }
      if (!me.editor) {
        console.log('editor, loading error')
        const timeNum = data && data.length ? 800 : 400
        setTimeout(() => {
          me.displayData(data)
        }, timeNum)
        return
      }
      me.articleList = []
      const articleIdList = []
      data &&
        data.map(function (value, index) {
          if (value.type === '1' && value.link) {
            // 对应链接
            me.editor.insertLink(value.link, value.content || value.link)
          } else if (value.type === '1') {
            // 对应段落
            me.editor.makeParagraph(
              value.content,
              value.highlightPosition || '',
              value.textAlign || ''
            )
          } else if (value.type === '2' || value.type === '3') {
            // 对应图片
            me.editor.insertImages(
              value.img,
              value.link || '',
              value.content,
              !!value.imgType
            )
          } else if (value.type === '4') {
            // 对应标题
            const innerHTML =
              `<h2 class="squire-paragraph-title">` + value.content + `</h2>`
            me.editorDom.innerHTML = me.editorDom.innerHTML + innerHTML
          } else if (value.type === '5' && value.content) {
            // 关联：外链 内容 link 骑行轨迹
            me.editor.insertLink(
              value.link,
              value.content,
              JSON.stringify(value)
            )
          } else if (value.type === '6' || value.type === '7') {
            // 对应视频
            const content =
              value.content !== 'undefined' && value.content !== undefined
                ? value.content
                : ''
            const timeId = Date.parse(new Date()) + index
            me.editor.insertVideo(value.link, timeId, content, value.img)
            const videoContent = {
              ...value,
              coverUrl: value.img,
              sourceVideoUrl: value.link,
              time: timeId,
              id: value.id,
              vodTaskStatus: 'CompleteAllSucc'
            }
            me.videoAll.push(videoContent)
          } else if (value.type === '8') {
            // 对应外链
            me.editor.insertLink(value.link)
          } else if (['9', '10', '12'].includes(value.type)) {
            const labelList = { 9: '淘宝商品', 10: '京东商品', 12: '自营商品' }
            // 对应商品详情
            me.editor.insertShopDetail(
              value.id,
              value.content,
              value.img,
              labelList[value.type]
            )
          } else if (value.type === '11') {
            const article = value.article || {}
            articleIdList.push(article.id)
            // 对应插入文章链接
            me.editor.insertArticle(article)
          }
        })
      if (articleIdList.length) {
        me.getArticleDataList(articleIdList)
        // setTimeout(() => {
        //   me.updateArticleData()
        // }, 3000)
      }
      me.editorDom.innerHTML =
        me.editorDom.innerHTML + `<p class="squire-paragraph"><br></p>`
      if (data && data.length) {
        me.writeMark = true
        me.pictureKeyup()
        me.getDelImg()
      }
    },
    // 上传图片
    async httpRequest(option) {
      option.imageType = 'forum'
      this._uploads = this._uploads || []
      this._uploads.push({
        fn: this.$oss.ossUploadImage,
        option
      })
      this.$tools.debounce(this.call, 100)()
    },
    async call() {
      for (const a of this._uploads) {
        await a.fn(a.option)
      }
      this._uploads = []
    },
    onSuccess(res) {
      if (!res) return
      console.log(res)
      if (res.name) {
        const data = {
          url: res.imgOrgUrl
        }
        this.imgList.push(data)
        this.imgDrop()
        return
      } else {
        this.loading = false
        this.$notify.error({
          title: '上传错误'
        })
      }
    },
    onError(res) {
      console.log(res)
      this.$message.error(res)
    },
    // 删除图片
    delImg(index) {
      this.imgList.splice(index, 1)
    },
    /**
     * 上传视频
     * 1、获取签名 videoSign 2、调用腾讯sdk上传视频 3、视频上传完成后转码 4、定时器监听转码结果 getVideoTaskStatus 5、定时器监听：转码成功后获取视频信息 getVideoMes
     */
    postVideo(e) {
      const data = e.target.files
      const me = this
      if (
        data[0].name.indexOf('.mp4') === -1 &&
        data[0].name.indexOf('.mov') === -1 &&
        data[0].name.indexOf('.ts') === -1
      ) {
        return me.setToast('视频格式不是mp4、mov、ts')
      }
      me.writeMark = true
      const timeId = Date.parse(new Date())
      const videoContent = deepCopy(me.videoContent)
      // me.videoTips.push({
      //   time: timeId,
      //   name: data[0].name,
      //   content: `视频文件名${data[0].name},正在上传中`
      // })
      // const num = me.videoTips.findIndex(_ => {
      //   return _.time === timeId
      // })
      me.editor['insertVideo'](window.URL.createObjectURL(data[0]), timeId)
      videoContent.time = timeId
      me.close()

      me.loading = true
      return createUploader
        .call(me, data[0])
        .then((result = {}) => {
          me.loading = false
          videoContent.id = result.videoId || ''
          videoContent.src = result.videoUrl || ''
          videoContent.sourceVideoUrl = result.videoUrl || ''
          me.videoAll.push(videoContent)
          $emit(me, 'updateAddVideo', true)
        })
        .catch((_) => {
          me.loading = false
          me.setMessageBoxNoCancel(_)
        })
    },
    // 添加导入链接地址
    getImportLinkData() {
      const me = this
      me.linkAddress = (me.linkAddress && me.linkAddress.trim()) || ''
      if (!me.linkAddress) {
        return me.$message.error('请输入导入链接地址')
      }
      const objExp =
        /^(((ht|f)tp(s?)):\/\/)?(www.|[a-zA-Z].)[a-zA-Z0-9-.]+.(com|edu|gov|mil|net|org|biz|info|name|museum|us|ca|uk|cn|im)(:[0-9]+)*(\/($|[a-zA-Z0-9.,;?'&%$#=~_-]+))*$/
      if (!objExp.test(me.linkAddress)) {
        return me.$message.error('URL无效')
      }
      if (me.disableSubmit) {
        return me.$message.error('正在获取信息，请稍后...')
      }
      me.disableSubmit = true
      const loadingInstance = Loading.service({
        fullscreen: true,
        text: '处理中'
      })
      importEssay({
        action: '30008OSS',
        url: me.linkAddress
      })
        .then((response) => {
          loadingInstance.close()
          let data = {}
          try {
            data = JSON.parse(response.data)
            data.data.content = data.data.content
              .replace(/<a href='(.*?)'(.*?)>/g, '')
              .replace(/<video([^<>]*)>([^<>]*)<\/video>/g, '')
              .replace(/<iframe([^<>]*)>([^<>]*)<\/iframe>/g, '')
              .replace(/<\/a>/g, '') // 运营需求，删除a标签（不删除内容），删除video、iframe 标签
            const div = document.createElement('DIV')
            div.innerHTML = data.data.content
            me.setSticker(div.children) // 重新走粘贴方法
            me.close()
            me.linkAddress = ''
          } catch (error) {
            me.$message.error(response && response.data)
          }
          me.writeMark = true
          me.disableSubmit = false
        })
        .catch((err) => {
          loadingInstance.close()
          me.writeMark = true
          me.disableSubmit = false
          console.log(err)
        })
    },
    // 获取ctrl + v 后内容
    getSticker(type, handler) {
      console.log('handler :>> ', handler)
      this.setSticker(type.fragment.children)
    },
    // 粘贴
    setSticker(data) {
      const me = this
      data = Array.from(data)
      data.map(function (value) {
        // 其他标签时，需要针对嵌套标签做截取
        let newHtml = value.innerHTML
          .replace(
            /<img(.*?)src="(.*?)"(.*?)>/g,
            '(delimiter)[img src=$2](delimiter)'
          )
          .replace(
            /<img(.*?)src='(.*?)'(.*?)>/g,
            '(delimiter)[img src=$2](delimiter)'
          )
          .replace(
            /<a href="(.*?)"(.*?)>(.*?)<\/a>/g,
            '(delimiter)$3(delimiter)'
          )
          .replace(/<\/?.+?>/g, '') // 截取出img、link 和文字部分，使用(delimiter) 切割
        newHtml = newHtml.split('(delimiter)') // 拆分成数组，完成替换
        newHtml.map(function (nValue) {
          if (!nValue || nValue === '&nbsp;') {
            return
          }
          if (nValue.indexOf('[img') > -1) {
            // 图片
            let src = nValue.replace(/\[img src=(.*?)\]/g, '$1')
            src = src.replace(/amp;/g, '')
            me.editor.insertImages(src)
          } else {
            // 文字
            me.editor.makeParagraph(nValue)
          }
        })
        me.pictureKeyup()
        me.getDelImg()
      })
    },
    // 添加videoTips
    addVideoTips(id, name) {
      const me = this
      let num = 0
      me.videoTips.map(function (value) {
        if (parseInt(id) === value.time) {
          num++
        }
      })
      if (num === 0) {
        me.videoTips.push({
          time: id,
          name: name,
          content: `视频文件名${name},正在转码中`
        })
      }
    },
    // 删除videoTips
    delVideoTips(id) {
      const me = this
      me.videoTips.map(function (value, index) {
        if (
          parseInt(id) === parseInt(value.time) ||
          value.status === undefined
        ) {
          me.videoTips.splice(index, 1)
        }
      })
    },
    // 增加商品
    addShopDetail(detail) {
      console.log(detail)
      this.editor['insertShopDetail'](
        detail.id,
        detail.goodsName,
        detail.firstImg,
        detail.labelName
      )
      this.getDelImg()
    },
    // 顺序拉文章列表信息
    async httpRequestArticle(id, lastOneId) {
      const me = this
      const option = {
        id,
        page: 1,
        lastOneId
      }
      async function getArticleData(option) {
        const { data } = await searchArticleList(option)
        const backData = data.data.listData || []
        me.articleList.push(backData[0] || {})
        if (option.lastOneId) {
          me.updateArticleData()
        }
      }
      this._uploadsArticle = this._uploadsArticle || []
      this._uploadsArticle.push({
        fn: getArticleData,
        option
      })
      this.$tools.debounce(this.callArticle, 100)()
    },
    async callArticle() {
      for (const a of this._uploadsArticle) {
        await a.fn(a.option)
      }
      this._uploadsArticle = []
    },
    // 获取所有文章
    getArticleDataList(list) {
      console.log(list)
      const me = this
      list.map((_, index) => {
        const lastId = index + 1 === list.length
        me.httpRequestArticle(_, lastId ? _ : '')
      })
    },
    // 更新部分dom节点
    updateArticleData() {
      const me = this
      const dom = document.querySelector('#editor-content')
      let arrlist = dom.querySelectorAll('.squire-article-content')
      arrlist = Array.from(arrlist)
      arrlist.map((_) => {
        const artId = Number(_.dataset.id)
        const findData =
          me.articleList.find((_) => {
            return _.id === artId
          }) || {}
        if (!findData.id) return
        const findDataImg =
          findData.mediaInfo && findData.mediaInfo.length
            ? findData.mediaInfo[0].img
            : ''
        const delImg = `<img class="squire-del-img" src='/static/img/<EMAIL>'>`
        let img = findDataImg
          ? `<img class="squire-article-img" src='${findDataImg}'>`
          : ''
        const mes = `<div class='squire-article-mes ${
          findDataImg ? 'squire-article-mes-padding' : ''
        }'>
      <p class='squire-article-title dotdotdot1'>${findData.title}</p>
      <p class='squire-article-tip'>${findData.viewNum}浏览 · ${
          findData.replycnt
        }评论<span class="squire-article-tag">文章</span></p>
      <p class='squire-article-tip'>作者：${findData.author}</p>
      </div>`
        _.innerHTML = `${img ? img : ''}${mes}${delImg}`
      })
    },
    // 侦测del-img 图标的点击事件
    getDelImg() {
      const me = this
      let imgAll = me.editorDom.querySelectorAll('.squire-del-img')
      imgAll = Array.from(imgAll)
      imgAll.map(function (value) {
        value.addEventListener('click', me.delContent) //
      })
      let shopAll = me.editorDom.querySelectorAll('.squire-shop-del-img')
      shopAll = Array.from(shopAll)
      shopAll.map(function (value) {
        value.addEventListener('click', me.delShopContent) //
      })
    },
    // 删除图片或者链接区域
    delContent(e) {
      let delDom = e.target.parentNode
      const classNameList = ['squire-link-content', 'squire-article-content']
      if (
        delDom.className.indexOf('squire-img-content') < 0 &&
        !classNameList.includes(delDom.className)
      ) {
        delDom = delDom.parentNode
      }
      this.editorDom.removeChild(delDom)
    },
    // 删除商品详情
    delShopContent(e) {
      console.log(e)
      this.$confirm('是否确认删除该商品', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const delDom = e.target.parentNode.parentNode
        this.editorDom.removeChild(delDom)
      })
    },
    // 监听picture-title 的click事件,因删除时会误删除此
    pictureKeyup() {
      const me = this
      let title = document.querySelectorAll('.squire-picture-title')
      title = Array.from(title)
      title.map(function (value) {
        value.addEventListener('click', me.clickContent) //
      })
    },
    clickContent(e) {
      setTimeout(() => {
        e.target.focus()
      }, 20)
    },
    // 复制其中内容提示
    clipboardSuccess() {
      this.$message({
        message: '内容整段拷贝成功',
        type: 'success',
        duration: 1500
      })
    },
    setMessageBoxNoCancel(content) {
      this.$confirm(content, '提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'warning',
        center: true
      })
    },
    // 插入文章(弹框)
    addDetailDialog() {
      this.$refs.SquireAddDetail.init()
    },
    addDetail(content) {
      this.editor.insertArticle(content)
      this.articleList.push(content)
      this.getDelImg()
    },
    //是否存在图片红框
    getIsFailIamge() {
      const redAll = this.editorDom.querySelectorAll('.red-border')
      return redAll && redAll.length > 0
    }
  },
  emits: ['squire', 'updateAddVideo']
}
</script>

<style lang="scss">
.squire-dialog-content {
  .image-content {
    display: inline-block;
    width: 90px;
    height: 90px;
    margin: 5px;
    position: relative;
    .imgs {
      display: inline-block;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .imgs-del {
      position: absolute;
      right: 0;
      top: 0;
      width: 25px;
      height: 25px;
    }
  }
}
</style>

<style lang="scss">
@import '../../styles/util.scss';
.squire-editing {
  min-width: 500px;
  margin-top: 20px;
  .red-border {
    border: 1px red solid;
  }
  .squire-del-img {
    cursor: pointer;
  }
  .editing-function {
    height: 40px;
    background-color: #fafafa;
    border: 1px solid #e5e5e5;
    .icon-section {
      display: inline-block;
      position: relative;
    }
    .copy-icon {
      width: 30px !important;
      height: 30px !important;
      margin: 5px;
    }
    .icon {
      cursor: pointer;
      display: inline-block;
      width: 40px;
      height: 40px;
    }
    .text-icon {
      display: inline-block;
      width: 24px;
      height: 40px;
    }
    .import-icon {
      display: inline-block;
      width: 38px;
      height: 38px;
      object-fit: none;
    }
    .text-right-icon {
      display: inline-block;
      width: 12px;
      height: 38px;
      position: relative;
      top: -2px;
    }
  }
  .squire-paragraph {
    @include english-word-break();
  }
  .video-tips {
    margin-top: 0.1px;
    .video-tip {
      line-height: 20px;
      margin: 5px 0;
      height: 20px;
    }
  }
  .squire-shop-content {
    width: 100%;
    height: 100px;
    margin-bottom: 10px;
    position: relative;
    .squire-shop-img {
      position: absolute;
      left: 0;
      top: 0;
      display: inline-block;
      width: 100px;
      height: 100px;
    }
    .squire-shop-mes {
      padding-left: 110px;
      line-height: 18px;
      margin: 0;
    }
    .squire-shop-tip {
      margin-top: 5px;
      font-size: 12px;
      color: #666;
    }
    .squire-shop-del-img-content {
      display: none;
      position: absolute;
      width: 50%;
      height: 50%;
      margin: 0 auto;
      top: 25%;
      left: 0;
      text-align: center;
      right: 0;
      background-color: rgba(0, 0, 0, 0.3);
    }
    .squire-shop-del-img {
      display: inline-block;
      width: 44px;
      height: 44px;
      background-image: url('../../assets/image/shop-close.png');
    }
  }
  .squire-shop-content:hover .squire-shop-del-img-content {
    display: block;
  }
  .squire-article-content {
    margin: 0 auto;
    width: 440px;
    height: 96px;
    padding: 8px;
    background-color: #f8f9fb;
    border-radius: 4px;
    position: relative;
    .squire-article-img {
      position: absolute;
      left: 8px;
      width: 80px;
      height: 80px;
      object-fit: cover;
    }
    .squire-article-mes-padding {
      padding-left: 88px;
    }
    p {
      margin: 0;
    }
    .squire-article-title {
      font-size: 16px;
      color: #333;
      line-height: 22px;
      margin-bottom: 5px;
    }
    .squire-article-tip {
      font-size: 12px;
      color: #666;
      line-height: 17px;
      margin-bottom: 5px;
    }
    .squire-article-tag {
      width: 30px;
      height: 17px;
      border-radius: 4px;
      padding: 1px 5px;
      border: 1px solid #cccccc;
      font-size: 10px;
      color: #ccc;
      line-height: 14px;
      margin-left: 4px;
    }
  }
}
.open-view-img-button {
  cursor: pointer;
  line-height: 24px;
  font-size: 16px;
  > div {
    &:hover {
      color: #000000;
    }
  }
}
</style>
