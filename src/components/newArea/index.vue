<template>
  <div class="newArea">
    <el-dialog
      v-model="dialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handlerClose"
      title="城市选择"
      width="850px"
    >
      <div class="content">
        <div class="content-select">
          <div>
            <span>城市过滤：</span
            ><el-input
              class="content-select-input"
              placeholder="请输入城市名称"
              v-model="filterText"
              clearable
            >
            </el-input>
          </div>
          <el-button style="height: 40px" type="info" plain @click="reset()"
            >重置</el-button
          >
        </div>
        <el-tree
          :data="treeData"
          :props="defaultProps"
          :default-checked-keys="defaultCheckedKeys"
          :default-expanded-keys="defaultExpandedKeys"
          :filter-node-method="filterNode"
          @check="check"
          show-checkbox
          node-key="name"
          accordion
          ref="tree"
        >
        </el-tree>
      </div>
      <div class="fooder">
        <el-button type="success" @click="confirm">确认</el-button>
        <el-button type="danger" @click="handlerClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
// import { cityInfo } from '@/utils/newCityinfo.js'
export default {
  name: 'NewMultiArea',
  props: {
    // 默认勾选项，只需要传递城市ID, 如果传入的是省份ID会默认勾选下面所有市 （传城市名回显啊）
    // defaultCheckedKeys: {
    //   type: Array,
    //   default: () => []
    // },
    //  是否勾选全国 true勾选全国
    isNationwide: {
      type: Boolean,
      default: false
    },
    // 是否单选 true单选，单选只针对处理市级选择，全国和省级无效,defaultCheckedKeys只取第一个数据作为默认项
    isSingle: {
      type: Boolean,
      default: false
    },
    //  是否展示全国勾选项 true展示全国勾选项
    isShowNationwide: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    // 数据筛选
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  data() {
    return {
      dialogVisible: false,
      treeData: [], // 树形图数据
      defaultProps: {
        children: 'citys',
        label: 'name'
      },
      filterText: '',
      checkAll: true, // 是否选中全国
      defaultExpandedKeys: [], //默认展开项
      defaultCheckedKeys: [] // 默认勾选项，只需要传递城市ID, 如果传入的是省份ID会默认勾选下面所有市
    }
  },
  mounted() {
    const cityInfoArr = JSON.parse(localStorage.getItem('cityMapList'))
    console.log('cityInfoArr=====', cityInfoArr)
    if (this.isShowNationwide) {
      cityInfoArr.unshift({
        id: '10000',
        name: '全国',
        citys: []
      })
    }
    cityInfoArr.map((item) => {
      item.cityLength = item.citys.length
      // 单选
      if (this.isSingle) {
        item.disabled = true
      }
    })
    this.treeData = cityInfoArr
  },
  methods: {
    // 当复选框被点击的时候触发
    check(data) {
      if (data.id === '10000') {
        if (this.checkAll) {
          this.reset()
          this.$refs.tree.setCheckedKeys(['全国'])
          this.checkAll = false
        } else {
          this.$refs.tree.setChecked('全国', false, true)
          this.checkAll = true
        }
      } else {
        this.checkAll = true
        this.$refs.tree.setChecked('全国', false, true)
        if (this.isSingle) {
          this.$refs.tree.setCheckedKeys([data.name])
        }
      }
    },
    // 数据筛选
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },

    // 提交数据
    confirm() {
      const treeList = this.$refs.tree.getCheckedNodes(false, true)
      console.log('treeList=====', treeList)
      // 处理省级数据
      const provinceArr = []
      treeList.map((item) => {
        const provinceObj = {}
        // eslint-disable-next-line no-prototype-builtins
        if (item.hasOwnProperty('citys')) {
          provinceObj.provinceName = item.name
          provinceObj.provinceId = item.id
          provinceObj.cityLength = item.cityLength || 0
          provinceArr.push(provinceObj)
        }
      })
      console.log('provinceArr====', provinceArr)
      // 处理市级数据
      provinceArr.map((item) => {
        item.cityList = []
        treeList.map((it) => {
          // eslint-disable-next-line no-prototype-builtins
          if (
            !it.hasOwnProperty('citys') &&
            item.provinceId === it.provinceId
          ) {
            const cityObj = {}
            cityObj.cityId = it.id
            cityObj.provinceId = it.provinceId
            cityObj.cityName = it.name
            item.cityList.push(cityObj)
          }
        })
      })
      provinceArr.map((item) => {
        if (item.cityLength === item.cityList.length) {
          item.isAllCity = true
        } else {
          item.isAllCity = false
        }
      })
      $emit(this, 'getCityData', { cityData: provinceArr })
      this.reset()
      this.dialogVisible = false
    },
    // 初始化打开弹窗
    init(info) {
      const me = this
      me.dialogVisible = true
      this.defaultExpandedKeys = []
      this.defaultCheckedKeys = info?.defaultCheckedKeys || []
      this.$nextTick(() => {
        // 单选关闭所有展开项
        const treeAllNodes = this.$refs.tree.store._getAllNodes()
        if (treeAllNodes && treeAllNodes.length > 0) {
          treeAllNodes.map((item) => (item.expanded = false))
        }
        if (this.isSingle) {
          console.log(this.isSingle, 'this.isSingle')
          this.reset()
          // 单选关闭所有展开项
          // const treeAllNodes = this.$refs.tree.store._getAllNodes()
          // if (treeAllNodes && treeAllNodes.length > 0) {
          //   treeAllNodes.map(item => (item.expanded = false))
          // }
          if (this.defaultCheckedKeys.length > 0) {
            this.$refs.tree.setCheckedKeys([this.defaultCheckedKeys[0]])
            // 展开单个选择项
            this.defaultExpandedKeys = [this.defaultCheckedKeys[0]]
          }
        } else {
          if (this.defaultCheckedKeys.length > 0) {
            // this.$refs.tree.setCheckedKeys([this.defaultCheckedKeys[0]])
            // 展开单个选择项
            // this.defaultExpandedKeys = [this.defaultCheckedKeys[0]]
          } else {
            // 勾选全国
            if (this.isNationwide) {
              this.reset()
              this.$refs.tree.setCheckedKeys(['全国'])
            }
          }
        }
      })
    },
    // 关闭弹窗
    handlerClose() {
      this.reset()
      this.dialogVisible = false
    },
    // 重置数据
    reset() {
      this.$refs.tree && this.$refs.tree.setCheckedKeys([])
    }
  },
  emits: ['getCityData']
}
</script>

<style lang="scss">
.newArea {
  .content {
    .el-tree {
      width: 800px;
      display: flex;
      flex-wrap: wrap;
      .el-tree-node {
        width: 200px;
      }
    }
  }
  .content-select {
    border-bottom: 1px solid rgb(228, 227, 223);
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    .content-select-input {
      width: 200px;
      margin-bottom: 10px;
      margin-left: 20px;
    }
  }
  .fooder {
    margin-top: 20px;
    text-align: center;
  }
}
</style>
