<template>
  <div v-loading="loading" class="search-user">
    <el-form ref="searchUser" :model="ruleForm" :inline="true" class="search">
      <el-form-item
        :label="name"
        :label-width="labelWidth"
        :required="required"
      >
        <el-select
          v-model="ruleForm.uid"
          :remote-method="remoteMethodUser"
          :loading="loading"
          :placeholder="placeholder"
          :style="{ width: selectWidth }"
          :disabled="isDisabled"
          filterable
          remote
          clearable
          style="position: relative"
          @change="setValue()"
        >
          <el-option v-if="isAll" label="全部" value="" />
          <el-option
            v-for="item in userOptions"
            :key="item.uid"
            :label="item.username"
            :value="item.uid"
          />
          <div
            class="text-center"
            style="
              position: sticky;
              background: #fff;
              height: 30px;
              top: 0;
              z-index: 1;
            "
          >
            <el-pagination
              :current-page="page"
              :total="total"
              layout="total, prev, next"
              style="justify-content: center"
              @current-change="currentChange"
            />
          </div>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { $emit } from '../../utils/gogocodeTransfer'
import { selectedUser } from '@/api/user'
export default {
  name: 'SearchUser',
  props: {
    name: {
      type: String,
      default: '用户名'
    },
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    labelWidth: {
      type: String,
      default: '60px'
    },
    selectWidth: {
      type: String,
      default: '160px'
    },
    required: {
      type: Boolean,
      default: false
    },
    isAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isDisabled: false,
      searchNameValue: '',
      page: 1,
      total: 0,
      loading: false,
      userOptions: [],
      ruleForm: {
        uid: '',
        name: ''
      }
    }
  },
  methods: {
    currentChange(page) {
      this.page = page
      this.loading = true
      this.$tools.debounce(this.searchUser, 300)()
    },
    remoteMethodUser(query) {
      this.loading = true
      this.page = 1
      this.$tools.debounce(() => this.searchUser(query), 300)()
    },
    searchUser(query = '') {
      selectedUser({
        username: query || this.searchNameValue,
        page: this.page,
        limit: 20
      })
        .then((response) => {
          this.userOptions = []
          if (response.data.code === 0) {
            this.userOptions = response.data.data.list
            this.total = response.data.data.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 设置数据并返回
    setValue() {
      const me = this
      if (me.ruleForm.uid) {
        const user = this.userOptions.find((item) => {
          return item.uid === me.ruleForm.uid
        })
        if (user) {
          me.ruleForm.name = user.username
          me.ruleForm.uid = user.uid
        }
      }
      $emit(me, 'sendData', me.ruleForm.uid, me.ruleForm.name)
    },
    // 设置userOptions 数据
    setUserOptions(data) {
      console.log(data)
      this.userOptions = []
      this.userOptions.push(data)
      this.ruleForm.uid = this.userOptions[0].uid
    },
    // 清除数据
    clearData() {
      this.ruleForm.uid = ''
      this.ruleForm.name = ''
      this.searchNameValue = ''
      this.userOptions = []
      this.page = 1
      this.total = 0
    },
    setDisabled(status) {
      this.isDisabled = status
    }
  },
  emits: ['sendData']
}
</script>

<style lang="scss">
.search-user {
  display: inline-block;
}
</style>
