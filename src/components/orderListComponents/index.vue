<template>
  <div v-loading="loading" class="dealerOrderList" style="padding: 10px 20px">
    <header class="action" style="margin-bottom: 10px">
      <el-form
        ref="activitySearch"
        :model="ruleForm"
        :inline="true"
        class="activitySearch"
      >
        <el-form-item label="主订单号" prop="parentOrderNum">
          <el-input
            v-model="ruleForm.parentOrderNum"
            type="text"
            placeholder="请输入主订单号"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="订单号" prop="orderNum">
          <el-input
            v-model="ruleForm.orderNum"
            type="text"
            placeholder="请输入订单号"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="商家ID" prop="shopId">
          <el-input
            v-model="ruleForm.shopId"
            type="text"
            maxlength="11"
            placeholder="请输入商家ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="经销商名称">
          <el-select
            v-model="ruleForm.shopId"
            :remote-method="remoteMethodShop"
            placeholder="请输入经销商名称"
            filterable
            remote
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in shopList"
              :key="item.shopId"
              :label="item.name"
              :value="item.shopId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="业务类型" v-if="['1', '2'].includes(formType)">
          <el-select
            v-model="ruleForm.businessType"
            clearable
            placeholder="请选择业务类型"
            style="width: 160px"
          >
            <el-option
              v-for="(value, index) in businessTypeListEnmu"
              :key="value"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发票状态">
          <el-select v-model="ruleForm.invoiceStatus" style="width: 100px">
            <el-option
              v-for="(value, index) in invoiceTypeEnum"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="流水支付状态">
          <el-select
            v-model="ruleForm.payStatus"
            clearable
            placeholder="请选择流水支付状态"
            style="width: 150px"
          >
            <el-option
              v-for="(value, index) in paymentStatusNew"
              :key="value"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="流水支付渠道">
          <el-select
            v-model="ruleForm.channelCode"
            clearable
            placeholder="请选择流水支付渠道"
            style="width: 150px"
          >
            <el-option
              v-for="(value, index) in channelCodeList"
              :key="value"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="流水支付方式">
          <el-select
            v-model="ruleForm.wayCode"
            clearable
            placeholder="请选择流水支付方式"
            style="width: 150px"
          >
            <el-option
              v-for="(value, index) in wayCodeList"
              :key="value"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="流水动账账户">
          <el-select
            v-model="ruleForm.transactionAccount"
            clearable
            placeholder="请选择流水动账账户"
            style="width: 200px"
          >
            <el-option
              v-for="(value, index) in transactionAccountList"
              :key="value"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="动账时间">
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="payTimeDateRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="开票时间">
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="invoiceTimeDateRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="退款时间" v-if="['1'].includes(formType)">
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="refundTimeDateRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button @click="initGetList">重置</el-button>
          <!-- 导出功能权限只有刘学磊(uid)===3、uid=78（胡晶晶）uid=45（王长文）账号有（parseInt(uid)===25 || parseInt(uid)===101 || parseInt(uid)===102 财务专属） || parseInt(uid)===115 (刘敏)-->
          <!-- uid=323（高睿琦） -->
          <el-button type="success" @click="exportExcel()">导出</el-button>
        </el-form-item>
      </el-form>
    </header>
    <div class="main">
      <el-table
        ref="orderList"
        :data="orderList"
        highlight-current-row
        row-key="orderNum"
        :expand-row-keys="expands"
        border
        class="orderList"
      >
        <el-table-column type="expand">
          <template #default="props">
            <el-row v-if="props.row.detailList.length">
              <el-table
                :data="props.row.detailList"
                border
                style="width: 1050px"
                row-class-name="orderListTableRowClassName"
                header-row-class-name="orderListTableHeadRowClassName"
              >
                <el-table-column
                  label="支付流水编号"
                  width="240px"
                  align="center"
                  prop="transactionOrderId"
                />
                <el-table-column
                  prop="payPlatform"
                  label="流水类型"
                  align="center"
                  width="100px"
                >
                  <template v-slot="scope">
                    {{ serialsTypeList[scope.row.serialsType] }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="payPlatform"
                  label="交易状态"
                  align="center"
                  width="100px"
                >
                  <template v-slot="scope">
                    {{ transactionStatus[scope.row.transactionStatus] }}
                  </template>
                </el-table-column>

                <el-table-column
                  label="交易金额"
                  prop="transactionPrice"
                  align="center"
                  width="100px"
                />
                <el-table-column
                  prop="channelCode"
                  label="支付渠道"
                  align="center"
                  width="100px"
                >
                  <template v-slot="scope">
                    {{ channelCodeListEnum[scope.row.channelCode] }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="wayCode"
                  label="支付方式"
                  align="center"
                  width="100px"
                >
                  <template v-slot="scope">
                    {{ wayCodeListEnum[scope.row.wayCode] }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="动账账户"
                  prop="transactionAccount"
                  align="center"
                  width="150px"
                />
                <el-table-column
                  prop
                  label="动账时间"
                  width="160px"
                  align="center"
                >
                  <template v-slot="scope">
                    <span>{{ $filters.timeFullS(scope.row.successTime) }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column
          prop="orderNum"
          label="订单号"
          align="center"
          width="200px"
        />
        <el-table-column
          prop="shopId"
          label="商家ID"
          align="center"
          width="100px"
        />

        <el-table-column
          prop="shopName"
          label="经销商名称"
          align="center"
          width="180px"
        >
          <template v-slot="scope">
            <div
              style="color: blue; cursor: pointer"
              @click="JumpDetails(scope.row)"
            >
              {{ scope.row.shopName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="receiveNumber"
          label="收款账户"
          align="center"
          width="120px"
        />
        <el-table-column
          prop="payUser"
          label="付款人"
          align="center"
          width="120px"
        />

        <el-table-column prop label="付款时间" align="center" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.payTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="orderChannelCode"
          label="支付渠道"
          align="center"
          width="100px"
        >
          <template v-slot="scope">
            {{ channelCodeListEnum[scope.row.orderChannelCode] }}
          </template>
        </el-table-column>
        <el-table-column
          prop="orderWayCode"
          label="支付方式"
          align="center"
          width="100px"
        >
          <template v-slot="scope">
            {{ wayCodeListEnum[scope.row.orderWayCode] }}
          </template>
        </el-table-column>

        <el-table-column
          prop="price"
          label="订单金额"
          align="center"
          width="120px"
        />
        <el-table-column label="开票金额" align="center" width="100px">
          <template v-slot="scope">
            {{ scope.row.allowInvoicePrice ? scope.row.allowInvoicePrice : '' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="transactionNumber"
          label="订单流水编号"
          align="center"
          width="140px"
        />
        <el-table-column
          prop="payNumber"
          label="付款账号"
          align="center"
          width="150px"
        />
        <el-table-column
          prop="status"
          align="center"
          label="支付状态"
          width="100px"
        >
          <template v-slot="scope">
            <span>{{ convertPayOrderStatusNew[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="invoiceNo"
          label="发票编号"
          align="center"
          width="150px"
        />
        <el-table-column
          prop="invoiceStatus"
          align="center"
          label="发票状态"
          width="100"
        >
          <template v-slot="scope">
            <span>{{ invoiceType[scope.row.invoiceStatus] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop label="开票时间" align="center" width="160px">
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.invoiceTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="['1'].includes(formType)"
          prop
          label="权益开始时间"
          align="center"
          width="160px"
        >
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.beginTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="['1'].includes(formType)"
          prop
          label="权益结束时间"
          align="center"
          width="160px"
        >
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.deadlineTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column
          v-if="['1', '2', '4'].includes(formType)"
          prop
          label="退款时间"
          align="center"
          width="160px"
        >
          <template v-slot="scope">
            <span>{{ $filters.timeFullS(scope.row.refundTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column
          v-if="['1', '2'].includes(formType)"
          prop="businessType"
          label="业务类型"
          align="center"
          width="140px"
        >
          <template v-slot="scope">
            <span>{{ convertBusinessTypeList[scope.row.businessType] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="['2'].includes(formType)"
          prop="goldPrice"
          label="支付金币"
          align="center"
          width="150px"
        />
        <el-table-column
          v-if="['4'].includes(formType)"
          prop="usedGoldPrice"
          label="消耗金币数"
          align="center"
          width="150px"
        />
        <el-table-column
          prop="parentOrderNum"
          label="主订单号"
          align="center"
          width="180px"
        />
      </el-table>
      <el-footer class="text-center">
        <div v-if="['1', '2'].includes(formType)">
          <span style="margin-right: 40px"
            >合计：{{
              $filters.toThousandFilter(totalData.totalSummaryAmount || 0)
            }}</span
          >
          <span style="margin-right: 40px"
            >总收入金额：{{
              $filters.toThousandFilter(totalData.totalPayAmount || 0)
            }}</span
          >
          <!-- <span style="margin-right: 40px"
            >总缴费金额：{{
              $filters.toThousandFilter(totalData.totalSummaryAmount || 0)
            }}</span
          > -->
          <span style="margin-right: 40px"
            >总退款金额：{{
              $filters.toThousandFilter(totalData.totalRefundAmount || 0)
            }}</span
          >
          <span style="margin-right: 40px"
            >总转移出金额：{{
              $filters.toThousandFilter(totalData.totalTransferOutAmount || 0)
            }}</span
          >
          <span
            >总转移入金额：{{
              $filters.toThousandFilter(totalData.totalTransferInAmount || 0)
            }}</span
          >
        </div>
        <div v-if="['4'].includes(formType)">
          <span
            >累计订单：{{
              $filters.toThousandFilter(totalData.totalOrderAmount || 0)
            }}</span
          >
          <span style="margin: 0 40px"
            >消耗金币：{{
              $filters.toThousandFilter(totalData.totalusedGoldAmount || 0)
            }}</span
          >
          <span style="margin: 0 40px"
            >合计：{{
              $filters.toThousandFilter(totalData.totalSummaryAmount || 0)
            }}</span
          >
          <span style="margin: 0 40px"
            >总支付金额：{{
              $filters.toThousandFilter(totalData.totalPayAmount || 0)
            }}</span
          >
          <span
            >总退款金额：{{
              $filters.toThousandFilter(totalData.totalRefundAmount || 0)
            }}</span
          >
        </div>
        <div v-if="['3'].includes(formType)">
          <span style="margin-right: 40px"
            >总收入金额：{{
              $filters.toThousandFilter(totalData.totalPayAmount || 0)
            }}</span
          >
          <span style="margin-right: 40px"
            >总计订单金额：{{
              $filters.toThousandFilter(totalData.totalOrderPrice || 0)
            }}</span
          >
          <span
            >总汇总金额：{{
              $filters.toThousandFilter(totalData.totalSummaryAmount || 0)
            }}</span
          >
        </div>
        <el-pagination
          v-model:current-page="pageNum"
          :page-size="20"
          :page-sizes="[10, 20, 40, 60]"
          :total="total"
          background
          layout="total, prev, pager, next, jumper"
          style="text-align: center; margin-top: 10px"
          @size-change="currentChange"
          @current-change="currentChange"
        />
      </el-footer>
    </div>
  </div>
</template>

<script>
import {
  paymentStatusNew,
  payOrderStatusNew,
  invoiceTypeAllEnum,
  businessTypeList,
  businessTypeVipList,
  businessTypeClueList,
  serialsTypeList,
  transactionStatus,
  channelCodeList,
  wayCodeList,
  transactionAccountList
} from '@/utils/enum'
import { convertKeyValueEnum } from '@/utils/convert'
import {
  orderRechargeGetList,
  orderRechargeExportList,
  orderRechargeGetSummaryInfo,
  GetShopApplyByName
} from '@/api/garage'
import { forwardPickerOptions } from '@/utils/configData'
export default {
  data() {
    return {
      pickerOptions: forwardPickerOptions,
      // 支付状态
      paymentStatusNew: { ...{ 全部: '', ...paymentStatusNew } },
      convertPayOrderStatusNew: convertKeyValueEnum(payOrderStatusNew),
      // 业务类型
      businessTypeList: {},
      convertBusinessTypeList: convertKeyValueEnum(businessTypeList),
      // 发票状态
      invoiceTypeEnum: invoiceTypeAllEnum,
      invoiceType: convertKeyValueEnum(invoiceTypeAllEnum),
      // 流水类型
      serialsTypeList,
      // 交易状态
      transactionStatus,
      // 流水支付渠道
      channelCodeList: { ...{ 全部: '', ...channelCodeList } },
      channelCodeListEnum: convertKeyValueEnum(channelCodeList),
      // 流水支付方式
      wayCodeList: { ...{ 全部: '', ...wayCodeList } },
      wayCodeListEnum: convertKeyValueEnum(wayCodeList),
      // 流水动账账户
      transactionAccountList,
      ruleForm: {
        orderNum: '',
        shopId: '',
        shopName: '',
        businessType: '',
        price: '',
        payStatus: '',
        invoiceStatus: '', // 开票状态 1：已开 0：未开
        successTimeBegin: '',
        successTimeEnd: '',
        invoiceTimeBegin: '',
        invoiceTimeEnd: '',
        refundTimeBegin: '',
        refundTimeEnd: '',
        pageNum: 1,
        pageSize: 20,
        channelCode: '',
        wayCode: '',
        transactionAccount: '',
        parentOrderNum: '' // 主订单号
      },

      // 车辆认证列表数据
      orderList: [],
      // 加载状态
      loading: false,
      // 页码
      pageNum: 1,
      // 总数
      total: 0,
      // 总金额
      totalData: {},
      shopList: [],
      expands: [], // 展开行keys
      exportType: {
        1: {
          title: '会员费充值订单明细(新)',
          header: ['退款时间', '业务类型', '权益开始时间', '权益结束时间'],
          filterVal: ['refundTime', 'businessType', 'beginTime', 'deadlineTime']
        },
        2: {
          title: '线索包充值订单明细(新)',
          header: ['业务类型', '支付金币'],
          filterVal: ['businessType', 'goldPrice']
        },
        3: {
          title: '金币充值订单明细(新)',
          header: ['金币数量'],
          filterVal: ['goldNumber']
        },
        4: {
          title: '广告费充值订单明细(新)',
          header: ['消耗金币数'],
          filterVal: ['usedGoldPrice']
        }
      },
      businessTypeListEnmu: {}
    }
  },
  name: 'DealerOrderListNew',
  props: {
    formType: {
      type: String,
      default: ''
    },
    orderNum: {
      type: String,
      default: ''
    }
  },
  computed: {
    // 权益结束时间 todo
    deadlineDateRange: {
      get() {
        if (this.ruleForm.applyStartDate && this.ruleForm.applyEndDate) {
          return [this.ruleForm.applyStartDate, this.ruleForm.applyEndDate]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.applyStartDate = value[0]
          this.ruleForm.applyEndDate = value[1]
        } else {
          this.ruleForm.applyStartDate = ''
          this.ruleForm.applyEndDate = ''
        }
      }
    },

    // 开票时间
    invoiceTimeDateRange: {
      get() {
        if (this.ruleForm.invoiceTimeBegin && this.ruleForm.invoiceTimeEnd) {
          return [this.ruleForm.invoiceTimeBegin, this.ruleForm.invoiceTimeEnd]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.invoiceTimeBegin = value[0]
          this.ruleForm.invoiceTimeEnd = value[1]
        } else {
          this.ruleForm.invoiceTimeBegin = ''
          this.ruleForm.invoiceTimeEnd = ''
        }
      }
    },
    // 退款时间
    refundTimeDateRange: {
      get() {
        if (this.ruleForm.refundTimeBegin && this.ruleForm.refundTimeEnd) {
          return [this.ruleForm.refundTimeBegin, this.ruleForm.refundTimeEnd]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.refundTimeBegin = value[0]
          this.ruleForm.refundTimeEnd = value[1]
        } else {
          this.ruleForm.refundTimeBegin = ''
          this.ruleForm.refundTimeEnd = ''
        }
      }
    },
    // 动账时间
    payTimeDateRange: {
      get() {
        if (this.ruleForm.successTimeBegin && this.ruleForm.successTimeEnd) {
          return [this.ruleForm.successTimeBegin, this.ruleForm.successTimeEnd]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.successTimeBegin = value[0]
          this.ruleForm.successTimeEnd = value[1]
        } else {
          this.ruleForm.successTimeBegin = ''
          this.ruleForm.successTimeEnd = ''
        }
      }
    }
  },
  mounted() {
    const me = this
    if (me.$route.query && me.$route.query.orderNum) {
      me.ruleForm.orderNum = me.$route.query.orderNum
    }
    me.getOrderList({ pageNum: me.pageNum })
    me.getSummaryInfo()
    const typeList =
      this.formType === '1' ? businessTypeVipList : businessTypeClueList
    this.businessTypeListEnmu = { ...{ 全部: '', ...typeList } }
  },
  methods: {
    // 设置class 隐藏没有二级分类
    getRowClassName({ row, rowIndex }) {
      if (!(row.detailList && row.detailList.length > 1)) {
        return 'row-expand-cover'
      }
    },
    // 设置class
    tableRowClassName({ row, rowIndex }) {
      return 'warning-expand-row'
    },
    // 查询经销商名称索引
    remoteMethodShop(query) {
      this.loading = true
      this.$tools.debounce(() => this.getShopListOne(query), 300)()
    },
    // 查询经销商
    getShopListOne(query) {
      const me = this
      GetShopApplyByName({
        shopName: query, // 经销商名称
        limit: 100
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.shopList = []
            const result = response.data.data
            result.map(function (value) {
              const newObj = {
                name: value.shopName,
                shopId: value.shopId
              }
              me.shopList.push(newObj)
              console.log(me.shopList)
            })
          }
        })
        .finally((_) => {
          me.loading = false
        })
    },
    // 查看发票详情
    skipInvoiceDetail(item) {
      this.$router.push({
        path: 'invoiceDetail',
        query: {
          orderNum: item.orderNum, // 订单编号
          type: 'check'
        }
      })
    },
    // 双击点中
    JumpDetails(data) {
      const id = data && data.shopId
      window.open(
        `https://oss.corp.mddmoto.com/#/dealerManagement/DistributorDetails?id=${id}`
      )
    },
    // 获取汇总数据
    getSummaryInfo() {
      const me = this
      const requestParams = {
        ...me.ruleForm,
        type: this.formType
      }
      orderRechargeGetSummaryInfo(requestParams).then((res) => {
        const data = res.data
        me.totalData = data.data
      })
    },
    // 获取列表数据
    getOrderList(paramsObj) {
      const me = this
      const requestParams = {
        ...me.ruleForm,
        ...paramsObj,
        type: this.formType,
        export: 0
      }
      me.expands = []
      me.loading = true
      orderRechargeGetList(requestParams)
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            me.orderList = data?.listData || []
            me.orderList.map((item) => {
              if (item.detailList && item.detailList.length > 1) {
                me.expands.push(item.orderNum)
              }
            })

            me.pageNum = requestParams.pageNum
            me.total = data?.total
          }
        })
        .catch((err) => {
          me.$message.error(err.message)
        })
        .finally(() => {
          me.loading = false
        })
    },
    // 更新页码
    currentChange(pageNum) {
      this.pageNum = pageNum
      this.orderList = []
      this.getOrderList({
        pageNum: pageNum
      })
    },
    // 查询
    search() {
      const me = this
      me.pageNum = 1
      me.orderList = []
      me.getOrderList({ pageNum: 1 })
      me.getSummaryInfo()
    },
    // 重置
    initGetList() {
      this.pageNum = 1
      this.setPostData()
      this.orderList = []
      this.getOrderList({ pageNum: 1 })
      this.getSummaryInfo()
    },
    // 设置发送数据
    setPostData() {
      const me = this
      me.ruleForm = Object.assign(me.ruleForm, {
        orderNum: '',
        shopId: '',
        shopName: '',
        businessType: '',
        price: '',
        payStatus: '',
        invoiceStatus: '',
        successTimeBegin: '',
        successTimeEnd: '',
        invoiceTimeBegin: '',
        invoiceTimeEnd: '',
        refundTimeBegin: '',
        refundTimeEnd: '',
        pageNum: 1,
        pageSize: 20,
        channelCode: '',
        wayCode: '',
        transactionAccount: '',
        parentOrderNum: ''
      })
    },
    // 导出Excel
    exportExcel() {
      this.handleDownload()
    },
    handleDownload() {
      const me = this
      me.$confirm('你确认导出到Excel么', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '正在导出，请稍等......',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          const requestParams = {
            ...me.ruleForm,
            pageNum: 1,
            pageSize: 999,
            type: this.formType,
            export: 1
          }
          orderRechargeExportList(requestParams)
            .then(async (res) => {
              const data = res.data.data.listData || []
              if (!data || data.length === 0) {
                loading.close()
                me.$message.success('暂无数据可以导出')
                return
              }
              const { export_json_to_excel } = await import(
                '@/vendor/Export2Excel'
              )
              // 导出的表头
              const commonHeader = [
                '订单号',
                '商家ID',
                '经销商名称',
                '收款账户',
                '付款人',

                '付款时间',
                '支付渠道',
                '支付方式',
                '订单金额',
                '开票金额',
                '订单流水编号',

                '付款账号',
                '支付状态',
                '发票编号',
                '发票状态',
                '开票时间'
              ]
              const commonFilterVal = [
                'orderNum',
                'shopId',
                'shopName',
                'receiveNumber',
                'payUser',

                'payTime',
                'orderChannelCode',
                'orderWayCode',
                'price',
                'allowInvoicePrice',
                'transactionNumber',

                'payNumber',
                'status',
                'invoiceNo',
                'invoiceStatus',
                'invoiceTime'
              ]

              // 导出的表头
              const tHeader = [
                ...commonHeader,
                ...this.exportType[this.formType].header,
                ...['主订单号']
              ]
              const filterVal = [
                ...commonFilterVal,
                ...this.exportType[this.formType].filterVal,
                ...['parentOrderNum']
              ]
              const exportData = this.formatJson(filterVal, data)
              export_json_to_excel(
                tHeader,
                exportData,
                this.exportType[this.formType].title
              )
              me.$message.success('导出成功')
              loading.close()
            })
            .finally((_) => {
              loading.close()
            })
        })
        .catch()
    },

    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          if (j === 'orderChannelCode') {
            return this.channelCodeListEnum[v[j]]
          }
          if (j === 'orderWayCode') {
            return this.wayCodeListEnum[v[j]]
          }
          if (j === 'status') {
            return this.convertPayOrderStatusNew[v[j]]
          }
          if (j === 'invoiceStatus') {
            return this.invoiceType[v[j]]
          }
          if (j === 'businessType') {
            return this.convertBusinessTypeList[v[j]]
          }
          return v[j]
        })
      )
    }
  }
}
</script>

<style>
.el-table .warning-expand-row {
  background: rgb(245, 245, 227);
}
.row-expand-cover .el-table__expand-icon {
  visibility: hidden;
}
.orderListTableRowClassName,
.orderListTableHeadRowClassName {
  --el-table-tr-bg-color: #eaf5fe;
  --el-table-header-bg-color: #eaf5fe;
}
</style>
<style lang="scss" scoped>
:deep(.el-table__expanded-cell) {
  padding: 0;
  background: #eaf5fe;
}
.orderList {
  width: 100%;
  height: 65vh;
}
</style>
