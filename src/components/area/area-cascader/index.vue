<template>
  <div v-loading="isLoading">
    <el-cascader
      ref="cascader"
      v-model="value"
      :props="props"
      :options="options"
      @change="handleChange"
      @expand-change="expandChange"
    ></el-cascader>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer'
import {
  getProvinceListMap,
  getCityListMap,
  getDistinctListMap
} from '@/api/search-map'
import { cloneDeep } from 'lodash-es'
export default {
  name: 'AreaCascader',
  props: {
    needDistinct: {
      // 是否需要区
      type: Boolean,
      default: false
    },
    isShowAll: {
      // 是否显示全部
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isLoading: false,
      value: [],
      options: [],
      provinceList: [],
      provinceCode: null,
      cityList: [],
      cityCode: null,
      distinctList: [],
      props: {}
    }
  },
  computed: {
    // options() {
    //   const rootList = cloneDeep(this.provinceList)
    //   if (this.provinceCode && this.cityList.length) {
    //     const province = rootList.find(
    //       (list) => list.value === this.provinceCode
    //     )
    //     province.children.push(...this.cityList)
    //   }
    //   if (this.cityCode && this.distinctList.length) {
    //     const city = this.cityList.find((list) => list.value === this.cityCode)
    //     city.children.push(...this.distinctList)
    //   }
    //   console.log(rootList)
    //   return rootList
    // }
  },
  mounted() {
    this.getProvince()
  },
  methods: {
    // 获取省
    async getProvince() {
      this.isLoading = true
      const list = await getProvinceListMap()
      this.provinceList = list.map((province) => {
        return {
          value: province.provinceCode,
          label: province.name,
          children: []
        }
      })

      this.options = this.provinceList
      this.isLoading = false
      const me = this
      this.props = {
        lazy: true,
        async lazyLoad(node, resolve) {
          const { value, level, pathValues } = node
          if ((!me.needDistinct && level === 2) || level === 3)
            return resolve([])
          if (level === 1) {
            const list = await getCityListMap(value)
            const cityList = list.map((city) => ({
              value: city.cityCode,
              label: city.name,
              children: me.needDistinct ? [] : undefined,
              leaf: !me.needDistinct
            }))
            me.cityList = cityList
            resolve(cityList)
          } else {
            const list = await getDistinctListMap(pathValues[0], pathValues[1])
            me.distinctList = list.map((distinct) => ({
              value: distinct.adCode,
              label: distinct.name,
              leaf: true
            }))
            resolve(me.distinctList)
          }
          // setTimeout(() => {
          //   const nodes = Array.from({ length: level + 1 }).map((item) => ({
          //     value: ++id,
          //     label: `Option - ${id}`,
          //     leaf: level >= 2
          //   }))
          //   // Invoke `resolve` callback to return the child nodes data and indicate the loading is finished.
          //   resolve(nodes)
          // }, 1000)
        }
      }
    },
    // 获取市
    async getCity() {
      this.isLoading = true
      const list = await getCityListMap(this.provinceCode)
      this.cityList = list.map((city) => ({
        value: city.cityCode,
        label: city.name,
        children: this.needDistinct ? [] : undefined
      }))
      if (this.isShowAll) {
        this.cityList.unshift({
          value: '',
          label: '全部',
          children: undefined
        })
      }
      this.isLoading = false
    },
    // 获取区
    async getDistinct() {
      this.isLoading = true
      const list = await getDistinctListMap(this.provinceCode, this.cityCode)
      this.distinctList = list.map((distinct) => ({
        value: distinct.adCode,
        label: distinct.name
      }))
      this.isLoading = false
    },
    // 当展开节点发生变化时触发
    async expandChange(arr) {
      if (arr.length === 1) {
        this.provinceCode = arr[0]
        this.cityList = []
        this.distinctList = []
        await this.getCity()
      } else if (arr.length === 2 && this.needDistinct) {
        this.distinctList = []
        this.cityCode = arr[1]
        await this.getDistinct()
      }
    },
    async handleChange(event) {
      if (this.value.length === 1) return
      const province = this.value[0]
      const city = this.value[1]
      const provinceName = this.provinceList.find((_) => _.value === province)
      const cityName = this.cityList.find((_) => _.value === city)
      let distinctName = {}
      if (this.needDistinct) {
        const distinc = this.value[2]
        distinctName = this.distinctList.find((_) => _.value === distinc) || {}
      }
      await this.$nextTick()
      $emit(this, 'on-update', {
        provinceName: provinceName.label,
        cityName: cityName?.label || '',
        distinctName: distinctName?.label || '',
        provinceCode: provinceName?.value || '',
        cityCode: cityName?.value || '',
        distinctCode: distinctName.value || ''
      })
    },
    clear() {
      this.value = []
    }
  },
  emits: ['on-update']
}
</script>
