<template>
  <el-dialog
    v-model="showStatus"
    :title="dialogTitle"
    :width="dialogWidth"
    append-to-body
  >
    <div class="content">
      <el-select
        v-model="provinceName"
        placeholder="请选择省份"
        clearable
        @change="
          () => {
            change(provinceName, 'province')
            clearDataName = 'province'
          }
        "
        @clear="clearDataName = 'province'"
        class="select-width"
      >
        <el-option
          v-for="(value, index) in provinceList"
          :key="index"
          :label="value.name"
          :value="value.provinceCode"
        />
      </el-select>
      <el-select
        v-if="showCityName"
        v-model="cityName"
        :disabled="cityStatus"
        placeholder="请选择城市"
        clearable
        @change="
          () => {
            change(cityName, 'city')
            clearDataName = 'city'
          }
        "
        @clear="clearDataName = 'city'"
        class="select-width"
      >
        <el-option
          v-for="(value, index) in cityList"
          :key="index"
          :label="value.name"
          :value="value.cityCode"
        />
      </el-select>
      <el-select
        v-if="showDistName"
        v-model="distName"
        :disabled="distStatus"
        placeholder="请选择区域"
        clearable
        @change="change(distName, 'dist')"
        @clear="distName = ''"
        class="select-width"
      >
        <el-option
          v-for="(value, index) in distList"
          :key="index"
          :label="value.name"
          :value="value.adCode"
        />
      </el-select>
      <el-button type="success" @click="confirm(true)">确认</el-button>
      <el-button type="danger" @click="confirm(false)">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { $emit } from '../../utils/gogocodeTransfer'
import { GetArea } from '@/api/searchMap'

export default {
  name: 'CArea',
  props: {
    dialogTitle: {
      type: String,
      default: '选择省市区位置'
    }
  },
  data() {
    return {
      provinceName: '', // 省级名
      provinceCode: '', // 省份代码
      cityName: '', // 城市位置
      cityCode: '', // 城市代码
      distName: '', // 区位置
      distCode: '', // 区代码
      index: '', // 索引值，getName 方法所用，回传
      clearDataName: '', // 清除名称为
      provinceList: [], // 省级列表
      cityList: [], // 城市列表
      distList: [], // 区列表
      showStatus: false, // 是否显示
      isShowAll: false, // 是否显示全部
      cityStatus: false, // 城市选择是否禁用
      distStatus: false, // 区域选择是否禁用
      showCityName: true, // 是否显示城市
      showDistName: true, // 是否显示区域
      setNamenum: 0 // 变更num
    }
  },
  computed: {
    dialogWidth() {
      let width = 215
      let num = 1
      if (this.showCityName) {
        num++
      }
      if (this.showDistName) {
        num++
      }
      width = width + num * 215
      return width + 'px'
    }
  },
  watch: {
    provinceCode(value) {
      if (value && this.showCityName) {
        this.setData(value, this.provinceList, 'province')
      }
    },
    cityCode(value) {
      if (value && this.showDistName) {
        this.setData(value, this.cityList, 'city')
      }
    },
    provinceName(value) {
      if (this.isShowAll) {
        this.cityStatus = !value
        this.distStatus = !value
      }
    },
    cityName(value) {
      if (this.isShowAll) {
        this.distStatus = !value
      }
    },
    clearDataName(value) {
      if (value === 'province' && this.isShowAll) {
        this.cityName = ''
        this.distName = ''
      }
    }
  },
  methods: {
    init(type, status, showCityName, showDistName) {
      this.showStatus = true
      this.isShowAll = status || this.isShowAll
      this.showCityName = showCityName !== undefined ? showCityName : true // 城市选择是否禁用
      this.showDistName = showDistName !== undefined ? showDistName : true // 区域选择是否禁用
      this.provinceCode = ''
      this.cityCode = ''
      this.distCode = ''
      this.setNamenum = 0
      if (type === 'cache') {
        // type = cache，从缓存取数据
        this.provinceList = JSON.parse(sessionStorage.provinceList || '[]')
      } else {
        this.getProvice()
      }
    },
    setArea(provinceName, cityName, distName) {
      this.provinceName = provinceName
      this.cityName = cityName
      this.distName = distName
      this.getProvice()
    },
    // 获取地理位置
    getProvice() {
      const me = this
      GetArea().then((res) => {
        const list = res.data.data && res.data.data.list
        if (!list.length) {
          return
        }
        me.provinceList = list
        const provinceData = me.provinceList.filter(
          (_) => _.name === me.provinceName
        )
        me.provinceName =
          (provinceData[0] && provinceData[0].name) || me.provinceList[0].name
        me.provinceCode =
          (provinceData[0] && provinceData[0].provinceCode) ||
          me.provinceList[0].provinceCode
      })
    },
    // 获取地理位置 城市
    getCity() {
      const me = this
      const params = {
        provinceName: me.provinceName,
        provinceCode: me.provinceCode
      }
      GetArea(params).then((res) => {
        const list = res.data.data && res.data.data.list
        if (!list.length) {
          return
        }
        me.cityList = list
        const cityData = me.cityList.filter((_) => _.name === me.cityName)
        me.cityName = (cityData[0] && cityData[0].name) || me.cityList[0].name
        me.cityCode =
          (cityData[0] && cityData[0].cityCode) || me.cityList[0].cityCode
      })
    },
    // 获取地理位置 区域
    getDist() {
      const me = this
      // 不展示区域不请求
      if (!me.showDistName) {
        return
      }
      const params = {
        provinceName: me.provinceName,
        provinceCode: me.provinceCode,
        cityName: me.cityName,
        cityCode: me.cityCode
      }
      GetArea(params).then((res) => {
        const list = res.data.data && res.data.data.list
        if (!list.length) {
          return
        }
        me.distList = list
        const distData = me.distList.filter((_) => _.name === me.distName)
        me.distName = (distData[0] && distData[0].name) || me.distList[0].name
        me.distCode =
          (distData[0] && distData[0].adCode) || me.distList[0].adCode
        setTimeout(() => {
          me.setName()
        }, 30)
      })
    },
    // 变更name值
    setName() {
      if (!this.isShowAll) {
        return
      }
      if (this.isShowAll && this.setNamenum === 0) {
        this.provinceName = ''
        this.distName = ''
        this.cityName = ''
        this.setNamenum++
      }
      if (this.clearDataName === 'province') {
        this.distName = ''
        this.cityName = ''
      } else if (this.clearDataName === 'city') {
        this.distName = ''
      }
    },
    // 根据字段
    change(value, type) {
      let data = []
      if (type === 'province' || type === 'city') {
        data = type === 'province' ? this.provinceList : this.cityList
        type === 'province'
          ? (this.provinceCode = value)
          : (this.cityCode = value)
      } else {
        data = this.distList
      }
      this.setData(value, data, type)
    },
    // 设置数据
    setData(value, list, type) {
      const me = this
      if (value === '' && type === 'city') {
        // 当数据为空，切type 是全部时，区列表清空
        me.distName = ''
        me.distCode = ''
        me.distList = []
        return
      }
      list.map(function (d) {
        // console.log(JSON.stringify(d), value, type)
        if (type === 'province' && d.provinceCode === value) {
          me[`${type}Name`] = d.name || ''
          me[`${type}Code`] = d.provinceCode || ''
          me.getCity()
          return
        }
        if (type === 'city' && d.cityCode === value) {
          me[`${type}Name`] = d.name || ''
          me[`${type}Code`] = d.cityCode || ''
          me.getDist()
          return
        }
        if (type === 'dist' && d.adCode === value) {
          me[`${type}Name`] = d.name || ''
          me[`${type}Code`] = d.adCode || ''
        }
      })
    },
    // 确认位置
    confirm(type) {
      const me = this
      if (!type) {
        me.showStatus = false
        return
      }
      if (!me.isShowAll && (!me.provinceName || !me.cityName || !me.distName)) {
        me.$message.error('请选择省市区地址')
        return
      }
      me.showStatus = false
      const data = {
        provinceName: me.provinceName, // 省份名称
        provinceCode: me.provinceCode, // 省份代码
        cityName: me.cityName, // 城市位置
        cityCode: me.cityCode, // 城市代码
        distName: me.distName, // 区位置
        distCode: me.distCode // 区代码
      }
      $emit(me, 'backArea', data)
    }
  },
  emits: ['backArea']
}
</script>

<style lang="scss" scoped>
.content {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 10px;

  .select-width {
    width: 200px;
    margin-right: 15px;
  }
}
</style>
