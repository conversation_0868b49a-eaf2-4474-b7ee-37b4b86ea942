<template>
  <div class="multi-area">
    <el-dialog v-model="dialogVisible" title="省市多选">
      <div
        v-if="checkedProvinceList.length || checkedCityList.length"
        class="header"
      >
        <div v-if="checkedProvinceList.length">
          已选省：{{ checkedProvinceList }}
        </div>
        <div v-if="checkedCityList.length">已选市：{{ checkedCityList }}</div>
      </div>
      <div class="body">
        <div v-if="isShowAll" class="module">
          <el-checkbox
            :indeterminate="isCheckedCountry"
            v-model="checkAll"
            @change="handlerCheckedAll"
            >全国</el-checkbox
          >
        </div>
        <div class="module">
          <div>省</div>
          <el-checkbox-group
            v-model="checkedProvinceList"
            :max="isSingle ? 1 : provinceList.length"
          >
            <el-checkbox
              style="margin-top: 10px"
              v-for="province in provinceList"
              :indeterminate="
                provinceObjectList[
                  provinceObjectList.findIndex((_) => _.name === province)
                ].isIndeterminateProvince
              "
              :label="province"
              :key="province"
              @change="handlerCheckedProvince(province, $event)"
            ></el-checkbox>
          </el-checkbox-group>
        </div>
        <div v-if="cityList && cityList.length" class="module">
          <div>市</div>
          <el-checkbox-group
            v-model="checkedCityList"
            :max="isSingle ? 1 : 100"
          >
            <el-checkbox
              style="margin-top: 10px"
              v-for="city in cityList"
              :label="city"
              :key="city"
              @change="handlerCheckedCity(city, $event)"
            ></el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div class="fooder center">
        <el-button type="success" @click="confirm(true)">确认</el-button>
        <el-button type="danger" @click="handlerClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
/**
 * 示例：
 * <MultiArea ref="MultiArea" @backData="backData" />
 * import MultiArea from '@/components/area/MultiArea.vue'
 * this.$refs['MultiArea'].init({
 *  checkAll, // 是否选中全国 Bool
 *  provinceAddress, // 选中的省 String
 *  cityAddress, // 选中的市 String
 * })
 */
import { GetArea } from '@/api/searchMap'
export default {
  name: 'MultiArea',
  props: {
    isSingle: {
      type: Boolean,
      default: false
    },
    isShowAll: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      checkAll: false, // 是否选中全国
      isIndeterminateCountry: false, // 全国是否部分选中  isIndeterminateProvince 省是否部分选中
      provinceObjectList: [], // 存放所有省对象
      cityObjectList: [], // 存放所有市对象
      checkedProvinceList: [], // 当前选中的省
      checkedCityList: [], // 当前选中的市
      provinceList: [], // 省级列表
      cityList: [], // 城市列表
      prevProvinceName: '', // 上一个选中的省名
      provinceName: '', // 当前省名
      provinceCode: '', // 当前省Code
      selectedData: {}, // 选中数据
      selectedDataNew: {} // 选中数据
    }
  },
  computed: {
    isCheckedCountry() {
      const me = this
      return (
        !me.checkAll &&
        me.checkedProvinceList &&
        me.checkedProvinceList.length > 0
      )
    }
  },
  activated() {
    // if (this.provinceObjectList.length === 0) {
    //   this.getProvice()
    // }
  },
  methods: {
    init(params) {
      if (this.provinceObjectList.length === 0) {
        this.getProvice()
      }
      const me = this
      if (me.dialogVisible) return
      // 重置数据
      me.provinceObjectList.map((_) => (_.isIndeterminateProvince = false))
      me.cityList = []
      me.prevProvinceName = '' // 上一个选中的省名
      me.provinceName = '' // 当前省名
      me.provinceCode = '' // 当前省Code
      me.selectedData = {} // 选中数据变空
      // 初始化数据
      const provinceList =
        (params.provinceAddress && params.provinceAddress.split(',')) || [] // 省份数组
      const cityList =
        (params.cityAddress && params.cityAddress.split(',')) || [] // 城市数组
      me.checkedProvinceList = provinceList
      me.checkedCityList = cityList
      me.checkAll = params.checkAll || false // 是否选中全国 Bool
      if (params.locationArr && params.locationArr.length) {
        params.locationArr.map((_) => {
          me.selectedDataNew[_.province] =
            me.selectedDataNew[_.province] &&
            me.selectedDataNew[_.province].size
              ? me.selectedDataNew[_.province]
              : new Set()
          if (_.cityList && _.cityList.length) {
            _.cityList.map((val) => {
              me.selectedDataNew[_.province].add(val)
            })
          }
        })
      }
      me.dialogVisible = true
    },
    // 获取地理位置 省份
    getProvice() {
      const me = this
      GetArea().then((res) => {
        const provinceObjectList = res.data.data && res.data.data.list
        if (!provinceObjectList.length) {
          return
        }
        provinceObjectList.map((_) => {
          _.isIndeterminateProvince = false
          me.provinceList.push(_.name)
        })
        me.provinceObjectList = provinceObjectList
      })
    },
    getCityNameList(list) {
      return list && list.map((_) => _.name)
    },
    // 获取地理位置 城市，checked 是否选择当前省
    getCity(checked) {
      const me = this
      let cityList = me.cityObjectList[me.provinceName] || []

      if (cityList.length) {
        me.cityList = me.getCityNameList(cityList)
        // 移除当前省下已选择的城市
        cityList.map((_) => {
          const index = me.checkedCityList.indexOf(_.name)
          if (index > -1) {
            me.checkedCityList.splice(index, 1)
          }
        })

        return
      }

      GetArea({
        provinceName: me.provinceName,
        provinceCode: me.provinceCode
      }).then((res) => {
        const list = res.data.data && res.data.data.list
        if (!list.length) {
          return
        }
        cityList = me.cityObjectList[me.provinceName] = list
        me.cityList = me.getCityNameList(cityList)
        // 移除当前省下已选择的城市
        cityList.map((_) => {
          const index = me.checkedCityList.indexOf(_.name)
          if (index > -1) {
            me.checkedCityList.splice(index, 1)
          }
        })
      })
    },
    /**
     * @param {bool} checked 全国是否选中
     */
    handlerCheckedAll(checked) {
      this.provinceObjectList.map((_) => (_.isIndeterminateProvince = false))
      this.checkedProvinceList = checked ? this.provinceList : []
      this.checkedCityList = []
      this.cityList = []
    },
    /**
     * @param {string} value 当前省
     * @param {bool} checked 当前省是否选中
     */
    handlerCheckedProvince(value, checked) {
      const me = this
      if (checked) {
        me.selectedData[value] =
          me.selectedData[value] && me.selectedData[value].size
            ? me.selectedData[value]
            : new Set()
        me.selectedDataNew[value] =
          me.selectedDataNew[value] && me.selectedDataNew[value].size
            ? me.selectedDataNew[value]
            : new Set()
      } else {
        delete me.selectedData[value]
        delete me.selectedDataNew[value]
      }
      me.checkAll = me.checkedProvinceList.length === me.provinceList.length
      const currentProvince = me.provinceObjectList.find(
        (_) => _.name === value
      )
      me.provinceName = currentProvince.name
      me.provinceCode = currentProvince.provinceCode
      const provinceObjectIndex = me.provinceObjectList.findIndex(
        (_) => _.name === me.provinceName
      )
      if (
        me.prevProvinceName !== me.provinceName &&
        me.provinceObjectList[provinceObjectIndex].isIndeterminateProvince
      ) {
        me.cityList = me.getCityNameList(me.cityObjectList[me.provinceName])
        me.checkedProvinceList = me.checkedProvinceList.filter(
          (_) => _ !== me.provinceName
        ) // 部分选择市，去除当前省
        me.prevProvinceName = me.provinceName
        return
      }
      me.prevProvinceName = me.provinceName
      me.provinceObjectList[provinceObjectIndex].isIndeterminateProvince = false

      me.getCity(checked)
    },
    /**
     * @param {string} value 当前市
     * @param {bool} checked 当前市是否选中
     */
    handlerCheckedCity(value, checked) {
      const me = this
      const cityList = me.cityObjectList[me.provinceName] || []
      let checkedAllCity = true // 当前省下是否全选市
      let checkedSomeCity = false // 当前省下是否部分选中市
      checked
        ? me.selectedData[me.provinceName].add(value)
        : me.selectedData[me.provinceName].delete(value)
      checked
        ? me.selectedDataNew[me.provinceName].add(value)
        : me.selectedDataNew[me.provinceName].delete(value)
      cityList.map((_) => {
        const checkedCity = me.checkedCityList.indexOf(_.name) > -1
        if (checkedCity) {
          checkedSomeCity = true
        } else {
          checkedAllCity = false
        }
      })

      // 当前省下是否部分选中市
      const provinceObjectIndex = me.provinceObjectList.findIndex(
        (_) => _.name === me.provinceName
      )
      me.provinceObjectList[provinceObjectIndex].isIndeterminateProvince =
        checkedSomeCity && !checkedAllCity
      // 选择市时移除省(非单选的时候)

      if (checkedSomeCity && !me.isSingle) {
        me.checkedProvinceList = me.checkedProvinceList.filter(
          (_) => _ !== me.provinceName
        )
      }
    },
    // 确认位置
    confirm(type) {
      const me = this
      if (
        me.isSingle &&
        me.checkedProvinceList.length &&
        !me.checkedCityList.length
      ) {
        return me.$message.error('需要选择城市')
      }
      me.dialogVisible = false
      const selectedData = {}
      if (Object.keys(me.selectedData) && Object.keys(me.selectedData).length) {
        Object.keys(me.selectedData).map((item, index) => {
          selectedData[item] = [...Object.values(me.selectedData)[index]].join()
        })
      } // 省市 数据
      const selectedDataNew = {}
      if (
        Object.keys(me.selectedDataNew) &&
        Object.keys(me.selectedDataNew).length
      ) {
        Object.keys(me.selectedDataNew).map((item, index) => {
          selectedDataNew[item] = [
            ...Object.values(me.selectedDataNew)[index]
          ].join()
        })
      } // 省市 数据
      const data = {
        checkAll: me.checkAll, // 是否选中全国
        provinceAddress: me.checkAll
          ? ''
          : (me.checkedProvinceList || []).join(','), // 省份数组
        cityAddress: me.checkAll ? '' : (me.checkedCityList || []).join(','), // 城市数组
        selectedData: JSON.stringify(selectedData),
        selectedDataNew: JSON.stringify(selectedDataNew)
      }
      console.log(data)
      $emit(me, 'backData', data)
    },
    // 取消
    handlerClose() {
      this.dialogVisible = false
    }
  },
  emits: ['backData']
}
</script>

<style lang="scss">
.multi-area {
  .el-dialog__body {
    min-height: 450px;
    max-height: 650px;
    overflow-y: scroll;
  }
  .header {
    margin-bottom: 20px;
    > div {
      line-height: 25px;
    }
  }
  .body {
    margin-bottom: 20px;
    .module {
      margin-bottom: 20px;
      .el-checkbox__label {
        font-size: 25px;
      }
    }
  }
  .el-checkbox__inner {
    width: 40px !important;
    height: 40px !important;
  }
  .el-checkbox__inner::after {
    width: 12px !important;
    height: 27px !important;
    left: 12px !important;
  }
}
</style>
