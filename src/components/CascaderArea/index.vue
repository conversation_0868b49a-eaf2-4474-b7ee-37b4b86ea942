<template>
  <div>
    <el-popover
      popper-class="cascader-area"
      :visible="visible"
      @show="show"
      ref="popover"
      placement="bottom-start"
      :width="checkProvinceName ? (checkCityName ? 512 : 348) : 174"
    >
      <div class="box">
        <div class="box-left">
          <div class="scroll-box">
            <div
              v-show="!provinceName"
              @click="selectProvince()"
              :class="[
                'item pl10 el-select-dropdown__item',
                !checkProvinceName ? 'selected' : ''
              ]"
            >
              不限
            </div>
            <div
              @click="selectProvince(province)"
              :class="[
                'item pl10 el-select-dropdown__item',
                checkProvinceName === province.name ? 'selected' : ''
              ]"
              v-for="(province, index) in realBrandList"
              :key="index"
            >
              {{ province.name }}
            </div>
          </div>
        </div>
        <div v-if="checkProvinceName">
          <div class="scroll-box">
            <div
              v-show="!cityName"
              @click="selectCity()"
              :class="[
                'item pl10 el-select-dropdown__item',
                !checkCityName ? 'selected' : ''
              ]"
            >
              不限
            </div>
            <div
              @click="selectCity(city)"
              :class="[
                'item pl10 el-select-dropdown__item',
                checkCityName === city.name ? 'selected' : ''
              ]"
              v-for="(city, index) in cityList"
              :key="index"
            >
              {{ city.name }}
            </div>
          </div>
        </div>
        <div v-if="checkCityName">
          <div class="scroll-box">
            <div
              v-show="!cityName"
              @click="selectDist()"
              :class="[
                'item pl10 el-select-dropdown__item',
                !checkDistName ? 'selected' : ''
              ]"
            >
              不限
            </div>
            <div
              @click="selectDist(dist)"
              :class="[
                'item pl10 el-select-dropdown__item',
                checkDistName === dist.name ? 'selected' : ''
              ]"
              v-for="(dist, index) in distList"
              :key="index"
            >
              {{ dist.name }}
            </div>
          </div>
        </div>
      </div>
      <template v-slot:reference>
        <div @click="visible = true">
          <slot :areaName="name"></slot>
        </div>
      </template>
    </el-popover>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { GetArea } from '@/api/searchMap'

export default {
  data() {
    return {
      visible: false,
      provinceName: '',
      provinceList: [],
      cityList: [],
      distList: [],
      cityName: '',
      distName: '',
      checkProvinceName: '',
      checkCityName: '',
      checkDistName: ''
    }
  },
  computed: {
    realBrandList() {
      if (!this.provinceName) return this.provinceList
      return this.provinceList.filter((item) =>
        item.provinceName.includes(this.provinceName)
      )
    },

    name() {
      if (!this.checkProvinceName) return '不限'
      if (this.checkProvinceName && !this.checkCityName)
        return `${this.checkProvinceName} - 不限`
      if (this.checkProvinceName && this.checkCityName && !this.checkDistName)
        return `${this.checkProvinceName} - ${this.checkCityName} - 不限`
      return `${this.checkProvinceName} - ${this.checkCityName} - ${this.checkDistName}`
    }
  },
  mounted() {
    this.getProvinceList()
  },
  methods: {
    reset() {
      this.activeProvince = null
      this.activeCity = null
      this.checkProvinceName = ''
      this.checkCityName = ''
      this.checkDistName = ''
      this.cityList = []
      this.distList = []
    },
    show() {
      // this.provinceName = ''
      // this.cityName = ''
    },
    async getProvinceList() {
      const res = await GetArea({
        provinceName: this.provinceName
      })
      if (res.data.code === 0) {
        const list = res.data.data && res.data.data.list
        if (!list.length) {
          return
        }
        this.provinceList = list
      }
    },
    async getCityList() {
      const res = await GetArea({
        provinceName: this.activeProvince.name,
        provinceCode: this.activeProvince.provinceCode
      })
      if (res.data.code === 0) {
        const list = res.data.data && res.data.data.list
        if (!list.length) {
          return
        }
        this.activeCity = null
        this.checkCityName = ''
        this.checkDistName = ''
        this.distList = []
        this.cityList = list
      }
    },
    async getDistList() {
      const res = await GetArea({
        provinceName: this.activeProvince.name,
        provinceCode: this.activeProvince.provinceCode,
        cityName: this.activeCity.name,
        cityCode: this.activeCity.cityCode
      })
      if (res.data.code === 0) {
        const list = res.data.data && res.data.data.list
        if (!list.length) {
          return
        }
        this.checkDistName = ''
        this.distList = list
      }
    },
    selectProvince(province) {
      if (!province) {
        this.checkProvinceName = ''
        $emit(this, 'select', {
          ownerProvince: '',
          ownerCity: '',
          ownerDistrict: ''
        })
        this.visible = false
        return
      }
      this.checkProvinceName = province.name
      this.activeProvince = province
      $emit(this, 'select', {
        ownerProvince: this.activeProvince.name,
        ownerCity: '',
        ownerDistrict: ''
      })
      this.$nextTick(() => {
        this.$refs.popover.updatePopper()
      })
      this.getCityList()
    },
    selectCity(city) {
      if (!city) {
        this.checkCityName = ''
        $emit(this, 'select', {
          ownerProvince: this.activeProvince.name,
          ownerCity: '',
          ownerDistrict: ''
        })
        this.visible = false
        return
      }
      this.checkCityName = city.name
      this.activeCity = city
      $emit(this, 'select', {
        ownerProvince: this.activeProvince.name,
        ownerCity: this.activeCity.name,
        ownerDistrict: ''
      })
      this.$nextTick(() => {
        this.$refs.popover.updatePopper()
      })
      this.getDistList()
    },
    selectDist(dist) {
      if (!dist) {
        this.checkDistName = ''
        $emit(this, 'select', {
          ownerProvince: this.activeProvince.name,
          ownerCity: this.activeCity.name,
          ownerDistrict: ''
        })
        this.visible = false
        return
      }
      this.checkDistName = dist.name
      $emit(this, 'select', {
        ownerProvince: this.activeProvince.name,
        ownerCity: this.activeCity.name,
        ownerDistrict: dist.name
      })
      this.visible = false
    }
  },
  emits: ['select']
}
</script>

<style lang="scss">
.cascader-area {
  .box {
    display: flex;
    /* ::-webkit-scrollbar {
      display: none;
    } */
    .scroll-box {
      width: 170px;
      height: 200px;
      overflow-y: auto;
      margin-top: 10px;
    }
    .box-left {
      border-right: 1px solid gainsboro;
      margin-left: -10px;
    }
    .item {
      overflow: hidden;
      cursor: pointer;
      text-overflow: ellipsis;
      overflow: hidden;
      &.active {
        background: #7f7f7f;
        color: white;
      }
    }
  }
}
</style>
