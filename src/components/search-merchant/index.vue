<template>
  <el-select
    v-model="shopId"
    :remote-method="remoteMethodShop"
    :loading="searchLoading"
    :placeholder="placeholder"
    filterable
    clearable
    remote
    style="width: 230px"
    @clear="clearShopId"
    @change="updateShopId"
  >
    <el-option
      v-for="item in queryShopList"
      :key="item.shopId"
      :label="item.name"
      :value="item.shopId"
    />
  </el-select>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { debounce } from 'lodash-es'
import {
  SearchShopByShopName,
  GetShopApplyByName,
  SearchShopByShopId,
} from '@/api/garage'

export default {
  name: 'SearchMerchant',
  props: {
    placeholder: {
      type: String,
      default: '输入经销商名称、ID进行搜索',
    },
    isSearchPayedMerchant: {
      // 是否查询的是付费经销商
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      shopId: '',
      queryShopList: [], // 根据经销商名称关联到的经销商列表
      searchLoading: false,
    }
  },
  methods: {
    // 查询经销商名称索引
    remoteMethodShop(query) {
      // 判断输入的是名称还是ID
      if (/^\d+$/.test(query) && parseInt(query) > 0) {
        this.getShopListById(query)
      } else {
        this.getShopListByName(query)
      }
    },
    getShopListByName: debounce(function (query) {
      this.searchLoading = true
      const searchShopNameApi = this.isSearchPayedMerchant
        ? SearchShopByShopName
        : GetShopApplyByName
      searchShopNameApi({
        shopName: query, // 经销商名称
        page: 1,
        limit: 50,
      })
        .then((response) => {
          if (response.data.code === 0) {
            const result = response.data.data
            this.queryShopList = result.map((value) => ({
              name: value.shopName,
              shopId: value.shopId,
            }))
            this.searchLoading = false
          }
        })
        .catch(() => {
          this.searchLoading = false
        })
    }, 300),
    // 非会员的查不到
    getShopListById: debounce(function (shopId) {
      this.searchLoading = true
      SearchShopByShopId(shopId)
        .then((response) => {
          if (response.data.code === 0) {
            const result = response.data.data
            this.queryShopList = [
              {
                name: result.shopName,
                shopId: result.shopId,
              },
            ]
            this.searchLoading = false
          }
        })
        .catch(() => {
          this.searchLoading = false
        })
    }, 300),

    clearShopId() {
      $emit(this, 'on-clear')
    },
    updateShopId(shopId) {
      const selectedShop = this.queryShopList.find(
        (item) => item.shopId === shopId
      )
      if (selectedShop) {
        $emit(this, 'on-update', selectedShop)
      }
    },
    reset() {
      ;(this.shopId = ''), (this.queryShopList = [])
    },
  },
  emits: ['on-update', 'on-clear'],
}
</script>
