<template>
  <el-dialog v-model="dialogVisible" title="退款日志">
    <div style="max-height: 65vh">
      <el-table
        :data="descList"
        highlight-current-row
        style="height: 65vh; overflow-y: auto"
        max-height="75vh"
        border
      >
        <el-table-column prop="refundOrderId" label="退款单号" align="center" />
        <el-table-column align="center" label="退款日期">
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.refundDate)
          }}</template>
        </el-table-column>
        <el-table-column prop="refundPrice" label="退款金额" align="center" />
        <el-table-column label="退款状态" align="center">
          <template v-slot="scope">
            <span>{{ refundState[scope.row.state] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import { getRefundOrderDesc } from '@/api/garage'
export default {
  name: 'RefundDescList',
  data() {
    return {
      dialogVisible: false,
      refundState: {
        0: '待退款',
        1: '已退款',
        2: '退款出错',
        3: '退款中'
      },
      descList: []
    }
  },
  methods: {
    // 重置状态
    init(params) {
      this.dialogVisible = true
      this.descList = []
      getRefundOrderDesc({
        orderNum: params.orderNumber || params.orderNum
      }).then((res) => {
        const data = res.data
        if (data.code === 0) {
          this.descList = data.data || []
        }
      })
    }
  }
}
</script>
