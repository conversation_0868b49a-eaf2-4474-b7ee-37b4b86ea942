<template>
  <el-dialog v-model="dialogVisible" title="退款信息" width="520px">
    <el-form label-width="140px">
      <el-form-item label="退款渠道：" required>
        <el-select
          disabled
          v-model="form.refundChannel"
          placeholder="微信/支付宝/银行账户"
          style="width: 300px"
          @change="changePayPlatform"
        >
          <el-option
            v-for="(value, index) in reFundTypes"
            :key="value"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="退款说明：" required>
        <el-input
          v-model="form.refundInstructions"
          :rows="3"
          type="textarea"
          placeholder="请输入退款说明"
          clearable
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="订单权益" required>
        <el-select
          v-model="form.refundOnlyStatus"
          clearable
          placeholder="请选择订单权益"
          style="width: 200px"
          :disabled="refundOnlyFormClue"
        >
          <el-option
            v-for="(value, index) in refundOnlyStatusType"
            :key="value"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="退款金额：" required>
        <el-input
          v-model="form.refundAmount"
          type="text"
          placeholder="请输入退款金额"
          clearable
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item
        v-if="
          (![1, 2].includes(Number(form.serviceType)) &&
            form.newCluePacketFlag) ||
          [1, 2].includes(Number(form.serviceType))
        "
        :label="`使用${
          [1, 2, 8].includes(Number(form.serviceType)) ? '天' : '条'
        }数：`"
        required
      >
        <el-input
          disabled
          v-model="form.usedNum"
          type="text"
          placeholder="请输入退款金额"
          clearable
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item
        v-if="
          (![1, 2].includes(Number(form.serviceType)) &&
            form.newCluePacketFlag) ||
          [1, 2].includes(Number(form.serviceType))
        "
        :label="`退款${
          [1, 2, 8].includes(Number(form.serviceType)) ? '天' : '条'
        }数：`"
        required
      >
        <el-input
          disabled
          v-model="form.refundNum"
          type="text"
          placeholder="请输入退款金额"
          clearable
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="订单号：" required>
        <el-input
          v-model="form.orderNum"
          type="text"
          placeholder="请输入订单流水编号"
          clearable
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="订单流水编号：">
        <el-input
          v-model="form.orderSerialsNum"
          type="text"
          placeholder="请输入订单流水编号"
          clearable
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="发票情况" required>
        <el-input
          v-model="form.invoiceInfo"
          type="textarea"
          placeholder="请输入发票情况"
          clearable
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="发票状态">
        <el-input
          v-model="invoiceType[form.invoiceStatus]"
          placeholder="已开票/未开票"
          style="width: 300px"
          disabled
        />
      </el-form-item>
      <el-form-item>
        <div style="color: red">
          <p style="line-height: 10px">退款前须确认</p>
          <p style="line-height: 10px">
            1、订单已开普票，需将原票先退回再进行退款
          </p>
          <p style="line-height: 10x">
            2、订单已开专票，需要有开具红色发票信息通知
          </p>
        </div>
      </el-form-item>
      <!-- 微信退款 -->
      <!-- <div v-if="form.refundChannel == 1">
        <el-form-item label="微信商户号：" required>
          <el-input
            v-model="form.weChatAccount"
            type="text"
            placeholder="请输入微信商户号"
            clearable
            style="width: 300px"
          />
        </el-form-item>
      </div> -->
      <!-- 支付宝退款 -->
      <!-- <div v-if="form.refundChannel == 2">
        <el-form-item label="支付宝商户号：" required>
          <el-input
            v-model="form.alipayAccount"
            type="text"
            placeholder="请输入支付宝商户号"
            clearable
            style="width: 300px"
          />
        </el-form-item>
      </div> -->
      <!-- 转账退款 -->
      <div v-if="form.refundChannel == '线下转账'">
        <el-form-item label="收款人/单位全称：" required>
          <el-input
            v-model="form.payee"
            :rows="2"
            type="textarea"
            placeholder="请输入收款人/单位全称"
            clearable
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="银行账号：" required>
          <el-input
            v-model="form.bankAccount"
            type="text"
            placeholder="请输入银行账号"
            clearable
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="开户行名称：" required>
          <el-input
            v-model="form.bankName"
            :rows="2"
            type="textarea"
            placeholder="开户行名称精确到支行"
            clearable
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="开户行行号：">
          <el-input
            v-model="form.bankNumber"
            type="text"
            placeholder="请输入收款人/单位全称"
            clearable
            style="width: 300px"
          />
        </el-form-item>
      </div>
      <el-form-item label="附言：">
        <el-input
          v-model="form.remark"
          :rows="5"
          type="textarea"
          resize="none"
          placeholder="请输入附言"
          style="width: 300px"
          :disabled="orderTimeStatus"
        />
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmAction">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { $emit } from '@/utils/gogocodeTransfer'
import { refundByOA } from '@/api/garage'
import { shopInvoiceType, refundOnlyStatusType } from '@/utils/enum'
export default {
  name: 'ReFundForm',
  data() {
    return {
      dialogVisible: false,
      reFundTypes: {
        微信: 1,
        支付宝: 2,
        银行账户: 3
      },
      // 发票状态
      invoiceType: shopInvoiceType,
      refundOnlyStatusType, // 订单权益
      refundOnlyFormClue: false,
      form: {
        serviceType: '', // 订单服务类型: 1-新车会员，2-二手车会员，3-询价线索，4-试驾线索 5-租车线索，6-驾考线索
        rechargeChannel: '', // 充值时渠道
        refundChannel: '', // 退款渠道  1、微信、2、支付宝、3、银行账户
        refundInstructions: '', // 退款说明
        refundAmount: '', // 退款金额
        orderNum: '', // 订单号
        orderSerialsNum: '', // 订单流水编号
        payee: '', // 收款人/单位全称
        bankAccount: '', // 银行账号
        bankName: '', // 开户行名称
        bankNumber: '', // 开户行行号
        invoiceInfo: '', // 发票情况
        invoiceStatus: '', // 发票状态
        remark: '', // 附言
        weChatAccount: '', // 微信商户号
        alipayAccount: '', // 支付宝商户号
        refundOnlyStatus: 0
      }
    }
  },
  methods: {
    // 重置状态
    init(params) {
      console.log('params======', params)
      this.dialogVisible = true
      this.orderTimeStatus = params.orderTimeStatus || false
      this.refundOnlyFormClue = params.refundOnlyFormClue || false
      let usedNum = ''
      let refundNum = ''
      if ([1, 2, 8].includes(Number(params.serviceType))) {
        refundNum = params.refundShopMemberDays
        usedNum = params.costShopMemberDays
      } else {
        refundNum = params.restCLueNum
        usedNum = params.number - params.restCLueNum
      }

      this.form = {
        newCluePacketFlag: params.newCluePacketFlag,
        refundShopMemberDays: params.refundShopMemberDays,
        costShopMemberDays: params.costShopMemberDays,
        restCLueNum: params.restCLueNum,
        usedNum: usedNum,
        refundNum: refundNum,
        serviceType: params.serviceType, // 订单来源类型
        rechargeChannel: params.payPlatform, // 充值时渠道
        refundChannel: params.refundChannel, // 退款渠道  1、微信、2、支付宝、3、银行账户
        refundInstructions: '', // 退款说明
        refundAmount: '', // 退款金额
        orderNum: params.orderNum, // 订单号
        orderSerialsNum: params.orderSerialsNum || '', // 订单流水编号
        payee: '', // 收款人/单位全称
        bankAccount: '', // 银行账号
        bankName: '', // 开户行名称
        bankNumber: '', // 开户行行号
        invoiceInfo: '', // 发票情况
        invoiceStatus: params.invoiceStatus, // 发票状态
        remark: params.orderTimeStatus ? '财务无需打款' : '', // 附言
        weChatAccount: '', // 微信商户号
        alipayAccount: '', // 支付宝商户号
        refundOnlyStatus: params.refundOnlyStatus || 0 // 订单权益
      }
    },
    changePayPlatform() {
      this.form = Object.assign(this.form, {
        payee: '', // 收款人/单位全称
        bankAccount: '', // 银行账号
        bankName: '', // 开户行名称
        bankNumber: '', // 开户行行号
        weChatAccount: '', // 微信商户号
        alipayAccount: '' // 支付宝商户号
      })
    },
    confirmAction() {
      const me = this
      console.log(me.form, '**********')
      if (!this.form.refundChannel) {
        return this.$message.error('请选择退款渠道!')
      }
      if (!this.form.refundInstructions) {
        return this.$message.error('请填写退款说明!')
      }

      if (!this.form.refundAmount) {
        return this.$message.error('请填写退款金额!')
      }
      if (!this.form.orderNum) {
        return this.$message.error('请填写订单号!')
      }
      if (!this.form.invoiceInfo) {
        return this.$message.error('请填写发票情况!')
      }

      // if (this.form.refundChannel === 1) {
      //   if (!this.form.weChatAccount) {
      //     return this.$message.error('请填写微信商户号!')
      //   }
      // }

      // if (this.form.refundChannel === 2) {
      //   if (!this.form.alipayAccount) {
      //     return this.$message.error('请填写支付宝商户号!')
      //   }
      // }

      if (this.form.refundChannel === '线下转账') {
        if (!this.form.payee) {
          return this.$message.error('请填写收款人/单位全称!')
        }
        if (!this.form.bankAccount) {
          return this.$message.error('请填写银行账号!')
        }
        if (!this.form.bankName) {
          return this.$message.error('请填写开户行名称!')
        }
      }
      this.$tools.debounce(this.refundByOA, 1000)()
    },
    refundByOA() {
      refundByOA({
        ...this.form,
        refundChannelAccount: this.form.refundChannel
      }).then((res) => {
        const data = res.data
        if (data.code === 0) {
          this.$message.success('退款操作成功!')
          this.dialogVisible = false
          $emit(this, 'getRefundInfo')
        }
      })
    }
  },
  emits: ['getRefundInfo']
}
</script>
