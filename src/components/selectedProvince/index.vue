<template>
  <div class="newArea">
    <el-dialog
      v-model="dialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handlerClose"
      :title="dialogTitle"
      width="850px"
    >
      <div class="content">
        <div class="content-select">
          <p v-if="selectedProvince.length">
            已选省份：<span v-for="(p, index) in selectedProvince" :key="index"
              >{{ p }}&ensp;
            </span>
          </p>
          <p>
            <el-button style="height: 40px" type="info" plain @click="reset()"
              >重置</el-button
            >
          </p>
        </div>
        <el-checkbox-group v-model="selectedProvince" @change="updataList">
          <el-checkbox
            v-for="(province, index) in treeData"
            :label="province"
            :key="index"
          />
        </el-checkbox-group>
      </div>
      <div class="fooder">
        <el-button type="success" @click="confirm">确认</el-button>
        <el-button type="danger" @click="handlerClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { GetArea } from '@/api/searchMap'
import { settings } from 'nprogress'
import { mapGetters } from 'vuex'

export default {
  name: 'NewMultiArea',
  data() {
    return {
      dialogVisible: false,
      treeData: [], // 树形图数据
      oldSelectedProvince: [], // 历史选中的省份
      selectedProvince: [] // 选中的省份
    }
  },
  props: {
    dialogTitle: {
      typeof: String,
      default: '城市选择'
    }
  },
  computed: {
    ...mapGetters(['cityList'])
  },
  methods: {
    // 获取地理位置 省份
    getProvice(data) {
      const me = this
      GetArea({
        provinceName: data.name,
        provinceCode: data.provinceCode
      }).then((res) => {
        const provinceObjectList = res.data.data && res.data.data.list
        me.treeData = provinceObjectList.map((item) => {
          return item.name
        })
        me.treeData.unshift('全国')
      })
    },
    // 展示数据
    showData() {
      const me = this
    },
    // 提交数据
    confirm() {
      $emit(this, 'backData', {
        selectedDataNew: JSON.stringify(this.selectedProvince)
      })
      this.reset()
      this.dialogVisible = false
    },
    // 初始化打开弹窗
    init(info) {
      const me = this
      me.dialogVisible = true
      me.selectedProvince = info.defaultCheckedKeys || []
      me.oldSelectedProvince = me.selectedProvince || []
      me.getProvice({})
    },
    // 关闭弹窗
    handlerClose() {
      this.reset()
      this.dialogVisible = false
    },
    // 重置数据
    reset() {
      this.selectedProvince = [] // 选中的城市
    },
    // 选中城市后更新
    updataList(findData) {
      const me = this
      console.log(
        findData,
        me.selectedProvince,
        me.oldSelectedProvince,
        '1231231'
      )
      if (
        me.selectedProvince.includes('全国') &&
        !me.oldSelectedProvince.includes('全国')
      ) {
        me.selectedProvince = ['全国']
      } else if (
        (!me.selectedProvince.includes('全国') &&
          me.oldSelectedProvince.includes('全国')) ||
        (me.selectedProvince.includes('全国') &&
          me.oldSelectedProvince.includes('全国') &&
          me.selectedProvince.length > 1)
      ) {
        me.selectedProvince.splice(
          me.selectedProvince.findIndex((item) => {
            return item === '全国'
          })
        , 1)
      }
      me.oldSelectedProvince = me.selectedProvince
    }
  },
  emits: ['backData']
}
</script>

<style lang="scss">
.newArea {
  .content {
    .el-tree {
      width: 800px;
      display: flex;
      flex-wrap: wrap;
      .el-tree-node {
        width: 200px;
      }
    }
  }
  .content-select {
    border-bottom: 1px solid rgb(228, 227, 223);
    margin-bottom: 10px;
    justify-content: space-between;
    .content-select-input {
      width: 200px;
      margin-bottom: 10px;
      margin-left: 20px;
    }
  }
  .fooder {
    margin-top: 20px;
    text-align: center;
  }
}
</style>
