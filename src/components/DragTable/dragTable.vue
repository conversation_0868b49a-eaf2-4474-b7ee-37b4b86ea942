<template>
  <div class="table-content">
    <div class="item header">
      <div
        v-for="header in headers"
        :key="header.name"
        class="item-cell"
        :style="{ width: header.width, flex: header.span }"
      >
        {{ header.name }}
      </div>
    </div>
    <el-scrollbar class="item-container">
      <draggable
        v-model="tableData"
        :animation="300"
        :style="{ height: options.height }"
        v-bind="$attr"
        itemKey="id"
      >
        <template #item="{ element, index }">
          <div
            class="item drag"
            :class="{ 'no-drag': element.disabled || index < disabledIndex }"
            :ref="setRefs"
          >
            <slot :row="element" :index="index"></slot>
            <div v-if="element === NODATA" class="noData flex-center">
              暂无数据
            </div>
          </div>
        </template>
      </draggable>
    </el-scrollbar>
  </div>
</template>

<script setup>
import draggable from 'vuedraggable'
import { ref, computed, onMounted, nextTick } from 'vue'

const NODATA = 'noData'
let columnRef = []
const headers = ref([])
const props = defineProps({
  modelValue: {
    type: Array,
    // eslint-disable-next-line vue/valid-define-props
    default: () => [NODATA]
  },
  disabledIndex: {
    type: Number,
    default: -1
  },
  options: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const tableData = computed({
  get() {
    return props.modelValue.length ? props.modelValue : [NODATA]
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

const setRefs = (el) => {
  if (el) {
    columnRef = [...columnRef, el]
  }
}

onMounted(() => {
  nextTick(() => {
    if (columnRef.length) {
      const el = columnRef[0]?.$el || columnRef[0]
      const defaultSpan = 1
      el.children.forEach((item) => {
        const isCell = item.getAttribute('class')?.includes('item-cell')
        const name = item.getAttribute('name')
        const span = Number(item.getAttribute('flexSpan')) || defaultSpan
        if (headers.value.length < el.children.length && isCell) {
          headers.value.push({
            name: name,
            span: span
          })
        }
      })
    }
  })
})
</script>

<style lang="scss" scoped>
.table-content {
  margin: 20px 0;
  text-align: center;
  width: 100%;
  overflow-x: auto;
}
.item-container {
  text-align: center;
  overflow-y: auto;
  border: 1px solid #ebeef5;
}
.noData {
  position: absolute;
  text-align: center;
  width: 100%;
  height: 100%;
  background-color: white;
}
.item {
  color: #606266;
  font-size: 14px;
  width: 100%;
  display: flex;
  align-items: stretch;
  position: relative;

  :deep(.item-cell) {
    word-break: break-all;
    border-right: 1px solid #ebeef5;
    min-height: 45px;
    line-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px;
  }

  &.header {
    color: #333;
    border-width: 1px;
    font-weight: 600;
    border: 1px solid #ebeef5;
    border-width: 1px 1px 0px 1px;
    background-color: #f5f7fa;
  }
  &:active {
    background: #f5f7fa;
  }
}
.drag {
  border: 1px solid #ebeef5;
  border-width: 1px 0px 0px 1px;
}
.no-drag {
  background-color: #f7f6f6;
  // pointer-events: none;
}
</style>
