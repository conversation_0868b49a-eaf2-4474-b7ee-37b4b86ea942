import DragTable from './dragTable.vue'
import { h, renderSlot, VNode, SetupContext } from 'vue'
interface DragCloumProps {
  span?: number
  name?: string
}
const dragCloum = function (
  props: DragCloumProps,
  context: SetupContext
): VNode {
  const { span = 1, name } = props

  return h(
    'div',
    {
      class: 'item-cell',
      style: {
        flex: span
      },
      // span,
      flexSpan: span,
      name,
      ...context.attrs
    },
    renderSlot(context.slots, 'default')
  )
}

export { DragTable, dragCloum }
