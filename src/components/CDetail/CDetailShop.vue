<template>
  <section class="c-detail-shop">
    <img
      :src="goodData.firstImg || data.firstImg || data.img"
      class="c-detail-shop-img"
    />
    <div class="c-detail-shop-content">
      <p class="dotdotdot2 shop-name">
        {{ goodData.goodsName || data.goodsName }}
      </p>
      <p
        class="shop-old-price"
        v-if="(goodData.sellPrice || data.sellPrice) && goodsType !== 3"
      >
        {{ tip }}原价：<del>¥{{ goodData.sellPrice || data.sellPrice }}</del>
      </p>
      <p
        class="shop-content_subsidy"
        v-if="
          goodData.remitAmount ||
          goodData.couponAmount ||
          data.remitAmount ||
          data.couponAmount
        "
      >
        <span
          class="shop-content_subsidy-amount shop-content_subsidy-halo"
          v-if="goodData.remitAmount || data.remitAmount"
        >
          <strong>官方补贴</strong>¥<small>{{
            goodData.remitAmount || data.remitAmount
          }}</small>
        </span>
        <span></span>
        <span
          v-if="goodData.couponAmount || data.couponAmount"
          class="shop-content_subsidy-amount"
        >
          <strong>券</strong>¥<small>{{
            goodData.couponAmount || data.couponAmount
          }}</small>
        </span>
      </p>
      <div class="shop-content-button">
        <p
          v-if="goodData.discountsPrice || data.discountsPrice"
          class="shop-price-content_tip"
        >
          到手价<span
            >¥{{ goodData.discountsPrice || data.discountsPrice }}</span
          >
        </p>
        <p v-else class="shop-price-content_tip">
          到手价={{ tip }}实付价-官方补贴
        </p>
      </div>
    </div>
  </section>
</template>

<script type="es6">
export default {
  name: 'CDetailShop',
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    },
    noJson: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isClient: window.isClient,
      goodData: {},
      goodsType: 1,
      tip: '淘宝'
    };
  },
  created() {},
  mounted() {
    this.getData()
  },
  methods: {
    // // 获取商品详情
    getData() {
      if (!this.noJson) {
        this.goodData = JSON.parse(this.data.content)
      }
      const goodTypeList = {
        1: '淘宝',
        2: '京东',
        3: '自营'
      }
      this.goodsType = this.noJson ? this.data.goodsType : this.goodData.goodsType
      this.tip = goodTypeList[(this.noJson ? this.data.goodsType : this.goodData.goodsType) || '1']
    }
  }
}
</script>

<style lang="scss">
@import 'CDetailShop.scss';
</style>
