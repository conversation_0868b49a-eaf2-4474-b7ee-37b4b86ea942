<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="预览区域"
      center
      class="choose-iframe"
      append-to-body
    >
      <p class="footer-content">
        <el-button type="primary" @click="seeQrCode()">查看二维码</el-button>
      </p>
      <div class="iframe-content" v-if="loadStaus" v-show="!stringType">
        <c-details-head :data="detail"></c-details-head>
        <c-details-content
          :data="detail"
          :sourceType="sourceType"
        ></c-details-content>
        <div v-show="type === 'essay_detail'" class="is-end text-center">
          <i>-</i>&ensp;<span>END</span>&ensp;<i>-</i>
        </div>
        <c-outer-chain v-if="detail.link" :data="detail.link"></c-outer-chain>
        <c-shop-swipe
          v-if="detail.mallGoodsList && detail.mallGoodsList.length"
          :data="detail.mallGoodsList"
        ></c-shop-swipe>
        <c-related-answer
          :id="id"
          v-if="type === 'topic_detail'"
        ></c-related-answer>
        <div v-if="isSpecialType" class="special-type">
          当前动态数据为二手车或者红包
        </div>
      </div>
      <choose-qr-code ref="qrCode" />
    </el-dialog>
  </div>
</template>

<script>
import CDetailsHead from './CDetailsHead.vue'
import COuterChain from './COuterChain.vue'
import CDetailsContent from './CDetailsContent.vue'
import CRelatedAnswer from './CRelatedAnswer.vue'
import CShopSwipe from './CShopSwipe.vue'
import ChooseQrCode from '@/components/Dialog/ChooseQrCode.vue'
import { getQueryEassyDetail } from '@/api/articleModule'
export default {
  name: 'ChooseDetail',
  components: {
    CDetailsHead,
    COuterChain,
    CDetailsContent,
    CRelatedAnswer,
    CShopSwipe,
    ChooseQrCode,
  },
  data() {
    return {
      dialogVisible: false,
      loadStaus: false,
      detail: {},
      type: '',
      id: '',
      url: '',
      sourceType: '',
      isSpecialType: false,
      stringType: '',
    }
  },
  methods: {
    init(data, url) {
      const me = this
      me.id = data.itemId
      me.url = url
      me.sourceType = data.sourceType
      me.dialogVisible = true
      me.loadStaus = false
      getQueryEassyDetail({
        action: '22004',
        id: data.itemId,
        sourceType: me.sourceType,
        platform: '3',
        version: '3.42.0',
      }).then((response) => {
        me.loadStaus = true
        if (response.data.code === 0 && !response.data.data) {
          return (me.stringType = 'labeldatanone')
        }
        if (response.data.code === 0) {
          const result = response.data.data
          result.datetime =
            result.updateTime > result.dateline
              ? result.updateTime
              : result.dateline
          result.timetip =
            result.updateTime > result.dateline ? '更新于' : '发表于'
          // 默认用户名居中显示
          result.certifybrand = false
          // 如果有品牌认证，设置属性result.certifybrand = true，因为此时不需要用户名居中显示
          if (result.certifyList && result.certifyList.length > 0) {
            result.certifyList.map(function (v) {
              if (parseInt(v.type) === 3 && parseInt(v.status) === 1) {
                return (result.certifybrand = true)
              }
            })
          }
          console.log(result)
          // if (result.link && result.link.length) {
          //   const findData = result.link.find(_ => {
          //     return _.relationType === 'red_packet' || _.relationType === 'used_car'
          //   })
          //   // me.isSpecialType = !!(findData && findData.relationType && me.sourceType)
          // }
          me.detail = result
          me.type = result.type
        }
        // 帖子：为维护社区环境，该内容已删除（604002）观点内容不存在状态提示（101007）、观点待审核状态提示（101005）、观点不存在状态提示（101004）
        if (response.data.code !== 0) {
          me.stringType = 'common'
        }
      })
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
      this.id = ''
    },
    // 查看二维码
    seeQrCode() {
      this.$refs.qrCode.init(this.url)
    },
  },
}
</script>

<style lang="scss">
.choose-iframe {
  .iframe-content {
    width: 400px;
    height: 600px;
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 5px;
    margin: 0 auto;
    display: block;
    overflow: hidden;
    overflow-y: scroll;
    position: relative;
  }
  .is-end {
    padding: 30px 0;
    color: #999;
    i {
      display: inline-block;
    }
    span {
      font-size: 12px;
      font-weight: 600;
    }
  }
  .special-type {
    position: absolute;
    top: 0;
    width: 100%;
    height: 50%;
    line-height: 300px;
    text-align: center;
    font-size: 25px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.5);
  }
}
</style>
