<template>
  <section class="c-detail-article">
    <img
      v-if="articleData.img"
      :src="articleData.img"
      class="c-detail-article-img"
    />
    <div
      class="c-detail-article-mes"
      :class="{ 'c-detail-article-mes-padding': articleData.img }"
    >
      <p class="c-detail-article-title dotdotdot1">{{ articleData.title }}</p>
      <p class="c-detail-article-tip">
        {{ articleData.replyCnt }}评论<span class="c-detail-article-tag"
          >文章</span
        >
      </p>
      <p class="c-detail-article-tip">作者：{{ articleData.author }}</p>
    </div>
  </section>
</template>

<script type="es6">
export default {
  name: 'CDetailArticle',
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    },
    goPayStatus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isClient: window.isClient,
      articleData: {}
    };
  },
  created() {},
  mounted() {
    this.getData()
  },
  methods: {
    // // 获取商品详情
    getData() {
      if (this.data.article && this.data.article.id) {
        this.articleData = this.data.article
        console.log(this.articleData)
      }
    },
    updataData(data) {
      this.articleData = data
    }
  }
}
</script>

<style lang="scss">
@import 'CDetailArticle.scss';
</style>
