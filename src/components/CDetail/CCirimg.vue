<template>
  <section class="circle-img">
    <div v-if="imgData.imgOriginal" class="sub-img-wrap">
      <div class="flex">
        <div
          v-for="(i, iIndex) in imgData.data"
          :key="iIndex"
          class="imgsOnlyone"
          @click.stop="imgBig(type, i.imgUrl || i)"
        >
          <img
            :src="changeImgGIF(i.imgUrl || i)"
            :style="{ height: imgHeight }"
            onerror='onerror=null;src="/static/img/detail/<EMAIL>" '
            class="detail-imgs"
          />
          <div v-if="isGif(i.imgUrl || i)" class="is-gif">GIF</div>
        </div>
      </div>
    </div>
    <div v-else class="sub-img-wrap">
      <div v-for="(is, isIndex) in imgData.data" :key="isIndex" class="flex">
        <div
          v-for="(i, iIndex) in is"
          :key="iIndex"
          :style="{ height: setHeight + 'px', width: setHeight + 'px' }"
          class="imgs"
          @click.stop="imgBig(type, i.imgUrl)"
        >
          <img
            :src="i.imgUrl"
            :style="{ height: setHeight + 'px' }"
            onerror='onerror=null;src="/static/img/detail/<EMAIL>"'
            class="imgs detail-imgs"
          />
        </div>
      </div>
    </div>
    <choose-show-image ref="showImage" />
  </section>
</template>

<script type="es6">
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
export default {
  name: 'CCircleImg',
  props: ['type'],
  components: {
    ChooseShowImage
  },
  data() {
    return {
      data: {},
      imgData: {},
      imgStatus: false,
      setHeight: '100',
      imgWidth: '61vw',
      imgHeight: '46vw'
    };
  },
  computed: {},
  created() {
    this.data = this.type
    this.ready()
  },
  methods: {
    // 数据更新
    updata(data) {
      console.log(`data`, data)
      this.data = {}
      this.imgData = {}
      this.data = data
      this.ready()
    },
    ready() {
      const me = this
      if (me.data === undefined) return
      if (me.data.imgOriginal && me.data.link && me.data.link.length === 0) {
        me.imgData.imgOriginal = true
        me.imgData.data = me.data.imgs
      } else {
        // 非骑行记录时单独处理 (老动态)
        me.setImg()
      }
    },
    setImg() {
      const me = this
      me.imgData.data = []
      const imgAll = me.data.image && me.data.image.length ? me.data.image : me.data.imgs || me.data.img
      // if (me.data.image.length == 1 || me.data.imgs.length === 1) {
      if (imgAll && imgAll.length && parseInt(imgAll.length) === 1) {
        me.imgData.imgOriginal = true
        me.imgData.data = imgAll
        const size = imgAll[0].imgOrgUrl.split('_')
        if (!isNaN(size[size.length - 1]) && !isNaN(size[size.length - 2])) {
          if (parseInt(size[size.length - 2]) < parseInt(size[size.length - 1])) {
            me.imgHeight = '61vw'
          } else if (parseInt(size[size.length - 2]) === parseInt(size[size.length - 1])) {
            me.imgHeight = '46vw'
          }
        }
        me.imgData.data.map(function (value) {
          if (/images.58moto.com|imgs.58moto.com|imgs2.58moto.com/.test(value.imgOrgUrl)) {
            value.imgUrl = value.imgOrgUrl.replace(/(.*)\?/, '$1600?') // 最后一个?前加600
          } else {
            value.imgUrl = value.imgOrgUrl
          }
        })
      } else if (imgAll && imgAll.length && parseInt(imgAll.length) === 4) {
        setImgData('2')
      } else {
        setImgData('3')
      }
      function setImgData(num) {
        for (let n = 0; n < num; n++) {
          let result = {}
          result = imgAll.slice(n * num, (n + 1) * num)
          me.imgData.data[n] = result
        }
      }
      if (imgAll.length === 1) {
        return
      }
    },
    imgBig(circle, img) {
       this.$refs.showImage.init(img)
    },
    isGif(url) {
      return url.indexOf('.gif') > -1
    },
    // 修改动图
    changeImgGIF(url) {
      const isGif = url.indexOf('.gif') > -1
      if (isGif) {
        url = url.substring(0, url.indexOf('.gif')) + '.gif!nowater'
      }
      return url
    }
  }
}
</script>

<style lang="scss">
@import 'CCirimg.scss';
</style>
