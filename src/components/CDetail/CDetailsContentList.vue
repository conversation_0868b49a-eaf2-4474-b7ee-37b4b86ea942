<template>
  <section class="c-details-content-list">
    <div v-for="(content, index) in showContent" :key="index" class="content">
      <div class="text-mes">
        <!-- 文字部分 -->
        <pre
          v-if="content.type === '1'"
          :class="{ dotdotdot3: isAnswerStatus }"
          :style="content.style"
          class="text-content"
          v-html="content.content"
        ></pre>
        <!-- 问答详情展示全文 -->
        <strong v-if="isAnswerStatus && content.type === '1'" class="answer-all"
          >展开全文<img
            src="/static/img/detail/<EMAIL>"
            alt=""
        /></strong>
      </div>
      <h3 v-if="content.type === '4'" class="text-content paragraph-headings">
        {{ content.content }}
        <!-- 段落标题 -->
      </h3>
      <div
        v-if="content.type === '2' || content.type === '3'"
        class="img-content"
      >
        <!-- 单张图或图文模式 -->
        <div class="show-img-content">
          <img
            v-if="
              content.images && content.images[0] && content.images[0].imgUrl
            "
            :src="content.images[0].imgUrl"
            class="detail-imgs"
            onerror="onerror=null;src='/static/img/detail/<EMAIL>'"
            @click="imgBig(content.images[0].imgUrl)"
          />
          <div
            v-if="
              isGif(
                content.images && content.images[0] && content.images[0].imgUrl
              )
            "
            class="is-gif"
          >
            GIF
          </div>
        </div>
        <div
          v-if="content.type === '3' && content.content.length"
          class="content-explain"
        >
          <img src="/static/img/detail/<EMAIL>" alt="" />
          <pre v-html="content.content"></pre>
        </div>
      </div>
      <c-outer-chain v-if="content.type === '5'" :data="content.linkData">
        <!-- 引用部分 -->
      </c-outer-chain>
      <div
        v-if="
          (content.type === '6' || content.type === '7') && content.videoUrl
        "
        class="video-content"
      >
        <!-- 视频部分直接使用video播放或腾讯、优酷播放 -->
        <video
          v-if="content.videoUrl"
          :src="content.videoUrl"
          :poster="content.img"
          :style="{ width: content.width, height: content.height }"
          class="video"
          controls="controls"
        ></video>
        <video
          v-if="content.videoUrl && !content.isYk"
          id="id_video_container"
          :data-id="content.id"
          class="tx-video video"
          height="240"
        ></video>
        <!-- <div v-else :id="ykUrl" class="youkuplayers"></div> -->
        <div
          v-if="content.type === '7' && content.content.length"
          class="content-explain"
        >
          <img src="/static/img/detail/<EMAIL>" alt="" />
          <pre v-html="content.content"></pre>
        </div>
      </div>
      <div v-if="content.type === '8' && content.link" class="url-content">
        <!-- 纯url -->
        <div class="url-mes">
          <i></i>
          <p>
            <a :href="content.link">{{ content.link }}</a>
          </p>
        </div>
      </div>
      <div v-if="['9', '10', '12'].includes(content.type)">
        <c-detail-shop :data="content" />
      </div>
      <div
        v-if="content.type === '11' && content.article && content.article.id"
      >
        <c-detail-article :data="content" ref="cDetailArticle" />
      </div>
    </div>
    <!-- 动态详情、问答详情图片展示 -->
    <div
      v-if="
        (type === 'moment_detail' ||
          type === 'riding_detail' ||
          type === 'topic_detail' ||
          type === 'car_detail' ||
          !type) &&
        showMomentImg
      "
      class="content moment"
    >
      <c-Cirimg ref="cirimg"></c-Cirimg>
    </div>
    <choose-show-image ref="showImage" />
  </section>
</template>

<script type="es6">
import { getLinkName } from '@/utils'
import COuterChain from './COuterChain.vue'
import CDetailShop from './CDetailShop.vue'
import CDetailArticle from './CDetailArticle.vue'
import CCirimg from './CCirimg.vue'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
export default {
  name: 'CDetailsContentList',
  components: {
    COuterChain,
    CCirimg,
    CDetailShop,
    CDetailArticle,
    ChooseShowImage
  },

  props: {
    data: {
      type: Array,
      default() {
        return []
      }
    },
    isAnswer: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      imgAll: [], // 所有图片集合
      videoImgAll: [], // 视频图片集合
      linkImgAll: [], // （link字段）外链图片集合
      showContent: [], // 展示的文章内容信息
      showMomentImg: false // 是否显示动态详情图片
    };
  },
  computed: {},
  watch: {},
  mounted() {
    this.showContent = []
    this.isAnswerStatus = this.isAnswer || false
    this.initData()
    this.getImgs()
    this.setContent()
  },
  methods: {
    // 获取动态展示第一张图片
    initData() {
      const me = this
      // 动态时，特殊处理
      if (me.type === 'moment_detail' || me.type === 'riding_detail' || me.type === 'topic_detail' || me.type === 'car_detail' || !me.type) {
        me.data.map(function (value) {
          // type 1.文字 2.单图 3.图文 4.段落 5.关联 6.单视频 7.视频文字 8纯url
          if (!(value.type === '2' || value.type === '3')) {
            me.showContent = me.showContent.concat(value)
          }
        })
      } else {
        me.data.map(function (value) {
          // type 1.文字 2.单图 3.图文 4.段落 5.关联 6.单视频 7.视频文字 8纯url
          if (value.type === '1' && value.textAlign) {
            value.style = {
              "text-align": `${value.textAlign}`
            }
          }
        })
        me.showContent = me.data
      }
      console.log(me.showContent)
    },
    // 整合所有图片，大图展示使用
    getImgs() {
      const me = this
      // type 1.文字 2.单图 3.图文 4.段落 5.关联 6.单视频 7.视频文字 8纯url
      me.data.map(function (value) {
        if (value.type === '2' || value.type === '3') {
          me.imgAll = me.imgAll.concat(value.images)
        } else if (value.type === '6' || value.type === '7') {
          me.videoImgAll = me.videoImgAll.concat(value.images)
        }
      })
    },
    // 针对文章详情过滤
    setContent() {
      const me = this
      let videoNum = 0 // 视频出现的次数
      me.showContent.map(function (value) {
        // type 1.文字 2.单图 3.图文 4.段落 5.关联 6.单视频 7.视频文字 8纯url
        if (value.type === '1') {
          // 文字时，解析短话题及外链
          value.content = value.content.replace(
            /(([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~\/])+)/g,
            `<div class='url-mes'><i></i><p><a href='$1'>$1</a></p></div>`
          )
          if (!(value.shortTopicList && value.shortTopicList.length)) {
            // 判定短话题
            return
          }
          value.shortTopicList.map(function (shortValue) {
            const shortContent = ('#' + shortValue.shortContent + '#').toLowerCase()
            const content = value.content.toLowerCase()
            value.content = content.replace(
              `${shortContent}`,
              `<strong class='short-topic' data-id='${shortValue.id}'>#` + shortValue.shortContent + `#</strong>`
            )
          })
        } else if (value.type === '5') {
          value.linkData = {
            id: value.id,
            content: value.content,
            type: value.relationType,
            link: value.link,
            image: value.img,
            price: value.contentDesc
          }
          value.linkData.name = getLinkName(value.linkData.type)
        } else if (value.type === '6' || value.type === '7') {
          if (value.link && value.link.indexOf('mp4') > -1) {
            if ((videoNum !== 0 && me.type === 'video_detail') || me.type !== 'video_detail') {
              value.videoUrl = value.link
            }
            videoNum += 1
          } else {
            value.isYk = false
          }
          if (!(me.data.type === 'topic_detail' || me.data.type === 'moment_detail')) {
            return
          }
          value.height = '52vw'
          value.width = '100%'
          if (!value.vodType) {
            return
          }
          const size = value.vodType.split('x')
          if (!(!isNaN(size[size.length - 1]) && !isNaN(size[size.length - 2]))) {
            return
          }
          if (size[size.length - 2] > size[size.length - 1]) {
            value.width = '52vw'
            value.height = '92vw'
          }
        } else if (value.type === '8' && value.link.indexOf('youku.com') > -1) {
          // 外链是优酷时特殊处理
          value.type === '6'
          value.isYk = true
          value.ykUrl = value.link.replace(
            /https?:\/\/[\w-+&@#/%?=~|!:,.;]+youku.com[\w-+&@#/%?=~|!:,.;]*\/(\w*==|id_\w*|\w*\/)(\/v.html|.html|.swf|\/v.swf)/g,
            '$1'
          )
        }
      })
      if (me.imgAll && me.imgAll.length) {
        me.showMomentImg = true
        setTimeout(() => {
          me.$refs && me.$refs.cirimg && me.$refs.cirimg.updata({ img: me.imgAll })
        }, 10)
      }
    },
    // 大图展示
    imgBig(img) {
      this.$refs.showImage.init(img)
    },
    isGif(url) {
      return url.indexOf('.gif') > -1
    },
    updateArticleData(ids, lists) {
      const me = this
      ids.map((id, index) => {
        let findData =
          lists.find(_ => {
            return _.id === id
          }) || {}
        if (findData.id) {
          let imgAll = []
          findData.content.map(function (value) {
            if (value.type === '2' || value.type === '3') {
              imgAll = imgAll.concat(value.images)
            }
          })
          console.log(imgAll, 'imgAllimgAllimgAll')
          findData = {
            ...findData,
            author: findData.auther,
            viewNum: findData.viewcnt,
            replycnt: findData.replycnt,
            img: imgAll.length ? imgAll[0].imgUrl : ''
          }
          me.$refs.cDetailArticle[index].updataData(findData)
        }
      })
    }
  }
}
</script>

<style lang="scss">
@import 'CDetailsContentList.scss';
</style>
