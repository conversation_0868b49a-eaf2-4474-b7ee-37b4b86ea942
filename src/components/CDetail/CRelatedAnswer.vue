<template>
  <div class="c-related-answer">
    <div class="recommend-container">
      <div class="recommend-header flex">
        <span class="header-left">{{ answerNum }}个回答</span>
        <span class="header-right" @click="setLabel()"
          >{{ tip }}排序<img src="/static/img/detail/<EMAIL>" alt=""
        /></span>
      </div>
      <div class="label" v-if="showLable">
        <i class="sanjiao_down"></i>
        <div class="label-content">
          <p
            v-for="(tip, index) in tipMes"
            :key="index"
            class="tip text-center"
            @click="setLabel(tip)"
          >
            {{ tip.name }}
          </p>
        </div>
      </div>
      <c-feedList :cards="detail" v-if="detail && detail.length"></c-feedList>
      <div v-else>暂无数据</div>
      <div class="all-answer text-center" v-if="answerNum > 20">
        查看全部回答
      </div>
    </div>
  </div>
</template>

<script type="es6">
import CFeedList from '../CFeedList/index.vue'
import { getQueryEassyDetail } from '@/api/articleModule'
export default {
  name: 'c-related-answer',
  props: ['id'],
  components: {
    CFeedList
  },
  data() {
    return {
      detail: {},
      tip: '精选',
      digest: '1',
      tipMes: [
        {
          name: '精选',
          digest: '1'
        },
        {
          name: '最新',
          digest: '3'
        }
      ],
      showLable: false,
      answerNum: 0,
      stringType: '' //接口返回的提示信息
    };
  },
  mounted() {
    let me = this
    me.getData()
  },
  methods: {
    // 获取问答列表
    getData() {
      let me = this
      getQueryEassyDetail({
        action: '22009',
        page: 1,
        id: me.id,
        autherid: '',
        limit: 20,
        digest: me.digest
      })
        .then(response => {
          me.detail = {}
          if (response.data.code === 0) {
            let result = response.data.data
            me.answerNum = result.replyNum
            result.replyList.map(function (value) {
              value.digest = 0
              value.img = value.images
              value.imageCount = value.imgNum
            })
            me.detail = result.replyList
          }
          if (me.detail && !me.detail.length) {
            me.stringType = 'mesanswerdatanone'
          }
        })
        .catch(err => {
          me.stringType = 'networktimeerror'
          console.log(err)
        })
    },
    // 切换列表
    setLabel(data) {
      let me = this
      if (data && me.tip !== data.name) {
        me.tip = data.name
        me.digest = data.digest
        me.getData()
      }
      me.showLable = !me.showLable
    }
  },
  watch: {}
}
</script>

<style lang="scss">
@import 'CRelatedAnswer.scss';
</style>
