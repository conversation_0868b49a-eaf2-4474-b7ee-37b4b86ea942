<template>
  <section class="c-details-content">
    <c-details-content-list
      v-if="data.type"
      ref="cDetailsContentList"
      :type="data.type || ''"
      :data="data.content"
      :is-answer="isAnswer"
    />
    <!-- 动态详情、关联 -->
    <div
      v-if="
        data.type === 'moment_detail' &&
        data.essayDetailNest &&
        data.essayDetailNest.id
      "
      class="essay-detail-nest"
    >
      <div class="nest-user-content dotdotdot1">
        <span class="nest-user">@{{ data.essayDetailNest.auther }}:</span>
        {{ data.essayDetailNest.title }}
      </div>
      <c-details-content-list
        :type="data.type || ''"
        :data="data.essayDetailNest.content"
        :is-answer="false"
      />
    </div>
    <div v-if="data.hoopId || data.circleList.length" class="show-circle-name">
      <span v-if="data.hoopId" class="circle-name">
        <img :src="data.hoopLogo" alt class="circle-name-img" />
        &ensp;{{ data.hoopName }}
      </span>
      <span
        v-for="(topic, index) in data.circleList"
        :key="index"
        class="circle-name"
      >
        <img
          :src="topic.img || '/static/img/detail/<EMAIL>'"
          alt
          class="circle-name-img topic-icon"
        />
        &ensp;{{ topic.name }}
      </span>
    </div>
    <!-- 动态、文章地址部分 -->
    <div
      v-if="
        (type === 'moment_detail' || type === 'essay_detail') && data.location
      "
      class="address"
    >
      <div class="address-left dotdotdot1">
        <img src="/static/img/detail/<EMAIL>" alt />
        <span class="dotdotdot1">{{ data.location }}</span>
      </div>
    </div>
    <div v-if="data.content" class="mianze">
      {{
        data.original === '1'
          ? '原创作品版权归作者和摩托范所有，转载请联系摩托范官方团队'
          : '本文由用户自行发布，如有侵权请系摩托范官方团队删除'
      }}
    </div>
  </section>
</template>

<script type="es6">
import CDetailsContentList from './CDetailsContentList.vue'
import { getQueryEassyDetail } from '@/api/articleModule'
export default {
  name: 'CDetailsContent',
  components: {
    CDetailsContentList
  },
  props: ['data'],
  data() {
    return {
      imgAll: [], // 所有图片集合
      videoImgAll: [], // 视频图片集合
      linkImgAll: [], // （link字段）外链图片集合
      showContent: [], // 展示的文章内容信息
      uploadsArticle: [],
      articleList: [],
      type: '',
      showMomentImg: false, // 是否显示动态详情图片
      isAnswer: false // 问答详情是否展示全文
      // isShowMore: false
    };
  },
  computed: {},
  watch: {},
  mounted() {
    const me = this
    me.articleList = []
    me.uploadsArticle = []
    me.type = me.data.type ? me.data.type : me.type
    me.getLinkImgAll()
    me.setContent()
    me.getImgs()
  },
  methods: {
    // 获取外链图片集合（如车辆外链 问答详情id：1560）
    getLinkImgAll() {
      const me = this
      me.data.link.map(function (value) {
        me.linkImgAll = me.linkImgAll.concat(value.images)
      })
    },
    // 针对文章详情过滤
    setContent() {
      const me = this
      if (me.type === 'moment_detail' || me.type === 'riding_detail' || me.type === 'topic_detail' || me.type === 'car_detail' || !me.type) {
        me.data.content.map(function (value) {
          // type 1.文字 2.单图 3.图文 4.段落 5.关联 6.单视频 7.视频文字 8纯url
          if (!(value.type === '2' || value.type === '3')) {
            me.showContent = me.showContent.concat(value)
          }
        })
      } else {
        me.showContent = me.data.content
      }
      // 等待页面渲染完成后，加载优酷腾讯sdk、短话题
      me.$nextTick(function () {
        if (!me.type) {
          const dom = document.querySelector('.text-content')
          if (!dom) return
          const offsetHeight = 72
          me.isAnswer = dom.offsetHeight > offsetHeight
          console.log(dom.offsetHeight)
        }
      })
    },
    // 整合所有图片，大图展示使用
    getImgs() {
      const me = this
      const articleIdList = []
      // type 1.文字 2.单图 3.图文 4.段落 5.关联 6.单视频 7.视频文字 8纯url
      me.data.content.map(function (value) {
        if (value.type === '2' || value.type === '3') {
          me.imgAll = me.imgAll.concat(value.images)
        } else if (value.type === '6' || value.type === '7') {
          me.videoImgAll = me.videoImgAll.concat(value.images)
        } else if (value.type === '11') {
          const article = value.article || {}
          article.id ? articleIdList.push(article.id) : null
        }
      })
      if (articleIdList.length) {
        me.getArticleDataList(articleIdList)
      }
    },
    // 获取所有文章
    getArticleDataList(list) {
      const me = this
      list.map((_, index) => {
        const lastId = index + 1 === list.length
        setTimeout(() => {
          if (_) {
            me.httpRequestArticle(_, lastId ? _ : '', list)
          }
        }, 1000 * index)
      })
    },
    // 顺序拉文章列表信息
    httpRequestArticle(id, lastOneId, idList) {
      const me = this
      const option = {
        action: '22004',
        id,
        lastOneId,
        sourceType: me.sourceType || '',
        platform: '3',
        version: '3.42.0'
      }
      getQueryEassyDetail(option)
        .then(response => {
          if (response.data.code === 0) {
            const result = response.data.data || {}
            me.articleList.push(result || {})
            if (option.lastOneId) {
              me.updateArticleData(idList)
            }
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    updateArticleData(idList) {
      this.$refs.cDetailsContentList.updateArticleData(idList, this.articleList)
    }
  }
}
</script>

<style lang="scss">
@import 'CDetailsContent.scss';
</style>
