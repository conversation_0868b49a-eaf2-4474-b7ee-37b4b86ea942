.c-details-content-list {
    .content {
        margin-bottom: 20px;
        padding: 0 14px;

        .video-content {
            width: 100%;
            min-height: 196px;
            position: relative;

            .video {
                width: 100%;
                height: 196px;
                z-index: 1;
            }

            .content-explain {
                margin-top: 10px;
            }
        }

        .text-mes {
            .answer-all {
                font-size: 14px;
                color: #e5332c;
                img {
                    width: 15px;
                    height: 15px;
                    transform: rotate(90deg);
                }
            }
        }
        .text-content {
            font-size: 17px;
            color: #333;
            line-height: 30px;
            word-wrap: break-word;
            white-space: pre-wrap;
            white-space: -moz-pre-wrap;
            white-space: -pre-wrap;
            white-space: -o-pre-wrap;
            word-wrap: break-word;
            overflow: hidden;
            letter-spacing: .005px;
        }

        .paragraph-headings {
            font-size: 17px;
            color: #333;
            line-height: 20px;
            font-weight: 600;
        }

        .img-content {
            .show-img-content {
                position: relative;
                min-height: 100px;
                background-color: #f8f8f8;

                .detail-imgs {
                    width: 100%;
                    max-height: 1000px;
                    min-height: 10px;
                    object-fit: cover;
                }

                .is-gif {
                    position: absolute;
                    width: 50px;
                    height: 20px;
                    bottom: 10px;
                    right: 10px;
                    background-color: #0000003a;
                    color: #ffffff;
                    border-radius: 50px;
                    line-height: 20px;
                    text-align: center;
                }
            }
        }

        .content-explain {
            margin-top: 15px;

            img {
                width: 10px;
                height: 10px;
                margin: 3px 6px;
                position: absolute;
            }

            pre {
                padding: 0 0 0 25px;
                font-size: 14px;
                color: #666;
                line-height: 20px;
                white-space: pre-wrap;
                white-space: -moz-pre-wrap;
                white-space: -pre-wrap;
                white-space: -o-pre-wrap;
                word-wrap: break-word;
                overflow: hidden;
            }
        }

        .c-outer-chain {
            .alone {
                padding: 0;
            }
        }

        .url-mes {
            i {
                display: inline-block;
                position: absolute;
                background-image: url('/static/img/<EMAIL>');
                background-size: 100% 100%;
                width: 20px;
                height: 20px;
            }

            p {
                text-indent: 25px;

                a {
                    font-size: 14px;
                    color: #2eaee5;
                    line-height: 20px;
                    word-wrap: break-word;
                }
            }
        }
    }
}