.circle-img {
    width: 100%;
    margin: 10px 0 8px;
    .flex {
        justify-content: left;
    }
    .imgs {
        display: inline-block;
        position: relative;
        width: 100%;
        background-color: #f5f5f8;
        margin: 0 4px 4px 0;
        object-fit: cover;
    }
    .imgs:nth-of-type(3) {
        margin: 0 0 4px 0;
    }
    .imgsOnlyone {
        min-width: 100px;
        min-height: 100px;
        object-fit: contain;
        display: inline-block;
        position: relative;
        background-color: #f5f5f8;
        img {
            max-height: 462.5px;
            width: 100%;
            object-fit: cover;
        }
        .is-gif {
            position: absolute;
            width: 50px;
            height: 20px;
            bottom: 6px;
            right: 6px;
            background-color: #0000003a;
            color: #ffffff;
            border-radius: 50px;
            line-height: 20px;
            text-align: center;
        }
    }
}
