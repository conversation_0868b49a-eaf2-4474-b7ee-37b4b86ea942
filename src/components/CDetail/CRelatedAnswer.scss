.c-related-answer {
    border-top: 10px solid #f5f5f8;
    position: relative;

    .recommend-header {
        background-color: #fff;
        height: 40px;
        padding: 0 10px;
        position: relative;
        line-height: 40px;
        font-size: 15px;
        color: #666;
        border-bottom: .5px solid rgba(0,0,0,.2);

        .header-right {
            position: absolute;
            right: 10px;

            img {
                width: 10px;
                height: 10px;
                top: -15px;
                position: relative;
                margin-left: 10px;
            }
        }
    }

    .label {
        position: absolute;
        right: 0;
        z-index: 1;

        .sanjiao_down {
            top: -7px;
            left: 80px;
            position: relative;
            width: 0;
            height: 0;
            overflow: hidden;
            font-size: 0;
            /*是因为, 虽然宽高度为0, 但在IE6下会具有默认的 */
            line-height: 0;
            /* 字体大小和行高, 导致盒子呈现被撑开的长矩形 */
            border-width: .1px;
            border-style: dashed dashed solid dashed;
            /*IE6下, 设置余下三条边的border-style为dashed,即可达到透明的效果*/
            border-color: transparent transparent #f8f8f8 transparent;
        }

        .label-content {
            background-color: #f8f8f8;
            padding: 0 10px;
            line-height: 40px;
        }

        .tip {
            width: 100px;
            height: 40px;
            font-size: 15px;
            color: #333;
        }

        .tip:nth-of-type(1) {
            border-bottom: .5px solid rgba(0,0,0,.2);
        }
    }

    .all-answer {
        width: 90%;
        height: 40px;
        line-height: 40px;
        margin: 0 auto 10px;
        background-color: #e5332c;
        font-size: 15px;
        color: #fff;
    }

    .default-graph {
        div {
            height: 424.5px;
        }
    }
}