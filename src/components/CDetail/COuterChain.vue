<template>
  <section class="c-outer-chain">
    <!-- 轮播图 -->
    <div v-if="data && data.length > 1" class="slider">
      <el-carousel height="200px" direction="vertical" :autoplay="false">
        <el-carousel-item v-for="(data, index) in slider" :key="index">
          <div class="detail-link details-link">
            <div v-if="data.score" class="score">
              <span>{{ data.score.split('.')[0] }}.</span
              ><span>{{ data.score.split('.')[1] }}</span>
            </div>
            <img
              :src="data.linkData.image"
              class="head-img"
              onerror='onerror=null;src="/static/img/detail/<EMAIL>"'
            />
            <div :class="{ setWidth: slider.score }" class="content flex">
              <p class="dotdotdot2 content-name">{{ data.linkData.content }}</p>
              <span
                v-if="data.linkData.type !== 'car_detail'"
                class="type-name"
                >{{ data.linkData.name }}</span
              >
              <div v-else class="good-price">
                <div
                  v-if="
                    data.minPrice !== '' &&
                    data.maxPrice !== '' &&
                    (data.minPrice !== '0.00' || data.maxPrice !== '0.00')
                  "
                  class="price dotdotdot1"
                >
                  <span class="company">￥</span>
                  <span v-if="data.minPrice !== '0.00'">{{
                    data.minPrice || intervalNum
                  }}</span>
                  <span
                    v-if="
                      data.minPrice !== '0.00' &&
                      data.maxPrice !== '0.00' &&
                      data.minPrice !== data.maxPrice
                    "
                    >-</span
                  >
                  <span
                    v-if="
                      data.maxPrice !== '0.00' &&
                      data.minPrice !== data.maxPrice
                    "
                    >{{ data.maxPrice || intervalNum }}</span
                  >
                </div>
                <span v-else>暂无报价</span>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
    <div v-else class="alone">
      <div v-if="slider && slider.content" class="detail-link">
        <div v-if="slider.score" class="score">
          <span>{{ slider.score.split('.')[0] }}.</span
          ><span>{{ slider.score.split('.')[1] }}</span>
        </div>
        <img
          :src="slider && slider.image"
          class="head-img"
          onerror='onerror=null;src="/static/img/detail/<EMAIL>"'
        />
        <div :class="{ setWidth: slider.score }" class="content flex">
          <p class="dotdotdot2 content-name">{{ slider && slider.content }}</p>
          <span v-if="slider.type !== 'car_detail'" class="type-name">{{
            slider.name
          }}</span>
          <div v-else class="good-price">
            <div
              v-if="
                slider.minPrice !== '' &&
                slider.maxPrice !== '' &&
                (slider.minPrice !== '0.00' || slider.maxPrice !== '0.00')
              "
              class="price dotdotdot1"
            >
              <span class="company">￥</span>
              <span v-if="slider.minPrice !== '0.00'">{{
                slider.minPrice || intervalNum
              }}</span>
              <span
                v-if="
                  slider.minPrice !== '0.00' &&
                  slider.maxPrice !== '0.00' &&
                  slider.minPrice !== slider.maxPrice
                "
                >-</span
              >
              <span
                v-if="
                  slider.maxPrice !== '0.00' &&
                  slider.minPrice !== slider.maxPrice
                "
                >{{ slider.maxPrice || intervalNum }}</span
              >
            </div>
            <span v-else>暂无报价</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script type="es6">
import { getLinkName } from '@/utils'
export default {
  name: 'COuterChain',
  components: {},
  props: ['data', 'trackid'],
  data() {
    return {
      slider: null
    };
  },
  computed: {},
  watch: {
    intervalNum(data) {
      if (data === undefined) {
        return
      }
      if (typeof data === 'number') {
        data = data.toFixed(2)
      }
      data = data.replace('.00', '')
      data = data.substring(data.length - 2) === '.0' ? data.replace('.0', '') : data
      // 小于3位数，直接返回
      if (data < 1000) {
        return data
      }
      if (!data.split('.')[0].length > 3) {
        return data
      }
      let price = data.split('.')[0]
      const pricePoint = data.split('.')[1]
      let result = ''
      price = price.toString()
      result =
        price.length > 6
          ? `${price.substring(0, price.length - 6)},${price.substring(price.length - 6, price.length - 3)},${price.substring(
              price.length - 3,
              price.length
            )}`
          : `${price.substring(0, price.length - 3)},${price.substring(price.length - 3, price.length)}`
      result = pricePoint ? `${result},${pricePoint}` : result
      return result
    }
  },
  mounted() {
    const me = this
    me.slider = me.data
    if (me.slider.length > 1 || !me.slider.content) {
      me.setData()
    }
  },
  methods: {
    // 设置数据
    setData() {
      const me = this
      me.slider.map(function (value) {
        // 加油站不能外链
        if (value.relationType === 'shop_detail' && value.label === '4') {
          return
        }

        value.linkData = {
          id: value.id,
          content: value.content,
          type: value.relationType,
          link: value.link,
          image: (value.images && value.images[0] && value.images[0].imgUrl) || value.img,
          price: value.contentDesc,
          score: value.score,
          maxPrice: value.maxPrice,
          minPrice: value.minPrice
        }
        value.linkData.name = getLinkName(value.linkData.type)
      })
      if (me.slider.length === 1) {
        me.slider = me.slider[0] && me.slider[0].linkData
      }
      const result = me.slider
      me.slider = []
      me.slider = result
    }
  }
}
</script>

<style lang="scss">
@import 'COuterChain.scss';
</style>
