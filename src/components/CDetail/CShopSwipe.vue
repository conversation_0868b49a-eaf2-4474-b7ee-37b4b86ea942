<template>
  <section class="c-shop-swipe">
    <!-- 轮播图 -->
    <div
      v-if="data.length > 0"
      class="plate-slide slider"
      :style="{ left: offLeft }"
    >
      <div v-for="(item, si) in data" :key="si" class="slide-imgs">
        <c-detail-shop :data="item" :no-json="true" />
      </div>
    </div>
    <div v-else class="slider">
      <c-detail-shop :data="data[0]" />
    </div>
  </section>
</template>

<script type="es6">
import CDetailShop from './CDetailShop.vue'
export default {
  name: 'CShopSwipe',
  components: {
    CDetailShop
  },
  props: ['data'],
  data() {
    return {
      goodData: {},
      offLeft: 0,
      activeIndex: 0
    };
  },
  created() {
    console.log(this.data)
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss">
@import 'CShopSwipe.scss';
</style>
