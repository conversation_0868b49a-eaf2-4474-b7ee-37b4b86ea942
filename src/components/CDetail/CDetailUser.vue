<template>
  <section :class="showClass" class="c-detail-user flex">
    <div class="user-img-content">
      <img
        :src="autherImg"
        onerror="onerror=null;src='/static/img/common/<EMAIL>'"
        class="user-img"
      />
      <div v-if="certifyList && certifyList.length > 0" class="certify-list">
        <span
          v-for="(certify, index) in certifyList"
          :key="index"
          class="certify"
        >
          <img
            v-if="certify.type === '5' && certify.status === 1"
            src="/static/img/detail/<EMAIL>"
            alt=""
            class="icon first"
          />
          <img
            v-if="certify.type === '7' && certify.status === 1"
            src="/static/img/detail/<EMAIL>"
            alt=""
            class="icon two"
          />
          <img
            v-if="certify.type === '4' && certify.status === 1"
            src="/static/img/detail/<EMAIL>"
            alt=""
            class="icon three"
          />
          <img
            v-if="certify.type === '6' && certify.status === 1"
            src="/static/img/detail/<EMAIL>"
            alt=""
            class="icon four"
          />
          <img
            v-if="certify.type === '8' && certify.status === 1"
            src="/static/img/detail/<EMAIL>"
            alt=""
            class="icon five"
          />
          <img
            v-if="certify.type === '1' && certify.status === 1"
            src="/static/img/detail/<EMAIL>"
            alt=""
            class="icon six"
          />
          <img
            v-if="certify.type === '2' && certify.status === 1"
            src="/static/img/detail/<EMAIL>"
            alt=""
            class="icon seven"
          />
        </span>
      </div>
    </div>
    <div :class="{ 'set-width': !isCircle }" class="num-info">
      <!-- mediate: !card.certifybrand没有品牌认证时，居中显示-->
      <div
        :class="{ mediate: !carId && !showTime }"
        class="auther-name-content flex"
      >
        <span class="auther-name dotdotdot1">{{ auther }}</span>
        <i
          :class="{
            'user-man': gender === 1,
            'user-female': gender === 2,
            'icon-ios': isIOS
          }"
          class="icon"
        />
        <i
          v-show="showVowner && ifLocalAuther === 1"
          class="icon vehicle-owner"
        ></i>
        <i
          v-show="showAuthor && ifLocalAuther === 1"
          class="icon author-owner"
        ></i>
        <i v-show="data.topStatus === 1" class="icon top"></i>
        <br />
      </div>
      <!-- 认证的品牌 -->
      <div
        v-if="(certifyList && certifyList.length > 0) || showTime"
        :class="{ 'set-time-auther': showTime }"
        class="auther-info dotdotdot1 flex"
      >
        <time v-if="data.dateline && showTime" class="auther-time">{{
          $filters.timeFullS(data.dateline * 1000)
        }}</time>
        <template v-for="(certify, index) in certifyList" :key="index">
          <div
            v-if="certify.type === '3' && certify.status === 1"
            class="brand dotdotdot1"
          >
            <span class="name dotdotdot1">{{ certify.certifyName }}</span>
          </div>
        </template>
      </div>
    </div>
  </section>
</template>

<script type="es6">
export default {
  name: 'CDetailUser',
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    },
    highLight: {
      type: Boolean,
      default: false
    },
    eId: {
      type: String,
      default: null
    },
    eNid: {
      type: String,
      default: ''
    },
    pEid: {
      type: String,
      default: ''
    },
    pNeid: {
      type: String,
      default: ''
    },
    contentType: {
      type: String,
      default: ''
    },
    isCircle: {
      type: Boolean,
      default: false
    },
    isGoNext: {
      type: Boolean,
      default: true
    },
    showVowner: {
      type: Boolean,
      default: false
    }, // 是否显示车主
    showAuthor: {
      type: Boolean,
      default: false
    }, // 是否显示作者
    showClass: {
      type: String,
      default: ''
    },
    showTime: {
      type: Boolean,
      default: false
    } // 是否显示时间
  },
  data() {
    return {
      userAutherid: '',
      auther: '',
      autherid: '',
      autherImg: '',
      gender: 0,
      carId: '', // 是否有车辆，如果有车辆，此值为type值
      ifLocalAuther: 0, // 是否是车主或作者，需配合showVowner使用
      certifyList: [],
      authenticationList: [],
      isIOS: window.isIOS
    };
  },
  computed: {},
  watch: {},
  mounted() {
    this.ready()
  },
  methods: {
    ready(data) {
      const user = JSON.parse(localStorage.userInfo || '{}')
      const newData = data || this.data
      this.userAutherid = user.uid || ''
      this.auther = newData.auther || newData.author || (newData.userInfo && newData.userInfo.auther) || (newData.user && newData.user.auther)
      this.autherid = newData.autherid || (newData.userInfo && newData.userInfo.autherid) || (newData.user && newData.user.autherid)
      this.autherImg =
        newData.autherImg || newData.autherimg || (newData.userInfo && newData.userInfo.autherimg) || (newData.user && newData.user.autherimg)
      this.gender = newData.gender || (newData.userInfo && newData.userInfo.gender) || (newData.user && newData.user.gender) || 0
      this.gender = parseInt(this.gender)
      this.certifyList = newData.certifyList || (newData.userInfo && newData.userInfo.certifyList) || (newData.user && newData.user.certifyList) || []
      this.ifLocalAuther = newData.ifLocalAuther
      this.authenticationList = []
      this.carId = ''
      if (!this.certifyList.length) {
        return
      }
      this.setAutherList()
    },
    setAutherList() {
      const me = this
      const car = this.certifyList.filter(_ => _.type === '3')
      me.carId = car && car[0] && car[0].status && car[0].status === 1 ? car[0].type : ''
      me.certifyList.map(function (value) {
        if (value.type !== me.carId) {
          me.authenticationList.push(value)
        }
      })
      me.authenticationList = me.carId ? me.authenticationList.splice(0, 2).push(car[0]) : me.authenticationList.splice(0, 2)
    }
  }
}
</script>

<style lang="scss">
@import 'CDetailUser.scss';
</style>
