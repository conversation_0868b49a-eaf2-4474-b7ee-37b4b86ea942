.c-detail-user {
  margin-bottom: 10px;
  position: relative;
  height: 32px;

  .user-img-content {
    position: absolute;
    left: 0;
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    vertical-align: middle;

    .user-img {
      display: inline-block;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      background-image: ('/static/img/detail/<EMAIL>');
      background-size: 100% 100%;
    }

    .certify-list {
      height: 16px;
      display: inline-block;
      vertical-align: top;
      position: absolute;
      top: 22px;
      left: 22px;

      .certify {
        .icon {
          position: absolute;
          left: -3px;
          top: -3px;
          display: inline-block;
          height: 15px;
          object-fit: cover;
        }

        .first {
          z-index: 7;
        }

        .two {
          z-index: 6;
        }

        .three {
          z-index: 5;
        }

        .four {
          z-index: 4;
        }

        .five {
          z-index: 3;
        }

        .six {
          z-index: 2;
        }

        .seven {
          z-index: 1;
        }
      }
    }
  }

  .num-info {
    position: relative;
    padding: 0 65px 0 39px;
    width: 100%;

    .auther-name-content {
      display: inline-block;
      width: 100%;

      .auther-name {
        display: inline-block;
        font-size: 12px;
        color: #111E36;
        font-weight: 600;
        max-width: 90%;
        margin-right: .05px;
      }

      .icon {
        position: relative;
        width: 12px;
        height: 12px;
      }

      .user-man {
        display: inline-block;
        background-image: url('/static/img/detail/<EMAIL>');
        background-size: 100% 100%;
      }

      .user-female {
        display: inline-block;
        background-image: url('/static/img/detail/<EMAIL>');
        background-size: 100% 100%;
      }

      .vehicle-owner {
        display: inline-block;
        width: 28px;
        height: 15px;
        top: 1px;
        // background-image: url('/static/img/detail/owner-icon.png');
        background-size: 100% 100%;
      }

      .author-owner {
        display: inline-block;
        width: 28px;
        height: 15px;
        top: 1px;
        background-image: url('/static/img/detail/author-owner.png');
        background-size: 100% 100%;
      }

      .top {
        display: inline-block;
        width: 27px;
        height: 15px;
        background-image: ('/static/img/detail/top-icon.png');
        background-size: 100% 100%;
      }
    }

    .auther-name-content.mediate {
      position: relative;
      height: 30px;
      line-height: 30px;

      .icon {
        position: relative;
        top: -9px;
      }
    }

    .auther-info {
      .auther-time {
        font-size: 10px;
        color: #999;
        margin-right: 5px;
        line-height: 16px;
      }

      .brand {
        display: inline-block;
        width: 100%;
        margin-top: 1px;
        position: relative;

        img {
          display: inline-block;
          width: 8px;
          height: 10px;
          object-fit: cover;
          position: absolute;
          top: 3px;
        }

        .name {
          display: inline-block;
          width: 100%;
          font-size: 1px;
          color: #999;
          line-height: 12.5px;
        }
      }
    }

    .set-time-auther {
      .brand {
        margin-top: 0;

        img {
          top: 2px;
        }
      }
    }

    .gift-info {
      font-size: 11px;
      color: #999;
    }
  }

  .btn-circle-attention {
    position: absolute;
    right: 0;
    top: 9px;
  }

  .set-width {
    padding: 0 0 0 39px;
  }
}

.c-detail-user-title {
  height: 45px;

  .user-img-content {
    width: 45px;
    height: 45px;

    .certify-list {
      .certify {
        .icon {
          left: 12px;
          top: 8px;
        }
      }
    }
  }

  .num-info {
    padding: 0 65px 0 54px;

    .auther-name-content {
      line-height: 20px;

      .icon {
        top: -4px;
      }

      .auther-name {
        font-size: 18px;
      }
    }

    // 文章详情上
    .auther-name-content.mediate {
      position: relative;
      height: 45px;
      line-height: 45px;

      .icon {
        position: relative;
        top: -16px;
      }

      .icon-ios {
        top: 0;
      }
    }
  }
}