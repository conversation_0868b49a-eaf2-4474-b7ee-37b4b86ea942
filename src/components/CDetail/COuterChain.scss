.c-outer-chain {
    position: relative;

    .detail-link {
        position: relative;
        border: 1px solid #eee;
        margin: 10px 0 14px;
        border-radius: 6px;
        height: 90px;
        background-color: #fff;

        .score {
            position: absolute;
            width: 40px;
            height: 40px;
            padding: 2px 2px 0 0;
            right: 0;
            top: 0;
            z-index: 100;
            background-image: url('/static/img/detail/<EMAIL>');
            background-size: 100% 100%;
            background-size: cover;
            text-align: right;

            span:nth-of-type(1) {
                font-size: 15px;
                color: #fff;
                font-weight: bold;
            }

            span:nth-of-type(2) {
                font-size: 11px;
                color: #fff;
                font-weight: bold;
            }
        }

        .head-img {
            position: absolute;
            width: 70px;
            height: 70px;
            margin: 10px;
            object-fit: cover;
        }

        .content {
            justify-content: center;
            flex-direction: column;
            height: 80px;
            padding: 0 0 0 90px;
            font-size: 14px;
            color: #999;

            .content-name {
                margin-bottom: 8px;
                line-height: 18px;
                font-size: 15px;
                color: #333;
            }

            .type-name {
                color: #fff;
                background-color: #333;
                border-radius: 3px;
                width: 27px;
                padding: 1px;
            }

            .good-price {
                color: #e5332c;
                margin-bottom: 8px;

                .price {
                    font-size: 15px;

                    .company {
                        font-size: 12px;
                    }

                    span {
                        font-weight: 600;
                    }
                }
            }
        }

        .content.setWidth {
            padding: 0 40px 0 90px;
        }
    }

    //首页轮播图
    .slider {
        position: relative;
        background-size: contain;
        background-position-y: bottom;

        .mint-swipe {
            position: relative;
            width: 100%;
            height: 110px;
            border-radius: 2px;
            overflow: inherit;
            z-index: 100;

            .mint-swipe-indicators {
                position: absolute;
                transform: translateX(0);
                bottom: .1px;
                text-align: center;
                left: 38%;

                .mint-swipe-indicator {
                    opacity: 1;
                    width: 4px;
                    height: 4px;
                    border-radius: 50%;
                    margin: 0 .06px;
                    background-color: #999;
                }

                .mint-swipe-indicator:nth-last-child(1) {
                    margin: 0 0 0 .06px;
                }

                .is-active {
                    display: inline-block;
                    width: 17px;
                    height: 4px;
                    border-radius: 10px;
                    opacity: 1;
                    background-color: #999;
                }
            }

            .mint-swipe-items-wrap {
                .mint-swipe-item {
                    padding: 0 10px;
                }
            }
        }
    }

    .alone {
        padding: 0 14px;
    }

    .details-link {
        padding-bottom: 30px;
    }
}