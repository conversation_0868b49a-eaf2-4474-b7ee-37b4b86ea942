<template>
  <section class="c-details-head">
    <!-- 观点详情、文章详情（顶部有图）顶部标题 -->
    <div v-if="data.type" class="detail-title">
      <img
        v-if="data.imgOriginal"
        :src="data.imgOriginal"
        alt=""
        class="title-img"
      />
      <div v-if="data.type === 'video_detail' && videoInfo.link">
        <video
          :src="videoInfo.link"
          :poster="videoInfo.img"
          controls="controls"
          class="title-video"
        />
      </div>
      <h2 v-if="data.title" class="title">{{ data.title }}</h2>
      <div v-if="data.type === 'opinion_detail'" class="opinion-title flex">
        <p>
          查看全部<strong class="num">{{ data.askNum }}</strong
          >个回答<img
            src="/static/img/detail/<EMAIL>"
            alt=""
            class="arrow-right"
          />
        </p>
        <div v-if="data.type === 'opinion_detail'" class="title-right">
          <img src="/static/img/detail/icon_white_c@<EMAIL>" alt="" />写回答
        </div>
      </div>
      <div v-if="data.type === 'opinion_detail'" class="opinion_line" />
    </div>
    <!-- 动态详情、观点详情、文章详情顶部信息发布人信息 -->
    <div v-if="data.type" class="detail-auther-info">
      <div class="detail-auther">
        <c-detail-user
          :data="data"
          :high-light="false"
          :is-circle="true"
          :show-class="'c-detail-user-title'"
        />
      </div>
      <div class="detail-info">
        <time class="dotdotdot1"
          >{{ data.timetip
          }}{{ $filters.timeFullS(data.datetime * 1000) }}</time
        >
      </div>
    </div>
    <!-- 问答详情顶部信息发布人信息 -->
    <div v-else class="answer-title">
      <img v-if="data.cover" :src="data.cover" alt="" class="title-img" />
      <div class="answer-title-content">
        <img
          src="/static/img/detail/<EMAIL>"
          alt=""
          class="content-img"
        />
        <h2 class="title">{{ data.title }}</h2>
        <p class="content-mes flex">
          <span
            :class="{
              secret: data.gender === 0,
              man: data.gender === 1,
              female: data.gender === 2
            }"
            class="autherimg"
          >
            <img
              :src="data.authorimg || data.autherimg || data.autherImg"
              onerror='onerror=null;src="/static/img/detail/<EMAIL>"'
              alt=""
            />
          </span>
          <span>{{ data.author || data.auther }}&ensp;</span>
          <time>问于{{ $filters.timeFullS(data.dateline * 1000) }}</time>
        </p>
      </div>
    </div>
  </section>
</template>

<script type="es6">
import cDetailUser from './CDetailUser.vue'
export default {
  name: 'CDatasHead',
  components: {
    cDetailUser
  },
  props: ['data'],
  data() {
    return {
      videoInfo: {}
    };
  },
  computed: {},
  watch: {},
  mounted() {
    this.data.content &&
      this.data.content.length > 0 &&
      this.data.content.map(item => {
        if (['6', '7'].includes(item.type) && !this.videoInfo.link) {
          this.videoInfo = item
        }
      })
  },
  methods: {}
}
</script>

<style lang="scss">
@import 'CDetailsHead.scss';
</style>
