.c-details-head {
    .title {
        margin-top: 15px;
        font-size: 20px;
        color: #333;
        padding: 0 14px;
        line-height: 30px;
        font-weight: 500;
        word-wrap: break-word;
        word-break: break-all;
    }

    .title-img {
        width: 100%;
        height: 210px;
        margin-bottom: 5px;
        object-fit: cover;
    }

    .title-video {
        width: 100%;
        height: 210px;
        margin-bottom: 5px;
    }

    .detail-title {
        .opinion-title {
            margin-top: 12px;
            padding: 0 14px;
            font-size: 15px;
            color: #666;
            position: relative;
            line-height: 40px;

            img {
                position: relative;
            }

            .arrow-right {
                width: 15px;
                height: 15px;
                top: -12px;
                transform: rotate(270deg);
            }

            .title-right {
                position: absolute;
                right: 14px;
                height: 40px;
                border-radius: 50px;
                font-size: 15px;
                color: #666;

                img {
                    width: 22px;
                    height: 22px;
                    top: 10px;
                    margin-left: 05px;
                }
            }
        }

        .opinion_line {
            display: inline-block;
            width: 100%;
            height: 10px;
            background-color: #f5f5f8;
        }
    }

    .detail-auther-info {
        .detail-auther {
            position: relative;
            margin-top: 10px;
            padding: 10px 14px 0;
        }
    }

    .detail-info {
        position: relative;
        padding: 0 14px;

        time {
            display: inline-block;
            width: 100%;
            font-size: 12px;
            color: #666;
            overflow: hidden;

            i {
                display: inline-block;
                width: 10px;
                height: 12px;
                background-color: #999;
                margin: 0 10px;
                position: relative;
                top: 20px;
            }
        }
    }

    .answer-title {
        padding: 0 14px;
        margin-bottom: 30px;

        .title-img {
            position: relative;
            left: -14px;
        }

        .answer-title-content {
            background-color: #fff;
            border-radius: 4px;
            position: relative;
            box-shadow: 0 1px 15px 2px rgba(153, 153, 153, 0.14);

            .content-img {
                width: 85px;
                height: 85px;
                position: absolute;
                right: 0;
                top: 0;
            }

            .title {
                padding-top: 35px;
                position: relative;
            }

            .content-mes {
                padding: 20px 15px;
                line-height: 20px;

                .autherimg {
                    display: inline-block;
                    width: 25px;
                    height: 25px;
                    border-radius: 50%;
                    margin-right: 8px;
                    vertical-align: middle;

                    img {
                        display: inline-block;
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        object-fit: cover;
                    }
                }

                span {
                    font-size: 12px;
                    color: #999;
                }

                time {
                    font-size: 12px;
                    color: #999;
                }
            }
        }
    }
}