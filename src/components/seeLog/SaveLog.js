// 保存日志
// 个别查看公用日志需要先手动传递数据
// params
// {
//      module      所属模块ID
//      businessId  业务对象ID（如文章id，广告ID）
//      operateType 操作类型（新增、编辑、删除）
//      remark      详情-详情为空时，方可做前后比对
//      userName    操作人
//      before      修改前的对象JSON String
//      after       修改后的对象JSON String
// }

import store from '@/store'
import { saveLog } from '@/api/activeConfiguration'
export function postLog(
  module,
  businessId,
  operateType,
  remark,
  before,
  after
) {
  saveLog({
    module,
    businessId,
    operateType,
    remark,
    userName: store.getters.name,
    before,
    after
  })
    .then((res) => {
      console.log(res)
    })
    .catch((err) => {
      console.log(err)
    })
}
