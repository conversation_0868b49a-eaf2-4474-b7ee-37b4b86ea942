/** 日志 */
<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :title="dialogTitle"
      width="1000px"
    >
      <el-table
        ref="shopList"
        :data="listData"
        highlight-current-row
        row-key="shopList"
        style="overflow-y: auto"
        width=""
        height="500px"
        border
      >
        >
        <el-table-column
          prop="operateDate"
          label="操作时间"
          align="center"
          width="160"
        />
        <el-table-column
          prop="userName"
          label="操作人"
          align="center"
          width="80"
        />
        <el-table-column
          prop="operateType"
          label="操作类型"
          align="center"
          width="110"
        />
        <el-table-column prop="remark" label="操作内容" align="left">
          <template v-slot="scope">
            <template v-if="scope.row.remarkIsJson">
              <div
                v-for="(content, index) in Object.keys(scope.row.showRemark)"
                :key="index"
              >
                {{ content }}: {{ scope.row.showRemark[content] }}
              </div>
            </template>
            <div v-else v-html="scope.row.showRemark"></div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
// '1:驾校,2:供应链-sku,3:供应链-spu,4:经销商开通关闭各业务,5:淘宝商品,6:京东商品,7:自营-sku,8:自营-spu,9:自营订单,10:用户封禁,11:增长官,12:二手车处罚,13:自营秒杀,14:口碑管理,15:身份证解密,16:电话记录,17:询价记录,18:一口价记录,19:经销商资料审核详情,20:资质审核详情,21:商家入驻审核详情,22:经销商详情,23:试驾记录,24:致命词,25:标注,26:用户信息,27:内容审核,28:评论审核,29:驾考题库操作记录,30:脱敏-手机号,31:脱敏-微信号,32:脱敏-支付宝账号,33:脱敏-身份证,34:脱敏-详情,35:用户处罚,36:二手车举报,37:异常用户规则,38:二手车异常用户审核记录,39:特邀作者,40:二手车,41:厂家,42:厂家订单,43:用户风险管理,44:AXN虚拟号绑定记录,45:AXB虚拟号绑定记录,46:广告方操作记录,47:广告计划操作记录,48:广告操作记录,49:二手车手机微信黑名单记录,
// 51:厂家订单合作项目,52:厂家订单收款,53:厂家订单开票,54:厂家订单合同，55:商家广告订单管理
import { getLogList } from '@/api/activeConfiguration'
export default {
  name: 'seeLog',
  components: {},
  data() {
    return {
      dialogTitle: '操作日志',
      dialogVisible: false,
      listData: [],
      module: '',
      businessId: ''
    }
  },
  mounted() {},
  methods: {
    init(module, businessId) {
      this.module = module
      this.businessId = businessId
      this.dialogVisible = true
      this.getList()
    },
    getList() {
      const me = this
      getLogList({
        limit: 1000,
        page: 1,
        businessId: me.businessId,
        module: me.module
      }).then((response) => {
        if (response.data.code === 0) {
          const data = response.data.data
          const showData = data.list || []
          showData.map((item) => {
            item.remarkIsJson = me.isJsonString(item.remark)
            item.showRemark = item.remarkIsJson
              ? JSON.parse(item.remark) || {}
              : item.remark
          })
          me.listData = showData
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    // 判定是否是json
    isJsonString(str) {
      try {
        JSON.parse(str)
      } catch (e) {
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss"></style>
