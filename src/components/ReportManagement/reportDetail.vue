<template>
  <div>
    <el-form ref="form" :model="form" :inline="true" label-width="70px">
      <el-form-item label="处理类型">
        <el-select
          v-model="form.status"
          style="width: 120px"
          @change="currentChange(1)"
        >
          <el-option
            v-for="(value, index) in reportStatusStrEnum"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div style="min-height: 600px">
      <el-table
        :data="tableData"
        border
        highlight-current-row
        style="width: 100%; height: calc(100vh - 240px); overflow-y: auto"
        @row-dblclick="dealSingle"
      >
        <el-table-column align="center" prop="autherId" label="举报人ID" />
        <el-table-column align="center" prop="auther" label="举报人" />
        <el-table-column align="center" label="举报人手机号" width="140">
          <template v-slot="scope">
            <span>{{ scope.row.mobile }}</span
            >&ensp;
            <el-button
              v-if="scope.row.mobile"
              type="primary"
              size="small"
              @click="seeMobile(scope.row.mobile)"
              >查看号码</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          v-if="isReport"
          align="center"
          label="所属商家名称"
          width="140"
        >
          <template v-slot="scope">
            <span>{{ scope.row.shopName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="内容分类">
          <template v-slot="scope">{{
            reportReasonTypeEnum[scope.row.reportReasonType] || '其他'
          }}</template>
        </el-table-column>

        <el-table-column align="center" label="其他内容" width="140">
          <template v-slot="scope">
            <p
              v-show="
                scope.row.reportReasonType === 6 ||
                scope.row.reportReasonType === null
              "
            >
              {{ scope.row.text }}
            </p>
            <img
              v-for="(img, index) in scope.row.imgs"
              :key="index"
              :src="img.replace('!forum', '!forum300')"
              alt=""
              style="
                display: inline-block;
                width: 100px;
                height: 100px;
                object-fit: cover;
              "
              @click="seeBigImg(img)"
            />
          </template>
        </el-table-column>

        <el-table-column align="center" label="举报时间">
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.createDate)
          }}</template>
        </el-table-column>

        <el-table-column align="center" label="处理类型">
          <template v-slot="scope">
            {{
              reportTypeEnum[scope.row.idType]
                ? reportTypeEnum[scope.row.idType]['name']
                : '其他'
            }}
            <br />
            {{
              scope.row.userReportReason
                ? userReportReasonEnum[scope.row.userReportReason]
                : ''
            }}
          </template>
        </el-table-column>

        <el-table-column align="center" label="处理状态">
          <template v-slot="scope">{{
            reportStatusEnum[scope.row.status | 0]
          }}</template>
        </el-table-column>
        <el-table-column v-if="showPage" label="H5" align="center">
          <template v-slot="scope">
            <el-button
              v-if="
                scope.row.userReportReason === 'essay_detail' ||
                scope.row.userReportReason === 'essaypid'
              "
              link
              type="primary"
              size="small"
              @click="showH5Preview(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>

        <el-table-column align="center" label="处理时间">
          <template v-slot="scope">{{
            $filters.timeFullS(scope.row.handleDate)
          }}</template>
        </el-table-column>
        <el-table-column prop="operator" align="center" label="处理人" />
        <el-table-column
          v-if="showPage"
          prop="reason"
          align="center"
          label="处理原因"
        />
        <el-table-column label="操作" align="center" width="80px">
          <template v-slot="scope">
            <el-button
              size="small"
              type="primary"
              link
              @click="goToDetail(scope.row.id)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <info-dialog
      ref="InfoDialog"
      @updateSuccess="
        () => {
          getList()
          $emit('updateSuccessRoot')
        }
      "
    />
    <ChooseCommonDecrypt ref="seePhone" />
    <el-pagination
      v-if="total"
      :page-size="limit"
      :page-sizes="[10, 20, 40, 60]"
      v-model:current-page="page"
      :total="total"
      align="center"
      layout="total, sizes, prev, pager, next, jumper"
      class="el-pagination-center"
      @size-change="handleSizeChange"
      @current-change="currentChange"
    />
    <choose-show-image ref="showImage" />
    <reportRecordDetail ref="reportRecordDetail" />
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { getReportDetail } from '@/api/user'
import ChooseShowImage from '@/components/Dialog/ChooseShowImage.vue'
import InfoDialog from './infoDialog.vue'
import ChooseCommonDecrypt from '@/components/Dialog/ChooseCommonDecrypt.vue'
import { recordOldData } from '@/utils/enum/logData'
import reportRecordDetail from './reportRecordDetail.vue'

export default {
  name: 'ReportDetail',
  components: {
    InfoDialog,
    ChooseCommonDecrypt,
    ChooseShowImage,
    reportRecordDetail
  },
  props: {
    showPage: {
      type: Boolean,
      default: false
    },
    isReport: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      reportStatusEnum: {
        0: '未处理',
        1: '忽略',
        3: '永久忽略',
        2: '已处理'
      },
      reportStatusStrEnum: {
        全部: '',
        未处理: '0',
        忽略: '1',
        永久忽略: '3',
        已处理: '2'
      },
      reportReasonTypeEnum: {
        1: '垃圾广告',
        2: '低俗色情',
        3: '人身攻击',
        4: '违法信息',
        5: '政治敏感',
        6: '其他',
        7: '题文不符',
        8: '内容不实',
        9: '抄袭',
        10: '虚假谣言',
        11: '信息不实',
        12: '冒用账号',
        13: '虚假欺骗',
        14: '标题夸张',
        15: '旧闻重提',
        16: '封面反感',
        17: '内容质量差'
      },
      userReportReasonEnum: {
        essay_detail: '文章举报',
        person_detail: '直接举报',
        essaypid: '评论举报',
        chat_detail: '私信举报'
      },
      reportTypeEnum: {
        essay_detail: {
          name: '文章',
          status: {
            '-1': '个人删除',
            0: '系统删除',
            1: '显示',
            2: '待审核',
            3: '审核不通过'
          }
        },
        person_detail: {
          name: '用户',
          status: {
            0: '正常',
            1: '禁用'
          }
        },
        short_topic: {
          name: '话题',
          status: {
            0: '无效',
            1: '有效'
          }
        },
        essaypid: {
          name: '评论',
          status: {
            '-1': '个人删除',
            0: '正常',
            1: '待审核',
            2: '系统删除'
          }
        },
        moment_detail: {
          name: '动态',
          status: {
            '-1': '个人删除',
            0: '系统删除',
            1: '显示',
            2: '待审核',
            3: '审核不通过'
          }
        },
        chat_detail: {
          name: '私信',
          status: {
            0: '正常',
            1: '删除'
          }
        }
      },
      form: {
        beginDate: '', // 开始时间
        endDate: '', // 结束时间
        status: '0' // 状态
      },
      relatedType: '',
      relatedId: 0,
      tableData: [],
      page: 1,
      limit: 10,
      total: 0
    }
  },
  methods: {
    currentChange(page) {
      this.page = page
      this.getList()
    },
    handleSizeChange(limit) {
      this.limit = limit
      this.getList()
    },
    init(detailData) {
      this.page = 1
      this.tableData = []
      this.relatedId = detailData.relatedId
      this.form = {
        beginDate: detailData.beginDate, // 开始时间
        endDate: detailData.endDate, // 结束时间
        status: '0', // 状态
        packageType: detailData.packageType // 平台
      }
      this.relatedType = detailData.relatedType
      this.getList()
    },
    showH5Preview(item) {
      if (item.userReportReason === 'essay_detail') {
        item.showId = item.userRelatedId
      } else if (item.userReportReason === 'essaypid') {
        item.showId = item.relatedEssayId
      }
      item.relatedType = 'essay_detail'
      $emit(this, 'showH5Preview', item)
    },
    // 单个处理
    dealSingle(data) {
      if (!this.showPage) return
      this.$refs['InfoDialog'].init(data)
      this.$refs['InfoDialog'].dialogVisible = true
    },
    // 查询列表
    getList() {
      getReportDetail({
        ...this.form,
        originalId: this.relatedId,
        type: this.relatedType,
        page: this.page,
        limit: this.limit
      }).then((response) => {
        if (response.data.code === 0) {
          this.parseContent(response.data.data.list)
          this.tableData = response.data.data.list
          this.total = response.data.data.total
          recordOldData(this.tableData, 'oldTwoPageData')
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    parseContent(list) {
      console.log(list)
      list.map((item) => {
        const contentJson =
          item.content.indexOf('[{') > -1 ? JSON.parse(item.content) : []
        item.imgs =
          item.content.indexOf('[{') > -1
            ? contentJson.filter((_) => _.img).map((_) => _.img)
            : []
        item.text =
          item.content.indexOf('[{') > -1
            ? contentJson
                .filter((_) => _.content)
                .map((_) => _.content)
                .join(',')
            : item.content
        item.text = item.text === '[]' ? '' : item.text
      })
    },
    // 查看手机号码
    seeMobile(mobile) {
      this.$refs.seePhone &&
        this.$refs.seePhone.init({
          decryptContent: mobile,
          source: 'ReportManagement',
          type: 1, // 1手机号解密，2微信号解密，3，身份证解密，4支付宝
          operateType: 1
        })
    },
    // 大图查看图片
    seeBigImg(link) {
      this.$refs.showImage.init(link)
    },
    goToDetail(id) {
      this.$refs.reportRecordDetail.init(id)
    }
  },
  emits: ['updateSuccessRoot', 'showH5Preview']
}
</script>
