<template>
  <div>
    <div>
      <el-form ref="form" :model="form" :inline="true" label-width="70px">
        <el-form-item v-if="!type" label="类型">
          <el-select v-model="form.ossQueryType">
            <el-option
              v-for="(value, index) in typeList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="内容ID">
          <el-input v-model="form.originalId" style="width: 125px" />
        </el-form-item>
        <el-form-item label="被举报人ID" label-width="100px">
          <el-input v-model="form.reportedUid" style="width: 150px" />
        </el-form-item>
        <el-form-item v-if="isInformer" label="举报人ID" label-width="100px">
          <el-input v-model="form.authorId" style="width: 150px" />
        </el-form-item>
        <el-form-item v-if="isReport" label="平台">
          <el-select v-model="form.packageType">
            <el-option
              v-for="(value, index) in packageType"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="举报时间">
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            v-model="daterange"
            style="width: 400px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="处理类型">
          <el-select v-model="form.status">
            <el-option
              v-for="(value, index) in treatmentType"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="
              () => {
                page = 1
                getList()
              }
            "
            >查询</el-button
          >
          <el-button @click="reset()">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div style="min-height: 600px">
      <el-table
        :data="tableData"
        border
        highlight-current-row
        style="width: 100%; height: calc(100vh - 240px); overflow: auto"
        @row-dblclick="chooseRow"
      >
        <el-table-column prop="relatedId" label="内容ID" align="center" />
        <el-table-column
          prop="reportedUsername"
          label="被举报人用户名"
          align="center"
        >
          <template v-slot="scope">
            <div>{{ scope.row.reportedUsername }}</div>
            <div style="color: red">
              {{ scope.row.ifShopUser ? '认证商家' : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="reportedUid" label="被举报人ID" align="center" />
        <el-table-column prop="title" label="标题" align="center" />
        <el-table-column
          v-if="!type"
          prop="keywords"
          label="类型"
          align="center"
        >
          <template v-slot="scope">
            {{
              reportTypeEnum[scope.row.relatedType]
                ? reportTypeEnum[scope.row.relatedType]['name']
                : '其他'
            }}
            <span v-if="scope.row.relatedType === 'person_detail'">
              <br />
              <el-button size="small" @click="historical(scope.row)">
                历史记录</el-button
              >
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="groupId" label="状态" align="center">
          <template v-slot="scope">
            <span>{{
              groupTypes[scope.row.groupId] ||
              (reportTypeEnum[scope.row.relatedType]
                ? reportTypeEnum[scope.row.relatedType]['status'][
                    scope.row.relatedStatus
                  ]
                : '')
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="packageType" label="平台" align="center">
          <template v-slot="scope">
            <span>{{
              scope.row.packageType
                ? convertPackageType[scope.row.packageType]
                : ''
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="H5" align="center">
          <template v-slot="scope">
            <el-button
              v-if="scope.row.relatedType !== 'short_topic'"
              type="info"
              size="small"
              @click="showH5Preview(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>

        <el-table-column prop="count" label="未处理数" align="center" />

        <el-table-column label="操作" align="center" width="80px">
          <template v-slot="scope">
            <!-- <el-button v-if="scope.row.count > 0 && scope.row.revocableNum <= 0" size="small" type="warning" @click="dealAll(scope.row, 0)"
                            >全处理</el-button
                          > -->
            <el-button
              v-if="scope.row.count > 0 && scope.row.revocableNum <= 0"
              size="small"
              type="warning"
              @click="deal(scope.row)"
              >全处理</el-button
            >
            <el-button
              v-if="scope.row.revocableNum > 0"
              size="small"
              link
              type="primary"
              @click="dealAll(scope.row, 1)"
              >撤销</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      v-if="total"
      :page-size="limit"
      :page-sizes="[10, 20, 40, 60]"
      v-model:current-page="page"
      :total="total"
      align="center"
      layout="total, sizes, prev, pager, next, jumper"
      class="el-pagination-center"
      @size-change="handleSizeChange"
      @current-change="currentChange"
    />
    <handle-dialog ref="HandleDialog" @updateSuccess="getList" />
    <el-dialog
      v-model="dialogVisible"
      title="历史记录"
      center
      class="choose-handle"
      append-to-body
    >
      <div v-if="historicalList.length" class="histor-list">
        <p v-for="(h, i) in historicalList" :key="i" class="list">
          {{ $filters.timeFullS(h.operateTime) }}&emsp;&emsp;&emsp;{{
            historicalType[h.type]
          }}{{ h.days ? `${h.days}天` : '永久' }}
        </p>
      </div>
      <div v-else class="text-center">暂无记录</div>
      <p class="footer-content">
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </p>
    </el-dialog>
    <PublishDialog
      ref="publishDialog"
      :from="0"
      :punishInfo="punishInfo"
      v-model:visible="visiblePublishDialog"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
const types = {
  游客: 7,
  待审核: 8,
  小黑屋: 16,
  新手上路: 21,
  禁言: 4,
  封号: 25
}
const packageType = {
  全部: '',
  主版本: 1,
  商家版: 2
}
import { convertKeyValueEnum } from '@/utils/convert'
import { getReportSummary, GetCurrentLimitRec } from '@/api/user'
import HandleDialog from './handleDialog.vue'
import { recordOldData } from '@/utils/enum/logData'
import PublishDialog from './publishDialog.vue'
import { batchHandleReport } from '@/api/user'
import { mapGetters } from 'vuex'
export default {
  data() {
    return {
      form: {
        ossQueryType: '', // 类型
        beginDate: '', // 开始时间
        endDate: '', // 结束时间
        originalId: '', // 内容ID
        reportedUid: '', // 被举报人ID
        packageType: '',
        status: '0', // 处理类型
        authorId: '' // 举报人ID
      },
      groupTypes: convertKeyValueEnum(types),
      typeList: {
        全部: '',
        用户: '1',
        文章: '2',
        私信: '3',
        评论: '4',
        视频: '5',
        其他: '0'
      },
      historicalType: {
        1: '拉黑',
        2: '禁言',
        3: '封号'
      },
      treatmentType: {
        全部: '',
        未处理: '0',
        忽略: '1',
        永久忽略: '3',
        已处理: '2'
      },
      packageType: packageType,
      convertPackageType: convertKeyValueEnum(packageType),
      reportTypeEnum: {
        essay_detail: {
          name: '文章',
          status: {
            '-1': '个人删除',
            0: '系统删除',
            1: '显示',
            2: '待审核',
            3: '审核不通过'
          }
        },
        person_detail: {
          name: '用户',
          status: {
            0: '正常',
            1: '禁用'
          }
        },
        short_topic: {
          name: '话题',
          status: {
            0: '无效',
            1: '有效'
          }
        },
        moment_detail: {
          name: '动态',
          status: {
            '-1': '个人删除',
            0: '系统删除',
            1: '显示',
            2: '待审核',
            3: '审核不通过'
          }
        },
        essaypid: {
          name: '评论',
          status: {
            '-1': '个人删除',
            0: '正常',
            1: '待审核',
            2: '系统删除'
          }
        },
        chat_detail: {
          name: '私信',
          status: {
            0: '正常',
            1: '删除'
          }
        },
        used_car: {
          name: '二手车详情',
          status: {
            // 0: '正常',
            // 1: '下架'
            0: '系统下架',
            1: '正常',
            2: '待审核',
            3: '审核不通过',
            4: '个人删除',
            5: '已售出',
            6: '用户下架',
            7: '系统下架',
            8: '发票过期'
          }
        },
        video_detail: {
          name: '视频',
          status: {
            '-1': '个人删除',
            0: '系统删除',
            1: '显示',
            2: '待审核',
            3: '审核不通过'
          }
        }
      },
      dialogVisible: false,
      // 历史记录
      historicalList: [],
      tableData: [],
      page: 1,
      limit: 10,
      total: 0,
      visiblePublishDialog: false,
      punishInfo: {},
      dayjs
    }
  },
  name: 'ReportSummary',
  components: {
    HandleDialog,
    PublishDialog
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    isInformer: {
      type: Boolean,
      default: false
    },
    isReport: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(['name']),
    daterange: {
      get() {
        if (this.form.beginDate && this.form.endDate) {
          return [this.form.beginDate, this.form.endDate]
        }
        return []
      },
      set(value) {
        if (value) {
          this.form.beginDate = value[0]
          this.form.endDate = value[1]
        } else {
          this.form.beginDate = ''
          this.form.endDate = ''
        }
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    currentChange(page) {
      this.page = page
      this.getList()
    },
    handleSizeChange(limit) {
      this.limit = limit
      this.getList()
    },
    reset() {
      this.page = 1
      this.form = {
        ossQueryType: '', // 类型
        beginDate: '', // 开始时间
        endDate: '', // 结束时间
        originalId: '', // 内容ID
        reportedUid: '',
        packageType: '',
        status: '0',
        authorId: ''
      }
      this.getList()
    },
    chooseRow(row) {
      const detailData = {
        relatedId: row.relatedId,
        beginDate: this.form.beginDate,
        endDate: this.form.endDate,
        packageType: row.packageType,
        relatedType: row.idType
      }
      $emit(this, 'chooseRow', detailData)
    },
    deal(row) {
      if (row.relatedType === 'used_car') {
        this.punishInfo = row
        this.visiblePublishDialog = true
        this.$nextTick(() => {
          this.$refs['publishDialog'].init()
        })
      } else {
        this.dealAll(row, 0)
      }
    },
    // 全处理
    dealAll(row, revocable) {
      const labelName = row.relatedType === 'person_detail' ? '用户名' : '标题'
      const data = {
        data: row,
        title: '',
        labelName: labelName,
        type: 'report',
        revocable
      }
      this.$refs['HandleDialog'].init(data)
    },
    // 7 天忽略
    postIgnore(data) {
      batchHandleReport({
        originalId: data.relatedId,
        status: '1',
        handleresult: '',
        type: data.relatedType,
        repostid: data.relatedEssayId,
        forbidDays: '',
        operateType: '',
        operator: this.name,
        ignoreDays: '7'
      }).then((response) => {
        if (response.data.code === 0) {
          this.$message.success('操作成功')
          this.dialogVisible = false
          this.getList()
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    showH5Preview(item) {
      if (item.relatedType === 'essaypid') {
        item.showId = item.relatedEssayId
      } else if (
        item.relatedType === 'essay_detail' ||
        item.relatedType === 'person_detail' ||
        item.relatedType === 'used_car' ||
        item.relatedType === 'moment_detail' ||
        item.relatedType === 'video_detail'
      ) {
        item.showId = item.relatedId
      } else {
        item.showId = item.reportedUid
      }
      $emit(this, 'showH5Preview', item)
    },
    // showStatusH5Preview(item) {
    //   return ['essay_detail', 'person_detail', 'essaypid', 'used_car', 'moment_detail'].findIndex(_ => { return _ === item.relatedType }) !== -1
    // },
    // 查询列表
    getList() {
      const postData = {
        page: this.page,
        limit: this.limit,
        beginDate: this.form.beginDate, // 开始时间
        endDate: this.form.endDate, // 结束时间
        status: this.form.status, // 状态
        originalId: this.form.originalId, // 内容ID
        reportedUid: this.form.reportedUid,
        authorId: this.form.authorId,
        type: this.type ? 'used_car' : '',
        ossQueryType: this.type ? '' : this.form.ossQueryType,
        packageType: this.form.packageType || ''
      }
      console.log(postData)
      if (!this.type) delete postData.type
      getReportSummary(postData).then((response) => {
        if (response.data.code === 0) {
          this.tableData = response.data.data.list || []
          this.total = response.data.data.total
          recordOldData(this.tableData)
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    // 历史记录
    historical(item) {
      const me = this
      GetCurrentLimitRec({
        uid: item.relatedId
      }).then((response) => {
        if (response.data.code === 0) {
          me.historicalList = response.data.data
          me.dialogVisible = true
        } else {
          me.$message.error(response.data.msg)
        }
      })
    }
  },
  emits: ['chooseRow', 'showH5Preview']
}
</script>
