<template>
  <div v-loading="loading">
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :title="title"
      center
      class="choose-handle"
      append-to-body
    >
      <el-form ref="ruleForm" :model="userData" label-width="100px">
        <el-form-item v-if="operationType !== 'more'" :label="labelName">
          <el-input v-focus v-model="userData.title" readonly type="text" />
        </el-form-item>
        <el-form-item
          v-if="userData.relatedType === 'person_detail'"
          label="禁言时段"
        >
          <el-radio
            v-model="formData.sayDays"
            label="3"
            @change="updataData('titleDays')"
            >3天</el-radio
          >
          <el-radio
            v-model="formData.sayDays"
            label="7"
            @change="updataData('titleDays')"
            >7天</el-radio
          >
          <el-radio
            v-model="formData.sayDays"
            label="30"
            @change="updataData('titleDays')"
            >30天</el-radio
          >
          <el-radio
            v-model="formData.sayDays"
            label="0"
            @change="updataData('titleDays')"
            >永久</el-radio
          >
        </el-form-item>
        <el-form-item
          v-if="userData.relatedType === 'person_detail'"
          label="封号时段"
        >
          <el-radio
            v-model="formData.titleDays"
            label="0"
            @change="updataData('sayDays')"
            >永久(封账号+设备)</el-radio
          >
        </el-form-item>
        <el-form-item label="处理类型">
          <el-radio v-model="formData.status" label="2">处理</el-radio>
          <el-radio v-model="formData.status" label="1" @change="resetData"
            >忽略</el-radio
          >
          <el-radio v-model="formData.status" label="3" @change="resetData"
            >永久忽略</el-radio
          >
        </el-form-item>
        <el-form-item
          v-if="
            userData.relatedType === 'person_detail' && formData.status === '1'
          "
          label="忽略时间"
        >
          <el-radio
            v-if="userData.relatedType === 'person_detail'"
            v-model="formData.ignoreDays"
            label="7"
            @change="resetData"
            >7天忽略(可选)</el-radio
          >
        </el-form-item>
        <el-form-item
          v-if="
            (userData.relatedType === 'person_detail' &&
              formData.status === '2') ||
            (userData.showReason && formData.status === '2')
          "
          label="处理原因"
        >
          <el-radio
            v-for="(reason, index) in reasonList"
            :key="index"
            :label="index"
            v-model="chooseReasonIndex"
            @change="
              () => {
                formData.reason =
                  chooseReasonIndex === reasonList.length - 1 ? '' : reason
              }
            "
            >{{ reason }}</el-radio
          >
          <el-input
            v-show="chooseReasonIndex === reasonList.length - 1"
            v-model="formData.reason"
            placeholder="请填写处理原因"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="userData.relatedType === 'used_car' && formData.status === '2'"
          label="处理原因"
        >
          <el-input
            v-model="formData.reason"
            placeholder="请填写处理原因"
          ></el-input>
        </el-form-item>
        <p class="footer-content">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmButton()">确 定</el-button>
        </p>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { mapGetters } from 'vuex'
import {
  batchHandleReport,
  postHandle,
  deleteUserContent,
  updataBatchHadle,
  batchHandleRevocable,
  getUserExistOrder
} from '@/api/user'
import { recordBeforeAlter } from '@/utils/enum/logData'
import { whistleblowersReasonList } from '@/utils/enum'
export default {
  name: 'ChooseHandle',
  data() {
    return {
      loading: false,
      dialogVisible: false,
      userData: {},
      formData: {},
      multipleSelection: [],
      reasonList: whistleblowersReasonList,
      title: '',
      labelName: '',
      type: '',
      operationType: 'single',
      chooseReasonIndex: -1,
      revocable: ''
    }
  },
  computed: {
    ...mapGetters(['uid', 'name'])
  },
  methods: {
    init({
      data,
      title,
      labelName,
      type,
      operationType,
      multipleSelection,
      revocable
    }) {
      this.dialogVisible = true
      this.formData = {
        sayDays: undefined,
        titleDays: undefined,
        status: undefined,
        reason: '',
        ignoreDays: ''
      }
      console.log(data)
      this.userData = data
      this.userData.showReason =
        this.userData.relatedType === 'essaypid' ||
        this.userData.relatedType === 'essay_detail' ||
        this.userData.relatedType === 'moment_detail' ||
        this.userData.relatedType === 'video_detail'
      this.title = title
      this.labelName = labelName || '用户'
      this.type = type
      this.operationType = operationType || 'single'
      this.multipleSelection = multipleSelection || []
      this.revocable = revocable || ''
      this.chooseReasonIndex = -1
      if (!title) {
        this.setTitle(title)
      }
    },
    setTitle(title) {
      const me = this
      if (me.userData) {
        switch (me.userData.relatedType) {
          case 'essay_detail':
            me.title = '文章/动态举报处理'
            break
          case 'person_detail':
            me.title = '用户举报处理'
            break
          case 'short_topic':
            me.title = '话题举报处理'
            break
          case 'essaypid':
            me.title = '评论举报处理'
            break
          case 'used_car':
            me.title = '二手车举报处理'
            break
          case 'video_detail':
            me.title = '视频举报处理'
            break
          default:
            '其他举报处理'
        }
      }
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
    },
    // 变更选项
    updataData(type) {
      this.formData[type] = undefined
    },
    // 确认提交
    confirmButton() {
      if (this.type === 'report' && !this.formData.status) {
        return this.$message.error('请选择处理类型')
      }
      this.formData.forbidDays =
        this.formData.sayDays || this.formData.titleDays
      if (
        this.type === 'report' &&
        this.userData.relatedType === 'person_detail' &&
        !this.formData.forbidDays &&
        this.formData.status === '2'
      ) {
        return this.$message.error('请选择处理时间')
      }
      if (
        this.type === 'report' &&
        this.userData.relatedType === 'person_detail' &&
        !this.formData.reason.trim() &&
        this.formData.status === '2'
      ) {
        const errMsg =
          this.chooseReasonIndex === this.reasonList.length - 1
            ? '请填写处理原因'
            : '请选择处理原因'
        return this.$message.error(errMsg)
      }
      this.submitFormReady() // 操作举报
    },
    // 提交准备
    submitFormReady() {
      if (this.formData.sayDays) return this.submitForm()
      const me = this
      getUserExistOrder({
        userId: me.userData.relatedId || me.userData.uid
      })
        .then((response) => {
          console.log(response)
          me.submitForm()
        })
        .catch((err) => {
          if (err.response.data.code === 8015) {
            me.$confirm(err.message, '提示', {
              confirmButtonText: '继续封号',
              cancelButtonText: '取消',
              type: 'warning',
              center: true
            }).then(() => {
              me.submitForm()
            })
          } else {
            me.$message.error(err.response || '操作失败')
          }
        })
    },
    // 提交变更
    submitForm() {
      let operateType = '' // 1 2禁用，3 4封号
      // 用户封禁
      if (this.type === 'user') {
        operateType = this.formData.sayDays ? 1 : 4 // 1禁用 4封号
        return this.operationType === 'more'
          ? this.batchData(operateType, this.formData.forbidDays)
          : this.editUser(operateType, this.formData.forbidDays)
      }
      if (
        this.userData.relatedType === 'person_detail' &&
        this.formData.forbidDays
      ) {
        operateType = this.formData.sayDays ? 2 : 3 // 2禁用 3封号
      }
      this.formData.ignoreDays =
        this.formData.status === '1' ? this.formData.ignoreDays : ''
      recordBeforeAlter({ relatedId: this.userData.relatedId }, 'relatedId')
      const reportDispose = this.revocable
        ? batchHandleRevocable
        : batchHandleReport
      reportDispose({
        originalId: this.userData.relatedId,
        status: this.formData.status,
        type: this.userData.relatedType,
        repostid: this.userData.relatedEssayId,
        forbidDays: this.formData.forbidDays,
        operateType: operateType,
        operator: this.name,
        reason: this.formData.reason,
        ignoreDays: this.formData.ignoreDays || ''
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.$message.success('操作成功')
            this.dialogVisible = false
            $emit(this, 'updateSuccess')
            if (operateType === 3 && this.formData.forbidDays === '0') {
              // 3 4永久封禁，0设定的时间
              this.deleteUserContent()
            }
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          console.log(err)
          // me.$message.error(err.message || '修改失败')
        })
    },
    // 操作禁言
    editUser(operateType, forbidDays, popup) {
      const me = this
      recordBeforeAlter(me.userData, 'uid')
      postHandle({
        uid: me.userData.uid,
        status: operateType,
        operator: me.name,
        days: forbidDays,
        remark: me.formData.reason,
        popup: popup || ''
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('修改成功')
            $emit(me, 'updateSuccess')
            if (operateType === 4 && forbidDays === '0') {
              // 3 4永久封禁，0设定的时间
              return me.deleteUserContent()
            }
            me.dialogVisible = false
          } else {
            me.$message.error(response.data.msg || '修改失败')
          }
        })
        .catch((err) => {
          console.log(err)
          // me.$message.error(err.message || '修改失败')
        })
    },
    deleteUserContent() {
      const me = this
      me.loading = true
      deleteUserContent({
        uid: me.userData.uid || me.userData.relatedId,
        operator: me.name
      })
        .then((response) => {
          console.log(response)
          if (response.data.code === 0) {
            me.$message.success('异常信息删除中，请5分钟后查看')
          } else {
            me.$message.error('删除用户内容失败')
          }
        })
        .catch(() => {
          me.$message.error('删除用户内容失败异常')
        })
        .finally((_) => {
          me.loading = false
          me.dialogVisible = false
        })
    },
    batchData(operateType, forbidDays) {
      const me = this
      const ids = me.multipleSelection.map((_) => {
        return _.uid
      })
      updataBatchHadle({
        uids: ids.join(),
        status: operateType,
        operator: me.name,
        days: forbidDays
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.dialogVisible = false
            me.$message.success('修改成功')
            $emit(me, 'updateSuccess')
          } else {
            me.$message.error(response.data.msg || '修改失败')
          }
        })
        .catch((err) => {
          console.log(err)
          // me.$message.error(err.message || '修改失败')
        })
    },
    resetData() {
      this.formData.titleDays = undefined
      this.formData.sayDays = undefined
      this.formData.reason = ''
      this.chooseReasonIndex = -1
    }
  },
  emits: ['updateSuccess']
}
</script>
