<template>
  <el-dialog
    v-model="dialogVisible"
    :before-close="handleClose"
    title="举报处理"
    class="reportDetailDialog"
    center
    width="600px"
  >
    <el-form ref="ruleForm" :model="ruleForm" status-icon label-width="100px">
      <el-form-item v-if="row.userReportReason === 'essaypid'" label="评论内容">
        <el-input
          v-model="row.replyInfo"
          type="textarea"
          style="width: 400px"
          readonly
        />
      </el-form-item>
      <el-form-item label="举报内容">
        <el-input
          v-if="dialogVisible"
          v-model="row.text"
          type="textarea"
          style="width: 400px"
          readonly
        />
      </el-form-item>
      <el-form-item label="图片">
        <div class="img-list">
          <img
            v-for="(img, index) in row.imgs"
            :src="img"
            :key="index"
            alt=""
            @click="seeBigImg(img)"
          />
        </div>
      </el-form-item>
      <el-form-item label="处理结果" prop="handleresult">
        <el-input
          v-if="dialogVisible"
          v-model="ruleForm.handleresult"
          :readonly="row.status !== 0"
          type="textarea"
          style="width: 400px"
        />
      </el-form-item>
      <el-form-item label="处理类型" prop="dealType">
        <el-radio
          v-model="ruleForm.dealType"
          :disabled="row.status !== 0"
          label="2"
          >处理</el-radio
        >
        <el-radio
          v-model="ruleForm.dealType"
          :disabled="row.status !== 0"
          label="1"
          >忽略</el-radio
        >
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <span>
        <el-button v-if="row.status === 0" type="primary" @click="confirm"
          >确 定</el-button
        >
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { reportHandleRelation } from '@/api/user'
import { recordBeforeAlter } from '@/utils/enum/logData'
export default {
  name: 'InfoDialog',
  data() {
    return {
      row: {},
      ruleForm: {
        handleresult: '',
        dealType: '2',
      },
      dialogVisible: false,
      relatedId: '',
    }
  },
  methods: {
    init(item) {
      this.row = item
      this.relatedId = item.relatedId || ''
      this.ruleForm = {
        handleresult: item.handleResult ? item.handleResult : '',
        dealType: item.status ? item.status + '' : '2',
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    // 大图查看图片
    seeBigImg(link) {
      window.open(link)
    },
    confirm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.submitForm()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    submitForm() {
      recordBeforeAlter(
        { id: this.row.id },
        'id',
        'oldTwoPageData',
        'beforeAlterTwo'
      )
      reportHandleRelation({
        id: this.row.id,
        originalId: this.relatedId,
        status: this.ruleForm.dealType,
        handleresult: this.ruleForm.handleresult,
        type: this.row.idType,
        repostid: this.row.relatedEssayId,
      }).then((response) => {
        if (response.data.code === 0) {
          this.$message.success('操作成功')
          this.dialogVisible = false
          $emit(this, 'updateSuccess')
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
  },
  emits: ['updateSuccess'],
}
</script>

<style lang="scss">
.reportDetailDialog {
  .img-list {
    position: relative;
    img {
      width: 28%;
      max-height: 90px;
      margin: 0 5px;
      object-fit: cover;
    }
  }
}
</style>
