<template>
  <el-dialog
    v-model="dialogVisible"
    :before-close="handleClose"
    title="举报详情"
    center
    width="800px"
  >
    <el-descriptions title="被举报信息" :column="2">
      <el-descriptions-item label="用户名">{{
        detail.userRelatedName
      }}</el-descriptions-item>
      <el-descriptions-item label="内容类型">{{
        detail.contentType
      }}</el-descriptions-item>
      <el-descriptions-item label="用户ID">{{
        detail.userRelatedId
      }}</el-descriptions-item>
      <el-descriptions-item label="平台">{{
        detail.platform
      }}</el-descriptions-item>
      <el-descriptions-item label="内容ID">{{
        detail.reportContentId
      }}</el-descriptions-item>
      <el-descriptions-item label="标题">{{
        detail.title
      }}
      <img v-if="detail?.titleImageInfo?.imgUrl" :src="detail.titleImageInfo.imgUrl" class="comment-img" @click="seeBigImg(detail.titleImageInfo.imgUrl)"/>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions style="margin-top: 20px" title="举报人信息" :column="2">
      <el-descriptions-item label="用户名">{{
        detail.userName
      }}</el-descriptions-item>
      <el-descriptions-item label="举报类型">{{
        detail.reportTypeDesc
      }}</el-descriptions-item>
      <el-descriptions-item label="举报时间">{{
        $filters.timeFullS(detail.reportTypeTime)
      }}</el-descriptions-item>
      <el-descriptions-item label="绑定手机号">
        {{ detail.userMobile }}&nbsp;
        <el-button
          v-if="detail.userMobile"
          type="primary"
          size="small"
          @click="seeMobile(detail.mobileNO)"
        >
          查看号码
        </el-button>
      </el-descriptions-item>
      <el-descriptions-item label="所属商家">{{
        detail.shopName
      }}</el-descriptions-item>
      <el-descriptions-item label="联系人手机号">
        {{ detail.mobileNO }}&nbsp;
        <el-button
          v-if="detail.mobileNO"
          type="primary"
          size="small"
          @click="seeMobile(detail.userMobile)"
        >
          查看号码
        </el-button>
      </el-descriptions-item>
      <el-descriptions-item label="权利人姓名" :span="2">{{
        detail.powerName
      }}</el-descriptions-item>
      <el-descriptions-item label="举报内容描述" :span="2">{{
        detail.reportContentDesc
      }}</el-descriptions-item>
    </el-descriptions>
    <p>身份证/营业执照信息:</p>
    <el-image
      v-for="(img, index) in detail.idPic"
      class="img-list"
      :src="img"
      :key="index"
      :initial-index="index"
      :preview-src-list="detail.idPic"
      hide-on-click-modal
      :z-index="2000"
      :preview-teleported="true"
      fit="fill"
    />
    <p>图片证据:</p>
    <el-image
      v-for="(img, index) in detail.reportPic"
      class="img-list"
      :src="img"
      :key="'report ' + index"
      :initial-index="index"
      :preview-src-list="detail.reportPic"
      hide-on-click-modal
      :z-index="2000"
      :preview-teleported="true"
      fit="fill"
    />
    <choose-see-phone ref="seePhone" />
  </el-dialog>
</template>

<script>
import { getreportRecordDetail } from '@/api/user'
import ChooseSeePhone from '@/components/Dialog/ChooseSeePhone.vue'

export default {
  name: 'ReportRecordDetail',
  components: {
    ChooseSeePhone
  },
  data() {
    return {
      detail: {},
      dialogVisible: false,
      relatedId: ''
    }
  },
  methods: {
    init(id) {
      this.dialogVisible = true
      this.getDetails(id)
    },
    handleClose() {
      this.dialogVisible = false
    },
    // 大图查看图片
    seeBigImg(link) {
      window.open(link)
    },
    getDetails(reportId) {
      getreportRecordDetail({ reportId })
        .then((response) => {
          const data = response.data.data || {}
          this.detail = data
          this.detail.idPic = (data.idPic && data.idPic.split(',')) || []
          this.detail.reportPic =
            (data.reportPic && data.reportPic.split(',')) || []
        })
        .catch((error) => console.log(error))
    },
    confirm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.submitForm()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 查看手机号码
    seeMobile(mobile) {
      this.$refs.seePhone &&
        this.$refs.seePhone.init({ mobile: mobile, source: 'reportDetail' })
    }
  }
}
</script>

<style lang="scss" scoped>
.img-list {
  width: 100px;
  height: 100px;
  margin-left: 10px;
}
.comment-img {
  width: 50px;
  height: 50px;
  border-radius: 5px;
}
</style>
