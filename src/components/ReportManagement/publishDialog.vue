<template>
  <div>
    <el-dialog
      :title="from === 1 ? '下架二手车' : '二手车举报处理'"
      center
      append-to-body
      v-model="dataVisible"
      width="719px"
      class="publish-dialog"
      :before-close="handleClose"
    >
      <div>
        <div class="item">
          <span class="label">账户信息</span>
          <span>{{ punishInfo.ifShopUser ? '商家' : '个人' }}</span>
        </div>
        <table class="item" cellpadding="10" border="1" cellspacing="0">
          <tr>
            <th>{{ punishInfo.ifShopUser ? '经销商ID' : '用户ID' }}</th>
            <th>{{ punishInfo.ifShopUser ? '经销商名称' : '用户名' }}</th>
            <th v-if="punishInfo.ifShopUser">所属地区</th>
            <th style="width: 200px">联系电话</th>
          </tr>
          <tr>
            <td>{{ punishInfo.ifShopUser ? detail.shopId : detail.userId }}</td>
            <td>
              {{ punishInfo.ifShopUser ? detail.shopName : detail.userName }}
            </td>
            <td v-if="punishInfo.ifShopUser">
              {{
                `${detail.shopProvinceAddress || ''} ${
                  detail.shopCityAddress || ''
                }`
              }}
            </td>
            <td>
              <span>{{ detail.mobile }}</span>
              <el-button
                size="small"
                style="margin-left: 10px"
                @click="seeMobile(detail.mobile)"
                >查看</el-button
              >
            </td>
          </tr>
        </table>
        <div v-if="detail.id" class="item">
          <span class="label">车辆信息</span>
          <table border="1" cellpadding="10" cellspacing="0">
            <tr>
              <th>车源ID</th>
              <th>品牌车型</th>
              <th>上牌情况</th>
              <th>{{ carTypeInfo.label }}</th>
              <th>转让价</th>
            </tr>
            <tr>
              <td>{{ detail.id }}</td>
              <td>{{ detail.showName }}</td>
              <td>{{ carTypeInfo.upLabel }}</td>
              <td>{{ $filters.timeFullS(detail.placeTime) }}</td>
              <td>
                {{ carTypeInfo.transferPrice
                }}{{
                  detail.placeStatus === 2
                    ? `（尾款：${detail.carBalancePayment}）`
                    : ''
                }}
              </td>
            </tr>
          </table>
        </div>
        <div class="item">
          <span class="label">{{ from === 1 ? '车辆管理' : '处理结果' }}</span>
          <div class="subtitle">请选择处理方式</div>
          <el-radio-group v-model="ruleForm.status" @change="changeStatus">
            <template v-if="from === 0">
              <el-radio :label="1">忽略</el-radio>
              <el-radio :label="2">{{
                detail.id ? '下架问题车辆' : '记录违规'
              }}</el-radio>
            </template>
            <template v-if="from === 1">
              <el-radio :label="0">仅下架车辆</el-radio>
              <el-radio :label="2">下架车辆并记录违规</el-radio>
            </template>
          </el-radio-group>
        </div>
        <div v-if="ruleForm.status">
          <div class="item">
            <div class="subtitle">
              {{ ruleForm.status === 1 ? '选择忽略原因' : '选择处理原因' }}
            </div>
            <div v-if="ruleForm.status === 1">
              <el-radio-group
                v-model="ruleForm.reasonFirst"
                class="radio-cause"
                @change="updateOtherReason"
              >
                <el-radio label="无效举报"></el-radio>
                <el-radio label="无法核实"></el-radio>
                <el-radio label="无需处理"></el-radio>
                <el-radio label="其他问题"></el-radio>
              </el-radio-group>
            </div>
            <div v-if="ruleForm.status === 2 || showOtherReason">
              <div v-if="showOtherReason" class="subtitle">
                请选择具体忽略原因
              </div>
              <div>
                <div
                  v-for="(item, index) in reasonList"
                  :key="index"
                  class="given-reason"
                >
                  <div class="given-reason-left pt8">
                    <div>{{ item.text }}</div>
                    <div v-if="item.importance" class="mb6 mt3">
                      <el-tag type="warning" size="small">重要分类</el-tag>
                    </div>
                  </div>
                  <div class="given-reason-right">
                    <el-radio
                      v-for="(v, i) in item.reportReasonDetailVOS"
                      :key="i"
                      v-model="ruleForm.reason"
                      :label="v.handleReason"
                      class="given-reason-radio"
                      @change="getMsgReason(v, item)"
                    ></el-radio>
                  </div>
                </div>
                <div class="given-reason custom-reason">
                  <div class="given-reason-left">其他</div>
                  <div class="given-reason-right">
                    <el-radio
                      v-model="ruleForm.reason"
                      label="自定义原因"
                      @change="changeReason"
                    ></el-radio>
                    <el-input
                      v-if="ruleForm.reason === '自定义原因'"
                      size="small"
                      maxlength="15"
                      v-model="ruleForm.customReason"
                      placeholder="请输入自定义原因（15字内）"
                      style="width: 250px"
                    ></el-input>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="from === 0" class="item">
          <span class="label">系统通知</span>
          <div class="given-reason">
            <div class="given-reason-left pt8">通知类型</div>
            <div class="given-reason-right">
              <el-radio-group
                v-model="ruleForm.customType"
                @change="changeCustomType"
              >
                <el-radio label="1" class="given-reason-radio">
                  默认通知
                </el-radio>
                <el-radio
                  v-if="showReasonList2.length"
                  label="2"
                  class="given-reason-radio"
                >
                  自定义通知
                </el-radio>
              </el-radio-group>
            </div>
          </div>
          <div
            v-if="ruleForm.customType === '2' && showReasonList2.length"
            class="given-reason"
          >
            <div class="given-reason-left pt8">选择场景</div>
            <div class="given-reason-right">
              <el-radio
                v-for="(item, index) in showReasonList2"
                :key="index"
                v-model="ruleForm.customContentId"
                :label="item.id"
                class="given-reason-radio"
              >
                {{ item.scene }}
              </el-radio>
            </div>
          </div>
          <div class="given-reason pt8 pb10">
            <div class="given-reason-left">举报人通知文案</div>
            <div class="given-reason-right">{{ reasonItem.content || '' }}</div>
          </div>
        </div>
        <div class="item">
          <span class="label">备注</span>
          <el-input
            type="textarea"
            :rows="2"
            resize="none"
            placeholder="请输入处罚备注"
            v-model="ruleForm.remark"
          >
          </el-input>
        </div>
        <div v-if="from === 0 && punishInfo.ifShopUser" class="item">
          <span class="label">商家关联信息</span>
          <div class="associated-info">
            <div class="associated-info-left">看车地点</div>
            <div class="associated-info-right">
              <el-input
                v-model="ruleForm.viewLoc"
                maxlength="99"
                show-word-limit
              />
            </div>
          </div>
          <div class="associated-info mt5">
            <div class="associated-info-left">收款单位</div>
            <div class="associated-info-right">
              <el-input
                v-model="ruleForm.payUnit"
                maxlength="50"
                show-word-limit
              />
            </div>
          </div>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <ChooseCommonDecrypt ref="seePhone" />
  </div>
</template>

<script>
import { $emit } from '../../utils/gogocodeTransfer'
import { getListCars, updateUsedCarStatus } from '@/api/garage'
import { getUserAccountCorrelationV2, secondHandCarReport } from '@/api/user'
import ChooseCommonDecrypt from '@/components/Dialog/ChooseCommonDecrypt.vue'
import { recordBeforeAlter } from '@/utils/enum/logData'
import { getConfigList, getContentListByIdtype } from '@/api/usedCarPublish'

export default {
  components: {
    ChooseCommonDecrypt
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    from: {
      type: Number,
      default: 0 // 0 举报管理 1车源列表
    },
    punishInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      detail: {},
      ruleForm: {
        remark: '',
        reasonFirst: '',
        reason: '',
        status: '',
        customReason: '',
        customType: '1',
        customContentId: '',
        viewLoc: '',
        payUnit: ''
      },
      reasonList: [],
      msgReason: '',
      isFraud: 0,
      resultReasonId: '',
      resultReasonDetailId: '',
      showOtherReason: false,
      reasonList2: [],
      ignoreReasonTypeEnum: {
        无效举报: 1,
        无法核实: 2,
        无需处理: 3,
        其他问题: 4
      }
    }
  },
  computed: {
    dataVisible: {
      get() {
        return this.visible
      },
      set(val) {
        $emit(this, 'update:visible', val)
      }
    },
    carTypeInfo() {
      const map = {
        0: {
          label: '上牌时间',
          upLabel: `已上牌（${this.detail.licensePlateAddress}）`,
          transferPrice: this.detail.price
        },
        1: {
          label: '开票时间',
          upLabel: `发票车`,
          transferPrice: this.detail.price
        },
        2: {
          label: '提车时间',
          upLabel: `订单车`,
          transferPrice: this.detail.price
        }
      }
      return map[this.detail.placeStatus] || {}
    },
    showReasonList2() {
      const list = []
      this.reasonList2.forEach((v) => {
        if (v.customType === 2) {
          if (this.ruleForm.status) {
            if (this.ruleForm.status === v.resultType) {
              if (this.ruleForm.status === 1 && this.ruleForm.reasonFirst) {
                if (
                  this.ignoreReasonTypeEnum[this.ruleForm.reasonFirst] ===
                  v.ignoreReasonType
                ) {
                  list.push(v)
                }
              } else {
                list.push(v)
              }
            }
          } else {
            list.push(v)
          }
        }
      })
      return list
    },
    reasonItem() {
      let item = {}
      const status = this.ruleForm.status ? Number(this.ruleForm.status) : ''
      const reasonFirst = this.ruleForm.reasonFirst
        ? this.ignoreReasonTypeEnum[this.ruleForm.reasonFirst]
        : ''
      const customType = this.ruleForm.customType
        ? Number(this.ruleForm.customType)
        : ''
      const customContentId = this.ruleForm.customContentId || ''
      if (status === 2 || (status === 1 && reasonFirst)) {
        if (status === 2) {
          if (customType === 2) {
            item =
              this.reasonList2.find(
                (v) =>
                  v.resultType === status &&
                  v.customType === customType &&
                  v.id === customContentId
              ) || {}
          } else {
            item =
              this.reasonList2.find(
                (v) => v.resultType === status && v.customType === customType
              ) || {}
          }
        } else {
          if (customType === 2) {
            item =
              this.reasonList2.find(
                (v) =>
                  v.resultType === status &&
                  v.customType === customType &&
                  v.ignoreReasonType === reasonFirst &&
                  v.id === customContentId
              ) || {}
          } else {
            item =
              this.reasonList2.find(
                (v) =>
                  v.resultType === status &&
                  v.customType === customType &&
                  v.ignoreReasonType === reasonFirst
              ) || {}
          }
        }
      }
      item = JSON.parse(JSON.stringify(item))
      if (item.content) {
        if (this.detail?.showName && item.content.includes('【车型】')) {
          item.content = item.content.replaceAll(
            '【车型】',
            `【${this.detail?.showName}】`
          )
        }
        if (this.detail?.userName && item.content.includes('【用户名称】')) {
          item.content = item.content.replaceAll(
            '【用户名称】',
            `【${this.detail?.userName}】`
          )
        }
        if (status === 2 || (status === 1 && reasonFirst === 3)) {
          const reason =
            this.ruleForm.reason === '自定义原因'
              ? this.ruleForm.customReason
              : this.ruleForm.reason
          if (reason && item.content.includes('【具体原因】')) {
            item.content = item.content.replaceAll(
              '【具体原因】',
              `【${reason}】`
            )
          }
        }
      }
      return item
    }
  },
  methods: {
    // 重置
    changeStatus() {
      this.ruleForm.reason = ''
      this.ruleForm.reasonFirst = ''
      this.ruleForm.customReason = ''
      this.msgReason = ''
      this.isFraud = 0
      this.resultReasonId = ''
      this.resultReasonDetailId = ''
      this.showOtherReason = false
      this.changeCustomType()
    },
    // 查看手机号码
    seeMobile(mobile) {
      this.$refs.seePhone &&
        this.$refs.seePhone.init({
          decryptContent: mobile,
          source: 'UsedCarReporting',
          type: 1, // 1手机号解密，2微信号解密，3，身份证解密，4支付宝
          operateType: 24
        })
    },
    async init() {
      if (this.from === 0) {
        // 二手车举报入口
        if (this.punishInfo.relatedId) {
          await this.getDetail()
        } else {
          $emit(this, 'update:punishInfo', {
            ...this.punishInfo,
            ifShopUser: false
          })
          await this.getUserInfo()
        }
      } else {
        // 二手车列表入口
        this.detail = this.punishInfo
      }
      this.getConfigList()
      this.getConfigList2()
    },
    async getUserInfo() {
      const me = this
      try {
        const response = await getUserAccountCorrelationV2({
          uid: me.punishInfo.reportedUid,
          page: 1,
          limit: 20
        })
        if (response.data.code === 0) {
          const data = (response.data.data && response.data.data.list[0]) || {}
          me.userId = data.uid
          me.detail = {
            id: 0,
            userId: data.uid || '',
            userName: data.username || '',
            mobile: data.mobile || ''
          }
        }
      } catch (error) {
        console.log(error)
      }
    },
    async getDetail() {
      const me = this
      try {
        const response = await getListCars({
          id: this.punishInfo.relatedId
        })
        if (response.data.code === 0) {
          let data =
            (response.data.data.listData && response.data.data.listData[0]) ||
            {}
          if (!data.id) {
            return me.$message.error('没有查询到数据')
          }
          const showName =
            data.carportName || data.carportGoodsName
              ? `${data.carportName || ''}${data.carportGoodsName || ''}`
              : `${data.brandName || ''}${data.goodsName || ''}`
          data.showName = showName
          this.shopId = data.shopId
          this.userId = data.userId
          me.detail = data
          // 由于处罚车辆可能是有经销商以个人的名义发布的车辆
          $emit(this, 'update:punishInfo', {
            ...this.punishInfo,
            ifShopUser: data.source === 1 ? true : false
          })
        }
      } catch (error) {
        console.log(error)
      }
    },
    getConfigList() {
      getConfigList().then((res) => {
        if (res.data.code === 0) {
          this.reasonList = res.data.data || []
        }
      })
    },
    getConfigList2() {
      getContentListByIdtype({ idtype: 'used_car' }).then((res) => {
        if (res.data.code === 0) {
          this.reasonList2 = res.data.data || []
        }
      })
    },
    changeCustomType() {
      if (this.ruleForm.customType === '2') {
        if (this.showReasonList2.length) {
          const index = this.showReasonList2.findIndex(
            (v) => v.id === this.ruleForm.customContentId
          )
          if (index < 0) {
            this.ruleForm.customContentId = this.showReasonList2[0]?.id || ''
          }
        } else {
          this.ruleForm.customType = '1'
        }
      }
    },
    getMsgReason(data, item) {
      this.msgReason = data.msgReason || ''
      this.isFraud = item.importance || 0
      this.resultReasonId = item.id || ''
      this.resultReasonDetailId = data.id || ''
    },
    changeReason() {
      this.ruleForm.customReason = ''
      this.msgReason = ''
      this.isFraud = 0
      this.resultReasonId = ''
      this.resultReasonDetailId = ''
    },
    operate() {
      let violation = 0 // 是否违规
      if (this.ruleForm.status === 2) {
        if (this.from) {
          violation = 1
        } else {
          violation = this.detail.id ? 0 : 1
        }
      }
      const reason =
        this.ruleForm.reason === '自定义原因'
          ? this.ruleForm.customReason
          : this.ruleForm.reason || this.ruleForm.reasonFirst
      const msgReason =
        this.ruleForm.reason === '自定义原因'
          ? this.ruleForm.customReason
          : this.msgReason
      secondHandCarReport({
        originalId: this.detail.id,
        status: this.ruleForm.status,
        type: this.punishInfo.relatedType || 'used_car',
        reportedUid: this.punishInfo.reportedUid || this.punishInfo.userId,
        reason: `${this.showOtherReason ? '无需处理' : reason}`,
        remark: this.ruleForm.remark,
        viewLoc: this.ruleForm.viewLoc,
        payUnit: this.ruleForm.payUnit,
        isShopUser: this.punishInfo.ifShopUser ? 1 : 0,
        sourceId: this.punishInfo.ifShopUser
          ? this.punishInfo.shopId || this.shopId
          : this.punishInfo.reportedUid || this.punishInfo.userId,
        violation,
        violationReason: reason,
        fraud: this.isFraud,
        msgReason,
        resultReasonId: this.resultReasonId,
        resultReasonDetailId: this.resultReasonDetailId,
        customContentId: this.reasonItem.id || ''
      }).then((response) => {
        if (response.data.code === 0) {
          this.$message.success('操作成功')
          this.handleClose()
          $emit(this, 'updateSuccess')
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    reset() {
      this.detail = {}
      this.ruleForm = {
        remark: '',
        reason: '',
        reasonFirst: '',
        status: '',
        customReason: '',
        customType: '1',
        customContentId: '',
        viewLoc: '',
        payUnit: ''
      }
      this.reasonList = []
      this.msgReason = ''
      this.isFraud = 0
    },
    handleClose() {
      this.reset()
      $emit(this, 'update:visible', false)
    },
    updataStatus() {
      const me = this
      recordBeforeAlter(me.punishInfo, 'id')
      let violation = 0 // 是否违规
      if (me.ruleForm.status === 2) {
        if (me.from) {
          violation = 1
        } else {
          violation = me.detail.id ? 0 : 1
        }
      }
      const undercarriageReason =
        me.ruleForm.reason === '自定义原因'
          ? me.ruleForm.customReason
          : me.ruleForm.reason || me.ruleForm.reasonFirst || ''
      const violationReason =
        me.ruleForm.reason === '自定义原因'
          ? me.ruleForm.customReason
          : me.ruleForm.reason || me.ruleForm.reasonFirst
      const msgReason =
        this.ruleForm.reason === '自定义原因'
          ? this.ruleForm.customReason
          : this.msgReason
      updateUsedCarStatus({
        id: me.punishInfo.id,
        ifSell: me.punishInfo.ifSell,
        status: 0,
        undercarriageReason,
        violation,
        violationReason,
        remark: me.ruleForm.remark,
        fraud: this.isFraud,
        msgReason
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('操作成功')
            me.handleClose()
            $emit(me, 'updateSuccess')
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
        })
    },
    // 是否展示其它原因
    updateOtherReason(val) {
      this.showOtherReason = val === '无需处理'
      this.changeCustomType()
    },
    confirm() {
      if (this.ruleForm.status === '') {
        return this.$message.error('选择管理方式')
      }
      if (this.ruleForm.status === 0) {
        return this.updataStatus()
      }
      if (this.ruleForm.status) {
        if (!this.ruleForm.reason && !this.ruleForm.reasonFirst) {
          let content = '请选择忽略原因'
          if (this.ruleForm.status === 2) {
            content = '请选择处罚/下架原因'
          }
          return this.$message.error(content)
        } else if (this.ruleForm.reason === '自定义原因') {
          if (!this.ruleForm.customReason) {
            return this.$message.error('请输入自定义原因')
          }
        }
        if (
          this.from !== 1 &&
          this.ruleForm.status === 1 &&
          this.ruleForm.reasonFirst === '无需处理' &&
          !this.ruleForm.reason &&
          !this.ruleForm.customReason
        ) {
          return this.$message.error('请选择忽略原因')
        }
      }
      if (this.from === 1) {
        return this.updataStatus()
      }
      this.operate()
    }
  },
  emits: ['update:punishInfo', 'update:visible', 'updateSuccess']
}
</script>

<style lang="scss">
.publish-dialog {
  .el-dialog__body {
    text-align: initial;
  }
  .el-dialog__title {
    font-weight: bold;
  }
  .pointer {
    cursor: pointer;
  }
  table {
    border-collapse: collapse;
    th,
    td {
      width: 135px;
      text-align: center;
    }
  }
  .label {
    margin-right: 5px;
    margin-bottom: 10px;
    display: inline-block;
    color: black;
  }
  .subtitle {
    font-size: 13px;
    color: #aaaaaa;
    margin-bottom: 6px;
  }
  .radio-cause {
    margin-bottom: 8px;
  }
  .given-reason {
    display: flex;
    margin-bottom: 5px;
    .given-reason-left {
      width: 115px;
      word-break: break-all;
    }
    .given-reason-right {
      flex: 1;
      .given-reason-radio {
        margin-bottom: 6px;
      }
    }
  }
  .custom-reason {
    align-items: center;
    .given-reason-right {
      display: flex;
      align-items: center;
    }
  }
  .item {
    margin-bottom: 10px;
  }
  .publish-duration {
    margin-top: 10px;
    .active {
      background-color: #409eff !important;
      color: white !important;
    }
  }
  .publish-reason {
    display: flex;
  }
  .more-publish {
    // margin-top: 20px;
    display: flex;
    align-items: center;
    .label {
      margin-bottom: 0;
    }
    .rule {
      margin: 0 10px;
      color: #409eff;
      position: relative;
      cursor: pointer;
      img {
        width: 576px;
        position: absolute;
        left: 0;
        top: 20px;
        z-index: 100;
      }
    }
    .record {
      color: red;
    }
  }
  .freeze-box {
    border: 1px solid #dcdfe6;
    padding: 10px;
    margin: 10px 0;
  }
}
.publish-content {
  .publish-tip {
    color: red;
  }
  .publish-item {
    margin-bottom: 5px;
    span:nth-child(1) {
      font-weight: bold;
    }
  }
}
.associated-info {
  display: flex;
  align-items: center;
  .associated-info-left {
    width: 80px;
    min-width: 80px;
  }
  .associated-info-right {
    flex: 1;
  }
}
</style>
