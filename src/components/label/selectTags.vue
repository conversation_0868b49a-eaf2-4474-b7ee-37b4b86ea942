<template>
  <el-dialog v-bind="$attrs" width="400px" append-to-body>
    <div class="flex">
      <el-select
        remote
        filterable
        v-model="currentTag"
        placeholder="请选择标签"
        clearable
        :remote-method="remoteMethod"
        :loading="loading"
      >
        <el-option
          v-for="(option, index) in options"
          :key="index"
          :label="option.name"
          :value="option.name"
        />
      </el-select>
      <el-button
        v-if="!options.length && currentTag"
        class="ml10"
        type="primary"
        link
        @click="insertTag"
        >添加至标签库</el-button
      >
    </div>
    <div class="mt20 text-center">
      <el-button class="ml10" type="primary" @click="close">取消</el-button>
      <el-button class="ml10" type="primary" @click="updateTag">确认</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { getCarlabelTag, postAddCarlabelTag } from '@/api/garage'
import { ref, getCurrentInstance, watchPostEffect } from 'vue'
import { TagType } from '@/utils/enum'
const proxy = getCurrentInstance().proxy
const emit = defineEmits('updateData', 'update:modelValue')

const currentTag = ref('')
const loading = ref(false)
const options = ref([])
const type = ref(TagType.CONfIG)

watchPostEffect(() => {
  if (proxy.$attrs.modelValue) {
    currentTag.value = ''
    options.value = []
    type.value = proxy.$attrs.tagType
    getList()
  }
})

const remoteMethod = (query) => {
  if (!query) return
  loading.value = true
  currentTag.value = query
  proxy.$tools.debounce(() => getList(query), 300)()
}

const getList = (query) => {
  getCarlabelTag({ tagName: query, type: type.value })
    .then((res) => {
      if (res.data.code == 0) {
        options.value = res.data.data || []
      } else {
        options.value = []
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const insertTag = () => {
  if (!currentTag.value) return
  postAddCarlabelTag({ tagName: currentTag.value, type: type.value }).then(
    (res) => {
      if (res.data.code == 0) {
        ElMessage.success('添加成功')
        options.value.push({
          name: currentTag.value
        })
      }
    }
  )
}

const close = () => {
  emit('update:modelValue', false)
}

const updateTag = () => {
  if (!currentTag.value) return ElMessage.warning('请先选择标签')
  emit('updateData', currentTag.value, type.value)
  close()
}
</script>
<style lang="scss" scoped></style>
