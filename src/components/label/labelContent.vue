<template>
  <p class="labels-content">
    <draggable item-key="id" v-model="content" @end="updateSort">
      <template #item="{ element }">
        <el-tooltip
          :content="element.labelName"
          :disabled="true"
          effect="dark"
          placement="top-start"
        >
          <el-tag :closable="closable" @close="deleteLable(element)">{{
            $filters.subString(element.labelName, 20)
          }}</el-tag>
        </el-tooltip>
      </template>
    </draggable>
  </p>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { deepCopy } from '@/utils'
import draggable from 'vuedraggable'
import { delOssUserLabel } from '@/api/articleModule'
import { mapGetters } from 'vuex'
export default {
  name: 'LabelContent',
  components: {
    draggable
  },
  props: {
    labels: {
      type: Array,
      default: () => {
        return []
      }
    },
    type: {
      type: String,
      default: 'chart'
    },
    pageName: {
      type: String,
      default: ''
    },
    closable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      content: []
    }
  },
  computed: {
    ...mapGetters(['uid'])
  },
  activated() {
    // 如果页面名称是inquiryBusinessProcessing（询价业务处理），不走activated，因为不希望页面切换时，清空掉content值
    if (
      this.pageName === 'inquiryBusinessProcessing' ||
      this.pageName === 'activeConfigurationDetail'
    ) {
      return
    }
    this.content = this.labels
  },
  mounted() {
    this.content = this.labels
  },
  methods: {
    // 删除标签
    deleteLable(item) {
      const me = this
      if (me.type === 'userLabel') {
        return me.disengagement(index, item)
      }
      if (item.selected) {
        $emit(me, 'deleteLabel', item, me.type)
      }
      const index = me.content.findIndex((a) => this.id(a) === this.id(item))
      me.content.splice(index, 1)
      $emit(me, 'delLabel', me.content)
    },
    id(item) {
      return item.id || item.labelId || item.tagId
    },
    // 解除关系
    disengagement(item) {
      const me = this
      delOssUserLabel({
        ossUid: me.uid,
        labelId: item.labelId || item.id
      }).then((response) => {
        if (response.data.code === 0) {
          $emit(me, 'deleteLabel', item)
          const index = me.content.findIndex(
            (a) => this.id(a) === this.id(item)
          )
          me.content.splice(index, 1)
          if (this.type === 'userLabel') {
            sessionStorage.setItem('fasetLabels', JSON.stringify(me.content))
          }
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    // 增加标签。$refs 使用
    addLable(item) {
      if (!item) return
      const me = this
      let status = false
      const itemId = item.id || item.labelId || item.tagId
      console.log(me.content, 'me.content===')
      console.log(item, 'itemitemitem===')
      me.content.map(function (value) {
        const valueId =
          parseInt(value.id) || parseInt(value.labelId) || parseInt(value.tagId)
        if (itemId === valueId) {
          status = true
        }
      })
      if (status) {
        return me.$message.error('已有添加，请勿重复添加')
      }
      const data = me.content.concat(item)
      me.content = []
      me.content = data
      if (me.type === 'userLabel') {
        sessionStorage.setItem('fasetLabels', JSON.stringify(me.content))
      }
    },
    // 删除标签。$refs 使用
    delLable(item) {
      let num = ''
      this.content.map(function (value, index) {
        if (
          parseInt(value.labelId) === item.labelId ||
          parseInt(value.labelId) === item.id ||
          parseInt(value.id) === item.id ||
          parseInt(value.id) === item.labelId
        ) {
          num = index
        }
      })
      if (num === '') {
        return
      }
      this.content.splice(num, 1)
    },
    // 删除所有标签， $refs 使用，同deleteAllLabel
    clear() {
      this.deleteAllLabel()
    },
    // 删除所有标签， $refs 使用
    deleteAllLabel() {
      this.content = []
    },
    // 获取所有标签。$refs 使用
    getAllData() {
      $emit(this, 'labelAllData', this.content, this.type)
    },
    updateSort({ newIndex, oldIndex }) {
      const temArr = []
      this.content.map((_) => {
        temArr.push(deepCopy(_))
      })
      this.contet = temArr
      this.getAllData()
    }
  },
  emits: ['deleteLabel', 'delLabel', 'labelAllData']
}
</script>

<style lang="scss" scoped>
.labels-content {
  margin: 0;
}
.el-tag--medium {
  margin: 5px;
}
</style>
