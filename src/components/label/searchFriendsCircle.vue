<template>
  <div class="search-circle">
    <el-select
      v-model="circleId"
      multiple
      :multiple-limit="multipleLimit"
      filterable
      remote
      :reserve-keyword="reserveKeyword"
      :disabled="disabled"
      :placeholder="placeholder"
      :remote-method="remoteMethod"
      :loading="loading"
      @change="changeCircle"
    >
      <el-option
        v-for="item in circleList"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { getCircleList } from '@/api/circle'
export default {
  name: 'searchFriendsCircle',
  props: {
    disabled: {
      type: <PERSON>olean,
      default: false,
    },
    reserveKeyword: {
      type: Boolean,
      default: true,
    },
    searchByName: {
      type: Boolean,
      default: true,
    },
    reset: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: '关联摩友圈',
    },
    multipleLimit: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      loading: false,
      circleId: [],
      circleList: [],
    }
  },
  watch: {
    reset(val) {
      if (val) {
        this.clear()
        $emit(this, 'update:reset', false)
      }
    },
  },
  methods: {
    setData(data) {
      console.log(`data`, data)
      this.circleId = []
      data &&
        data.map((_) => {
          _.name = _.hoopName
          _.id = _.hoopId
          this.circleId.push(_.hoopId)
        })
      this.circleList = data || []
      this.changeCircle(this.circleId)
    },
    remoteMethod(query) {
      this.loading = true

      getCircleList({
        [this.searchByName ? 'hoopName' : 'id']: !this.searchByName
          ? query.replace(/[^\d]/g, '')
          : query,
        page: 1,
        limit: 20,
      })
        .then((res) => {
          if (res.data.code === 0) {
            const data = res.data.data
            this.circleList = (data && data.list) || []
          }
        })
        .finally((_) => {
          this.loading = false
        })
    },
    changeCircle(e) {
      if (this.multipleLimit === 1) {
        const findData =
          this.circleList.find((item) => {
            return item.id === e[0]
          }) || {}
        return $emit(this, 'updateCircleData', {
          name: findData.name,
          id: findData.id,
        })
      }
      $emit(this, 'updateCircleData', e)
    },
    clear() {
      this.circleId = []
      this.circleList = []
    },
  },
  emits: ['update:reset', 'updateCircleData'],
}
</script>
