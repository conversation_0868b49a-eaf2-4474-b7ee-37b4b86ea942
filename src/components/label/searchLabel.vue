<template>
  <div class="search-label">
    <el-select
      v-model="labelId"
      :remote-method="remoteMethod"
      :loading="loading"
      :placeholder="tipmsg"
      filterable
      remote
      clearable
      @blur="clearContent"
      @change="addLabel"
    >
      <el-option
        v-for="item in options"
        :key="item.name"
        :label="item.title"
        :value="item.name"
      />
    </el-select>
    <el-button
      v-if="addNewLabelStatus"
      class="confirm-data"
      @click="addNewLabel()"
      >新增标签</el-button
    >
  </div>
</template>

<script>
import { $emit } from '../../utils/gogocodeTransfer'
import {
  searchController,
  searchLabel,
  saveOssUserLabel,
  establishLabel,
  searchBrand
} from '@/api/articleModule'
import { SearchTopic } from '@/api/activeConfiguration'
import { getListSentimentBrand } from '@/api/brand'
import { carOnSaleEnum } from '@/utils/enum'
import { mapGetters } from 'vuex'
export default {
  name: 'SearchLabel',
  props: {
    type: {
      type: String,
      default: 'label'
    }
  },
  data() {
    return {
      tipmsg: '插入新的标签',
      dialogVisible: false,
      loading: false,
      addNewLabelStatus: false, // 增加标签状态
      searchValue: '',
      options: [],
      rawListData: [], // 原始数据，现在type = car 的时候使用
      labelId: '',
      labels: [],
      energyTypeList: {
        1: '汽油',
        2: '纯电动',
        3: '柴油',
        4: '混动'
      }
    }
  },
  computed: {
    ...mapGetters(['uid'])
  },
  mounted() {
    if (this.type === 'car') {
      this.tipmsg = '插入新的车辆'
    } else if (this.type === 'circle') {
      // circle 应该是topic 没有改动
      this.tipmsg = '插入新的话题'
    } else if (this.type === 'brand') {
      this.tipmsg = '插入新的品牌'
    } else if (this.type === 'opinionBrand') {
      this.tipmsg = '选择关联厂商'
    }
    // this.remoteMethod('')
  },
  methods: {
    remoteMethod(query) {
      if (!query) return
      this.loading = true
      this.searchValue = query
      this.$tools.debounce(() => this.getList(query), 300)()
    },
    // 增加label
    addLabel(name) {
      const me = this
      if (name === '') {
        me.addNewLabelStatus = false
        return
      }
      const label = {
        labelName: name
      }
      let labelContent = {}
      this.options.map(function (value) {
        if (value.name === name) {
          // console.log(value)
          label.id = value.id || value.name
          labelContent = value
        }
      })
      if (label.id === undefined) {
        me.addNewLabelStatus = false
        return
      }
      label.labelName =
        ['car', 'circle', 'brand', 'opinionBrand'].indexOf(this.type) > -1
          ? labelContent.title
          : label.labelName
      if (this.type === 'userLabel') {
        return this.increaseRelationship(label)
      }
      let rawData = {}
      if (me.type === 'car') {
        rawData = me.rawListData.find((item) => {
          return item.id === label.id
        })
      }
      // me.clearSearchLabel()
      $emit(me, 'addLabel', label, me.type, rawData)
    },
    // 增加关系
    increaseRelationship(label) {
      const me = this
      const userLabel = JSON.parse(sessionStorage['fasetLabels'] || '{}')
      if (userLabel.length > 29) {
        return me.$message.error('快捷标签已有30个，请删除后再添加')
      }
      saveOssUserLabel({
        ossUid: me.uid,
        labelId: label.id
      }).then((response) => {
        if (response.data.code === 0) {
          $emit(me, 'addLabel', label, me.type)
          me.clearSearchLabel()
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    // 标签搜索
    getList(query = '') {
      const me = this
      switch (me.type) {
        case 'car':
          if (me.searchValue.trim() === '') {
            me.loading = false
            return
          }
          // 车辆
          searchController({
            type: 'car_detail',
            version: '3.6.0',
            key: query || me.searchValue,
            page: 1,
            limit: 20
          })
            .then((response) => {
              if (response.data.code === 0) {
                const carList = []
                const result = response.data.data && response.data.data[1].list
                result.map(function (value) {
                  const onSale =
                    value.goodsSaleStatus != 1
                      ? `(${carOnSaleEnum[value.goodsSaleStatus]})`
                      : ''
                  const newObj = {
                    title: `${
                      value.energyType
                        ? me.energyTypeList[value.energyType] + ' | '
                        : ''
                    }${value.subject.replace(
                      /(<\w*>||<\/\w*>)?/g,
                      ''
                    )}${onSale}`,
                    name: value.id
                  }
                  carList.push(newObj)
                })
                me.rawListData = result
                me.options = carList
              }
            })
            .finally((_) => {
              me.loading = false
            })
          break
        case 'circle':
          SearchTopic({
            page: me.page || 1,
            limit: 20,
            title: me.searchValue
          })
            .then((response) => {
              if (response.data.code === 0) {
                const circleList = []
                const result = response.data.data.listData
                result.map(function (value) {
                  const newObj = {
                    title: value.title,
                    name: value.id
                  }
                  circleList.push(newObj)
                  me.options = circleList
                })
              }
            })
            .finally((_) => {
              me.loading = false
            })
          break
        case 'brand':
          searchBrand({
            name: me.searchValue,
            page: 1,
            limit: 100
          })
            .then((response) => {
              if (response.data.code === 0) {
                const brandList = []
                const result = response.data.data && response.data.data.listData
                result.map(function (value) {
                  const newObj = {
                    title: value.brandName,
                    name: value.brandId
                  }
                  brandList.push(newObj)
                  me.options = brandList
                  // console.log(me.options)
                })
              }
            })
            .finally((_) => {
              me.loading = false
            })
          break
        // 获取舆情品牌列表
        case 'opinionBrand':
          getListSentimentBrand({
            name: me.searchValue,
            page: 1,
            limit: 20
          })
            .then((response) => {
              if (response.data.code === 0) {
                const result = response.data.data.listData || []
                me.options = result.map(function (value) {
                  return {
                    title: value.brandName,
                    name: value.brandId
                  }
                  // console.log(me.options)
                })
              }
            })
            .finally((_) => {
              me.loading = false
            })
          break
        default:
          // 用户
          searchLabel({
            name: this.searchValue
          })
            .then((response) => {
              if (response.data.code === 0) {
                me.options = response.data.data
                if (!me.options.length && me.type === 'label') {
                  me.addNewLabelStatus = true
                  return
                }
              }
              me.addNewLabelStatus = false
            })
            .finally((_) => {
              me.loading = false
            })
          break
      }
    },
    // 创建新的标签
    setEstablish(name) {
      const me = this
      establishLabel({
        name: name
      })
        .then((response) => {
          if (response.data.code === 0) {
            if (response.data.data) {
              const newLabel = {
                labelName: name,
                id: response.data.data
              }
              me.$message.success(`新建标签${name}成功`)
              me.addNewLabelStatus = false
              $emit(me, 'addLabel', newLabel, me.type)
            } else {
              me.$message.error(`标签id获取失败,请重新查询标签名${name}`)
            }
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .finally((_) => {})
    },
    // 同clearSearchLabel
    clear() {
      this.clearSearchLabel()
    },
    clearSearchLabel() {
      this.options = []
      this.searchValue = ''
      this.labelId = ''
    },
    clearContent() {
      const me = this
      setTimeout(() => {
        me.addNewLabelStatus = false
      }, 500)
    },

    // 增加新的标签
    addNewLabel() {
      const me = this
      me.$confirm(`${me.searchValue}是新标签,是否新建`, '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(() => {
          me.setEstablish(this.searchValue)
        })
        .catch((_) => {
          me.addNewLabelStatus = false
        })
    }
  },
  emits: ['addLabel']
}
</script>

<style lang="scss" scoped>
.el-tag {
  margin: 0 10px 10px 0;
}
.search-label {
  position: relative;
  display: inline;
  .confirm-data {
    position: absolute;
    left: -100px;
    top: -10px;
  }
}
</style>
