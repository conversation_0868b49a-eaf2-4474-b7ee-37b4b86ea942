<template>
  <div>
    <draggable item-key="url" v-model="fileList" @end="refreshImageList">
      <template #item="{ element }">
        <span class="image-wrap">
          <img :src="element.url" class="image" alt />
          <div class="operation">
            <el-upload
              :http-request="httpRequestOrder"
              :on-success="amend"
              :show-file-list="false"
              name="activefile"
              style="display: inline-block"
              action
            >
              <el-button type="primary" circle @click="substitute = element">
                <template #icon> <IconEdit /> </template
              ></el-button>
            </el-upload>
            <el-button
              type="danger"
              class="delete"
              circle
              @click="handleRemove(element)"
            >
              <template #icon>
                <IconDelete />
              </template>
            </el-button>
          </div>
        </span>
      </template>
      <template
        #footer
        v-if="this.fileList && this.fileList.length < this.maxNum"
      >
        <el-upload
          :http-request="httpRequestOrder"
          :on-success="onSuccessTitleimageMore"
          :on-exceed="onExceed"
          :show-file-list="false"
          name="activefile"
          style="display: inline-block"
          action
          multiple
          :limit="this.maxNum"
          ref="moreUpload"
        >
          <div class="avatar-uploader-icon">
            <el-icon><IconPlus /></el-icon>
            <p class="avatar-uploader-icon-text">
              <span v-if="tip">{{ tip }}</span>
              <span v-else
                >({{ this.fileList && this.fileList.length }}/{{
                  this.maxNum
                }})</span
              >
            </p>
          </div>
        </el-upload>
      </template>
    </draggable>
  </div>
</template>

<script>
import {
  Plus as IconPlus,
  Edit as IconEdit,
  Delete as IconDelete
} from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import draggable from 'vuedraggable'
export default {
  data() {
    return {
      fileList: [],
      // 替换相关
      substitute: {}
    }
  },
  components: {
    draggable,
    IconPlus,
    IconEdit,
    IconDelete
  },
  props: {
    maxNum: {
      type: Number,
      default: 999
    },
    tip: {
      type: String,
      default: ''
    },
    imgList: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    imgList(val) {
      console.log('val===', val)
      this.fileList = val
    }
  },
  mounted() {
    console.log(this.imgList, 'imgListimgList===')
  },
  methods: {
    refreshImageList() {
      this.fileList = [...this.fileList]
      $emit(this, 'getFilesList', this.fileList)
    },
    // 移除图片
    handleRemove(file) {
      const list = this.fileList
      list.map((item, index) => {
        if (item.url === file.url) {
          list.splice(index, 1)
        }
      })
      this.fileList = list
      $emit(this, 'getFilesList', this.fileList)
    },
    // 修改图片
    amend(file) {
      const amendImgUrl = this.substitute.url
      const list = this.fileList
      if (file) {
        list.map((item) => {
          if (item.url === amendImgUrl) {
            item.url = file.imgOrgUrl
          }
        })
      }
      $emit(this, 'getFilesList', this.fileList)
    },

    // 多种图片上传
    // 上传成功回调
    onSuccessTitleimageMore(response, file) {
      if (!response) {
        return
      }
      const me = this
      const msg = `最多支持上传${this.maxNum}张图片`
      if (me.fileList.length >= this.maxNum) {
        this.$message.error(msg)
        this.$refs.moreUpload && this.$refs.moreUpload.clearFiles()
        return
      }
      const imgObj = {}
      if (response.name) {
        imgObj.url = file.response.imgOrgUrl
        imgObj.name = file.uid
        me.fileList.push(imgObj)
      } else {
        me.$notify.error({
          title: '上传错误'
        })
      }
      $emit(this, 'getFilesList', this.fileList)
    },
    onExceed(files, fileList) {
      this.$refs.moreUpload.clearFiles()
      if (files && files.length + this.fileList.length >= this.maxNum) {
        const msg = `最多支持上传${this.maxNum}张图片`
        this.$message.error(msg)
      }
    },

    // 上传图片，同步上传
    async httpRequestOrder(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1 // 不压缩
      this._uploads = this._uploads || []
      this._uploads.push({
        fn: this.$oss.ossUploadImage,
        option
      })
      this.$tools.debounce(this.call, 100)()
    },
    async call() {
      for (const a of this._uploads) {
        await a.fn(a.option)
      }
      this._uploads = []
    }
  },
  emits: ['getFilesList']
}
</script>

<style lang="scss" scoped>
.avatar-uploader-icon {
  font-size: 28px;
  width: 126px;
  height: 126px;
  line-height: 126px;
  text-align: center;
  border: 1px dashed #feaf38;
  .el-icon-plus {
    color: #feaf38;
  }
  .avatar-uploader-icon-text {
    font-size: 12px;
    margin-top: -100px;
    height: 70px;
    color: #feaf38;
  }
}
.image-wrap {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 126px;
  height: 126px;
  margin: 0 10px 10px 0;
  .image {
    width: 126px;
    height: 126px;
    border-radius: 6px;
    border: 1px solid #ccc;
  }
  &:hover {
    .operation {
      visibility: visible;
    }
  }
  .operation {
    display: flex;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    visibility: hidden;
    .delete {
      margin-left: 10px;
    }
  }
}
</style>
