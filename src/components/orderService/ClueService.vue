<template>
  <el-card class="wd-relative" :style="{ width: props.width }">
    <el-form
      hide-required-asterisk
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px"
    >
      <el-form-item label="线索服务：" prop="clueService">
        <el-select
          v-model="form.clueService"
          clearable
          placeholder="请选择"
          style="width: 350px"
          @change="cluePackageChange"
        >
          <el-option
            v-for="(value, index) in orderCluePackageType"
            :key="index"
            :label="value"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <div class="wd-flex">
        <ShopUser
          v-model:shop-id="form.shopId"
          v-model:shop-name="form.shopName"
          @changeShopId="changeShopId"
          @change="changeShop"
        />
      </div>
      <el-form-item label="线索包类型：" prop="clueType">
        <el-select
          v-model="form.clueType"
          @change="setFormPrice"
          :disabled="form.clueCount !== ''"
          clearable
          placeholder="请选择"
          style="width: 350px"
        >
          <el-option
            v-for="(value, index) in feeTypeList"
            :key="index"
            :label="value.name"
            :value="value.id"
          />
        </el-select>
      </el-form-item>
      <template v-if="form.clueService === '1' && form.clueType === 11">
        <el-form-item prop="createDateRange" label="年包开始-结束：">
          <div class="input-box-style">
            <el-date-picker
              :default-time="
                ['00:00:00', '23:59:59'].map((d) =>
                  dayjs(d, 'hh:mm:ss').toDate()
                )
              "
              :shortcuts="
                forwardPickerOptions && forwardPickerOptions.shortcuts
              "
              v-model="form.createDateRange"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :disabled="showTime"
              style="width: 350px"
            ></el-date-picker>
            <div v-if="showTime" class="price-hint">
              年包剩余天数：{{ lastDays }}天
            </div>
          </div>
        </el-form-item>
        <el-form-item label="年包品牌：" prop="minPrice">
          <!-- <div style="width: 100%"> -->
          <!-- <el-button
              v-if="showTime"
              type="primary"
              plain
              class="mb10"
              @click="createBrandOrder"
              >+添加品牌</el-button
            > -->
          <!-- <el-table :data="brandList" border>
              <el-table-column prop="brandName" label="品牌" align="center" />
              <el-table-column prop="num" label="条数" align="center" />
              <el-table-column label="刊例价" align="center">
                <template #default="{ row }">{{ row.num * 15 }}</template>
              </el-table-column>
            </el-table>
          </div> -->
          <DragTable
            v-loading="loading"
            v-model="brandList"
            @end="drageChangeEnd"
            :disabledIndex="disabledIndex"
          >
            <template #default="{ row, index }">
              <DragCloum name="序号">{{ index + 1 }}</DragCloum>
              <DragCloum name="品牌">{{ row.brandName }}</DragCloum>
              <DragCloum name="条数">{{ row.num }}</DragCloum>
              <DragCloum name="刊例价">
                {{ row.num || row.num == 0 ? row.num * 15 : '' }}
              </DragCloum>
              <DragCloum name="售卖渠道">
                {{ row.channelName }}
              </DragCloum>
              <DragCloum name="操作">
                <el-icon
                  v-if="index >= disabledIndex"
                  @click="deleteBrand(index)"
                  ><Delete
                /></el-icon>
              </DragCloum>
            </template>
          </DragTable>
          <div class="mb10 wd-text-center wd-w-full">
            <el-button
              :disabled="
                isNewCarVip && !isPackYears && frozenBrandIdList.length < 10
              "
              type="primary"
              plain
              @click="createBrandYearPackage"
            >
              +年包品牌
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="总条数：">
          <el-input v-model="total" disabled style="width: 300px" />
        </el-form-item>
        <el-form-item label="商家版显示价：">
          <div class="input-box-style">
            <el-input
              v-model="brandQueryInfo.showPrice"
              disabled
              style="width: 300px"
            />
            <div
              v-if="brandQueryInfo.freeNumber && !showTime"
              class="price-hint"
            >
              该价格已扣除基础线索量：{{ brandQueryInfo.freeNumber }}条/年
            </div>
          </div>
        </el-form-item>
        <el-form-item label="刊例价：">
          <div class="input-box-style">
            <el-input
              v-model="brandQueryInfo.originPrice"
              disabled
              style="width: 300px"
            />
            <div
              v-if="brandQueryInfo.freeNumber && !showTime"
              class="price-hint"
            >
              该价格已扣除基础线索量：{{ brandQueryInfo.freeNumber }}条/年
            </div>
          </div>
        </el-form-item>
        <el-form-item label="最低价：">
          <div class="input-box-style">
            <el-input v-model="form.minPrice" disabled style="width: 300px" />
            <div
              v-if="brandQueryInfo.freeNumber && !showTime"
              class="price-hint"
            >
              该价格已扣除基础线索量：{{ brandQueryInfo.freeNumber }}条/年
            </div>
          </div>
        </el-form-item>
      </template>
      <el-form-item
        v-if="!(form.clueService === '1' && form.clueType === 11)"
        label="自定义线索数量："
        prop="clueCount"
      >
        <el-input
          :disabled="!!form.clueType"
          v-int
          v-model="form.clueCount"
          placeholder="自定义线索数量"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item
        v-if="!(form.clueService === '1' && form.clueType === 11)"
        label="应付价格："
        prop="price"
      >
        <el-input
          v-model="form.price"
          :disabled="Number(route.query.type) === 0"
          placeholder="请输入"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="价格折扣：" prop="discount">
        <el-input
          :value="
            Number(route.query.type) === 0
              ? '0折'
              : discount !== '' &&
                !isNaN(Number(discount)) &&
                isFinite(Number(discount))
              ? discount + '折'
              : ''
          "
          placeholder="0-10折"
          disabled
          clearable
          style="width: 350px"
        />
        <!-- <span style="color: red">{{
          Number(route.query.type) === 0
            ? '0折'
            : discount !== '' &&
              !isNaN(Number(discount)) &&
              isFinite(Number(discount))
            ? discount + '折'
            : ''
        }}</span> -->
      </el-form-item>
      <el-form-item label="实付价格：" prop="realPrice">
        <div>
          <el-input
            :disabled="Number(route.query.type) === 0"
            @input="handleChangePrice"
            placeholder="请输入"
            v-model="form.realPrice"
            clearable
            style="width: 350px"
          />
          <div
            v-if="form.clueService === '1' && form.clueType === 11"
            style="color: #409eff"
          >
            实付价格小于等于最低价格92折将进入EHR审核
          </div>
        </div>
      </el-form-item>
      <el-form-item label="主订单号：" prop="orderNumber">
        <el-input v-model="form.orderNumber" clearable style="width: 350px" />
      </el-form-item>
      <el-form-item
        prop="reason"
        v-if="form.realPrice !== '' && Number(form.realPrice) === 0"
        label="赠送原因："
      >
        <el-input v-model="form.reason" clearable style="width: 350px" />
      </el-form-item>
    </el-form>
    <el-button
      v-if="showDelete"
      class="wd-absolute wd-right-10px wd-bottom-10px"
      text
      @click="handleDelete"
      type="primary"
      >删除</el-button
    >
    <!-- <AddBrandOrder ref="addBrandOrderRef" @success="brandOrderReturn" /> -->
    <AddBrandYearPackage
      ref="addBrandYearPackageRef"
      @success="brandYearPackageReturn"
    />
  </el-card>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { reactive, ref, defineProps, watchPostEffect } from 'vue'
import { GetFeeTypes } from '@/api/newCar'
import { orderCluePackageType } from '@/utils/enum'
import { forwardPickerOptions } from '@/utils/configData'
import ShopUser from '@/components/orderService/ShopUser.vue'
import { FormInstance } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { IserviceCfg, getBrandQueryInfo } from '@/api/order'
import { DragTable, dragCloum as DragCloum } from '../DragTable/index'
import AddBrandYearPackage from './AddBrandYearPackage.vue'
import { deepCopy } from '@/utils'

// import AddBrandOrder from './AddBrandOrder.vue'

interface IFeedTypeItem {
  name: string
  id: number
  number: number
  price: number
}
interface IForm {
  shopId: string
  shopName: string
  clueType: string | number
  clueService: string | number
  price: string | number
  realPrice: number | string
  startTime: string
  endTime: string
  clueCount: number | string
  reason: string
  orderNumber: string
  createDateRange: string[]
  minPrice: number | string
}
const emit = defineEmits<{
  (e: 'delete', index: number): void
  (
    e: 'setPrice',
    data: { price: number | string; index: number; discount: number | string }
  ): void
}>()
const props = withDefaults(
  defineProps<{
    width?: string | number
    showDelete?: boolean
    index: number
    id: number
  }>(),
  {
    width: '730px',
    showDelete: true
  }
)
const route = useRoute()
const form = reactive<IForm>({
  shopId: '',
  shopName: '',
  clueService: '',
  clueCount: '',
  clueType: '',
  price: Number(route.query.type) === 0 ? 0 : '',
  realPrice: Number(route.query.type) === 0 ? 0 : '',
  startTime: '',
  endTime: '',
  orderNumber: (route.query.orderNumber as string) || '',
  reason: '',
  createDateRange: [],
  minPrice: ''
})
const minPriceRules = (rule: any, value: any, callback: any) => {
  if (!brandList.value.length) {
    return callback(new Error('请添加年包品牌'))
  }
  callback()
}

const priceRules = (rule: any, value: any, callback: any) => {
  if (Number(value) < 1) {
    return callback(new Error('价格需至少为1元'))
  }
  callback()
}

const rules = reactive({
  shopId: [{ required: true, message: '请输入经销商ID', trigger: 'blur' }],
  shopName: [{ required: true, message: '请选择经销商', trigger: 'change' }],
  clueService: [
    { required: true, message: '请选择线索服务', trigger: 'change' }
  ],
  clueType: [{ required: true, message: '请选择线索类型', trigger: 'change' }],
  clueCount: [{ required: true, message: '请输入线索数量', trigger: 'blur' }],
  price: [{ required: true, validator: priceRules, trigger: 'blur' }],
  realPrice: [{ required: true, message: '请输入实付价格', trigger: 'blur' }],
  reason: [{ required: true, message: '请输入赠送原因', trigger: 'blur' }],
  createDateRange: [
    { required: true, message: '请选择时间', trigger: 'change' }
  ],
  orderNumber: [
    { required: false, message: '请输入主订单号', trigger: 'blur' }
  ],
  minPrice: [{ required: true, validator: minPriceRules }]
})
const feeTypeList = ref<IFeedTypeItem[]>([])
const formRef = ref<FormInstance>()
const brandList = ref<any[]>([])
// const addBrandOrderRef = ref()
const addBrandYearPackageRef = ref()
const showTime = ref(false)
const lastDays = ref(0)
const brandIdList = ref<any[]>([])
const frozenBrandIdList = ref<number[]>([])
const brandQueryInfo = ref<any>({})
const total = ref(0)
const isNewCarVip = ref(false)
const disabledIndex = ref<number>(-1)
let originBrandIds: any[] = []
const loading = ref(false)
const isPackYears = ref(false)

watchPostEffect(() => {
  if (brandList.value.length < 10 && !showTime.value) {
    const len = 10 - brandList.value.length
    if (len <= 0) return
    for (let i = 0; i < len; i++) {
      brandList.value.push({})
    }
  }
})

const discount = computed(() => {
  if (form.price && form.realPrice) {
    if (Number(form.price) === 0) {
      return 0
    }
    return parseFloat(
      ((Number(form.realPrice) / Number(form.price)) * 10).toFixed(2)
    )
  }
  return ''
})

onMounted(() => {
  const clearBrandOrderData = localStorage.getItem('clearBrandOrderData')
  if (clearBrandOrderData) {
    const item = JSON.parse(clearBrandOrderData)
    form.clueService = '1'
    form.shopId = (route.query.shopId as string) || ''
    form.shopName = (route.query.shopName as string) || ''
    form.clueType = 11
    form.createDateRange =
      item.createDateRange || item.dateRange || form.createDateRange || []
    if (item.dateRange) {
      showTime.value = true
    }
    if (item.lastDays) {
      lastDays.value = item.lastDays
    }

    isPackYears.value = item.isPackYears
    brandIdList.value = item.brandIdList || []
    originBrandIds = deepCopy(item.brandIdList || [], []) as any[]
    isNewCarVip.value = item.isNewCarVip || false
    getFeeTypes({ types: '1,3' })
    getBrandList()
    localStorage.removeItem('clearBrandOrderData')
  }
})

// const createBrandOrder = () => {
//   if (!form.shopId) {
//     return window.$message.error('请输入经销商ID')
//   }
//   const list: any = []
//   brandList.value.forEach((v: any) => {
//     list.push(v.brandId)
//   })
//   addBrandOrderRef.value.init({ shopId: form.shopId, brandIdList: list })
// }

// const brandOrderReturn = (data: any) => {
//   brandIdList.value = data.brandIdList
//   getBrandList()
// }

const createBrandYearPackage = () => {
  if (!form.shopId) {
    return window.$message.error('请输入经销商ID')
  }
  const list: any = []
  brandList.value.forEach((v: any) => {
    list.push(v.brandId)
  })
  addBrandYearPackageRef.value.open({
    shopId: form.shopId,
    brandList: list,
    brandIds: originBrandIds.length ? JSON.stringify(originBrandIds) : '',
    addBrand: showTime.value ? 1 : '',
    frozenBrandIdList: frozenBrandIdList.value
  })
}

const getChannelBrandId = (list: any) => {
  const data = list.filter((v: any) => v.brandId)
  return (
    data.map((v: any) => {
      return {
        brandId: v.brandId,
        channel: v.channel,
        channelType: v.channelType
      }
    }) || []
  )
}

let isAddBrand = false
const brandYearPackageReturn = (data: any) => {
  brandIdList.value = getChannelBrandId(data)
  isAddBrand = true
  getBrandList()
}

const deleteBrand = (index: number) => {
  brandList.value.splice(index, 1)
  brandIdList.value = getChannelBrandId(brandList.value)
  getBrandList()
}

const getBrandList = (flag?: any) => {
  loading.value = true
  brandList.value = []
  brandQueryInfo.value = {}
  total.value = 0
  if (!flag) {
    form.price = Number(route.query.type) === 0 ? 0 : ''
  }
  form.minPrice = ''
  if (!form.shopId || !(form.clueService === '1' && form.clueType === 11)) {
    return
  }
  getBrandQueryInfo({
    shopId: form.shopId,
    brandIds: brandIdList.value?.length
      ? JSON.stringify(brandIdList.value)
      : '',
    addBrand: showTime.value ? 1 : ''
  })
    .then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data || {}
        brandQueryInfo.value = data
        brandList.value = data.detail || []
        form.price = data.minPrice || ''
        form.minPrice = data.minPrice || ''
        if (isAddBrand) {
          isAddBrand = false
        } else {
          const list = deepCopy(data.detail || [], [])
          setFrozenBrandIdList(list)
        }
        brandList.value.forEach((v: any) => {
          // if (frozenBrandIdList.value.includes(v.brandId)) {
          //   v.disabled = true
          // }
          const num = v.num || 0
          total.value += num
        })
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const drageChangeEnd = (event: any) => {
  setFrozenBrandIdList(brandList.value)
}

const setFrozenBrandIdList = (list: any) => {
  // 已开年包，已开会员
  if (isPackYears.value && isNewCarVip.value) {
    frozenBrandIdList.value = []
  }
  // 未开年包，已开会员
  if (isNewCarVip.value && !isPackYears.value) {
    frozenBrandIdList.value = list.slice(0, 10).map((v: any) => {
      return v.brandId
    })
  }
  // 未开年包，未开会员
  if (!isNewCarVip.value && !isPackYears.value) {
    frozenBrandIdList.value = []
  }
  disabledIndex.value = frozenBrandIdList.value.length
}

const changeShop = (res: any) => {
  isNewCarVip.value = !!res.isNewCarVip
  isPackYears.value = res.isPackYears
}

const changeShopId = () => {
  showTime.value = false
  brandIdList.value = []
  getBrandList()
}

const setFormPrice = () => {
  const item = feeTypeList.value.find((item) => {
    return item.id === form.clueType
  })
  if (item) {
    form.price = item.price
  }
  form.realPrice = Number(route.query.type) === 0 ? 0 : ''
  showTime.value = false
  brandIdList.value = []
  getBrandList(item ? true : false)
}
const cluePackageChange = (query: string) => {
  form.clueType = ''
  form.realPrice = Number(route.query.type) === 0 ? 0 : ''
  setFormPrice()
  getFeeTypes({ types: query === '1' ? `1,3` : query }) // 1询价 2试驾 3年包, 8租车， 9收车
}
// 获取费用类型列表接口
const getFeeTypes = (paramsObj: any) => {
  const requestParams = {
    ...paramsObj
  }
  GetFeeTypes(requestParams)
    .then((response) => {
      feeTypeList.value = response.data.data
    })
    .catch((err) => {
      console.log(err)
    })
}
const clueServiceToServiceType: Record<any, number> = {
  1: 3,
  6: 6,
  8: 5,
  2: 4,
  9: 9,
  11: 11
}
const validate = () => {
  rules.orderNumber[0].required = !!route.query.orderNumber
  rules.clueCount[0].required = !form.clueType
  rules.clueType[0].required = !(form.clueCount !== '')
  return new Promise<IserviceCfg>((resolve, reject) => {
    formRef.value!.validate((valid) => {
      if (valid) {
        const number =
          form.clueCount === ''
            ? feeTypeList.value.find((item) => item.id === form.clueType)
                ?.number
            : form.clueCount
        const brandInfo = {
          ...brandQueryInfo.value
        }
        if (brandQueryInfo.value.detail) {
          brandInfo.detail = brandQueryInfo.value.detail.filter(
            (temp: any) => temp.brandId
          )
        }
        return resolve({
          shopId: form.shopId as string,
          shopName: form.shopName,
          number: number as number,
          beginTime: form.createDateRange[0],
          endTime: form.createDateRange[1],
          price: form.realPrice as string,
          orgPrice: form.price as string,
          discount: (Number(discount.value) / 10).toFixed(2),
          orgDiscount: discount.value,
          orderNumber: form.orderNumber,
          giveReason: form.reason,
          itemId: form.clueType || 0,
          itemName:
            feeTypeList.value.find((item) => item.id === form.clueType)?.name ||
            '',
          serviceType:
            clueServiceToServiceType[form.clueService as string] === 3 &&
            form.clueType === 11
              ? 8
              : clueServiceToServiceType[form.clueService as string],
          minPrice: form.minPrice as string,
          brandPriceDetail: JSON.stringify(brandInfo),
          addBrand: showTime.value ? 1 : ''
        })
      } else {
        return reject()
      }
    })
  })
}

const handleDelete = () => {
  emit('delete', props.id)
}
const handleChangePrice = (val: string) => {
  const data: any = {
    price: val,
    index: props.id
  }
  if (form.clueService === '1' && form.clueType === 11) {
    const discount = form.price
      ? Number(((Number(val) / Number(form.price)) * 10).toFixed(2))
      : 0
    data.discount = discount
  }
  emit('setPrice', data)
}
defineExpose({
  validate
})
</script>

<style lang="scss" scoped>
.input-box-style {
  display: flex;
  align-items: center;
  .price-hint {
    margin-left: 10px;
    line-height: 18px;
    color: #409eff;
  }
}
</style>
