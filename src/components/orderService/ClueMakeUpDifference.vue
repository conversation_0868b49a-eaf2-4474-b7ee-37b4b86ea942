<template>
  <el-card class="wd-relative" :style="{ width: props.width }">
    <el-form
      ref="formRef"
      hide-required-asterisk
      :rules="rules"
      :model="form"
      label-width="130px"
    >
      <div class="wd-flex">
        <ShopUser
          disabled
          v-model:shop-id="form.shopId"
          v-model:shop-name="form.shopName"
        />
      </div>
      <el-form-item label="付费类型：">
        <el-input value="年包补差价" disabled style="width: 350px" />
      </el-form-item>

      <el-form-item label="原有效起期：">
        <el-input
          :value="
            calculateResult.memberBeginTime
              ? dayjs(calculateResult.memberBeginTime).format(
                  'YYYY-MM-DD HH:mm:ss'
                )
              : ''
          "
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="原有效止期：">
        <el-input
          :value="
            calculateResult.memberEndTime
              ? dayjs(calculateResult.memberEndTime).format(
                  'YYYY-MM-DD HH:mm:ss'
                )
              : ''
          "
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="原订单状态：">
        <el-input
          :value="convertPaymentStatus[calculateResult.status]"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="原订单价格：">
        <el-input
          :value="calculateResult.origPrice"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="补差价天数：">
        <el-input
          :value="calculateResult.days"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="差价：">
        <el-input
          @input="handlePriceChange"
          v-model="calculateResult.diffPrice"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="价格折扣：" prop="discount">
        <el-input
          @input="handlePriceChange"
          placeholder="请输入"
          v-model="form.discount"
          clearable
          style="width: 350px"
        />
      </el-form-item>

      <el-form-item label="支付金额：" prop="realPrice">
        <el-input
          placeholder="请输入"
          v-model="form.realPrice"
          clearable
          style="width: 350px"
        />
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { reactive, ref, defineProps } from 'vue'
import ShopUser from '@/components/orderService/ShopUser.vue'
import { FormInstance, FormRules } from 'element-plus'
import { convertKeyValueEnum } from '@/utils/convert'
import { paymentStatus } from '@/utils/enum'
import dayjs from 'dayjs'
import { getOrderDiffDetail, IserviceCfg, UpgradeType } from '@/api/order'

interface IForm {
  shopId: string
  shopName: string
  realPrice: number | string
  discount: string
}

const emit = defineEmits<{
  (e: 'setPrice', data: { price: number | string; index: number }): void
}>()
const props = withDefaults(
  defineProps<{
    width?: string | number
  }>(),
  {
    width: '730px'
  }
)
const route = useRoute()
const convertPaymentStatus: any = convertKeyValueEnum(paymentStatus)
const { memberType = 1, shopId = 300638, shopName, orderNumber } = route.query

const formRef = ref<FormInstance>()

const realDiscount = computed(() => {
  return isNaN(Number(form.discount)) ? 1 : Number(form.discount) / 10
})
const handlePriceChange = () => {
  form.realPrice = Number(
    (realDiscount.value * calculateResult.value.diffPrice).toFixed(2)
  )

  emit('setPrice', { price: form.realPrice, index: -1 })
}
const form = reactive<IForm>({
  shopId: (shopId as string) || '',
  shopName: shopName as string,
  realPrice: '',
  discount: '10'
})
const rules = reactive<FormRules>({
  discount: [{ required: true, message: '请输入价格折扣', trigger: 'blur' }],
  realPrice: [{ required: true, message: '请输入支付金额', trigger: 'blur' }]
})

const calculateResult = ref<Record<string, any>>({})

const validate = () => {
  return new Promise<IserviceCfg>((resolve, reject) => {
    formRef.value!.validate((valid) => {
      if (valid) {
        return resolve({
          shopName: form.shopName,
          shopId: form.shopId,
          price: form.realPrice as string,
          orgPrice: calculateResult.value.diffPrice,
          discount: (Number(form.discount) / 10).toFixed(2),
          orgDiscount: form.discount,
          itemId: calculateResult.value.itemId || 0,
          serviceType: memberType as string,
          upgrade: UpgradeType.MAKEUPDIFFERENCE,
          beginTime: calculateResult.value.beginTime
            ? dayjs(calculateResult.value.beginTime).format(
                'YYYY-MM-DD HH:mm:ss'
              )
            : '',
          endTime: calculateResult.value.endTime
            ? dayjs(calculateResult.value.endTime).format('YYYY-MM-DD HH:mm:ss')
            : ''
        })
      } else {
        return reject()
      }
    })
  })
}

const getDetail = async () => {
  try {
    const res = await getOrderDiffDetail({
      orderNumber: orderNumber
    })
    if (res.data.code === 0) {
      calculateResult.value = res.data.data
      calculateResult.value.days = Math.abs(Number(calculateResult.value.days))
      calculateResult.value.totalCost = res.data.data.totalCost
    }
  } catch (error) {
    console.log(error)
  }
}
onMounted(() => {
  getDetail()
})
defineExpose({
  validate
})
</script>

<style scoped></style>
