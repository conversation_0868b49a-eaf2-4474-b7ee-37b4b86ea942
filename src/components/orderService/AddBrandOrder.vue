<template>
  <el-dialog v-model="dialogVisible" width="800">
    <div class="brand-order">
      <el-form>
        <el-form-item v-if="orderList.length > 1" label="选择年包">
          <el-select v-model="dateRange" style="width: 280px">
            <el-option
              v-for="(item, index) in orderList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="年包品牌">
          <el-table
            :data="brandList"
            border
            max-height="50vh"
            ref="elTableRef"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" align="center" width="55" />
            <el-table-column prop="brandName" label="品牌名称" align="center" />
            <el-table-column label="是否授权" align="center" width="90">
              <template #default="{ row }">
                {{ row.authStatus ? '有' : '无' }}
              </template>
            </el-table-column>
            <el-table-column label="商家版在售展示" align="center" width="125">
              <template #default="{ row }">
                <el-switch :model-value="row.showStatus === 1" />
              </template>
            </el-table-column>
            <el-table-column label="年包付费" align="center" width="90">
              <template #default="{ row }">
                {{ row.payStatus ? '已付费' : '未付费' }}
              </template>
            </el-table-column>
            <el-table-column label="售卖渠道" align="center" width="120">
              <template #default="{ row }">
                {{ channelEnum[row.channel] || '' }}
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>

      <div class="button-style">
        <el-button @click="dialogVisible = false"> 取消 </el-button>
        <el-button type="primary" @click="createOrder"> 创建订单 </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { channelList } from '@/utils/enum'
import { convertKeyValueEnum } from '@/utils/convert'
import { merchantDetail } from '@/api/garage'
import dayjs from 'dayjs'

const emit = defineEmits(['success'])

const channelEnum = convertKeyValueEnum(channelList)
const dialogVisible = ref(false)
const fromData = ref({})
const dateRange = ref('')
const brandList = ref([])
const orderList = ref([])
const brandIdList = ref([])
const elTableRef = ref()
const detailData = ref({})

const init = (data) => {
  fromData.value = data || {}
  dateRange.value = ''
  brandList.value = []
  orderList.value = []
  brandIdList.value = []
  if (data?.shopId) {
    getBrandList(data?.shopId, data?.brandIdList || [])
  }
  dialogVisible.value = true
}

const getBrandList = (shopId, idList) => {
  const brandArr = []
  merchantDetail({ shopId })
    .then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data || {}
        detailData.value = data
        const arr = data.shopBrands || []
        arr.forEach((v) => {
          if (!v.payStatus) {
            brandList.value.push(v)
          }
          if (idList && idList.length && idList.includes(v.brandId)) {
            brandArr.push(v)
          }
        })
        const list = data.shopCluePackageInfoList || []
        if (list && list.length && fromData.value.isAddBrand) {
          list.forEach((v) => {
            if (v.beginTime && v.endTime) {
              const beginTime = dayjs(v.beginTime).format('YYYY年MM月DD日')
              const endTime = dayjs(v.endTime).format('YYYY年MM月DD日')
              const beginTime_ = dayjs('00:00:00', 'hh:mm:ss').format(
                'YYYY-MM-DD HH:mm:ss'
              )
              const endTime_ = dayjs(v.endTime).format('YYYY-MM-DD HH:mm:ss')
              orderList.value.push({
                label: `${beginTime} - ${endTime}`,
                value: JSON.stringify([beginTime_, endTime_])
              })
            }
          })
          if (fromData.value.isAddBrand && orderList.value.length <= 1) {
            dateRange.value = orderList.value[0]?.value || ''
          }
        }
      }
    })
    .finally(() => {
      if (brandArr.length) {
        nextTick(() => {
          brandArr.forEach((v) => {
            elTableRef.value.toggleRowSelection(v, true)
          })
        })
      }
    })
}

const handleSelectionChange = (list) => {
  const arr = []
  list.forEach((v) => {
    arr.push(v.brandId)
  })
  brandIdList.value = arr
}

const createOrder = () => {
  if (fromData.value.isAddBrand && !dateRange.value) {
    return window.$message.error('请选择年包')
  }
  if (!brandIdList.value.length) {
    return window.$message.error('请选择品牌')
  }
  const data = {
    brandIdList: brandIdList.value
  }
  if (dateRange.value) {
    data.dateRange = JSON.parse(dateRange.value)
  }
  if (detailData.value.lastDays) {
    data.lastDays = detailData.value.lastDays
  }
  emit('success', data)
  dialogVisible.value = false
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.brand-order {
  .button-style {
    text-align: center;
    margin-top: 25px;
    .el-button {
      width: 100px;
      &:nth-child(2) {
        margin-left: 30px;
      }
    }
  }
}
</style>
