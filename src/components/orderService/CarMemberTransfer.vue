<template>
  <el-card class="wd-relative" :style="{ width: props.width }">
    <el-form
      hide-required-asterisk
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px"
    >
      <div class="wd-flex">
        <ShopUser :shop-name="shopName" disabled :shop-id="form.shopId" />
      </div>
      <el-form-item label="需转移服务：">
        <el-input
          :value="form.serviceName"
          placeholder="请输入"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="使用天数：" prop="usedNumber">
        <el-input
          disabled
          v-model="form.usedNumber"
          placeholder="请输入"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="退款天数：" prop="restNumber">
        <el-input
          disabled
          v-model="form.restNumber"
          placeholder="请输入"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="退款：" prop="refundPrice">
        <el-input
          v-model="form.refundPrice"
          placeholder="请输入"
          clearable
          style="width: 350px"
        />
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { reactive, ref, defineProps } from 'vue'
import ShopUser from '@/components/orderService/ShopUser.vue'
import { FormInstance } from 'element-plus'
import { ITransferServiceInfo } from '@/api/order'

const props = withDefaults(
  defineProps<{
    width?: string | number
  }>(),
  {
    width: '730px'
  }
)
const emit = defineEmits<{
  (e: 'setPrice', data: { price: number | string; index: number }): void
}>()
const form = ref<ITransferServiceInfo>({
  shopId: '',
  serviceName: '',
  usedNumber: '',
  restNumber: '',
  refundPrice: '',
  orderNumber: '',
  serviceType: '',
  level: 0
})
const rules = reactive({
  refundPrice: [{ required: true, message: '请输入金额', trigger: 'blur' }],
  usedNumber: [{ required: true, message: '请输入使用天数', trigger: 'blur' }],
  restNumber: [{ required: true, message: '请输入退款天数', trigger: 'blur' }]
})
const formRef = ref<FormInstance>()
const shopName = ref('')
const route = useRoute()
const init = (data: ITransferServiceInfo & { shopName: string }) => {
  form.value = data
  // form.value.usedNumber = route.query.usedNum as string
  // form.value.restNumber = route.query.refundNum as string
  shopName.value = data.shopName
}
const setPrice = () => {
  emit('setPrice', { price: form.value.refundPrice, index: -3 })
}
const validate = () => {
  return new Promise<ITransferServiceInfo>((resolve, reject) => {
    formRef.value!.validate((valid) => {
      if (valid) {
        return resolve({
          ...form.value
        })
      } else {
        return reject()
      }
    })
  })
}

defineExpose({
  validate,
  init
})
</script>

<style scoped></style>
