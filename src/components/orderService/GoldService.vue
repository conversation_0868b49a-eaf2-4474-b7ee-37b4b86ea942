<template>
  <el-card class="wd-relative" :style="{ width: props.width }">
    <el-form
      ref="formRef"
      hide-required-asterisk
      :rules="rules"
      :model="form"
      label-width="130px"
    >
      <el-form-item label="金币服务："> 金币服务 </el-form-item>
      <div class="wd-flex">
        <ShopUser
          v-model:shop-id="form.shopId"
          v-model:shop-name="form.shopName"
        />
      </div>
      <el-form-item label="金币数量：" prop="number">
        <el-input
          v-model="form.number"
          @input="setPriceFun"
          placeholder="请输入"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="应付价格：" prop="price">
        <el-input
          v-model="form.price"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="价格折扣：" prop="discount">
        <el-input
          :value="
            form.discount !== '' &&
            !isNaN(Number(form.discount)) &&
            isFinite(Number(form.discount))
              ? form.discount + '折'
              : ''
          "
          placeholder="0-10折"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="实付价格：" prop="realPrice">
        <el-input
          @input="setRealPRice"
          v-model="form.realPrice"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="主订单号：" prop="orderNumber">
        <el-input v-model="form.orderNumber" clearable style="width: 350px" />
      </el-form-item>
      <el-form-item
        prop="reason"
        v-if="form.realPrice !== '' && Number(form.realPrice) === 0"
        label="赠送原因："
      >
        <el-input v-model="form.reason" clearable style="width: 350px" />
      </el-form-item>
    </el-form>
    <el-button
      v-if="showDelete"
      @click="handleDelete"
      class="wd-absolute wd-right-10px wd-bottom-10px"
      text
      type="primary"
      >删除</el-button
    >
  </el-card>
</template>

<script setup lang="ts">
import { reactive, ref, defineProps } from 'vue'
import { FormInstance } from 'element-plus'
import ShopUser from '@/components/orderService/ShopUser.vue'
import { IserviceCfg } from '@/api/order'

interface IForm {
  shopId: string
  shopName: string
  number: string
  price: string
  realPrice: number | string
  discount: string
  orderNumber: string
  reason: string
}

const emit = defineEmits<{
  (e: 'delete', index: number): void
  (e: 'setPrice', data: { price: number | string; index: number }): void
}>()
const props = withDefaults(
  defineProps<{
    width?: string | number
    showDelete?: boolean
    index: number
    id: number
  }>(),
  {
    width: '730px',
    showDelete: true
  }
)

const formRef = ref<FormInstance>()

const route = useRoute()

const form = reactive<IForm>({
  shopId: '',
  shopName: '',
  number: '',
  price: '',
  realPrice: '',
  discount: '',
  orderNumber: (route.query.orderNumber as string) || '',
  reason: ''
})
const rules = reactive({
  shopId: [{ required: true, message: '请输入经销商ID', trigger: 'blur' }],
  shopName: [{ required: true, message: '请选择经销商', trigger: 'change' }],
  number: [{ required: true, message: '请输入金币数量', trigger: 'blur' }],
  // price: [{ required: true, message: '请输入应付价格', trigger: 'blur' }],
  realPrice: [{ required: true, message: '请输入实付价格', trigger: 'blur' }],
  orderNumber: [
    { required: false, message: '请输入主订单号', trigger: 'blur' }
  ],
  reason: [{ required: true, message: '请输入赠送原因', trigger: 'blur' }]
})

// 设置应付价格
const setPriceFun = (e) => {
  form.price = e
  setRealPRice()
}

// 设置折扣
const setRealPRice = () => {
  try {
    const discount =
      form.price !== '' && Number(form.price) === 0
        ? 0
        : ((Number(form.realPrice) / Number(form.price)) * 10).toFixed(2)
    form.discount =
      discount !== Number(discount) + '' ? Number(discount) + '' : discount
    emit('setPrice', { price: form.realPrice, index: props.id })
  } catch (error) {
    console.log(error)
  }
}

const handleDelete = () => {
  emit('delete', props.id)
}

const validate = () => {
  rules.orderNumber[0].required = !!route.query.orderNumber
  return new Promise<IserviceCfg>((resolve, reject) => {
    formRef.value!.validate((valid) => {
      if (valid) {
        return resolve({
          shopId: form.shopId,
          shopName: form.shopName,
          number: form.number,
          orgPrice: form.price,
          discount: (Number(form.discount) / 10).toFixed(2),
          orgDiscount: form.discount,
          price: form.realPrice as string,
          orderNumber: form.orderNumber,
          serviceType: 12,
          itemId: 0,
          giveReason: form.reason
        })
      } else {
        return reject()
      }
    })
  })
}

defineExpose({
  validate
})
</script>

<style lang="scss" scoped></style>
