<template>
  <el-card class="wd-relative" :style="{ width: props.width }">
    <el-form
      ref="formRef"
      hide-required-asterisk
      :rules="rules"
      :model="form"
      label-width="130px"
    >
      <el-form-item label="会员服务：">
        <span>{{ MemberLabels[props.memberType] }}</span>
      </el-form-item>
      <div class="wd-flex">
        <ShopUser
          @change="handleShopChange"
          v-model:shop-id="form.shopId"
          v-model:shop-name="form.shopName"
        />
      </div>
      <el-form-item label="会员级别：" prop="levelId">
        <el-select
          v-model="form.levelId"
          clearable
          placeholder="请选择"
          style="width: 350px"
          @change="setMemberLevel"
        >
          <el-option
            v-for="(value, index) in memberLevelList"
            :key="index"
            :label="value.levelName"
            :value="value.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="试用天数："
        prop="trialDay"
        v-if="props.memberType === MemberType.TryMemeber"
      >
        <el-select
          v-model="form.trialDay"
          @change="handleTrialDayChange"
          clearable
          placeholder="请选择"
          style="width: 350px"
        >
          <el-option
            v-for="(value, index) in trialDays"
            :key="index"
            :label="value.label"
            :value="value.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="props.memberType === MemberType.TryMemeber"
        label="试用服务时间："
        prop="startDate"
      >
        <div>
          <el-date-picker
            disabled
            v-model="form.startDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="date"
            placeholder="开始日期"
          />
          <el-date-picker
            class="wd-ml-10px"
            disabled
            v-model="endDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="date"
            placeholder="结束日期"
          />
        </div>
      </el-form-item>
      <el-form-item label="应付价格：" prop="price">
        <el-input
          v-model="form.price"
          @input="setRealPRice"
          placeholder="请输入"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="价格折扣：" prop="discount">
        <el-input
          :value="
            form.discount !== '' &&
            !isNaN(Number(form.discount)) &&
            isFinite(Number(form.discount))
              ? form.discount + '折'
              : ''
          "
          placeholder="0-10折"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="实付价格：" prop="realPrice">
        <el-input
          @input="setRealPRice"
          v-model="form.realPrice"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item
        v-if="props.memberType !== MemberType.TryMemeber"
        label="服务时间："
        prop="startDate"
      >
        <div>
          <el-date-picker
            v-model="form.startDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="date"
            placeholder="开始日期"
          />
          <el-date-picker
            class="wd-ml-10px"
            disabled
            v-model="endDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="date"
            placeholder="结束日期"
          />
        </div>
      </el-form-item>
      <el-form-item
        v-if="props.memberType === MemberType.TryMemeber"
        label="主订单号："
        prop="orderNumber"
      >
        <el-input v-model="form.orderNumber" clearable style="width: 350px" />
      </el-form-item>
      <el-form-item
        prop="reason"
        v-if="
          form.realPrice !== '' &&
          Number(form.realPrice) === 0 &&
          props.memberType === MemberType.TryMemeber
        "
        label="赠送原因："
      >
        <el-input v-model="form.reason" clearable style="width: 350px" />
      </el-form-item>
    </el-form>
    <el-button
      v-if="showDelete"
      @click="handleDelete"
      class="wd-absolute wd-right-10px wd-bottom-10px"
      text
      type="primary"
      >删除</el-button
    >
  </el-card>
</template>

<script setup lang="ts">
import { reactive, ref, defineProps } from 'vue'
import dayjs from 'dayjs'
import memberLevel from '@/api/member'
import ShopUser from '@/components/orderService/ShopUser.vue'
import { FormInstance, FormRules } from 'element-plus'
import { MemberLabels, MemberType, trialDays } from './config'
import { IserviceCfg, getTimeInfo } from '@/api/order'

interface ILevelItem {
  levelName: string
  id: number
  price: string
  level: number
}
interface IForm {
  shopId: string
  shopName: string
  levelId: number | string
  price: string
  realPrice: number | string
  discount: string
  trialDay: number | ''
  startDate: string
  orderNumber: string
  reason: string
}

const emit = defineEmits<{
  (e: 'delete', index: number): void
  (e: 'setPrice', data: { price: number | string; index: number }): void
}>()
const props = withDefaults(
  defineProps<{
    memberType: MemberType
    width?: string | number
    showDelete?: boolean
    index: number
    id: number
  }>(),
  {
    memberType: MemberType.NewCarMember,
    width: '730px',
    showDelete: true
  }
)
// const route = useRoute()

const formRef = ref<FormInstance>()

const form = reactive<IForm>({
  shopId: '',
  shopName: '',
  levelId: '',
  price: '',
  realPrice: props.memberType === MemberType.TryMemeber ? 0 : '',
  discount: '',
  trialDay: '',
  startDate: '',
  orderNumber: '',
  reason: ''
})
const endDate = computed(() => {
  if (form.startDate && props.memberType !== MemberType.TryMemeber) {
    const oneYearLater = new Date(form.startDate)
    oneYearLater.setFullYear(oneYearLater.getFullYear() + 1)
    return dayjs(oneYearLater).format('YYYY-MM-DD HH:mm:ss')
  }
  if (form.startDate && props.memberType === MemberType.TryMemeber) {
    if (form.trialDay) {
      const oneYearLater = new Date(form.startDate)
      oneYearLater.setDate(oneYearLater.getDate() + form.trialDay)
      return dayjs(oneYearLater).format('YYYY-MM-DD HH:mm:ss')
    }
  }
  return ''
})

const priceRules = (rule: any, value: any, callback: any) => {
  if (Number(value) < 1) {
    return callback(new Error('价格需至少为1元'))
  }
  callback()
}

const rules = reactive<FormRules>({
  shopId: [{ required: true, message: '请输入经销商ID', trigger: 'blur' }],
  shopName: [{ required: true, message: '请选择经销商', trigger: 'change' }],
  trialDay: [{ required: true, message: '请选择试用天数', trigger: 'change' }],
  levelId: [{ required: true, message: '请选择会员级别', trigger: 'change' }],
  // startDate: [{ required: true, message: '请选择服务时间', trigger: 'change' }],
  price: [{ required: true, validator: priceRules, trigger: 'blur' }],
  realPrice: [{ required: true, message: '请输入实付价格', trigger: 'blur' }],
  orderNumber: [
    { required: false, message: '请输入主订单号', trigger: 'blur' }
  ],
  reason: [{ required: true, message: '请输入赠送原因', trigger: 'blur' }]
})
const handleDelete = () => {
  emit('delete', props.id)
}
const amountPayableEnum: any = {
  7: 88,
  15: 166,
  30: 288
}
const handleTrialDayChange = (e: any) => {
  if (props.memberType === MemberType.TryMemeber) {
    form.startDate = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
    form.price = amountPayableEnum[e] || ''
    form.realPrice = form.discount === '' ? form.price : ''
    setRealPRice()
  }
}
const handleShopChange = ({ shopId }: any) => {
  form.levelId = ''
  getMemberLevelList(shopId)
  if (props.memberType !== MemberType.TryMemeber) {
    getMemberTimeInfo(shopId)
  }
}
const memberLevelList = ref<ILevelItem[]>([])

// 设置会员级别
const setMemberLevel = () => {
  if (props.memberType !== MemberType.TryMemeber) {
    const memberLevel = memberLevelList.value.find((item) => {
      return item.id === form.levelId
    })
    if (memberLevel) {
      form.price = memberLevel.price
      form.realPrice = form.discount === '' ? form.price : ''
      setRealPRice()
    }
  }
}
const getMemberTimeInfo = async (shopId: any) => {
  const res = await getTimeInfo({
    shopId,
    serviceType:
      props.memberType === MemberType.NewCarMember
        ? 1
        : props.memberType === MemberType.UsedCarMember
        ? 2
        : ''
  })
  const data = res.data.data
  if (data.memberExpireTime) {
    form.startDate = dayjs(new Date(data.memberExpireTime))
      .add(1, 'day')
      .format('YYYY-MM-DD HH:mm:ss')
  }
}
// 设置折扣
const setRealPRice = () => {
  try {
    const discount =
      form.price !== '' && Number(form.price) === 0
        ? 0
        : ((Number(form.realPrice) / Number(form.price)) * 10).toFixed(2)
    form.discount =
      discount !== Number(discount) + '' ? Number(discount) + '' : discount
    emit('setPrice', { price: form.realPrice, index: props.id })
  } catch (error) {
    console.log(error)
  }
}
// 获取会员等级列表接口
const getMemberLevelList = (
  id: string,
  fn = memberLevel.GetShopMemberLevelList
) => {
  fn(
    id,
    props.memberType === MemberType.TryMemeber
      ? MemberType.NewCarMember
      : props.memberType
  ).then((response) => {
    if (response.data.code === 0) {
      memberLevelList.value =
        props.memberType === MemberType.TryMemeber
          ? [
              {
                id: 1,
                level: 1,
                levelName: '白银会员',
                price: 0
              }
            ]
          : response.data.data
    }
  })
}

const validate = () => {
  return new Promise<IserviceCfg>((resolve, reject) => {
    formRef.value!.validate((valid) => {
      if (valid) {
        const level = memberLevelList.value.find((item) => {
          return item.id === form.levelId
        })?.level
        return resolve({
          trialDay:
            props.memberType === MemberType.TryMemeber
              ? trialDays.find((item) => {
                  return item.id === form.trialDay
                })?.label
              : '',
          trial: props.memberType === MemberType.TryMemeber,
          shopId: form.shopId,
          shopName: form.shopName,
          level,
          orgPrice: form.price,
          discount: (Number(form.discount) / 10).toFixed(2),
          orgDiscount: form.discount,
          price: form.realPrice as string,
          beginTime: form.startDate,
          endTime: endDate.value,
          itemId: form.levelId,
          serviceType:
            props.memberType === MemberType.NewCarMember
              ? 1
              : props.memberType === MemberType.UsedCarMember
              ? 2
              : props.memberType === MemberType.TryMemeber
              ? 1
              : '',
          orderNumber: form.orderNumber,
          giveReason: form.reason
        })
      } else {
        return reject()
      }
    })
  })
}
defineExpose({
  validate
})
</script>

<style scoped></style>
