<template>
  <el-dialog v-model="dialogVisible" width="800">
    <el-table :data="tableData" border max-height="50vh">
      <el-table-column prop="brandName" label="品牌" align="center" />
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-button
            :disabled="frozenBrandIdList.includes(row.brandId)"
            :type="row.select ? 'primary' : 'default'"
            @click="row.select = !row.select"
            >{{ row.select ? '已选' : '选中' }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="footer">
      <el-button @click="dialogVisible = false"> 取消 </el-button>
      <el-button type="primary" @click="submitBrand"> 提交 </el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { getBrandQueryInfo } from '@/api/order'

import { ref, computed } from 'vue'
const tableData = ref([])
const dialogVisible = ref(false)
const emit = defineEmits(['success'])
let selectList = []
const frozenBrandIdList = ref([])

const open = (data) => {
  selectList = data.brandList || []
  frozenBrandIdList.value = data.frozenBrandIdList || []
  if (data.shopId) {
    getList(data)
  }
  dialogVisible.value = true
}

const getList = (data) => {
  const { shopId, addBrand, brandIds } = data
  getBrandQueryInfo({
    shopId,
    brandIds,
    addBrand
  }).then((res) => {
    if (res.data.code === 0) {
      const data = res.data.data || {}
      const list = data.detail || []
      list.map((item) => {
        item.select = selectList.includes(item.brandId)
      })
      tableData.value = list
    }
  })
}

const submitBrand = () => {
  const list = tableData.value.filter((item) => item.select)
  // const brandIds = list.map((item) => item.brandId)
  dialogVisible.value = false
  emit('success', list)
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.footer {
  margin-top: 20px;
  text-align: center;
}
</style>
