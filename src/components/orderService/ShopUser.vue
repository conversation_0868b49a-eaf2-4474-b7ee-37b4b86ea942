<template>
  <el-form-item prop="id" label="经销商ID：">
    <el-input
      @blur="searchShopInfo"
      v-model="id"
      :disabled="disabled"
      placeholder="请输入经销商ID"
      clearable
      style="width: 200px"
      @change="changeShopId"
    />
  </el-form-item>
  <el-form-item class="wd-ml-10px" prop="shopName" label="经销商名称">
    <el-input
      v-model="shopName"
      :disabled="disabled"
      placeholder="请输入经销商名称"
      clearable
      style="width: 200px"
    />
  </el-form-item>
</template>

<script setup lang="ts">
import { merchantDetail } from '@/api/garage'

const props = defineProps(['shopId', 'shopName', 'disabled'])
const emit = defineEmits([
  'update:shopId',
  'update:shopName',
  'change',
  'changeShopId'
])

const id = computed({
  get() {
    return props.shopId
  },
  set(value) {
    emit('update:shopId', value?.trim())
  }
})
const shopName = computed({
  get() {
    return props.shopName
  },
  set(value) {
    emit('update:shopName', value?.trim())
  }
})

const searchShopInfo = () => {
  if (!id.value) return
  merchantDetail({
    shopId: id.value
  })
    .then((response) => {
      if (response.data.code === 0) {
        if (response.data.data === null) {
          window.$message.error('请输入正确的商家ID')
          return
        }
        const data = response.data.data
        shopName.value = data.shopName
        const isNewCarVip = data.shopMemberList.length
          ? data.shopMemberList.some((item) => item.memberType === 1)
          : false
        const isPackYears =
          data.shopCluePackageInfoList && data.shopCluePackageInfoList.length
        emit('change', {
          shopId: id.value,
          shopName: data.shopName,
          isNewCarVip: isNewCarVip,
          isPackYears
        })
      }
    })
    .catch((err) => {
      window.$message.error(err.message)
    })
    .finally(() => {})
}

const changeShopId = () => {
  emit('changeShopId')
}
</script>

<style scoped></style>
