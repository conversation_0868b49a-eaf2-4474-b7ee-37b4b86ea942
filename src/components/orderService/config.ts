// import memberLevel from '@/api/member'

export enum MemberType {
  TryMemeber = 0,
  NewCarMember = 1,
  UsedCarMember = 2
}
export enum OrderType {
  Zero = 0,
  Transfer = 1,
  Upgrade = 2,
  MakeUpDifference = 3
}
export const MemberLabels = {
  [MemberType.TryMemeber]: '新车会员试用',
  [MemberType.NewCarMember]: '新车会员',
  [MemberType.UsedCarMember]: '二手车会员'
}
export const orderCluePackageType = {
  3: '新车询价',
  6: '驾考',
  5: '租车',
  9: '收车',
  4: '试驾',
  7: '微信线索',
  11: '二手车询价'
}
export const trialDays = [
  { label: '7天', id: 7 },
  { label: '15天', id: 15 },
  { label: '30天', id: 30 }
]
export const memberPayType = [
  { id: 1, label: '白银会员' },
  { id: 2, label: '黄金会员' },
  { id: 3, label: '白金会员' },
  { id: 4, label: '钻石会员' },
  { id: 5, label: '黑金会员' }
]
type PromiseFunction<T> = (...args: any[]) => Promise<T>

export function firstPromise<T, This = any>(
  promiseFunction: PromiseFunction<T>
): (this: This, ...args: any[]) => Promise<T> {
  let p: Promise<T> | null = null
  return function (this: This, ...args: any[]): Promise<T> {
    // 请求的实例，已存在意味着正在请求中，直接返回实例，不触发新的请求
    return p
      ? p
      : // 否则发送请求，且在 finally 时将 p 置空，那么下一次请求可以重新发起
        (p = promiseFunction.apply(this, args))
  }
}
