<template>
  <el-card class="wd-relative" :style="{ width: props.width }">
    <el-form
      hide-required-asterisk
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="130px"
    >
      <el-form-item label="微信线索：">
        <el-input
          v-model="form.name"
          disabled
          placeholder="请输入"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <div class="wd-flex">
        <ShopUser
          v-model:shop-id="form.shopId"
          v-model:shop-name="form.shopName"
        />
      </div>
      <el-form-item label="配置类型：" prop="clueType">
        <el-select
          v-model="form.clueType"
          value-key="id"
          clearable
          placeholder="请选择"
          style="width: 350px"
          @change="changeClueType"
        >
          <el-option
            v-for="(value, index) in wechatSettingsTypeToLabel"
            :key="index"
            :label="value.label"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        prop="clueCount"
        v-if="form.clueType?.id === 1"
        label="配置数量："
      >
        <el-input
          v-int
          v-model="form.clueCount"
          placeholder="自定义线索数量"
          clearable
          style="width: 350px"
          @input="allocationQuantity"
        />
      </el-form-item>
      <el-form-item
        prop="createDateRange"
        v-if="form.clueType?.id === 2"
        label="配置时间："
      >
        <div>
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) => dayjs(d, 'hh:mm:ss').toDate())
            "
            :shortcuts="forwardPickerOptions && forwardPickerOptions.shortcuts"
            v-model="form.createDateRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </div>
      </el-form-item>
      <el-form-item label="应付价格：" prop="price">
        <el-input
          v-model="form.price"
          @input="handleChangePrice"
          placeholder="请输入"
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="价格折扣：" prop="discount">
        <el-input
          :value="
            form.discount !== '' &&
            !isNaN(Number(form.discount)) &&
            isFinite(Number(form.discount))
              ? form.discount + '折'
              : ''
          "
          placeholder="0-10折"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="实付价格：" prop="realPrice">
        <el-input
          @input="handleChangePrice"
          v-model="form.realPrice"
          clearable
          disabled
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="主订单号：" prop="orderNumber">
        <el-input v-model="form.orderNumber" clearable style="width: 350px" />
      </el-form-item>
      <el-form-item
        prop="reason"
        v-if="form.realPrice !== '' && Number(form.realPrice) === 0"
        label="赠送原因："
      >
        <el-input v-model="form.reason" clearable style="width: 350px" />
      </el-form-item>
    </el-form>
    <el-button
      v-if="showDelete"
      class="wd-absolute wd-right-10px wd-bottom-10px"
      text
      @click="handleDelete"
      type="primary"
      >删除</el-button
    >
  </el-card>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { reactive, ref, defineProps } from 'vue'
import { wechatSettingsTypeToLabel } from '@/utils/enum'
import { forwardPickerOptions } from '@/utils/configData'
import ShopUser from '@/components/orderService/ShopUser.vue'
import { FormInstance } from 'element-plus'
import { IserviceCfg } from '@/api/order'

interface IForm {
  name: string
  shopId: string
  shopName: string
  clueType: (typeof wechatSettingsTypeToLabel)[number] | null
  price: number | string
  startTime: string
  endTime: string
  clueCount: number | string
  reason: string
  orderNumber: string
  createDateRange: string[]
  realPrice: number | string
  discount: string
}
const emit = defineEmits<{
  (e: 'delete', index: number): void
  (e: 'setPrice', data: { price: number | string; index: number }): void
}>()
const props = withDefaults(
  defineProps<{
    width?: string | number
    showDelete?: boolean
    index: number
    id: number
  }>(),
  {
    width: '730px',
    showDelete: true
  }
)
const route = useRoute()
const form = reactive<IForm>({
  name: '微信线索',
  shopId: '',
  shopName: '',
  clueCount: '',
  clueType: null,
  price: '',
  realPrice: 0,
  startTime: '',
  endTime: '',
  orderNumber: (route.query.orderNumber as string) || '',
  reason: '',
  createDateRange: [],
  discount: ''
})
const formRef = ref<FormInstance>()

const priceRules = (rule: any, value: any, callback: any) => {
  if (Number(value) < 1) {
    return callback(new Error('价格需至少为1元'))
  }
  callback()
}
const rules = reactive({
  shopId: [{ required: true, message: '请输入经销商ID', trigger: 'blur' }],
  shopName: [{ required: true, message: '请选择经销商', trigger: 'change' }],
  clueType: [{ required: true, message: '请选择线索类型', trigger: 'change' }],
  clueCount: [{ required: true, message: '请输入线索数量', trigger: 'blur' }],
  price: [{ required: true, validator: priceRules, trigger: 'blur' }],
  realPrice: [{ required: true, message: '请输入实付价格', trigger: 'blur' }],
  reason: [{ required: true, message: '请输入赠送原因', trigger: 'blur' }],
  createDateRange: [
    { required: true, message: '请选择时间', trigger: 'change' }
  ],
  orderNumber: [{ required: false, message: '请输入主订单号', trigger: 'blur' }]
})

const validate = () => {
  rules.orderNumber[0].required = !!route.query.orderNumber
  return new Promise<IserviceCfg>((resolve, reject) => {
    formRef.value!.validate((valid) => {
      if (valid) {
        return resolve({
          shopName: form.shopName,
          shopId: form.shopId,
          number: form.clueCount as number,
          beginTime: form.createDateRange[0],
          endTime: form.createDateRange[1],
          price: form.realPrice as string,
          orgPrice: form.price as string,
          itemId: 0,
          orderNumber: form.orderNumber,
          settingType: form.clueType?.id,
          settingName: form.clueType?.label,
          giveReason: form.reason,
          serviceType: 7,
          discount: (Number(form.discount) / 10).toFixed(2),
          orgDiscount: form.discount
        })
      } else {
        return reject()
      }
    })
  })
}
const handleDelete = () => {
  emit('delete', props.id)
}
const changeClueType = (e: any) => {
  if (e.id === 1) {
    allocationQuantity()
  } else {
    form.price = ''
    handleChangePrice()
  }
}
const allocationQuantity = () => {
  const price = Number(form.clueCount) * 5
  form.price = price
  handleChangePrice()
}
const handleChangePrice = () => {
  try {
    const discount =
      form.price !== '' && Number(form.price) === 0
        ? 0
        : ((Number(form.realPrice) / Number(form.price)) * 10).toFixed(2)
    form.discount =
      discount !== Number(discount) + '' ? Number(discount) + '' : discount
    emit('setPrice', { price: form.realPrice, index: props.id })
  } catch (error) {
    console.log(error)
  }
}
// const createDateRange = computed({
//   get() {
//     if (form.startTime && form.endTime) {
//       return [form.startTime, form.endTime]
//     }
//     return []
//   },
//   set(value) {
//     if (value) {
//       form.startTime = value[0]
//       form.endTime = value[1]
//     } else {
//       form.startTime = ''
//       form.endTime = ''
//     }
//   }
// })
defineExpose({
  validate
})
</script>

<style scoped></style>
