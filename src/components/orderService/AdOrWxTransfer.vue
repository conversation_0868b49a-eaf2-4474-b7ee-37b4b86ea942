<template>
  <el-card class="wd-relative" :style="{ width: props.width }">
    <div class="title">待转移订单-{{ titleEnum[form.serviceType] || '' }}</div>
    <el-form
      hide-required-asterisk
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px"
    >
      <el-form-item label="服务类型：">
        <el-input
          :value="titleEnum_[form.serviceType] || ''"
          placeholder="请输入"
          disabled
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="经销商ID：">
        <el-input
          :value="form.shopId"
          placeholder="请输入"
          disabled
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="经销商名称：">
        <el-input
          :value="form.shopName"
          placeholder="请输入"
          disabled
          style="width: 350px"
        />
      </el-form-item>
      <template v-if="form.serviceType === 10">
        <el-form-item label="需转移服务：">
          <el-input
            :value="form.serviceName"
            placeholder="请输入"
            disabled
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item label="广告名称：">
          <el-input
            :value="form.advertTitle"
            placeholder="请输入"
            disabled
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item label="截止到期日期：">
          <el-date-picker
            v-model="form.deadlineTime"
            value-format="YYYY.MM.DD"
            format="YYYY.MM.DD"
            type="date"
            placeholder="到期日期"
            disabled
            style="width: 350px"
          />
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item label="配置类型：">
          <el-select v-model="form.settingType" disabled style="width: 350px">
            <el-option
              v-for="item in wechatSettingsTypeToLabel"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="`使用${form.settingType === 1 ? '条' : '天'}数：`"
        >
          <el-input
            :value="form.usedNumber"
            placeholder="请输入"
            disabled
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item
          :label="`退款${form.settingType === 1 ? '条' : '天'}数：`"
        >
          <el-input
            :value="form.restNumber"
            placeholder="请输入"
            disabled
            style="width: 350px"
          />
        </el-form-item>
      </template>
      <el-form-item label="退款：" prop="refundPrice">
        <el-input
          v-model="form.refundPrice"
          placeholder="请输入"
          clearable
          style="width: 350px"
        />
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup>
import { wechatSettingsTypeToLabel } from '@/utils/enum'

const props = defineProps({
  width: {
    type: [String, Number],
    default: '730px'
  }
})

const titleEnum = {
  7: '微信线索服务',
  10: '广告服务'
}

const titleEnum_ = {
  7: '微信线索类型',
  10: '广告服务'
}

const form = ref({
  serviceType: '',
  shopId: '',
  shopName: '',
  serviceName: '',
  advertTitle: '',
  deadlineTime: '',
  settingType: '',
  usedNumber: '',
  restNumber: '',
  refundPrice: '',
  orderNumber: ''
})
const rules = reactive({
  refundPrice: [{ required: true, message: '请输入退款金额', trigger: 'blur' }]
})
const formRef = ref()

const init = (data) => {
  form.value = data
}

const validate = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        return resolve({
          ...form.value
        })
      } else {
        return reject()
      }
    })
  })
}

defineExpose({
  validate,
  init
})
</script>

<style lang="scss" scoped>
.title {
  font-size: 18px;
  font-weight: bold;
  color: #000;
  margin-bottom: 20px;
}
</style>
