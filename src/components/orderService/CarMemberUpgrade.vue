<template>
  <el-card class="wd-relative" :style="{ width: props.width }">
    <el-form
      ref="formRef"
      hide-required-asterisk
      :rules="rules"
      :model="form"
      label-width="130px"
    >
      <div class="wd-flex">
        <ShopUser
          disabled
          v-model:shop-id="form.shopId"
          v-model:shop-name="form.shopName"
        />
      </div>
      <el-form-item label="付费类型：" prop="levelId">
        <el-select
          @change="memberUpgrade"
          v-model="form.levelId"
          placeholder="请选择"
          style="width: 350px"
        >
          <el-option
            v-for="(value, index) in memberTypeList"
            :key="index"
            :label="value.label"
            :value="value.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="原店铺级别：">
        <el-input
          :value="calculateResult.serviceName"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="原有效起期：">
        <el-input
          :value="
            calculateResult.memberBeginTime
              ? dayjs(calculateResult.memberBeginTime).format(
                  'YYYY-MM-DD HH:mm:ss'
                )
              : ''
          "
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="原有效止期：">
        <el-input
          :value="
            calculateResult.memberEndTime
              ? dayjs(calculateResult.memberEndTime).format(
                  'YYYY-MM-DD HH:mm:ss'
                )
              : ''
          "
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="原订单状态：">
        <el-input
          :value="convertPaymentStatus[calculateResult.status]"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="原订单价格：">
        <el-input
          :value="calculateResult.origPrice"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="升级月数：">
        <el-input
          :value="calculateResult.month"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="升级差价：">
        <el-input
          v-model="calculateResult.diffPrice"
          disabled
          clearable
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="价格折扣：" prop="discount">
        <el-input
          @input="handleDiscountChange"
          placeholder="请输入"
          v-model="form.discount"
          clearable
          style="width: 350px"
        />
      </el-form-item>

      <el-form-item label="支付金额：" prop="realPrice">
        <el-input
          placeholder="请输入"
          @input="handlePriceChange"
          v-model="form.realPrice"
          clearable
          style="width: 350px"
        />
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { reactive, ref, defineProps } from 'vue'
import { memberPayType } from './config'
import ShopUser from '@/components/orderService/ShopUser.vue'
import { FormInstance, FormRules } from 'element-plus'
import { MemberType } from './config'
import memberLevel from '@/api/member'
import { convertKeyValueEnum } from '@/utils/convert'
import { paymentStatus } from '@/utils/enum'
import dayjs from 'dayjs'
import { IserviceCfg, getOrderUpgradeDetail } from '@/api/order'

interface IForm {
  shopId: string
  shopName: string
  levelId: string | number
  realPrice: number | string
  discount: string
}

const emit = defineEmits<{
  (e: 'setPrice', data: { price: number | string; index: number }): void
}>()
const props = withDefaults(
  defineProps<{
    memberType: MemberType
    width?: string | number
    showDelete?: boolean
  }>(),
  {
    memberType: MemberType.NewCarMember,
    width: '730px',
    showDelete: true
  }
)
const route = useRoute()
const convertPaymentStatus: any = convertKeyValueEnum(paymentStatus)
const {
  memberType = 1,
  level,
  shopId = 300638,
  shopName,
  orderNumber = 'TESTSO2307115F1C8'
} = route.query
const memberTypeList = computed(() => {
  return memberPayType.filter((item) => {
    return item.id > Number(level)
  })
})
const formRef = ref<FormInstance>()
const handleDiscountChange = (val: any) => {
  if (calculateResult.value.totalCost) {
    form.realPrice = Math.round((calculateResult.value.totalCost * val) / 10)
    emit('setPrice', { price: form.realPrice, index: -1 })
  }
}
const handlePriceChange = (val: any) => {
  const discount = (
    (Number(val) / Number(calculateResult.value.diffPrice)) *
    10
  ).toFixed(2)
  form.discount =
    !isNaN(Number(discount)) && isFinite(Number(discount))
      ? discount === '0.00'
        ? '0'
        : discount
      : ''
  emit('setPrice', { price: val, index: -1 })
}
const form = reactive<IForm>({
  shopId: (shopId as string) || '',
  shopName: shopName as string,
  levelId: '',
  realPrice: '',
  discount: '10'
})
const rules = reactive<FormRules>({
  levelId: [{ required: true, message: '请选择会员级别', trigger: 'change' }],
  discount: [{ required: true, message: '请输入价格折扣', trigger: 'blur' }],
  realPrice: [{ required: true, message: '请输入支付金额', trigger: 'blur' }]
})

const calculateResult = ref<Record<string, any>>({})
const memberUpgrade = () => {
  getOrderUpgradeDetail({
    orderNumber: orderNumber,
    level: form.levelId
  })
    .then((response) => {
      if (response.data.code === 0) {
        calculateResult.value = response.data.data
        calculateResult.value.totalCost = response.data.data.totalCost
        handleDiscountChange(calculateResult.value.dicount)
      }
    })
    .catch((err) => {
      window.$message.error(err.message)
    })
}

const validate = () => {
  return new Promise<IserviceCfg>((resolve, reject) => {
    formRef.value!.validate((valid) => {
      if (valid) {
        return resolve({
          shopName: form.shopName,
          shopId: form.shopId,
          level: form.levelId as number,
          price: form.realPrice as string,
          orgPrice: calculateResult.value.diffPrice,
          discount: (Number(form.discount) / 10).toFixed(2),
          orgDiscount: form.discount,
          itemId: calculateResult.value.itemId || 0,
          serviceType: memberType as string,
          upgrade: 1,
          beginTime: calculateResult.value.beginTime
            ? dayjs(calculateResult.value.beginTime).format(
                'YYYY-MM-DD HH:mm:ss'
              )
            : '',
          endTime: calculateResult.value.endTime
            ? dayjs(calculateResult.value.endTime).format('YYYY-MM-DD HH:mm:ss')
            : ''
        })
      } else {
        return reject()
      }
    })
  })
}
defineExpose({
  validate
})
</script>

<style scoped></style>
