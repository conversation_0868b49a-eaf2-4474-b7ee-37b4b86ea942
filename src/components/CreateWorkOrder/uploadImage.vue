<template>
  <div class="upload-image">
    <div class="operate-box">
      <div class="upload-button" @click="selectImage">
        <el-icon class="mr2" size="16px"><IconUploadFilled /></el-icon>
        <span>上传图片</span>
      </div>
      <input
        ref="fileInput"
        type="file"
        name="file"
        multiple
        accept="image/*"
        @change="fileSelected($event)"
        style="display: none"
      />
    </div>
    <div class="input-box">
      <el-input
        ref="pasteUpload"
        type="textarea"
        :autosize="{ minRows: 2 }"
        placeholder="请上传图片，或在此处粘贴上传…"
        v-model="content"
        resize="none"
        maxlength="0"
      />
    </div>
    <div v-if="loading" class="uploading">
      <el-button type="primary" link :loading="loading">上传中...</el-button>
    </div>
  </div>
</template>

<script>
import { UploadFilled as IconUploadFilled } from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
export default {
  components: {
    IconUploadFilled
  },
  name: 'uploadImage',
  data() {
    return {
      content: '',
      loading: false
    }
  },
  mounted() {
    setTimeout(() => {
      this.executePaste()
    }, 500)
  },
  methods: {
    executePaste() {
      const paste = this.$refs.pasteUpload.$el.children[0]
      paste.addEventListener('paste', this.eventPaste)
    },
    eventPaste(event) {
      const me = this
      const clipboardData =
        event.clipboardData || event.originEvent.clipboardData
      const items = clipboardData.items
      const types = clipboardData.types
      if (types && types.length) {
        if (types.includes('Files')) {
          const i = types.findIndex((_) => _ === 'Files')
          const item = items[i]
          if (item && item.kind === 'file' && item.type.match(/^image\//i)) {
            const blob = item.getAsFile()
            const reader = new FileReader()
            reader.readAsDataURL(blob)
            reader.onload = function (e) {
              const src = e.target.result
              me.disposeImg([src])
            }
          }
        } else if (types.includes('text/html')) {
          const i = types.findIndex((_) => _ === 'text/html')
          const item = items[i]
          if (item && item.kind === 'string') {
            item.getAsString((string) => {
              const imgReg = /<img.*?(?:>|\/>)/gi // 匹配图片中的img标签
              // const srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i // 匹配图片中的src
              const srcReg = /src=['"]?([^'"]*)['"]?/i // 匹配图片中的src
              const arr = string.match(imgReg) || [] // 筛选出所有的img
              const srcArr = []
              arr.forEach((_) => {
                const src = _.match(srcReg)
                srcArr.push(src[1]) // 获取图片地址
              })
              me.disposeImg(srcArr)
            })
          }
        }
      }
    },
    selectImage() {
      this.$refs.fileInput.click()
    },
    fileSelected(e) {
      let imgFile = e.target.files
      imgFile = Array.from(imgFile)
      this.loading = true
      const list = []
      imgFile.forEach((file) => {
        const p = new Promise((resolve) => {
          this.$oss
            .ossUploadImage({ file, quality: 0.8, imageType: 'forum' })
            .then((res) => {
              resolve(res.imgOrgUrl)
            })
        })
        list.push(p)
      })
      Promise.all(list).then((res) => {
        this.loading = false
        $emit(this, 'uploadSuccess', res)
      })
    },
    // 处理图片
    disposeImg(arr) {
      // if (arr.length) {
      //   return this.$emit('uploadSuccess', arr)
      // }
      this.loading = true
      const list = []
      arr.forEach((url) => {
        const p = new Promise((resolve) => {
          if (url.startsWith('data:image')) {
            const file = this.base64ToFile(url)
            this.$oss
              .ossUploadImage({ file, quality: 0.8, imageType: 'forum' })
              .then((res) => {
                resolve(res.imgOrgUrl)
              })
          } else {
            resolve(url)
          }
        })
        list.push(p)
      })
      Promise.all(list).then((res) => {
        this.loading = false
        $emit(this, 'uploadSuccess', res)
      })
    },
    // base64转file
    base64ToFile(dataurl) {
      let arr = dataurl.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let suffix = mime.split('/')[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], `${new Date().getTime()}.${suffix}`, {
        type: mime
      })
    }
  },
  emits: ['uploadSuccess']
}
</script>

<style lang="scss" scoped>
.upload-image {
  border: 1px #dedede solid;
  .operate-box {
    padding: 5px 10px;
    background-color: #f2f2f2;
    line-height: 16px;
    display: flex;
    align-items: center;
    .upload-button {
      background-color: #ffffff;
      color: #333333;
      font-size: 14px;
      font-weight: 300;
      padding: 5px 8px;
      border-radius: 5px;
      border: 1px #dedede solid;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
  }
  .input-box {
    :deep(.el-textarea__inner) {
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color))
        inset;
      resize: none;
      cursor: default;
      border-radius: 0;
    }
  }
  .uploading {
    background-color: #ffffff;
    text-align: center;
    border-top: 1px #dedede solid;
  }
}
</style>
