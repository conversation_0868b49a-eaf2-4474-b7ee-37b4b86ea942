<template>
  <div v-if="visibleStatus">
    <el-dialog
      v-model="visibleStatus"
      :title="title"
      width="800px"
      class="dialog-content"
      center
      append-to-body
      top="5vh"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="80px"
        style="margin: 10px 0"
      >
        <el-form-item label="应用">
          <el-radio-group v-model="form.appSource">
            <el-radio :label="1">摩托范</el-radio>
            <el-radio :label="2">电摩范</el-radio>
            <el-radio :label="3">手表范</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="反馈用户" required>
          <div
            v-if="source === 'feedback' && form.feedbackId && form.feedbackName"
          >
            <el-tag type="info"
              >{{ form.feedbackName }} {{ form.feedbackId }}</el-tag
            >
          </div>
          <div v-else>
            <el-radio-group
              v-model="form.feedbackSource"
              @change="changeSource"
              :disabled="source === 'punish'"
            >
              <el-radio :label="1">用户</el-radio>
              <el-radio :label="2">平台</el-radio>
            </el-radio-group>
            <div v-if="form.feedbackSource === 1">
              <div>
                <el-input
                  v-model="idSearch"
                  placeholder="请输入用户ID"
                  clearable
                  style="width: 200px"
                ></el-input>
                <el-button type="primary" @click="idInquire(0)">
                  查询并添加
                </el-button>
              </div>
              <div v-if="form.feedbackId && form.feedbackName">
                <el-tag type="info" closable @close="closeTag"
                  >{{ form.feedbackName }} {{ form.feedbackId }}</el-tag
                >
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="问题类型" required>
          <el-cascader
            :props="cascaderLabel"
            v-model="cascaderValue"
            :options="bizTypeList"
            @change="handleChange"
          ></el-cascader>
        </el-form-item>
        <el-form-item
          v-if="
            [4, 5, 16, 6].includes(form.bizType) && form.feedbackSource === 1
          "
          label="关联订单"
        >
          <el-autocomplete
            v-model="relateOrder"
            :fetch-suggestions="searchOrderNumbe"
            placeholder="请选择或输入订单号"
            @select="selectOrderNumbe"
            clearable
            style="width: 200px"
          ></el-autocomplete>
          <div v-if="form.relateOrder">
            <el-tag type="info" closable @close="closeRelateOrder">{{
              form.relateOrder
            }}</el-tag>
          </div>
        </el-form-item>
        <div class="feedback-title">
          <el-form-item label="反馈信息">
            <div class="emergency">
              <el-checkbox v-model="form.urgent">紧急问题</el-checkbox>
            </div>
          </el-form-item>
        </div>
        <div class="feedback">
          <el-form-item label="事件描述">
            <el-input
              :rows="4"
              v-model="form.workOrderDesc"
              type="textarea"
              maxlength="500"
              placeholder="请填写事件描述内容..."
            ></el-input>
          </el-form-item>
          <el-form-item label="用户诉求">
            <el-input
              :rows="4"
              v-model="form.userDemand"
              type="textarea"
              maxlength="500"
              placeholder="请填写用户诉求信息..."
            ></el-input>
          </el-form-item>
          <el-form-item label="举证材料" id="squire-content">
            <div style="width: 100%">
              <UploadImage ref="uploadImage" @uploadSuccess="uploadSuccess" />
              <div class="img-box" v-if="form.datas && form.datas.length">
                <div
                  v-for="(item, index) in form.datas"
                  :key="index"
                  class="img-item"
                >
                  <el-icon class="delete-image" @click="deleteImage(index)"
                    ><IconClose
                  /></el-icon>
                  <el-image
                    :initial-index="index"
                    :preview-src-list="form.datas"
                    hide-on-click-modal
                    :z-index="2500"
                    :preview-teleported="true"
                    :src="item"
                    fit="cover"
                    class="imgs"
                  />
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
        <el-form-item label="关联对象">
          <div>
            <el-radio-group
              v-model="relateType"
              @change="relateidSearch = ''"
              :disabled="source === 'punish'"
            >
              <el-radio
                v-for="(item, index) in relateTypeList"
                :label="item.key"
                :key="index"
                >{{ item.name }}</el-radio
              >
            </el-radio-group>
            <span v-if="source === 'punish'">
              <el-tag class="ml20" type="info">{{
                form.related[0].relateName
              }}</el-tag>
              <span class="ml10">{{ form.related[0].relateId }}</span>
            </span>
            <div v-else>
              <div v-if="dumbObjects[relateType]">
                <el-input
                  v-model="relateidSearch"
                  :placeholder="`请输入${dumbObjects[relateType][1]}`"
                  clearable
                  style="width: 200px"
                ></el-input>
                <el-button type="primary" @click="idInquire(relateType)">
                  查询并添加
                </el-button>
              </div>
              <div v-if="relateType === 4">
                <el-button type="primary" @click="addRelated"> 添加 </el-button>
              </div>
              <div v-if="form.related && form.related.length">
                <el-tag
                  v-for="(item, index) in form.related"
                  :key="index"
                  type="info"
                  closable
                  @close="closeRelateTag(index)"
                  class="mr5"
                >
                  {{ relateTypeEnum[item.relateType]
                  }}{{
                    item.relateName && item.relateId
                      ? ` | ${item.relateName} ${item.relateId}`
                      : ''
                  }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="!isEdit" label="责任人">
          <el-select
            v-model="form.dutyId"
            filterable
            remote
            clearable
            placeholder="请输入用户名称"
            :remote-method="querySearchDuty"
            :loading="dutyLoading"
            @change="handleSelectDuty"
          >
            <el-option
              v-for="item in dutyPersonList"
              :key="item.uid"
              :label="item.name"
              :value="item.uid"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="footer-action-bar">
        <el-button
          class="confirm-button"
          type="success"
          :loading="flag"
          @click="confirm(true)"
          >确 认</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Close as IconClose } from '@element-plus/icons-vue'
import { $emit } from '../../utils/gogocodeTransfer'
import {
  createWorkOrder,
  getbizTypeList,
  getByDutyName,
  updateWorkOrder
} from '@/api/system'
import { merchantDetail, GetDrivingSchoolList } from '@/api/garage'
import { getSelfOrderList } from '@/api/shop'
import {
  getUserRelatedOrder,
  getRentalUserRelatedOrder
} from '@/api/commodityPlatform'
import { getBrandDetail } from '@/api/brand'
import { mapGetters } from 'vuex'
import { changeDate, format } from '@/utils/configData/format'
import { getSimpleInfoByUid } from '@/api/user'
import UploadImage from './uploadImage.vue'
import { associatedObject } from '@/utils/enum/index.js'
export default {
  components: {
    UploadImage,
    IconClose
  },
  name: 'CreateWorkOrder',
  props: {
    title: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    source: {
      type: String,
      default: 'workOrder'
    }
  },
  computed: {
    ...mapGetters(['name', 'uid'])
  },
  data() {
    // feedbackType: 1-技术;2-建议;3-其他;4-审核不通过;5-举报处理;6-退款;7-投诉;8-诈骗;9-催打款;10-参数错误;11-售车;
    // bizType: 1-账号;2-内容;3-摩友圈;4-租车;5-二手车;6-驾校;7-题库;8-骑行;9-车库;10-经销商;11-广告;12-金融;13-推送;14-其他业务;15-oos;16-电商
    return {
      idSearch: '',
      relateType: '',
      form: {
        appSource: 1,
        feedbackSource: 1,
        feedbackId: '', //反馈用户ID
        feedbackName: '', //反馈用户名
        bizType: '', //业务类型
        feedbackType: '', //反馈类型
        workOrderDesc: '', //问题描述
        relateOrder: '', // 关联订单
        userDemand: '', //用户诉求
        datas: [], //举证材料
        related: [], //关联对象
        relateType: '',
        relateId: '',
        relateName: '',
        dutyId: '', //责任人ID
        dutyName: '', //责任人
        urgent: false //是否紧急
      },
      bizTypeList: [], //业务类型
      relateTypeList: [
        { key: 1, name: '用户' },
        { key: 2, name: '商家' },
        { key: 3, name: '厂家' },
        { key: 4, name: '平台' }
      ],
      relateTypeEnum: associatedObject,
      dumbObjects: {
        0: ['用户名', '用户ID'],
        1: ['用户名', '用户ID'],
        2: ['经销商名称', '经销商ID'],
        3: ['厂家名', '厂家ID']
      },
      relateidSearch: '',
      visibleStatus: false,
      cascaderLabel: {
        value: 'code',
        label: 'name',
        children: 'feedbackTypes'
      },
      cascaderValue: [],
      orderList: [],
      relateOrder: '',
      flag: false,
      dutyPersonList: [],
      dutyLoading: false
    }
  },
  methods: {
    // 获取责任人
    querySearchDuty(query) {
      const requestParams = {
        name: query
      }
      this.dutyLoading = true
      getByDutyName(requestParams)
        .then((response) => {
          const data = response.data
          if (data.code === 0) {
            this.dutyPersonList = data.data || []
          }
        })
        .finally(() => {
          this.dutyLoading = false
        })
    },
    handleSelectDuty(dutyId) {
      if (dutyId) {
        const item = this.dutyPersonList.find((_) => _.uid === dutyId)
        this.form.dutyName = item.name
      } else {
        this.form.dutyId = ''
        this.form.dutyName = ''
      }
    },
    init(data) {
      this.visibleStatus = true
      this.bizTypeList = []
      this.brandList = []
      this.cascaderValue = []
      this.idSearch = ''
      this.relateType = ''
      this.form = {
        appSource: 1,
        feedbackSource: 1,
        feedbackId: '', //反馈用户ID
        feedbackName: '', //反馈用户名
        bizType: '', //业务类型
        feedbackType: '', //反馈类型
        workOrderDesc: '', //问题描述
        relateOrder: '', // 关联订单
        userDemand: '', //用户诉求
        datas: [], //举证材料
        related: [], //关联对象
        relateType: '',
        relateId: '',
        relateName: '',
        dutyId: '', //责任人ID
        dutyName: '', //责任人
        urgent: false, //是否紧急
        feedBackToWorkOrderId: ''
      }
      this.getbizTypeList()
      if (data) {
        Object.keys(this.form).forEach((key) => {
          if (key === 'urgent') {
            this.form[key] = data[key] || false
          } else if (['datas', 'related'].includes(key)) {
            this.form[key] = data[key] || []
          } else if (key === 'appSource') {
            // Only set appSource from data if explicitly provided, otherwise keep default (1 - 摩托范)
            this.form[key] = data[key] || 1
          } else {
            this.form[key] = data[key] || ''
          }
        })
        if (data.related && data.related.length) {
          this.relateType = data.related[0].relateType
        } else {
          if (data.relateType) {
            this.relateType = data.relateType || ''
            this.form.related = [
              {
                relateBlocked: data.relateBlocked || '',
                relateCanceled: data.relateCanceled || '',
                relateId: data.relateId || '',
                relateName: data.relateName || '',
                relateType: data.relateType || '',
                relateTypeName: data.relateTypeName || ''
              }
            ]
          }
        }
        if (this.isEdit) {
          this.form.id = data.id || ''
        }
        this.cascaderValue = [data.bizType, data.feedbackType || '']
        this.getOrderList()
      }
    },
    changeSource() {
      this.closeRelateOrder()
      this.closeTag()
    },
    addRelated() {
      const index = this.form.related.findIndex((_) => _.relateType === 4)
      if (index === -1) {
        this.form.related.push({
          relateType: 4,
          relateId: '',
          relateName: ''
        })
      }
    },
    // 问题类型
    handleChange(value) {
      console.log('value :>> ', value)
      this.form.bizType = value[0] //业务类型
      this.form.feedbackType = value[1] //反馈类型
      this.form.relateOrder = ''
      this.relateOrder = ''
      this.getOrderList()
    },
    closeRelateOrder() {
      this.form.relateOrder = ''
      this.relateOrder = ''
    },
    // 获取问题类型
    getbizTypeList() {
      getbizTypeList()
        .then((response) => {
          const data = response.data
          if (data.code === 0) {
            data.data.map((item) => {
              item.name = item.bizTypeName
              item.code = item.bizType
            })
            this.bizTypeList = data.data || []
          }
        })
        .catch((err) => console.log(err))
    },
    closeTag() {
      this.form.feedbackId = ''
      this.form.feedbackName = ''
      this.idSearch = ''
    },
    closeRelateTag(i) {
      this.form.related.splice(i, 1)
    },
    // 确认位置
    confirm() {
      // const me = this
      if (!this.form.feedbackName && this.form.feedbackSource === 1) {
        return this.$message.error('请选择反馈用户')
      }
      if (!this.form.bizType) {
        return this.$message.error('请选择问题类型')
      }
      if (this.form.dutyId && this.form.dutyName) {
        if (!this.form.workOrderDesc) {
          return this.$message.error('请填写事件描述')
        }
      }
      if (!this.form.dutyName.trim()) {
        this.form.dutyId = ''
        this.form.dutyName = ''
      }
      this.requireData()
    },
    requireData() {
      const me = this
      const related = []
      let relateObj = {
        relateType: '',
        relateId: '',
        relateName: ''
      }
      if (me.form.related && me.form.related.length) {
        me.form.related.forEach((_) => {
          related.push(JSON.stringify(_))
        })
        relateObj = {
          relateType: me.form.related[0].relateType,
          relateId: me.form.related[0].relateId,
          relateName: me.form.related[0].relateName
        }
      }
      const postData = me.isEdit ? updateWorkOrder : createWorkOrder
      if (this.flag) return
      this.flag = true
      postData({
        ...me.form,
        ...relateObj,
        related,
        optId: this.uid, //操作者ID
        optName: this.name //操作者用户名
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('操作成功')
            $emit(me, 'success')
            me.visibleStatus = false
          } else {
            me.$message.error('操作失败')
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          this.flag = false
        })
    },
    getOrderList() {
      if (!this.form.feedbackId || ![4, 5, 16, 6].includes(this.form.bizType))
        return
      this.orderList = []
      const requestMethod = {
        4: getRentalUserRelatedOrder, // 租车
        5: getUserRelatedOrder, // 二手车
        16: getSelfOrderList,
        6: GetDrivingSchoolList // 驾校报名
      }
      const timeBegin =
        format(
          changeDate({
            date: new Date(),
            day: -90
          }),
          'YYYY-MM-DD'
        ) + ' 00:00:00'
      const timeEnd = format(new Date(), 'YYYY-MM-DD') + ' 23:59:59'
      const parameter = {
        4: {
          userId: this.form.feedbackId
        },
        5: {
          userId: this.form.feedbackId
        },
        16: {
          uid: this.form.feedbackId,
          beginTime: timeBegin,
          endTime: timeEnd
        },
        6: {
          uid: this.form.feedbackId,
          beginTime: timeBegin,
          endTime: timeEnd
        }
      }
      requestMethod[this.form.bizType]({
        ...parameter[this.form.bizType],
        page: 1,
        limit: 999
      }).then((res) => {
        if (res.data.code === 0) {
          if (this.form.bizType === 16) {
            res.data.data.listData.map((_) => {
              this.orderList.push({ value: _.orderNum || _ })
            })
          } else if (this.form.bizType === 6) {
            res.data.data.list.map((_) => {
              this.orderList.push({ value: _.id.toString() })
            })
          } else {
            res.data.data.map((_) => {
              this.orderList.push({ value: _ })
            })
          }
        }
      })
    },
    searchOrderNumbe(value, cb) {
      const list = this.orderList
      const results = value
        ? list.filter(
            (item) =>
              item.value.toLowerCase().indexOf(value.toLowerCase()) === 0
          )
        : list
      cb(results)
    },
    selectOrderNumbe(item) {
      this.form.relateOrder = item.value
    },
    idInquire(type) {
      if (!type) {
        this.closeRelateOrder()
      } else {
        const index = this.form.related.findIndex((_) => _.relateType === type)
        if (index > -1) {
          const arr = []
          this.form.related.map((_) => {
            if (_.relateType === type) {
              arr.push(_.relateId)
            }
          })
          if (arr.includes(Number(this.relateidSearch))) {
            return this.$message.error(`${this.relateTypeEnum[type]}已添加`)
          }
        }
      }
      const requestMethod = {
        0: getSimpleInfoByUid, // 用户
        1: getSimpleInfoByUid, // 用户
        2: merchantDetail, // 商家
        3: getBrandDetail // 厂家
      }
      const parameter = {
        0: { uid: this.idSearch, appSource: this.form.appSource },
        1: { uid: this.relateidSearch, appSource: this.form.appSource },
        2: { shopId: this.relateidSearch },
        3: this.relateidSearch
      }
      const fields = {
        1: ['uid', 'userName'],
        2: ['shopId', 'shopName'],
        3: ['brandId', 'brandName']
      }
      requestMethod[type](parameter[type]).then((res) => {
        if (res.data.code === 0) {
          const data = res.data.data
          if (!data) {
            return this.$message.error(`${this.dumbObjects[type][1]}不存在`)
          }
          if (type === 0) {
            this.form.feedbackId = data.uid || ''
            this.form.feedbackName = data.userName || ''
            this.getOrderList()
          } else {
            const relateId = data[fields[type][0]] || ''
            const relateName = data[fields[type][1]] || ''
            this.form.related.push({
              relateType: type,
              relateId,
              relateName
            })
          }
        }
      })
    },
    uploadSuccess(arr) {
      this.form.datas = [...this.form.datas, ...arr]
    },
    deleteImage(i) {
      this.form.datas.splice(i, 1)
    }
  },
  emits: ['success']
}
</script>

<style lang="scss" scoped>
.feedback-title {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
  .emergency {
    display: flex;
    justify-content: end;
  }
}
.feedback {
  :deep(.el-form-item__label) {
    color: #8b8e94;
    font-size: 13px;
  }
  :deep(.el-form-item) {
    margin-bottom: 10px;
  }
}
.footer-action-bar {
  text-align: center;
  .confirm-button {
    width: 25%;
  }
}
.img-box {
  display: flex;
  flex-wrap: wrap;
  margin-top: 5px;
  margin-right: -5px;
  .img-item {
    width: calc(25% - 5px);
    margin: 5px 5px 0 0;
    height: 100px;
    position: relative;
    .delete-image {
      position: absolute;
      right: 0;
      top: 0;
      z-index: 1;
      font-size: 20px;
      color: #000000;
      font-weight: bold;
    }
    .imgs {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
