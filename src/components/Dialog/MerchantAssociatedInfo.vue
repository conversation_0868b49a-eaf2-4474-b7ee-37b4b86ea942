<template>
  <el-dialog v-model="dialogVisible" title="商家关联信息" width="65%">
    <div class="merchant-associated-info">
      <div class="title">
        <div class="shop-item">
          经销商：{{ shopData.shopName }}&#x3000;&#x3000;ID：{{
            shopData.shopId
          }}
        </div>
        <el-button v-if="props.isEdit" type="primary" link @click="openAdd"
          >新增</el-button
        >
      </div>
      <el-table :data="tableData" border>
        <el-table-column prop="viewLoc" label="看车地点" align="center" />
        <el-table-column prop="payUnit" label="收款单位" align="center" />
        <el-table-column
          v-if="props.isEdit"
          prop="reportId"
          label="关联举报ID"
          align="center"
        />
        <el-table-column prop="oper" label="操作人" align="center" />
        <el-table-column prop="updateTime" label="操作时间" align="center">
          <template #default="{ row }">
            {{ $filters.timeFullS(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="props.isEdit"
          label="操作"
          align="center"
          width="120"
        >
          <template #default="{ row }">
            <el-button type="primary" link @click="openDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog v-model="innerVisible" width="600" title="新增" append-to-body>
      <div class="mb15">
        经销商：{{ shopData.shopName }}&#x3000;&#x3000;ID：{{ shopData.shopId }}
      </div>
      <el-form :model="formData" label-width="auto">
        <el-form-item label="看车地点">
          <el-input v-model="formData.viewLoc" maxlength="99" show-word-limit />
        </el-form-item>
        <el-form-item label="收款单位">
          <el-input v-model="formData.payUnit" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="关联举报ID">
          <el-input v-model="formData.reportId" />
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button @click="innerVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAdd"> 确定 </el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import {
  queryMerchantReportRel,
  saveMerchantReportRel,
  deleteMerchantReportRel
} from '@/api/usedCarPublish'

const { proxy } = getCurrentInstance()

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  }
})

const dialogVisible = ref(false)
const innerVisible = ref(false)
const shopData = reactive({
  shopName: '',
  shopId: ''
})
const tableData = ref([])
const formData = reactive({
  viewLoc: '',
  payUnit: '',
  reportId: ''
})

const init = (data) => {
  shopData.shopId = data.shopId || ''
  shopData.shopName = data.shopName || ''
  if (shopData.shopId) {
    getList()
  }
  dialogVisible.value = true
}

const getList = () => {
  queryMerchantReportRel({
    shopId: shopData.shopId
  }).then((res) => {
    if (res.data.code === 0) {
      tableData.value = res.data.data || []
    }
  })
}

const openAdd = () => {
  formData.viewLoc = ''
  formData.payUnit = ''
  formData.reportId = ''
  innerVisible.value = true
}

const confirmAdd = () => {
  if (!formData.viewLoc && !formData.payUnit) {
    return proxy.$message.error('请填写看车地点或者收款单位')
  }
  if (!formData.reportId) {
    return proxy.$message.error('请填写举报ID')
  }
  saveMerchantReportRel({
    ...formData,
    shopId: shopData.shopId
  }).then((res) => {
    if (res.data.code === 0) {
      innerVisible.value = false
      getList()
      proxy.$message.success('操作成功')
    } else {
      proxy.$message.error(res.data.msg || '操作失败')
    }
  })
}

const openDelete = (data) => {
  proxy
    .$confirm('是否确认删除？', '', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      center: true
    })
    .then(() => {
      deleteMerchantReportRel({ id: data.id }).then((res) => {
        if (res.data.code === 0) {
          getList()
          proxy.$message.success('操作成功')
        } else {
          proxy.$message.error(res.data.msg || '操作失败')
        }
      })
    })
    .catch(() => {})
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.merchant-associated-info {
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
  }
  .shop-item {
    font-size: 16px;
    font-weight: normal;
  }
}
</style>
