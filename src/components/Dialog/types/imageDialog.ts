import { directions } from '../config'
export interface IGroup {
  id: string
  modelHeight: string
  imgList: IImage[]
  isExpand: false
}

export interface IImage {
  imgId: string
  checked: boolean
  imgOrgUrl: string
  imgUrl: string
  name: string
  carId: string
  imgCategory: EnumTabTyps
  id: string
  groupId: string
  shopId?: string
  shopName?: string
  provinceName?: string
  cityName?: string
  displayArea?: string
}
export interface IColorImage extends IImage {
  color: number | ''
  colorName: string
  tag?: (typeof directions)[number]
}
export interface IColorGroup extends IGroup {
  color: number
  colorName: string
  image?: string
  imgList: IColorImage[]
}
export enum EnumTabTyps {
  /** 外观tabId */
  APPEARANCE = '1',
  /** 细节tabId */
  DETAIL = '2',
  /** 骑姿tabId */
  RIDE = '18'
}
