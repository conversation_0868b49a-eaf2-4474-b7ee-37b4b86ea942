<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="二维码预览区域"
      center
      append-to-body
    >
      <div class="qr-content">
        <p style="text-align: center">
          <el-button type="danger" @click="handleClose()">关闭预览</el-button>
        </p>
        <div v-if="url" id="qrCode" ref="qrCodeDiv" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2-fix'
export default {
  name: 'ChooseQrCode',
  props: {},
  data() {
    return {
      url: '',
      dialogVisible: false
    }
  },
  methods: {
    init(url) {
      this.dialogVisible = true
      this.url = url.replace('wap.corp.mddmoto.com', 'wap.58moto.com')
      this.$nextTick(function () {
        this.bindQRCode()
      })
    },
    bindQRCode() {
      new QRCode(this.$refs.qrCodeDiv, {
        text: this.url,
        width: 200,
        height: 200,
        colorDark: '#333333', // 二维码颜色
        colorLight: '#ffffff', // 二维码背景色
        correctLevel: QRCode.CorrectLevel.L // 容错率，L/M/H
      })
    },
    // 关闭
    handleClose() {
      this.url = ''
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss">
.qr-content {
  img {
    margin: 0 auto;
  }
}
</style>
