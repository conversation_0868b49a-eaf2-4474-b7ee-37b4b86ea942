<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :title="title"
      center
      class="choose-show-image"
      append-to-body
    >
      <div :data-key="dataTime" class="choose-show-img-content">
        <img
          :src="imgUrl"
          :data-key="dataTime"
          class="choose-show-img-url"
          alt
        />
      </div>
      <div class="img-foot-fun noselect">
        <img
          src="../../assets/image/<EMAIL>"
          class="icon"
          alt
          @click="setRotate('left')"
        />
        <img
          src="../../assets/image/<EMAIL>"
          class="icon"
          alt
          @click="setRotate('right')"
        />
        <el-icon @click="setZoom('in')" class="size"><IconZoomIn /></el-icon>
        <el-icon @click="setZoom('out')" class="size"><IconZoomOut /></el-icon>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  ZoomIn as IconZoomIn,
  ZoomOut as IconZoomOut
} from '@element-plus/icons-vue'
export default {
  components: {
    IconZoomIn,
    IconZoomOut
  },
  name: 'ChooseShowImage',
  data() {
    return {
      dialogVisible: false,
      dataTime: null,
      title: '大图展示区域',
      imgUrl: '',
      angle: 0,
      size: 1,
      imgUrlList: [],
      imgContentList: []
    }
  },
  watch: {
    isWideImg(value) {
      if (value) {
        this.setPading()
      }
    }
  },
  methods: {
    init(link, title) {
      const me = this
      me.dataTime = Math.floor(new Date())
      console.log(me.dataTime)
      me.title = title || '大图展示区域'
      me.imgUrl = link
      me.dialogVisible = true
      setTimeout(function () {
        me.setClearPadding()
        me.setPading()
      }, 150)
    },
    setClearPadding() {
      const imgUrl = document.querySelectorAll(`.choose-show-img-url`)
      const imgContent = document.querySelectorAll(`.choose-show-img-content`)
      this.imgUrlList = Array.from(imgUrl)
      this.imgContentList = Array.from(imgContent)
      this.imgUrlList.map(function (value) {
        value.style.transform = `rotate(0deg)`
      })
      this.imgContentList.map(function (value) {
        value.style.paddingTop = 0
      })
    },
    setPading() {
      const me = this
      setTimeout(function () {
        const content = me.imgContentList.filter(
          (_) => parseInt(_.dataset.key) === me.dataTime
        )
        const url = me.imgUrlList.filter(
          (_) => parseInt(_.dataset.key) === me.dataTime
        )
        if (!url.length) {
          return
        }
        if (url[0].offsetWidth < url[0].offsetHeight) {
          return
        }
        content[0].style.paddingTop = `${
          (url[0].offsetWidth - url[0].offsetHeight) / 2
        }px`
      }, 100)
    },
    // 设置旋转
    setRotate(type) {
      this.angle = type === 'left' ? this.angle - 90 : this.angle + 90
      const url = this.imgUrlList.filter(
        (_) => parseInt(_.dataset.key) === this.dataTime
      )
      url[0].style.transform = `rotate(${this.angle}deg) scale(${this.size})`
    },
    // 设置缩放
    setZoom(type) {
      if (type === 'out' && this.size <= 0.5) return
      if (type === 'in' && this.size >= 1.5) return
      this.size = type === 'out' ? this.size - 0.2 : this.size + 0.2
      const url = this.imgUrlList.filter(
        (_) => parseInt(_.dataset.key) === this.dataTime
      )
      url[0].style.transform = `rotate(${this.angle}deg) scale(${this.size})`
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
      this.isWideImg = false
      this.angle = 0
      this.imgUrl = ''
    }
  }
}
</script>

<style lang="scss">
.choose-show-image {
  .el-dialog {
    margin-top: 5vh !important;
    width: 1000px;
    height: 900px;
  }
  .el-dialog__body {
    padding: 0 25px 30px;
  }
  .choose-show-img-content {
    margin-bottom: 20px;
    .choose-show-img-url {
      margin: 0 auto;
      display: block;
      transform: rotate(0deg);
      max-height: 700px;
      max-width: 700px;
      // animation: mymove 1s linear;
    }
    // @keyframes mymove {
    //   0% {
    //     opacity: 0;
    //   }
    //   100% {
    //     opacity: 1;
    //   }
    // }
  }
  .img-foot-fun {
    position: fixed;
    width: 300px;
    bottom: 40px;
    left: 0;
    right: 0;
    margin: 0 auto;
    background-color: #fff;
    text-align: center;
    .icon {
      width: 30px;
    }
    .icon:nth-of-type(1) {
      margin-right: 10px;
    }
    .icon:nth-of-type(2) {
      margin-left: 10px;
    }
    .size {
      font-size: 32px;
      margin-left: 20px;
      position: relative;
      bottom: 2px;
    }
  }
  .noselect {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}
</style>
