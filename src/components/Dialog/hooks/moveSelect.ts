interface IItem {
  index: number
  selected: boolean
  el: Element
}
interface IProps {
  item: string
  container: string
  update: (data: IItem[]) => void
}
let currentTarget: EventTarget | null = null
export class MoveSelectUtil {
  box!: HTMLElement
  selectArea: HTMLDivElement | null = null
  timer!: ReturnType<typeof setTimeout>
  item!: string
  container!: string
  items?: IItem[] = []
  selectStart = { x: 0, y: 0 }
  selectEnd = { x: 0, y: 0 }
  isSelecting = false
  isMove = false
  animationFrameId: number | null = null
  update!: (data: IItem[]) => void

  constructor({ container, item, update }: IProps) {
    this.update = update
    this.item = item
    this.container = container

    this.setContainer(this.container)
  }
  setContainer = (conatiner: string) => {
    this.box = document.querySelector(conatiner)!
    if (this.box) {
      this.collectItems()
    }
  }
  collectItems = () => {
    const items = this.box.querySelectorAll(this.item)
    this.items = Array.from(items).map((item: Element, index: number) => {
      return {
        index: index,
        selected: false,
        el: item
      }
    })
  }
  startSelect = (event: MouseEvent) => {
    event.preventDefault()
    currentTarget = event.currentTarget
    document.addEventListener('mouseup', this.onMouseUp)
    if (event.button === 0) {
      this.timer = setTimeout(() => {
        this.isSelecting = true
        this.selectStart = { x: event.clientX, y: event.clientY }
      }, 500)
    }
  }
  onMouseMove = (event: MouseEvent) => {
    if (currentTarget !== event.currentTarget) return
    if (this.isSelecting) {
      this.isMove = true
      this.selectEnd = { x: event.clientX, y: event.clientY }
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId)
      }

      this.animationFrameId = requestAnimationFrame(() => {
        this.updateSelection()
        this.update(this.items!)
        this.genSelectArea()
      })
    }
  }
  onMouseUp = () => {
    clearTimeout(this.timer)
    if (this.isSelecting) {
      this.isSelecting = false
      this.selectStart = { x: 0, y: 0 }
      this.selectEnd = { x: 0, y: 0 }
      this.isMove = false
      this.update(this.items!)
      this.removeArea()
    }
    document.removeEventListener('mouseup', this.onMouseUp)
  }
  updateSelection = () => {
    const { x: x1, y: y1 } = this.selectStart
    const { x: x2, y: y2 } = this.selectEnd

    this.items!.forEach((item: IItem) => {
      const itemRect = this.getItemRect(item)
      item.selected =
        itemRect.left < Math.max(x1, x2) &&
        itemRect.right > Math.min(x1, x2) &&
        itemRect.top < Math.max(y1, y2) &&
        itemRect.bottom > Math.min(y1, y2)
    })
  }
  getItemRect(item: IItem) {
    const rect = item.el.getBoundingClientRect()
    return {
      left: rect.left,
      top: rect.top,
      right: rect.right,
      bottom: rect.bottom
    }
  }
  genSelectArea() {
    if (!this.selectArea) {
      this.selectArea = document.createElement('div')
      this.selectArea.style.position = 'absolute'
      this.selectArea.style.zIndex = '1'
      this.selectArea.style.border = '1px dashed #000'
      this.selectArea.style.background = 'rgba(0, 0, 255, 0.1)'
      this.selectArea.style.pointerEvents = 'none'
      this.box.appendChild(this.selectArea)
    }

    const { left, top } = this.box.getBoundingClientRect()
    const { x: x1, y: y1 } = this.selectStart
    const { x: x2, y: y2 } = this.selectEnd
    this.selectArea.style.left = `${Math.min(x1, x2) - left}px`
    this.selectArea.style.top = `${Math.min(y1, y2) - top}px`
    this.selectArea.style.width = `${Math.abs(x2 - x1)}px`
    this.selectArea.style.height = `${Math.abs(y2 - y1)}px`
  }
  removeArea() {
    if (this.selectArea) {
      this.box.removeChild(this.selectArea)
      this.selectArea = null
    }
  }
}
