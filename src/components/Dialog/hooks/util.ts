import { EnumTabTyps, IColorImage } from '../types/imageDialog'
import { directions } from '../config'

/**
 * @description 根据directions生成对应的图片对象
 * @param {IColorImage[]} data
 * @returns {IColorImage[]}
 * @example generateImageListByDirections(data)
 */
export function generateImageListByDirections(
  data: IColorImage[],
  handle: (data: IColorImage) => IColorImage
): IColorImage[] {
  return directions.map((direction) => {
    const matchedObject = data.find((obj) => obj.tag === direction)
    return (
      matchedObject ||
      handle({
        carId: '',
        color: '',
        imgId: '',
        imgOrgUrl: '',
        name: '',
        groupId: '',
        colorName: '',
        imgCategory: EnumTabTyps.APPEARANCE,
        tag: direction,
        imgUrl: '',
        checked: false,
        id: ''
      })
    )
  })
}
