import { v4 as uuidv4 } from 'uuid'
import { EnumTabTyps, IColorGroup, IColorImage } from '../types/imageDialog'
import { ElMessage } from 'element-plus'
import { deleteImageV3 } from '@/api/garage'
import { directions } from '../config'

export const useImageTool = (groupList: Ref<IColorGroup[]>, rootStore: any) => {
  // 单组图片全选
  const imgList = computed(() => {
    return groupList.value.reduce((cur, next) => {
      return cur.concat(next.imgList.filter((item) => item.imgUrl))
    }, [] as IColorImage[])
  })
  const hasCheckImg = computed(() => imgList.value.some((item) => item.checked))
  const checkedImgList = computed(() => {
    return imgList.value.filter((item) => item.checked)
  })
  const checkImgAll = (index: number) => {
    const group = groupList.value[index]
    const hasNoCheck = group.imgList.some(
      (item) => item.imgUrl && !item.checked
    )
    group.imgList.forEach((_) => {
      if (_.imgUrl) {
        _.checked = hasNoCheck
      }
    })
  }
  // 所有组别全反选
  const checkAllGroupsImg = (type: 'all' | 'back') => {
    groupList.value.forEach((group) => {
      group.imgList.forEach((item) => {
        if (item.imgUrl) {
          item.checked = type === 'all' ? true : !item.checked
        }
      })
    })
  }
  // 选中单个图片
  const checkSingleImage = (value: boolean, index: number, inx: number) => {
    const item = groupList.value[index].imgList[inx]
    if (item.imgUrl) {
      item.checked = value
    } else {
      item.checked = false
    }
  }
  // 添加单图
  const addSingleImg = (url: string, index: number) => {
    const group = groupList.value[index]
    const img: IColorImage = {
      imgId: '',
      checked: true,
      imgOrgUrl: url,
      imgUrl: url,
      color: group.color,
      colorName: group.colorName,
      name: url + Date.parse(new Date().toString()),
      carId: rootStore.imgState.carId,
      imgCategory: rootStore.imgState.currentTabId,
      id: uuidv4(),
      groupId: group.id || ''
    }
    if (rootStore.imgState.currentTabId === EnumTabTyps.APPEARANCE) {
      const index = group.imgList.findIndex((img) => !img.imgUrl)
      if (index !== -1) {
        img.tag = group.imgList[index].tag
        group.imgList[index] = img
      }
    } else {
      groupList.value[index].imgList.push(img)
    }
  }
  const batchAddImg = (url: string, index: number) => {
    url
      .trim()
      .split(',')
      .filter((_: string) => _)
      .forEach((_: string) => {
        addSingleImg(_, index)
      })
  }
  const batchDelImg = (goodsId: number) => {
    const fileIds: string[] = []
    const imageIds: string[] = []

    checkedImgList.value.map((_) => {
      fileIds.push(_.imgOrgUrl)
      if (_.imgId) {
        imageIds.push(_.imgId)
      }
    })
    const source = JSON.parse(JSON.stringify(groupList.value))
    groupList.value.forEach((group) => {
      if (rootStore.imgState.currentTabId === EnumTabTyps.APPEARANCE) {
        group.imgList.forEach((item) => {
          if (item.checked) {
            item.imgUrl = ''
            item.imgOrgUrl = ''
            item.checked = false
          }
        })
      } else {
        group.imgList = group.imgList.filter((item) => {
          return !item.checked
        })
      }
    })
    ElMessage.success('删除成功')
    return
    // if (!goodsId) {
    //   ElMessage.success('删除成功')
    //   return
    // }
    // deleteImageV3({
    //   fileIds: fileIds.toString() || '',
    //   imageIds: imageIds.toString() || ''
    // })
    //   .then((response) => {
    //     if (response.data.code === 0) {
    //       ElMessage.success('删除成功')
    //     } else {
    //       groupList.value = source
    //       ElMessage.error(response.data.msg)
    //     }
    //   })
    //   .catch((err) => {
    //     groupList.value = source
    //     ElMessage.error(err.message)
    //   })
  }
  const moveImg = (toGroupIndex: number) => {
    const item = groupList.value[toGroupIndex]
    const emptyNum = item.imgList.filter(
      (img: IColorImage) => !img.imgUrl
    ).length
    let num = 0
    groupList.value.map((group: IColorGroup, index: number) => {
      group.imgList.map((img: IColorImage) => {
        if (
          img.checked &&
          ((num < emptyNum &&
            rootStore.imgState.currentTabId === EnumTabTyps.APPEARANCE) ||
            rootStore.imgState.currentTabId === EnumTabTyps.DETAIL) &&
          index !== toGroupIndex
        ) {
          addSingleImg(img.imgUrl, toGroupIndex)
          num++
          img.imgUrl = ''
          img.checked = false
        }
      })
    })
  }
  const syncImgInfo = (
    data: Pick<
      IColorImage,
      'shopId' | 'shopName' | 'provinceName' | 'cityName' | 'displayArea'
    >
  ) => {
    groupList.value.forEach((group) => {
      group.imgList.forEach((item) => {
        if (item.checked) {
          item.shopId = data.shopId
          item.shopName = data.shopName
          item.provinceName = data.provinceName
          item.cityName = data.cityName
          item.displayArea = data.displayArea
        }
      })
    })
  }
  const changeImgTag = (groupId: string) => {
    const group = groupList.value.find((_) => _.id === groupId)
    if (group) {
      directions.forEach((direction) => {})

      const imgList = group.imgList
      imgList.forEach((img, index) => {
        img.tag = directions[index]
      })
    }
  }

  return {
    imgList,
    hasCheckImg,
    syncImgInfo,
    changeImgTag,
    checkSingleImage,
    checkImgAll,
    moveImg,
    checkAllGroupsImg,
    addSingleImg,
    batchAddImg,
    batchDelImg
  }
}
