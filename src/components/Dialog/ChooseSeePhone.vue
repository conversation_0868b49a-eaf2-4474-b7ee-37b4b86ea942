<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="查看具体手机号码"
      center
      class="choose-dialog"
      width="600px"
      append-to-body
    >
      <el-form
        ref="phoneForm"
        :model="phoneData"
        label-width="100px"
        class="detail-entry"
      >
        <el-form-item label="手机号码" required>
          <el-input
            v-focus
            v-model="phoneData.phone"
            type="number"
            readonly
            maxlength="13"
          />
        </el-form-item>
      </el-form>
      <div class="dialog-content center footer">
        <el-button type="primary" @click="handleClose">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMobile } from '@/api/user'
export default {
  name: 'ChooseSeePhone',
  props: {},
  data() {
    return {
      dialogVisible: false,
      phoneData: {
        phone: '',
      },
      rules: {}, // 验证信息
    }
  },
  computed: {},
  methods: {
    init(data) {
      //  mobile, source
      const me = this
      me.dialogVisible = true
      getMobile(data)
        .then((response) => {
          if (response.data.code === 0) {
            me.phoneData.phone = response.data.data
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
        })
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
    },
  },
}
</script>
