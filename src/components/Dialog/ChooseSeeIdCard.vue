<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="查看具体身份证号码"
      center
      class="choose-dialog"
      width="600px"
      append-to-body
    >
      <el-form
        ref="phoneForm"
        :model="idData"
        label-width="100px"
        class="detail-entry"
      >
        <el-form-item label="身份证号码" required>
          <el-input v-focus v-model="idData.idCard" readonly />
        </el-form-item>
      </el-form>
      <div class="dialog-content center footer">
        <el-button type="primary" @click="handleClose">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetCommonDecryptIdcard } from '@/api/user'
export default {
  name: 'ChooseSeeIdCard',
  props: {},
  data() {
    return {
      dialogVisible: false,
      idData: {
        idCard: '',
      },
      rules: {}, // 验证信息
    }
  },
  computed: {},
  methods: {
    init(data) {
      //  mobile, source
      const me = this
      me.dialogVisible = true
      GetCommonDecryptIdcard(data)
        .then((response) => {
          if (response.data.code === 0) {
            me.idData.idCard = response.data.data
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
        })
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
    },
  },
}
</script>
