<template>
  <div class="wrap">
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      class="image-dialog"
      :close-on-click-modal="false"
      width="1720px"
      append-to-body
      destroy-on-close
    >
      <div v-loading="loading" :class="{ 'point-none': isFreeze }">
        <div class="mb5">
          <span class="mr5">图片管理--{{ goodsCarName }}</span>
          <el-button @click="save">保存</el-button>
          <el-button class="point-auto" @click="handleClose">取消</el-button>
        </div>
        <el-tabs
          v-model="currentTabId"
          type="card"
          class="tabs"
          @tab-click="handleClick"
        >
          <!-- name 切换对应 currentTabId 值  ${tab.id}-->
          <el-tab-pane
            v-for="(tab, index) in tabsControl"
            :key="index"
            :label="`${tab.name}(${dataManage(tab.id).imgList.length})`"
            :name="tab.id"
          >
            <SyncImageInfo
              v-model="shopIdByName"
              v-model:shopName="shopName"
              v-model:shopAliasName="shopAliasName"
              @sync-source="syncSource"
            />
            <div class="basic-action">
              <el-button v-if="isWg" @click="addColor">颜色管理</el-button>
              <el-button
                type="primary"
                v-if="currentTabId == 18"
                @click="addRideGroup(goodsId, carId)"
                >添加骑姿组别</el-button
              >
              <el-upload
                v-if="!isWg && currentTabId !== '2' && currentTabId !== '18'"
                :show-file-list="false"
                :http-request="httpRequest"
                :on-success="
                  (res, file, fileList) => onSuccess(res, file, fileList, 0)
                "
                :on-error="onError"
                name="upfile"
                style="display: inline-block"
                class="avatar-uploader"
                multiple
                action
              >
                <el-button class="button" type="primary" link>
                  <el-button>添加图片</el-button>
                </el-button>
              </el-upload>
              <el-upload
                v-if="currentTabId.toString() === '36'"
                :show-file-list="false"
                :http-request="httpRequest"
                :on-success="
                  (res, file, fileList) => onSuccess(res, file, fileList, 1)
                "
                :on-error="onError"
                name="upfile"
                style="display: inline-block"
                class="avatar-uploader"
                multiple
                action
              >
                <el-button class="button" type="primary" link>
                  <el-button>添加透明背景图片</el-button>
                </el-button>
              </el-upload>
              <el-button @click="deleteImgList">删除图片</el-button>
              <el-button @click="quantityMove">复制图片</el-button>
              <el-button v-if="isWg || currentTabId === '2'" @click="imgMove"
                >图片跨组别移动</el-button
              >
              <el-button
                v-if="!isWg && currentTabId !== '2' && currentTabId !== '18'"
                style="margin-left: 10px"
                size="small"
                type="success"
                @click="pictureBatchUpload"
                >图片池上传</el-button
              >
              <el-button
                style="margin-left: 10px"
                size="small"
                type="danger"
                plain
                @click="down()"
                >下载图片</el-button
              >
              <el-button v-if="carId" @click="seletedAll('all')"
                >全选</el-button
              >
              <el-button v-if="carId" @click="seletedAll('back')"
                >反选</el-button
              >
            </div>
            <template v-if="tab.id === '18'">
              <imgRideGroup
                :goodSaddleHigh="goodSaddleHigh"
                :carId="carId"
                :tabId="tab.id"
              />
            </template>
            <template v-if="tab.id === '1' || tab.id === '2'">
              <ImgGroup ref="imgGroup" :tabId="tab.id" />
            </template>
            <template v-if="!['1', '2', '18'].includes(tab.id)">
              <draggable
                v-if="tabs[tab.id] && tabs[tab.id].length"
                v-model="tabs[tab.id]"
                item-key="id"
              >
                <template #item="{ element, index }">
                  <span class="image-wrap">
                    <img
                      :src="element.imgUrl"
                      class="image"
                      alt
                      @click="seeBigImg(element.imgOrgUrl)"
                    />
                    <el-checkbox
                      v-if="element.checked"
                      :checked="element.checked"
                      class="check-box"
                      @change="checkImage(element)"
                    />
                    <el-checkbox
                      v-if="!element.checked"
                      :checked="element.checked"
                      class="check-box"
                      @change="checkImage(element)"
                    />
                    <el-button
                      v-if="!moreFunStatus"
                      class="delete"
                      type="danger"
                      circle
                      @click="deleteImg(element, index)"
                    >
                      <template #icon>
                        <IconDelete />
                      </template>
                    </el-button>
                    <span v-if="element.colorName" class="pic-tip"
                      >颜色：{{ element.colorName }}</span
                    >
                    <!-- <span style="width: 100px; display: block">{{ element.name }}</span> -->
                    <span
                      v-if="element.shopName && element.shopName !== 'null'"
                      class="pic-tip"
                      >商家：{{ element.shopName }}</span
                    >
                    <span v-if="element.provinceName" class="pic-tip"
                      >省份：{{ element.provinceName }}</span
                    >
                    <span v-if="element.cityName" class="pic-tip"
                      >城市：{{ element.cityName }}</span
                    >
                    <span
                      v-if="currentTabId.toString() === '36'"
                      style="color: #409eff"
                      >{{ goodsCarName
                      }}{{ getextensionName(element.imgUrl) }}</span
                    >
                  </span>
                </template>
              </draggable>
              <div v-else>暂无图片</div>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <el-dialog
      v-model="isShowMove"
      width="480px"
      append-to-body
      title="批量复制图片"
    >
      <!-- 批量复制图片 -->
      <div style="height: 400px">
        <el-select
          v-model="vehicleCarId"
          placeholder="请选择"
          @change="vehicleChange"
        >
          <el-option
            v-for="vehicle in vehicleTypeList"
            :key="vehicle.carId"
            :label="vehicle.goodsCarName"
            :value="vehicle.carId"
          ></el-option> </el-select
        ><br /><br />
        <el-select v-model="updataImg.tabsId" placeholder="请选择">
          <el-option
            v-for="item in tabsControl.filter((item) => item.id !== '18')"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
        <br /><br />
        <el-select
          v-if="updataImg.tabsId == 1 || updataImg.tabsId == 2"
          v-model="updataImg.groupId"
          placeholder="请选择"
        >
          <el-option
            v-for="item in groupList"
            :key="item.id"
            :label="item.colorName"
            :value="item.id"
          ></el-option>
        </el-select>
        <div style="margin-top: 260px" class="dialog-button">
          <el-button @click="cancalUpdataImg(updataImg)">取消</el-button>
          <el-button type="primary" @click="saveUpdataImg(updataImg)"
            >保存</el-button
          >
        </div>
      </div>
    </el-dialog>
    <choose-dialog ref="ChooseDialog" @sendData="chooseSendData" />
    <ChooseColorDialog ref="chooseColorDialog" @sendData="chooseNewSendData" />
    <PopuImgPool
      v-model="pictureBatchUploadVisible"
      @determineBatchUpload="determineBatchUpload"
    />
  </div>
</template>

<script>
import { storeToRefs } from 'pinia'
import { More as IconMore, Delete as IconDelete } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'

import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import draggable from 'vuedraggable'
import ChooseDialog from '@/components/Dialog/ChooseDialog.vue'
import ChooseColorDialog from '@/components/Dialog/chooseColorDialog.vue'
import PopuImgPool from '@/components/Dialog/components/popuImgPool.vue'
import { postLog } from '@/components/seeLog/SaveLog.js'
import {
  getImageList,
  updataCarImg,
  getAgeList,
  deleteImageV3,
  updateCopyCarImgs,
  getGroupList,
  postDownload,
  postCarImgInfo
} from '@/api/garage'
import { getImgPageSign } from '@/api/commonModule'
import { mapGetters } from 'vuex'
import ImgGroup from './components/imgGroup.vue'
import imgRideGroup from './components/imgRideGroup.vue'
import { useRideStore } from './stores/useRideStore'
import { useColorGroup } from './stores/useAppearanceStore'
import { useGroupStore } from './stores'
import SyncImageInfo from './components/syncImageInfo.vue'
import { SourceManage } from './config/dataSoureManage'

export default {
  data() {
    return {
      // 可传参
      // tabs 图片选项卡，[{ name: '店内图片', id: 1 }]
      tabsControl: [],
      // tabs 图片列表，{ 0: [{ imgOrgUrl, imgUrl, color, imgCategory }] }
      tabs: {},
      goodsId: 0,
      // 款型id，获取img列表时使用
      carId: '',
      // 是否需要传水印
      watermark: 0,
      // car_detail 水印类型
      systemType: '',
      // 已选中颜色，[{ "id": 55, name: '红色', image: '#111' }]
      labels: [],
      // 非传参
      currentTabId: '0',
      currentColorName: '',
      oldColorName: '',
      newColorName: '',
      // 更新移动的图片
      updataImg: {},
      // 上传中的图片
      uploadingImg: {},
      // 已上传图片数量
      count: 0,
      //360 透明图片数量
      tansCount: 0,
      // 蒙层显示状态
      dialogVisible: false,
      // 加载状态
      loading: false,
      // 是否显示批量迁移
      isShowMove: false,
      // 车辆下对应的款型列表
      vehicleTypeList: [],
      // 选择款形时选中的款型id
      vehicleCarId: '',
      // 阿里云上传图片类型
      imageType: '',
      shopIdByName: '',
      shopName: '',
      shopAliasName: '',
      pictureBatchUploadVisible: false,
      pictureBatchUploadUrl: '',
      imgType: false,
      // 身高
      modelHeight: '',
      // 坐高
      goodSaddleHigh: '',
      goodsCarName: '',
      groupParams: {}, //组别所需参数
      isModelType: false,
      // groupList: [],
      isRefresh: false, // 批量复制图片-是否允许刷新图片
      isDel: false,
      isFreeze: false // 电摩进来不能更改
    }
  },
  components: {
    SyncImageInfo,
    IconDelete,
    draggable,
    ChooseDialog,
    ChooseColorDialog,
    ImgGroup,
    imgRideGroup,
    PopuImgPool
  },
  name: 'AliImageDialog',
  props: {
    moreFunStatus: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(['uid']),

    //是外观
    isWg() {
      return this.currentTabId.toString() === '1'
    }
  },
  watch: {},
  setup() {
    const {
      updateRideGroupImgList,
      addRideGroup,
      batchDelImg,
      checkAllGroupsImg: checkAllGroupRideImg,
      syncImgInfo: syncRideImgInfo,
      getGroupList: getRideGroupList
    } = useRideStore()
    const { groupList: rideGroupList, rideImgList } = storeToRefs(
      useRideStore()
    )
    const {
      updateColorGroupImgList,
      batchDelImg: batchDelColorImg,
      getGroupList: getColorGroupList,
      checkAllGroupsImg: checkAllGroupsColorImg,
      syncImgInfo
    } = useColorGroup()
    const { groupList: appearanceGroupList } = storeToRefs(useColorGroup())
    const { updateState } = useGroupStore()
    const { appearanceImgList, deitalImgList, checkedGroupImgList } =
      storeToRefs(useGroupStore())
    return {
      getRideGroupList,
      syncRideImgInfo,
      syncImgInfo,
      checkedGroupImgList,
      getColorGroupList,
      updateState,
      updateColorGroupImgList,
      updateRideGroupImgList,
      addRideGroup,
      batchDelImg,
      batchDelColorImg,
      rideImgList,
      appearanceImgList,
      deitalImgList,
      checkAllGroupRideImg,
      checkAllGroupsColorImg,
      appearanceGroupList,
      rideGroupList
    }
  },
  methods: {
    dataManage(tabId) {
      const config = {
        1: new SourceManage(
          this.appearanceImgList,
          this.checkAllGroupsColorImg.bind(this),
          this.batchDelColorImg.bind(this, this.goodsId),
          this.syncImgInfo.bind(this)
        ),
        2: new SourceManage(
          this.deitalImgList,
          this.checkAllGroupsColorImg.bind(this),
          this.batchDelColorImg.bind(this, this.goodsId),
          this.syncImgInfo.bind(this)
        ),
        18: new SourceManage(
          this.rideImgList,
          this.checkAllGroupRideImg.bind(this),
          this.batchDelImg.bind(this),
          this.syncRideImgInfo.bind(this)
        )
      }
      if (config[tabId]) return config[tabId]
      return new SourceManage(
        this.tabs[tabId] || [],
        this.checkAllOtherImg.bind(this),
        this.deleteOtherImgList.bind(this),
        this.syncOtherSource.bind(this)
      )
    },
    checkAllOtherImg(type) {
      this.tabs[this.currentTabId].map(function (value) {
        value.checked = type === 'all' ? true : !value.checked
      })
    },
    init(params = {}) {
      this.isFreeze = params.isFreeze || false
      this.currentColorName = ''
      this.oldColorName = ''
      this.newColorName = ''
      this.tabsControl = params.tabsControl || this.tabsControl // tabs 图片选项卡，{ name: '店内图片', id: 1 }；
      this.tabs = {} // tabs 图片列表，{ 0: [{ imgOrgUrl, imgUrl, color, imgCategory }] }； // this.tabs
      this.currentTabId = this.tabsControl.length && this.tabsControl[0].id

      this.goodsId = params.goodsId // 车型id
      this.carId = params.carId || '' // 款型id，获取img列表时使用
      this.imageType = params.imageType || 'carport'
      this.watermark = params.watermark || 0 // 是否需要传水印
      this.systemType = params.systemType || '' // 值：car_detail 水印类型
      this.labels = params.labels || this.labels // 已选中颜色，{ "id": 55, name: '红色', image: '#111' }
      this.quality = params.quality || '' // 图片压缩比例
      this.uploadingImg = {}
      this.shopName = ''
      this.shopIdByName = ''
      this.shopAliasName = ''
      this.imgType = params.imgType || false
      this.modelHeight = params.modelHeight || ''
      this.goodSaddleHigh = params.goodSaddleHigh || ''
      this.isModelType = params.isModelType || false
      this.isRefresh = params.isRefresh || false
      this.goodsCarName = params.goodsCarName || ''
      this.updateState({
        goodsId: this.goodsId,
        carId: this.carId,
        currentTabId: this.currentTabId,
        labels: this.labels
      })
      // 特殊逻辑-批量图片复制功能
      if (!this.isRefresh) {
        this.dialogVisible = true
      }
      this.getColorGroupList(true)
      this.getRideGroupList(this.carId, true)
      this.gettabsControl()
      this.getVehicleList()
    },

    down() {
      const me = this
      let { fileIds } = this.dataManage(this.currentTabId)
      // 待下载图片url列表，多个逗号隔开
      fileIds = fileIds.join(',')
      if (fileIds) {
        postDownload({
          files: fileIds
        }).then((response) => {
          console.log(response)
          const blob = response.data
          const reader = new FileReader()
          reader.readAsDataURL(blob) // 转换为base64，可以直接放入a标签href
          reader.onload = function (e) {
            // 转换完成，创建一个a标签用于下载
            var a = document.createElement('a')
            a.download = `图片.zip`
            a.href = e.target.result // 修复firefox中无法触发click
            a.click()
          }
        })
      } else {
        me.$message.warning('请选择需要下载的图片！')
      }
    },

    async gettabsControl() {
      const needCall = this.tabsControl && this.tabsControl.length === 0
      if (needCall) {
        try {
          const response = await getImgPageSign()
          if (response.data.code === 0) {
            if (response.data.data && response.data.data.length) {
              // 重新排序
              const arr = response.data.data.map(function (v) {
                return { name: v.videoTab, id: v.id }
              })
              this.tabsControl = arr
              this.currentTabId = this.tabsControl[0].id
              this.updateState({
                currentTabId: this.currentTabId
              })
            }
          }
        } catch (error) {
          console.log(error)
        }
      }
      if (!Object.keys(this.tabs).length) {
        this.getImageList()
      }
    },
    // 获取车辆下款型列表
    getVehicleList() {
      const me = this
      getAgeList({
        goodsId: me.goodsId,
        page: 1,
        limit: 99
      }).then((response) => {
        if (response.data.code === 0) {
          const res = response.data.data
          me.vehicleTypeList = res.list
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    getImageList() {
      const me = this
      const keys = Object.keys(me.tabs)
      if (keys.length > 0) {
        return
      }
      // none 不需要请求接口
      if (me.carId === 'none') {
        return
      }
      me.loading = true
      getImageList({
        goodsId: me.goodsId,
        carId: me.carId
      })
        .then((response) => {
          if (response.data.code === 0) {
            if (!response.data.data || response.data.data.length === 0) {
              me.$message.info('暂无图片信息')
              return
            }
            const data = response.data.data || []

            let tabObj = {}
            Object.keys(data).map((value) => {
              // me.tabs[value] = [...response.data.data[value]]
              // 兼容获取图片数据的cardId默认为0的问题，cardId应为当前款型的cardId
              data[value].map((v) => {
                v.carId = parseInt(me.carId)
                if (v.shopName === 'null') {
                  // 处理脏数据
                  v.shopName = ''
                }
              })
              if (!['18', '1', '2'].includes(value)) {
                tabObj[value] = [...response.data.data[value]]
              }
            })

            me.tabs = tabObj
            this.beforeData = JSON.parse(JSON.stringify(data))
            this.updateRideGroupImgList(data['18'] || [])
            this.updateColorGroupImgList(
              (data['1'] || []).concat(data['2'] || [])
            )
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          console.log(err, 99)
          me.$message.error(err.message)
        })
        .finally(() => {
          me.loading = false
        })
    },

    // 保存图片
    save() {
      let groupIds = ''
      if (this.appearanceGroupList.length) {
        groupIds = this.appearanceGroupList.map((item) => item.id)
        this.tabs['1'] = this.appearanceImgList
        this.tabs['2'] = this.deitalImgList
      }
      if (this.rideImgList.length) {
        this.tabs['18'] = this.rideImgList
      }
      let imageArr = []
      Object.keys(this.tabs).map((name) => {
        imageArr = [...imageArr, ...this.tabs[name]]
      })
      postCarImgInfo({
        goodsImgs: JSON.stringify(imageArr),
        goodsId: this.goodsId,
        carId: this.carId,
        groupIds: groupIds ? groupIds.join(',') : ''
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.$message.success('保存成功')
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.message)
        })

      let beforeData = []
      Object.keys(this.beforeData).map((name) => {
        beforeData = [...beforeData, ...this.beforeData[name]]
      })
      postLog(
        77,
        this.carId,
        this.carId ? '编辑' : '新增',
        '',
        this.carId
          ? JSON.stringify({
              goodsImgs: JSON.stringify(beforeData)
            })
          : '{}',
        JSON.stringify({
          goodsImgs: JSON.stringify(imageArr)
        })
      )

      $emit(this, 'sendData', {
        tabs: this.tabs,
        labels: this.labels,
        modelHeight: this.modelHeight || '',
        // goodSaddleHigh: this.goodSaddleHigh || ''
        groupIds: groupIds ? groupIds.join(',') : ''
      })
      this.dialogVisible = false
    },

    chooseSendData(labels) {
      this.currentColorName = ''
      this.oldColorName = ''
      this.newtColor = ''
      this.labels = labels
    },
    checkImage(image) {
      image.checked = image.checked === undefined ? true : !image.checked
    },
    syncOtherSource(provinceName, cityName, displayArea) {
      this.tabs[this.currentTabId]?.forEach((childTab) => {
        if (childTab.checked) {
          childTab['shopId'] = this.shopIdByName
          childTab['shopName'] = this.shopAliasName || this.shopName
          childTab['provinceName'] = provinceName
          childTab['cityName'] = cityName
          childTab['displayArea'] = displayArea
        }
      })
    },
    // 写入来源
    syncSource({ provinceName, cityName, displayArea }) {
      const { hasChecked, syncShopInfo } = this.dataManage(this.currentTabId)
      if (!hasChecked) {
        return this.$message.error('请选择图片进行写入')
      }
      syncShopInfo({
        shopId: this.shopIdByName,
        shopName: this.shopAliasName || this.shopName,
        provinceName,
        cityName,
        displayArea
      })

      this.$message.success('图片来源写入成功')
    },
    determineBatchUpload(url) {
      this.pictureBatchUploadUrl = url
      if (!this.pictureBatchUploadUrl) {
        return this.$message.error('请输入url地址')
      }
      this.pictureBatchUploadVisible = false
      const pictureBatchUploadUrlArray = this.pictureBatchUploadUrl
        .trim()
        .split(',')
        .filter((_) => _)
      const batchUploadUrlArray = []
      pictureBatchUploadUrlArray.map((_) => {
        const obj = {
          carId: parseInt(this.carId),
          imgCategory: this.currentTabId,
          checked: true,
          imgOrgUrl: _,
          imgUrl: _,
          name: _ + Date.parse(new Date()),
          id: '',
          color: ''
        }
        batchUploadUrlArray.push(obj)
      })
      if (this.tabs[this.currentTabId]) {
        this.tabs[this.currentTabId] = [
          ...batchUploadUrlArray,
          ...this.tabs[this.currentTabId]
        ]
        return
      }
      this.tabs[this.currentTabId] = batchUploadUrlArray
    },

    // 只删除当前tab
    deleteImg(image, index) {
      const me = this
      this.tabs[this.currentTabId].splice(index, 1)
      this.$message.success('删除成功')
      me.isDel = true
      // me.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      //   center: true
      // })
      //   .then(() => {
      //     const fileIds = image.imgOrgUrl
      //     if (!this.goodsId) {
      //       this.tabs[this.currentTabId].splice(index, 1)
      //       this.$message.success('删除成功')
      //       return
      //     }
      //     this.deleteAction(fileIds, image.imgId, index)
      //   })
      //   .catch((err) => {
      //     me.$message({
      //       type: 'info',
      //       message: (err && err.message) || '已取消删除'
      //     })
      //   })
    },

    deleteAction(fileIds, imgIds, index) {
      deleteImageV3({
        fileIds: fileIds || '',
        imageIds: imgIds || ''
      })
        .then((response) => {
          if (response.data.code === 0) {
            if (index !== undefined) {
              this.tabs[this.currentTabId].splice(index, 1)
            } else {
              const currentList = this.tabs[this.currentTabId].filter(
                (_) => !_.checked
              )
              this.tabs[this.currentTabId] = currentList
            }
            this.$message.success('删除成功')
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.message)
        })
        .finally(() => {})
    },
    deleteOtherImgList(imgList, fields, imageIds) {
      const currentList = imgList.filter((_) => !_.checked)
      this.tabs[this.currentTabId] = currentList
      this.$message.success('删除成功')
      this.isDel = true
      return
      // if (!this.goodsId) {
      //   const currentList = imgList.filter((_) => !_.checked)
      //   this.tabs[this.currentTabId] = currentList
      //   this.$message.success('删除成功')
      //   return
      // }
      // this.deleteAction(fields.toString(), imageIds.toString())
    },
    // 只删除当前tab
    deleteImgList() {
      const { hasChecked, batchDelImg } = this.dataManage(this.currentTabId)
      if (!hasChecked) {
        return this.$message.error('请先选择要删除的图片')
      }

      const me = this
      // console.log(me.tabs)
      me.$confirm('此操作将批量删除选中文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          me.isDel = true
          batchDelImg()
        })
        .catch((err) => {
          me.$message({
            type: 'info',
            message: (err && err.message) || '已取消删除'
          })
        })
    },

    // 全选或反选
    seletedAll(type) {
      const { checkAllGroupsImg } = this.dataManage(this.currentTabId)
      checkAllGroupsImg(type)
    },
    getextensionName(url) {
      let extensionName = 'png'
      try {
        const str = url.split('!')[0]
        extensionName = `.${str.split('.').reverse()[0]}` // 文件扩展名
      } catch (e) {
        extensionName = 'png'
      }
      return extensionName
    },
    // 大图查看图片
    seeBigImg(link) {
      window.open(link)
    },
    /* @description [httpRequest 覆盖默认的上传行为，实现自定义上传]
     * @param    {object}   option [上传选项]
     * @return   {null}   [没有返回]
     */
    async httpRequest(option) {
      this.loading = true
      this.uploadingImg[option.file.name] = {}
      // 需要水印则传车库、经销商水印carport，不需要水印，则为nowater
      if (parseInt(this.currentTabId) === 36) {
        option.notCompress = true
        option.imageType = 'panoram' // currentTabId为36的时候是360标签，此时panoram是全景图水印
      } else {
        option.imageType =
          parseInt(this.watermark) === 1 ? 'carport' : 'nowater'
      }
      option.quality = this.quality

      // await this.$oss.ossUploadImage(option)
      this._uploads = this._uploads || []
      this._uploads.push({
        fn: this.$oss.ossUploadImage,
        option
      })
      this.$tools.debounce(this.call, 100)()
    },
    async call() {
      for (const a of this._uploads) {
        await a.fn(a.option)
      }
      this._uploads = []
    },
    onError(res, file) {
      this.loading = false
      res && this.$message.error(res)
      console.log(res)
    },
    onSuccess(res, file, fileList, imgType) {
      // 假性成功，直接return
      if (!res) return

      console.log(fileList)

      if (res.name) {
        const data = {
          name: file.name,
          imgOrgUrl: res.imgOrgUrl,
          imgUrl: res.imgUrl,
          color: '',
          imgCategory: this.currentTabId, // this.tabsControl[this.currentTabId].id
          checked: true,
          id: '',
          carId: parseInt(this.carId),
          imgType: imgType
        }
        // console.log(data)
        this.uploadingImg[file.name] = data
      } else {
        this.$notify.error({
          title: '上传错误',
          message: file.name,
          duration: 0
        })
      }
      if (imgType) {
        this.tansCount++
      } else {
        this.count++
      }
      const can =
        this.count === fileList.length ||
        (this.tansCount === fileList.length && imgType)
      if (can) {
        this.loading = false
        const result = []
        Object.keys(this.uploadingImg).map((_) => {
          const obj = this.uploadingImg[_]
          if (obj.imgUrl) result.push(obj)
        })
        this.tabs[this.currentTabId] = this.tabs[this.currentTabId] || []
        this.tabs[this.currentTabId].unshift(...result)
        this.uploadingImg = {} // 清除上传中图片
      }
    },
    pictureBatchUpload() {
      this.pictureBatchUploadUrl = ''
      this.pictureBatchUploadVisible = true
    },
    chooseColor() {
      this.$refs['ChooseDialog'].init({
        title: '选择颜色',
        goodsId: this.goodsId,
        carId: this.carId === 'none' ? '' : this.carId,
        labels: this.labels,
        canChoose: true
      })
    },
    // 新增颜色
    addColor() {
      this.$refs['chooseColorDialog'].init({
        title: '选择颜色',
        goodsId: this.goodsId,
        carId: this.carId === 'none' ? '' : this.carId,
        labels: this.labels,
        canChoose: true
      })
    },

    chooseNewSendData(labels) {
      console.log('labels=======', labels)
      this.currentColorName = ''
      this.oldColorName = ''
      this.newtColor = ''
      this.labels = [...labels]
      // this.$refs.imgGroup[0].setLabels(this.labels)
    },

    handleClick(tab, event) {
      this.count = 0
      this.tansCount = 0
      this.updateState({
        currentTabId: tab.props.name
      })
    },
    // 批量迁移功能
    quantityMove() {
      const me = this
      me.isShowMove = true
    },
    // 确认批量转换
    saveUpdataImg(data) {
      const me = this
      const { imageIds, fileIds } = this.dataManage(this.currentTabId)

      if (!fileIds || fileIds.length === 0) {
        me.$message.error('请先选择要复制的图片！')
        return
      }
      if (!me.vehicleCarId) {
        me.$message.error('请先选择图片要复制到那个款型！')
        return
      }
      if (!data.tabsId) {
        me.$message.error('请先选择图片要复制到那个菜单类型！')
        return
      }
      if (!me.rideGroupList.length && data.tabsId === '18') {
        me.$message.error('该款型骑姿没有对应组别，请创建后再试')
        return
      }
      if (!me.groupList.length && (data.tabsId == 1 || data.tabsId == 2)) {
        me.$message.error('该款型外观没有对应颜色组，请创建后再试')
        return
      }
      if (!data.groupId && data.tabsId === '18') {
        me.$message.error('请选择骑姿组别！')
        return
      }
      if (
        me.groupList.length &&
        !data.groupId &&
        (data.tabsId == 1 || data.tabsId == 2)
      ) {
        me.$message.error('请选择外观组别的颜色！')
        return
      }
      if (
        Number(this.carId) === me.vehicleCarId &&
        data.tabsId === me.currentTabId
      ) {
        return me.$message.error('禁止将图片复制到同款型！')
      }

      const carInfo = me.vehicleTypeList.filter(
        (_) => _.carId === me.vehicleCarId
      )

      this.$confirm(
        '请确认已经保存其它操作数据，复制功能和其它操作不能同时操作，否知操作数据会丢失?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }
      )
        .then(() => {
          updateCopyCarImgs({
            imgIds: imageIds.toString(),
            targetGoodsId: carInfo[0].goodsId,
            targetCarId: carInfo[0].carId,
            targetTabId: data.tabsId,
            groupId:
              data.tabsId == 1 || data.tabsId == 2 ? data.groupId || '' : ''
          }).then((res) => {
            if (res.data.code === 0) {
              me.$message.success('图片复制成功！')
              $emit(this, 'refreshImage')
              this.dialogVisible = false
            }
          })
          me.isShowMove = false
          me.vehicleCarId = ''
        })
        .catch((err) => {})
    },
    // 取消操作
    cancalOperation(data) {
      delete data.type
      delete data.tabsId
      this.operationDialogStatus = false
      this.vehicleCarId = ''
    },
    cancalUpdataImg(data) {
      delete data.type
      delete data.tabsId
      this.isShowMove = false
      this.vehicleCarId = ''
    },
    handleClose() {
      if (this.isDel) {
        ElMessageBox({
          title: '提示',
          message: '当前已删除的图片未保存!',
          type: 'warning',
          showCancelButton: true,
          confirmButtonText: '继续保存',
          cancelButtonText: '取消并退出',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              this.save()
              done()
            } else {
              this.dialogVisible = false
              done()
            }
          }
        })
        this.isDel = false
      } else {
        this.dialogVisible = false
      }
      const store = useColorGroup() // 这里缓存的数据还是清除吧
      store.groupAppearanceList = []
      store.groupDeitalList = []
    },
    imgMove() {
      this.$refs.imgGroup[this.currentTabId - 1].showMoveImg()
    },
    vehicleChange() {
      getGroupList({
        carId: this.vehicleCarId
      })
        .then((res) => {
          this.groupList = res.data.data
        })
        .catch((err) => {
          console.log(err)
        })
      this.updataImg.tabsId = ''
      this.updataImg.groupId = ''
    }
  },
  emits: ['sendData']
}
</script>

<style lang="scss">
.image-dialog {
  .el-tabs__item {
    pointer-events: auto;
  }
  .el-tabs__item.is-active {
    color: #fff;
    background-color: #409eff;
  }

  .el-checkbox__inner {
    width: 30px !important;
    height: 30px !important;
  }

  .el-checkbox__inner::after {
    left: 12px !important;
    height: 19px !important;
  }

  .el-icon-arrow-left {
    line-height: 1.5;
    font-size: 28px;
  }

  .el-tabs__nav-next {
    font-size: 28px;
  }
}
</style>

<style lang="scss" scoped>
.image-wrap {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 100px;
  margin: 5px;
  text-align: center;

  .image {
    width: 100px;
    height: 100px;
    border: 1px solid #ccc;
  }

  .check-box {
    position: absolute;
    left: 0;
    top: -1px;
  }

  .delete {
    position: absolute;
    top: -5px;
    right: -5px;
    transform: scale(0.7);
  }

  .pic-tip {
    width: 100px;
    display: block;
    font-size: 12px;
    text-align: left;
    margin-bottom: 4px;
  }

  .right-icon {
    position: absolute;
    right: 0;
    top: 0;
    padding: 5px;
    background-color: #fff;
  }
}

.image-dialog {
  .basic-action {
    margin-bottom: 10px;
  }

  .other-action > div {
    margin-bottom: 10px;
  }

  .tabs {
    min-height: 40vh;
    overflow-y: scroll;

    .image-wrap {
      position: relative;
      display: inline-block;
      vertical-align: top;
      width: 100px;
      margin: 5px;
      text-align: center;

      .image {
        width: 100px;
        height: 100px;
        border: 1px solid #ccc;
      }

      .check-box {
        position: absolute;
        left: 0;
        top: -1px;
      }

      .delete {
        position: absolute;
        top: -5px;
        right: -5px;
        transform: scale(0.7);
      }

      .pic-tip {
        width: 100px;
        display: block;
        font-size: 12px;
        text-align: left;
        margin-bottom: 4px;
      }

      .right-icon {
        position: absolute;
        right: 0;
        top: 0;
        padding: 5px;
        background-color: #fff;
      }
    }

    .heightSty {
      margin-bottom: 10px;
      display: flex;
      justify-content: end;
      align-items: center;
      margin-right: 30px;
    }
  }
}
</style>
