<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :title="`查看具体${typeList[ruleForm.type]}`"
      center
      class="choose-dialog"
      width="600px"
      append-to-body
    >
      <el-form
        ref="phoneForm"
        :model="phoneData"
        label-width="100px"
        class="detail-entry"
      >
        <el-form-item :label="`${typeList[ruleForm.type]}`" required>
          <el-input v-focus v-model="phoneData.phone" type="text" readonly />
        </el-form-item>
      </el-form>
      <div class="dialog-content center footer">
        <el-button type="primary" @click="handleClose">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { commonDecryptRecord } from '@/api/user'
export default {
  name: 'ChooseCommonDecrypt',
  props: {},
  data() {
    return {
      dialogVisible: false,
      phoneData: {
        phone: '',
      },
      ruleForm: {
        decryptContent: '', // 需要解密的号码
        source: '', // 来源
        type: 1, // 1手机号解密，2微信号解密，3，身份证解密，4支付宝
        operateType: '',
        //  * 1用户平台，2创作者，3用户实名认证，4城市官审核，
        //  * 5城市官管理，6增长官审核，7增长官管理，8实名认证审核，9购新车发票审核详情，10举报圈主审核，
        //  * 11收益权限管理，12二手车详情发布人电话，13二手车举报管理列表，14二手车举报管理列表查看二手车详情，15买家二手车订单详情手机号，16二手车订单详情手机号，
        //  * 17租车车辆详情，18租车订单详情买家手机号，19租车订单详情商家手机号 ，20用户领卷详情，21经销商领卷详情，22提现明细，23二手车详情经销商电话，24二手车处罚
        //  * 25购新车发票审核, 26二手车列表, 27二手车列表详情微信号
      },
      typeList: {
        1: '手机号',
        2: '微信号',
        3: '身份证号',
        4: '支付宝账号',
      },
    }
  },
  computed: {},
  methods: {
    init(data) {
      const me = this
      me.ruleForm = {
        ...data,
      }
      me.dialogVisible = true
      me.getNumber()
    },
    getNumber() {
      const me = this
      commonDecryptRecord(me.ruleForm)
        .then((response) => {
          if (response.data.code === 0) {
            me.phoneData.phone = response.data.data
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
        })
    },
    // 关闭
    handleClose() {
      this.phoneData.phone = ''
      this.dialogVisible = false
    },
  },
}
</script>
