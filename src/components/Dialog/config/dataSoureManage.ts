import { IColorImage } from '../types/imageDialog'

type Fn = (...str: any[]) => void
export class SourceManage {
  hasChecked!: boolean
  fileIds: string[] = []
  imageIds: string[] = []
  checkAllGroupsImg: Fn
  batchDelImg: Fn
  syncShopInfo: Fn

  constructor(
    public imgList: IColorImage[],
    checkAllGroupsImg: Fn,
    batchDelImg: Fn,
    syncShopInfo: Fn
  ) {
    this.imgList = imgList
    this.hasChecked = this.imgList.some((_) => _.checked)
    this.checkAllGroupsImg = checkAllGroupsImg
    this.getIds()
    this.batchDelImg = () =>
      batchDelImg(this.imgList, this.fileIds, this.imageIds)
    this.syncShopInfo = syncShopInfo
  }

  getIds() {
    const checkedImages = this.imgList.filter((_) => _.checked)
    const fileIds: string[] = []
    const imageIds: string[] = []
    checkedImages.map((_) => {
      fileIds.push(_.imgOrgUrl)
      imageIds.push(_.imgId)
    })
    this.fileIds = fileIds
    this.imageIds = imageIds.filter((_) => _)
  }
}
