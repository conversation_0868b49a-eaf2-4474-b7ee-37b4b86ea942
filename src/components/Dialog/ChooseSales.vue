<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="销量情况"
      center
      class="choose-dialog"
      width="600px"
      append-to-body
    >
      <el-form
        ref="salesForm"
        :model="salesData"
        :rules="rules"
        label-width="100px"
        class="detail-entry"
      >
        <el-form-item label="年份" required>
          <el-input
            v-focus
            v-model="salesData.year"
            type="text"
            maxlength="12"
          />
        </el-form-item>
        <el-form-item label="销量" required>
          <el-input
            v-focus
            v-model="salesData.saleNumber"
            type="number"
            maxlength="12"
          />
        </el-form-item>
      </el-form>
      <div class="dialog-content center footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { deepCopy } from '@/utils'
export default {
  name: 'ChooseSales',
  props: {},
  data() {
    return {
      dialogVisible: false,
      updataStatus: false, // 变更状态
      oldSalesData: {}, // 旧数据
      allData: [], // 所有数据，最后判定重复使用
      salesData: {
        year: '',
        saleNumber: '',
      },
      rules: {}, // 验证信息
    }
  },
  methods: {
    setData(data, allData) {
      const me = this
      me.salesData = {
        ...data,
      }
      if (data.year) {
        me.oldSalesData = deepCopy(me.salesData)
        me.updataStatus = true
      }
      me.allData = allData
      me.dialogVisible = true
      setTimeout(() => {
        me.$refs['salesForm'].resetFields() // 重置验证
      }, 100)
    },

    // 关闭
    handleClose() {
      if (!this.salesData.year && !this.salesData.saleNumber) {
        this.dialogVisible = false
        return false
      }
      this.$confirm('确认关闭？')
        .then((_) => {
          this.dialogVisible = false
        })
        .catch((_) => {})
    },
    // 确认
    confirm() {
      const me = this
      if (!me.salesData.year) {
        return me.$message.error('请输入年份')
      }
      if (!me.salesData.saleNumber) {
        return me.$message.error('请输入销量')
      }
      const tempArr =
        me.allData && me.allData.filter((_) => _.year === me.salesData.year * 1)
      if (
        (!me.updataStatus && tempArr.length) ||
        (me.updataStatus &&
          tempArr.length &&
          me.oldSalesData &&
          me.oldSalesData.year !== me.salesData.year)
      ) {
        // 新增时、编辑时
        me.$message.error('年份有重复')
        return
      }
      me.emitData()
    },
    // 发送数据
    emitData() {
      const me = this
      const salesData = deepCopy(me.salesData)
      $emit(me, 'sendSalesData', salesData, me.updataStatus)
      console.log(salesData)
      me.updataStatus = false
      me.oldSalesData = {}
      me.dialogVisible = false
    },
  },
  emits: ['sendSalesData'],
}
</script>
