<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :title="title"
      center
      class="choose-dialog"
      width="800px"
      append-to-body
    >
      <el-form
        :model="ruleForm"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item required label="颜色名称" props="name">
          <div style="display: flex">
            <el-input v-model="ruleForm.name" style="width: 200px"></el-input>
            <el-button
              size="small"
              style="margin-left: 30px"
              type="primary"
              @click="saveColor"
              >确认颜色</el-button
            >
            <el-button size="small" @click="resize">重置</el-button>
          </div>
        </el-form-item>
        <div style="display: flex">
          <el-form-item required label="颜色值1" props="image">
            <div>
              <el-color-picker
                v-model="ruleForm.image"
                :predefine="predefineColors"
              ></el-color-picker>
            </div>
          </el-form-item>
          <el-form-item label="颜色值2" props="image2">
            <div>
              <el-color-picker
                v-model="ruleForm.image1"
                :predefine="predefineColors"
              ></el-color-picker>
            </div>
          </el-form-item>
          <el-form-item label="颜色值3">
            <div>
              <el-color-picker
                v-model="ruleForm.image2"
                :predefine="predefineColors"
              ></el-color-picker>
            </div>
          </el-form-item>
          <el-form-item label="颜色值4">
            <div>
              <el-color-picker
                v-model="ruleForm.image3"
                :predefine="predefineColors"
              ></el-color-picker>
            </div>
          </el-form-item>
          <el-form-item label="颜色值5">
            <div>
              <el-color-picker
                v-model="ruleForm.image4"
                :predefine="predefineColors"
              ></el-color-picker>
            </div>
          </el-form-item>
        </div>
      </el-form>
      <div>
        <el-row>
          <el-col :span="10">
            <p>该车型下的颜色列表</p>
            <el-table
              :data="colorList"
              height="500px"
              @row-dblclick="rowDoubleClick"
            >
              <el-table-column type="index" align="center" label="序号" />
              <el-table-column prop="name" label="颜色名称" />
              <el-table-column align="center" label="设置">
                <template v-slot="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click="checkedCarColor(scope.row)"
                    :disabled="
                      labels
                        .map((item) => {
                          return item.id
                        })
                        .includes(scope.row.id)
                    "
                    >添加到款型</el-button
                  >
                  <el-button
                    type="text"
                    size="small"
                    @click="deleteGoodsColorId(scope.row, scope.$index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col
            :span="12"
            style="
              padding-left: 25px;
              margin-left: 25px;
              border-left: 1px solid #ebeef5;
            "
          >
            <p>款型颜色列表</p>
            <el-table
              ref="labelsTable"
              :data="defalutLabels"
              height="500px"
              :select-on-indeterminate="false"
              @row-dblclick="rowDoubleClick"
              @selection-change="handleSelectionChange"
              @select="selectRow"
            >
              <el-table-column v-if="isPick" type="selection">
              </el-table-column>
              <el-table-column type="index" align="center" label="序号" />
              <el-table-column prop="name" label="颜色名称" />
              <el-table-column align="center" label="不显示在外观列表">
                <template v-slot="scope">
                  <el-checkbox
                    v-model="scope.row.isHidden"
                    :true-label="1"
                    :false-label="0"
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column align="center" label="设置">
                <template v-slot="scope">
                  <el-button
                    v-if="scope.row.id !== -1"
                    type="text"
                    size="small"
                    @click="deleteColor(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>

      <div class="dialog-content center footer" style="margin-top: 20px">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { clone } from 'lodash-es'
import {
  saveColor,
  saveCarInfoColor,
  delColorRelation,
  getSearchAllByGoodId,
  delGoodsColorRelation
} from '@/api/garage'
import { useColorGroup } from './stores/useAppearanceStore'
export default {
  name: 'ColorDialog',
  props: ['isPick'],
  data() {
    return {
      goodsId: '', // 车型ID
      carId: '', // 款型ID
      dataList: [],
      title: '',
      dialogVisible: false,
      labels: [],
      colorList: [],
      ruleForm: {
        name: '',
        image: '',
        image1: '',
        image2: '',
        image3: '',
        image4: '',
        isHidden: 0
      },

      predefineColors: [
        '#E02020',
        '#FA6400',
        '#F7B500',
        '#6DD400',
        '#6236FF',
        '#0091FF',
        '#B620E0',
        '#FFFFFF',
        '#6D7278',
        '#000000',
        '#804000',
        '#C0C0C0',
        '#CD7F32',
        '#FFC0CB'
      ],
      selectColor: {},
      selectRowId: ''
    }
  },
  setup() {
    const { getGroupList } = useColorGroup()

    return {
      getGroupList
    }
  },
  computed: {
    defalutLabels() {
      if (this.isPick) {
        return [{ color: '', name: '无颜色', id: -1 }, ...this.labels]
      } else {
        return [...this.labels]
      }
    }
  },
  methods: {
    // 重置
    resize() {
      this.ruleForm = {
        name: '',
        image: '',
        image1: '',
        image2: '',
        image3: '',
        image4: '',
        isHidden: 0
      }
    },
    // 选择款型颜色
    checkedCarColor(item) {
      this.labels = [...this.labels, ...[item]]
    },
    // 获取车型颜色库
    getColorList() {
      getSearchAllByGoodId({
        goodId: this.goodsId
      }).then((res) => {
        const data = res.data
        if (data.code === 0) {
          this.colorList = data.data || []
          const imgArr = []
          this.colorList.map((item) => {
            let obj = {}
            if (item.image) {
              item.image.split(',').map((e, index) => {
                if (index === 0) {
                  obj.image = e
                } else {
                  obj['image' + index] = e
                }
              })
            }
            obj = { ...item, ...obj }
            imgArr.push(obj)
          })
          this.colorList = [...imgArr]

          let colorIdArr = []
          colorIdArr = this.colorList.map((item) => {
            return item.id
          })
          const labelList = []
          this.labels.map((item) => {
            if (colorIdArr.includes(item.id)) {
              labelList.push(item)
            }
          })
          this.labels = [...labelList]
          this.setRow()
        }
      })
    },

    setRow() {
      const index = this.defalutLabels.findIndex(
        (_) => _.id == this.selectRowId
      )
      if (index !== -1) {
        this.selectColor = this.defalutLabels[index]
        this.$nextTick(() => {
          this.$refs['labelsTable'].toggleRowSelection(
            this.defalutLabels[index],
            true
          )
        })
      } else {
        this.selectColor = {}
      }
    },
    // 删除车型下的颜色
    deleteGoodsColorId(row, num) {
      this.$confirm(
        `确定删除${row.name}，删除后该车型下的所有使用该颜色的款型对应颜色都会被清除，是否确认删除?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }
      )
        .then(() => {
          delGoodsColorRelation({
            goodId: this.goodsId,
            colorId: row.id
          }).then((res) => {
            if (res.data.code === 0) {
              if (this.selectColor.id === row.id) {
                this.selectColor = {}
              }
              this.$emit('sendDeleteColorId', row.id)
              this.colorList.splice(num, 1)
              this.labels.length &&
                this.labels.map((it, index) => {
                  if (it.id === row.id) {
                    this.labels.splice(index, 1)

                    this.$emit('sendData', this.labels)
                  }
                })
            }
          })
        })
        .catch((err) => {})
    },
    // 删除颜色
    deleteColor(row) {
      delColorRelation({
        goodId: this.goodsId,
        carId: this.carId,
        colorId: row.id
      }).then((res) => {
        if (res.data.code === 0) {
          if (this.ruleForm.id === row.id) {
            this.resize()
          }
          this.labels.map((item, index) => {
            if (item.id === row.id) {
              this.labels.splice(index, 1)
            }
          })
          this.getGroupList(true)
          this.$emit('sendDeleteColorId', row.id)
          this.setRow()
          return this.$message.error('颜色删除成功')
        }
      })
    },
    // 编辑颜色
    rowDoubleClick(row) {
      this.ruleForm = clone(row)
    },
    // 重置状态
    init(params = {}) {
      this.saveFlag = false
      this.dialogVisible = true
      this.goodsId = params.goodsId
      this.carId = params.carId
      this.title = params.title || '选择颜色'
      if (params.labels && params.labels.length) {
        this.labels = [...params.labels]
      } else {
        this.labels = []
      }
      this.selectRowId = params.selectRowId || ''
      this.getColorList()
    },
    // 生成颜色
    saveColor() {
      if (!this.ruleForm.name) {
        return this.$message.error('请输入颜色名称')
      }
      if (!this.ruleForm.image) {
        return this.$message.error('请选择颜色值')
      }
      if (this.ruleForm?.id) {
        return this.$message.info('颜色不允许编辑，请删除重新创建！')
      }
      const strArr = []
      strArr.push(this.ruleForm.image)
      strArr.push(this.ruleForm.image1)
      strArr.push(this.ruleForm.image2)
      strArr.push(this.ruleForm.image3)
      strArr.push(this.ruleForm.image4)
      const str = strArr.filter((item) => {
        return item && item.trim()
      })
      saveColor({
        goodsId: this.goodsId,
        name: this.ruleForm.name,
        image: str.toString()
      }).then((response) => {
        if (response.data.code === 0) {
          this.$message.success('生成颜色id成功')
          this.ruleForm.id = response.data.data
          this.labels.push(clone(this.ruleForm))
          this.colorList.push(clone(this.ruleForm))
          this.resize()
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    // 保存颜色集
    confirm() {
      // 颜色处理

      if (!this.selectColor?.id && this.isPick) {
        return this.$message.error('请勾选款型颜色！')
      }

      const arr = []
      if (this.labels && this.labels.length > 0) {
        this.labels.map(function (value) {
          arr.push({
            color: value.id,
            isHidden: value.isHidden ? 1 : 0
          })
        })
      }
      saveCarInfoColor({
        goodId: this.goodsId,
        carId: this.carId,
        colors: JSON.stringify(arr)
      })
        .then((response) => {
          if (response.data.code === 0) {
            this.saveFlag = true
            this.handleClose(true)
            this.$message.success('保存成功')
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .catch(() => {
          this.$message.error('保存异常')
        })
    },

    selectable(row, index) {
      if (this.selectColor?.id && this.selectColor?.id !== row.id) {
        return false
      }
      return true
    },

    handleSelectionChange(selection) {},
    selectRow(selection, row) {
      this.selectColor = row
      this.$refs['labelsTable'].clearSelection()
      this.defalutLabels.forEach((_) => {
        if (_?.id === row.id) {
          this.$refs['labelsTable'].toggleRowSelection(_, true)
        }
      })
    },
    // 关闭
    handleClose() {
      if (this.saveFlag) {
        this.$emit('sendData', this.labels)
      }
      this.resize()
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
::v-slotted(.el-table__header-wrapper .el-checkbox) {
  visibility: hidden;
}
</style>
