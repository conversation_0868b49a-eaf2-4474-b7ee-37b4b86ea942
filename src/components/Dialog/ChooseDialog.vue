<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :title="title"
      center
      class="choose-dialog"
      width="600px"
      append-to-body
    >
      <div v-if="canChoose" class="choose-labels">
        <p v-for="(label, index) in labels" :key="index" class="box">
          {{ label.name }}
          <span @click="checkAction(label)">删除</span>
        </p>
      </div>

      <p class="search">
        <el-input
          v-focus
          v-model="searchValue"
          placeholder="回车搜索"
          :prefix-icon="IconSearch"
          type="text"
          style="width: 300px"
          clearable
          @change="
            () => {
              page = 1
              getList()
            }
          "
        />
        <el-button type="primary" @click="newColor">新建颜色</el-button>
      </p>

      <div class="list">
        <el-table
          ref="multipleTable"
          :data="dataList"
          row-key="multipleTable"
          border
          style="width: 100%; overflow-y: auto; height: 55vh"
          @row-click="checkAction"
          @row-dblclick="editorColor"
          @select="handleSelect"
          @select-all="handleSelectAll"
        >
          <el-table-column
            v-if="canChoose"
            type="selection"
            width="70"
            class-name="check-all"
          />
          <el-table-column prop="id" label="ID" align="center" />
          <el-table-column prop="name" label="名称" align="center" />
          <el-table-column prop="image" label="值" align="center">
            <template v-slot="scope">
              <div
                :style="{ backgroundColor: scope.row.image }"
                class="block-color"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination
        :page-size="20"
        :current-page="page"
        :total="total"
        align="center"
        layout="total, prev, next, jumper"
        class="el-pagination-center"
        @current-change="currentChange"
      />

      <div v-if="showFooter" class="dialog-content center footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog v-model="colorVisible" title="添加颜色" class="color-dialog">
      <el-form ref="ruleColor" label-width="80px">
        <el-form-item label="颜色名称" required>
          <el-input v-focus v-model="colorName" type="text" />
        </el-form-item>
        <el-form-item label="颜色值" required>
          <el-color-picker v-model="colorValue" />
        </el-form-item>
      </el-form>
      <div class="center">
        <el-button @click="colorVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveColor">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Search as IconSearch } from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
// <choose-dialog ref="ChooseDialog" @sendData="sendData" @closeDialog="closeDialog"/>
// this.$refs['ChooseDialog'].init({
//   labels, // 已选中labels，非必需
//   title, // 弹框标题，非必需
//   getListUrl, // 请求接口地址，默认颜色，非必需
//   canChoose, // 是否支持选中，非必需
//   showFooter: true // 是否显示footer
// })
import { getColorList, saveColor, saveCarInfoColor } from '@/api/garage'
export default {
  data() {
    return {
      // 车型ID
      goodsId: '',
      // 款型ID
      carId: '',
      page: 1,
      total: 0,
      dataList: [],
      searchValue: '',
      dialogVisible: false,
      currentColorId: 0,
      colorName: '',
      colorValue: null,
      colorVisible: false,
      // 已选中标签
      labels: [],
      // 弹框标题
      title: '',
      // 查询接口地址
      getListUrl: '',
      // 是否支持选中
      canChoose: false,
      // 是否显示footer
      showFooter: true,
      IconSearch: markRaw(IconSearch)
    }
  },
  name: 'ColorDialog',
  props: {},
  methods: {
    // 重置状态
    init(params = {}) {
      this.dialogVisible = true
      this.page = 1
      this.searchValue = ''
      this.dataList = []
      if (params.labels && params.labels.length) {
        this.labels = [...params.labels]
      } else {
        this.labels = []
      }
      this.title = params.title || this.title
      this.goodsId = params.goodsId
      this.carId = params.carId
      this.getListUrl = params.getListUrl || getColorList // 默认获取颜色列表
      this.canChoose = params.canChoose
      this.showFooter = params.showFooter !== false
      this.dialogVisible = true
      this.getList()
    },
    saveColor() {
      if (!this.colorName) {
        return this.$message.error('请输入颜色名称')
      }
      if (!this.colorValue) {
        return this.$message.error('请选择颜色值')
      }
      const params = {
        name: this.colorName,
        image: this.colorValue
      }
      if (this.currentColorId > 0) {
        params.id = this.currentColorId || ''
      }
      saveColor(params)
        .then((response) => {
          if (response.data.code === 0) {
            this.$message.success('保存成功')
            this.getList()
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .catch(() => {
          this.$message.error('保存异常')
        })
        .finally(() => {
          this.colorVisible = false
        })
    },
    newColor() {
      this.colorVisible = true
      this.colorName = ''
      this.colorValue = ''
      this.currentColorId = 0
    },
    editorColor(item) {
      this.colorVisible = true
      this.colorName = item.name
      this.colorValue = item.image
      this.currentColorId = item.id
    },
    // 获取列表
    getList() {
      this.dataList = []

      this.getListUrl &&
        this.getListUrl({
          name: this.searchValue || '',
          page: this.page,
          limit: 20
        })
          .then((response) => {
            if (response.data.code === 0) {
              this.dataList = response.data.data.listData
              this.total = response.data.data.total
              this.initChecks()
            } else {
              this.$message.error(response.data.msg)
            }
          })
          .catch(() => {})
          .finally(() => {})
    },
    // 变更页码，上一页或下一页
    currentChange(page) {
      this.page = page
      this.getList()
    },
    checkLabel(target) {
      const me = this
      let list = []
      const exist = me.labels.some((_) => _.name === target.name)
      if (exist) {
        // 已存在，删除
        list = me.labels.filter((_) => _.name !== target.name)
      } else {
        // 不存在，添加
        list = me.labels
        list.splice(0, 0, target)
      }
      me.labels = list
    },
    checkAction(item) {
      const me = this
      // 是否未选择标签
      const unCheck = !me.labels.some((_) => _.name === item.name)
      let index = 0
      me.dataList.map((value, dIndex) => {
        if (value.name === item.name) {
          index = dIndex
        }
      })
      me.$refs.multipleTable.toggleRowSelection(me.dataList[index], unCheck)
      console.log(unCheck)
      me.checkLabel(item)
    },
    // 单选
    handleSelect(list, target) {
      this.checkLabel(target)
    },
    // 多选
    handleSelectAll(list) {
      // this.$refs.multipleTable.clearSelection()
      // return
      // console.log(list)
      // this.labels = list
      // console.log(list)
    },
    initChecks() {
      const me = this
      me.$nextTick(() => {
        me.dataList.map((value) => {
          // 是否已选择标签
          const check = me.labels.some((_) => _.name === value.name)
          me.$refs.multipleTable.toggleRowSelection(value, check)
        })
      })
    },
    // 关闭
    handleClose() {
      // console.log(this.labels)
      if (this.labels.length === 0) {
        this.dialogVisible = false
        this.closeDialog()
        return false
      }
      this.$confirm('确认关闭？')
        .then((_) => {
          this.dialogVisible = false
        })
        .catch((_) => {})
    },
    // 确认
    confirm() {
      console.log(this.labels)
      // 颜色处理
      const arr = []
      if (this.labels && this.labels.length > 0) {
        this.labels.map(function (value) {
          arr.push(value.id)
        })
      }
      saveCarInfoColor({
        goodId: this.goodsId,
        carId: this.carId,
        colors: arr.join(',')
      })
        .then((response) => {
          if (response.data.code === 0) {
            $emit(this, 'sendData', this.labels)
            this.$message.success('保存成功')
            this.getList()
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .catch(() => {
          this.$message.error('保存异常')
        })
        .finally(() => {
          this.closeDialog()
          this.dialogVisible = false
        })
    },
    closeDialog() {
      $emit(this, 'closeDialog')
    }
  },
  emits: ['sendData', 'closeDialog']
}
</script>

<style lang="scss">
.choose-dialog {
  .el-dialog--center .el-dialog__body {
    padding: 15px;
  }
  .el-dialog.el-dialog--center {
    margin-top: 10vh !important;
  }
  .el-table__header-wrapper .check-all .cell {
    .el-checkbox {
      display: none;
    }
    &::after {
      content: '选择';
    }
  }
}
</style>

<style lang="scss" scoped>
.choose-dialog {
  .choose-labels {
    border-bottom: 1px solid #ddd;
    max-height: 100px;
    min-height: 35px;
    overflow-y: auto;
    .box {
      margin: 0 10px 10px 0;
      line-height: 24px;
      height: 24px;
      border: 1px solid #ddd;
      padding-left: 5px;
      display: inline-block;
      color: #ffffff;
      background: #9a7b49;
      span {
        text-decoration: underline;
        cursor: pointer;
        border-left: 1px solid #ddd;
        padding: 0 3px;
        display: inline-block;
        height: 100%;
        background: #f56c6c;
        float: right;
        margin-left: 5px;
      }
    }
  }
  .list {
    max-height: 70vh;
    .block-color {
      width: 100px;
      height: 50px;
      border-radius: 5px;
      margin: 0 auto;
    }
  }
  .dialog-content {
    padding: 10px 0;
  }
}
</style>
