<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      center
      class="choose-dialog"
      width="600px"
      append-to-body
    >
      <div v-if="canChoose" class="choose-labels">
        <p v-for="(label, index) in labels" :key="index" class="box">
          {{ label.brandName }}
          <span @click="checkAction(label)">删除</span>
        </p>
      </div>

      <p class="search">
        <el-input
          v-focus
          v-model="searchValue"
          placeholder="回车搜索"
          :prefix-icon="IconSearch"
          type="text"
          style="width: 300px"
          clearable
          @change="getSearchList()"
        />
      </p>

      <div class="list">
        <el-table
          ref="multipleTable"
          :data="showList"
          row-key="multipleTable"
          border
          style="width: 100%; overflow-y: auto; height: 55vh"
          @row-click="checkAction"
          @select="handleSelect"
          @select-all="handleSelectAll"
        >
          <el-table-column
            v-if="canChoose"
            type="selection"
            label="选择"
            width="70"
            class-name="check-all"
          />
          <el-table-column prop="brandId" label="品牌ID" align="center" />
          <el-table-column prop="brandName" label="品牌名称" align="center" />
        </el-table>
      </div>

      <div v-if="showFooter" class="dialog-content center footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Search as IconSearch } from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
// <choose-dialog ref="ChooseDialog" @sendData="sendData"/>
// this.$refs['ChooseDialog'].init({
//   labels, // 已选中labels，非必需
//   title, // 弹框标题，非必需
//   getListUrl, // 请求接口地址，默认颜色，非必需
//   canChoose, // 是否支持选中，非必需
//   showFooter: true, // 是否显示footer
// })
import { getColorList } from '@/api/garage'
export default {
  data() {
    return {
      total: 0,
      // 显示数据
      showList: [],
      // 所有数据
      dataList: [],
      searchValue: '',
      dialogVisible: false,
      currentColorId: 0,
      // 已选中标签
      labels: [],
      // 弹框标题
      title: '',
      // 查询接口地址
      getListUrl: '',
      // 是否支持选中
      canChoose: false,
      // 是否显示footer
      showFooter: true,
      isMultiple: false,
      IconSearch: markRaw(IconSearch)
    }
  },
  name: 'ChooseDialogBrand',
  props: {},
  methods: {
    // 重置状态
    init(params = {}) {
      this.dialogVisible = true
      this.searchValue = ''
      this.showList = []
      if (params.labels && params.labels.length) {
        this.labels = [...params.labels]
      } else {
        this.labels = []
      }
      this.title = params.title || this.title
      this.getListUrl = params.getListUrl || getColorList // 默认获取颜色列表
      this.canChoose = params.canChoose
      this.showFooter = params.showFooter !== false
      this.isMultiple = params.multiple
      this.dialogVisible = true
      this.getList()
    },
    // 获取列表
    getList() {
      this.dataList = []
      this.getListUrl &&
        this.getListUrl()
          .then((response) => {
            if (response.data.code === 0) {
              this.dataList = response.data.data
              this.showList = this.dataList
              this.initChecks('upDataLable')
            } else {
              this.$message.error(response.data.msg)
            }
          })
          .catch(() => {})
          .finally(() => {})
    },
    checkLabel(target) {
      const me = this
      let list = []
      const tempArr = me.labels.filter((_) => _.brandName === target.brandName)
      const exist = tempArr.length > 0
      if (exist) {
        // 已存在，删除
        list = me.labels.filter((_) => _.brandName !== target.brandName)
      } else {
        // 不存在，添加
        list = me.labels
        list.splice(0, 0, target)
      }
      me.labels = list
    },
    // 选中或删除
    checkAction(item) {
      const me = this
      // 是否未选择标签
      const unCheck =
        me.labels.filter((_) => _.brandName === item.brandName).length === 0
      let index = 0
      me.dataList.map((value, dIndex) => {
        if (value.brandName === item.brandName) {
          index = dIndex
        }
      })
      me.$refs.multipleTable.toggleRowSelection(me.dataList[index], unCheck)
      me.checkLabel(item)
    },
    // 单选
    handleSelect(list, target) {
      this.checkLabel(target)
    },
    // 多选
    handleSelectAll(list) {
      // this.$refs.multipleTable.clearSelection()
      // return
      // console.log(list)
      // this.labels = list
      // console.log(list)
    },
    initChecks(type) {
      const me = this
      let tempArr = []
      me.$nextTick(() => {
        me.showList.map((value) => {
          // 是否已选择标签
          const check = me.labels.some((_) => _.brandName === value.brandName)
          tempArr = check ? tempArr.concat(value) : tempArr
          me.$refs.multipleTable.toggleRowSelection(value, check)
        })
      })
      if (type) {
        // 因为外层数据跟获取数据不一致，此步骤更新整个外层数据
        setTimeout(() => {
          me.labels = tempArr
        }, 500)
      }
    },
    getSearchList() {
      const me = this
      me.showList = []
      me.dataList.map(function (value) {
        if (value.brandName.indexOf(me.searchValue) > -1) {
          me.showList.push(value)
        }
      })
      me.initChecks()
    },
    // 关闭
    handleClose() {
      // console.log(this.labels)
      if (this.labels.length === 0) {
        this.dialogVisible = false
        return false
      }
      this.$confirm('确认关闭？')
        .then((_) => {
          this.dialogVisible = false
        })
        .catch((_) => {})
    },
    // 确认
    confirm() {
      this.dialogVisible = false
      console.log(this.labels)
      $emit(this, 'sendData', {
        labels: this.labels,
        isMultiple: this.isMultiple
      })
    }
  },
  emits: ['sendData']
}
</script>

<style lang="scss">
.choose-dialog {
  .el-dialog--center .el-dialog__body {
    padding: 15px;
  }
  .el-dialog.el-dialog--center {
    margin-top: 10vh !important;
  }
  .el-table__header-wrapper .check-all .cell {
    .el-checkbox {
      display: none;
    }
    &::after {
      content: '选择';
    }
  }
}
</style>

<style lang="scss" scoped>
.choose-dialog {
  .choose-labels {
    border-bottom: 1px solid #ddd;
    max-height: 100px;
    min-height: 35px;
    overflow-y: auto;
    .box {
      margin: 0 10px 10px 0;
      line-height: 24px;
      height: 24px;
      border: 1px solid #ddd;
      padding-left: 5px;
      display: inline-block;
      color: #ffffff;
      background: #9a7b49;
      span {
        text-decoration: underline;
        cursor: pointer;
        border-left: 1px solid #ddd;
        padding: 0 3px;
        display: inline-block;
        height: 100%;
        background: #f56c6c;
        float: right;
        margin-left: 5px;
      }
    }
  }
  .list {
    max-height: 70vh;
    .block-color {
      width: 100px;
      height: 50px;
      border-radius: 5px;
      margin: 0 auto;
    }
  }
  .dialog-content {
    padding: 10px 0;
  }
}
</style>
