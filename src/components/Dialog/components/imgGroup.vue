<template>
  <div class="group-content">
    <el-button
      type="primary"
      v-if="props.tabId === '1'"
      @click="changeGroup()"
      class="mb10"
      >新建组别</el-button
    >

    <draggable
      :disabled="props.tabId === '2'"
      v-model="groupList"
      item-key="id"
      handle=".handle"
      sort="false"
    >
      <template #item="{ element, index }">
        <div class="group-box" @click="selectGroup(index)">
          <div class="box-left handle">
            <div>
              <template v-if="props.tabId === '1'">
                <el-icon
                  @click="moveGroup('down', index)"
                  :color="index === groupList.length - 1 ? '#CDD0D6' : ''"
                  ><CaretBottom
                /></el-icon>
                <el-icon
                  @click="moveGroup('up', index)"
                  :color="index === 0 ? '#CDD0D6' : ''"
                  ><CaretTop
                /></el-icon>
                <el-button
                  link
                  type="primary"
                  @click="deleteGroup(element, index)"
                  >删除</el-button
                >
              </template>
            </div>
            <div>{{ element.id }}</div>
            <div>颜色： {{ element.colorName }}</div>
            <ColorCircle :segments="getColor(element)" />
            <div>
              <el-button
                v-if="props.tabId === '1'"
                type=""
                @click="changeGroup(element, index)"
                >调整组别</el-button
              >
            </div>
          </div>
          <div
            class="box-right"
            :class="`container${index}${props.tabId}`"
            @mousedown="
              (e) => handleMove(e, `container${index}${props.tabId}`, index)
            "
            @mousemove="moveSelectUtil.onMouseMove"
          >
            <div class="box-right-left">
              <el-button type="" @click="openImgPool">图片池添加</el-button>
              <el-upload
                :show-file-list="false"
                :http-request="httpRequest"
                :on-success="onSuccess"
                :on-error="onError"
                name="upfile"
                style="display: inline-block"
                class="avatar-uploader"
                multiple
                action
              >
                <el-button class="button" type="primary" link>
                  <el-button>添加图片</el-button>
                </el-button>
              </el-upload>
              <el-button type="" @click="checkImgAll(index)">全选</el-button>
            </div>
            <div class="box-right-right">
              <draggable
                item-key="id"
                @change="changeTag"
                v-model="element.imgList"
                direction="horizontal"
              >
                <template #item="{ element: img, index: inx }">
                  <span
                    @mousedown.stop
                    v-show="inx <= 8 || (inx > 8 && element.isExpand)"
                    class="image-wrap container-item"
                  >
                    <div class="img-box">
                      <img
                        :src="img.imgUrl"
                        class="image"
                        @click="seeBigImg(img.imgOrgUrl)"
                      />
                    </div>
                    <el-checkbox
                      v-model="img.checked"
                      class="check-box"
                      @change="(value: any) => checkSingleImage(value, index, inx)"
                    />

                    <span
                      v-if="img.shopName && img.shopName !== 'null'"
                      class="pic-tip"
                      >商家：{{ img.shopName }}</span
                    >
                  </span>
                </template>
              </draggable>
              <div class="tag-box" v-if="props.tabId === '1'">
                <div
                  v-for="(tag, index) in directions"
                  :key="tag"
                  class="tag-content"
                >
                  <div class="tag">{{ index + 1 }}｜{{ tag }}</div>
                </div>
              </div>
            </div>
          </div>

          <div
            @click="element.isExpand = !element.isExpand"
            v-if="element.imgList.length > 9"
            :class="[
              'box-expand',
              'wd-cursor-pointer',
              element.isExpand ? 'wd-bottom-20px' : 'wd-top-40px',
              'point-auto'
            ]"
          >
            <span>{{ element.isExpand ? '收起' : '展开' }}</span>
            <el-icon
              ><CaretBottom v-if="!element.isExpand" /> <CaretTop v-else />
            </el-icon>
          </div>
        </div>
      </template>
    </draggable>
    <el-dialog
      v-model="groupDialog"
      :title="pickedGroup?.id ? '调整组别' : '新建组别'"
      :before-close="closeGroupDialog"
      width="30%"
      center
    >
      <div style="text-align: center">
        <p v-if="pickedGroup.colorName">已选择: {{ pickedGroup.colorName }}</p>
        <el-button type="" @click="chooseColor">选择颜色</el-button>
        <div class="mt20">
          <el-button type="" @click="closeGroupDialog">取消</el-button>
          <el-button type="primary" @click="pickColor">确认</el-button>
        </div>
      </div>
      <ChooseColorDialog
        ref="chooseColorDialog"
        :isPick="true"
        @sendDeleteColorId="sendDeleteColorId"
        @sendData="chooseNewSendData"
      />
    </el-dialog>
    <PopuImgPool
      ref="popuImgPool"
      v-model="pictureBatchUploadVisible"
      @determineBatchUpload="determineBatchUpload"
    />
    <el-dialog
      v-model="imgMove"
      width="400px"
      append-to-body
      title="跨组别移动图片"
    >
      <div>
        <el-select v-model="moveIndex" placeholder="请选择">
          <el-option
            v-for="(item, index) in groupList"
            :key="item.id"
            :label="getLabelName(item)"
            :value="index"
          ></el-option>
        </el-select>
      </div>
      <div style="margin-top: 260px" class="dialog-button">
        <el-button @click="cancleImgMove">取消</el-button>
        <el-button type="primary" @click="saveImgMove">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
interface IGroupList {
  id: number
  colorName: string
  imgList: any[]
  [x: string]: any
}

import { ref, getCurrentInstance } from 'vue'
import { CaretBottom, CaretTop } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import ChooseColorDialog from '@/components/Dialog/chooseColorDialog.vue'
import PopuImgPool from './popuImgPool.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import ColorCircle from './ColorCircle.vue'
import { deleteImageV3 } from '@/api/garage'
import { MoveSelectUtil } from '../hooks/moveSelect'
import { useColorGroup } from '../stores/useAppearanceStore'
import { useGroupStore } from '../stores'
import { directions } from '../config'
import { storeToRefs } from 'pinia'
import { IColorGroup } from '../types/imageDialog'

const props = defineProps<{ tabId: any }>()
const { groupAppearanceList, groupDeitalList, hasCheckImg } = storeToRefs(
  useColorGroup()
)
const { imgState } = storeToRefs(useGroupStore())
const { updateState } = useGroupStore()

const {
  changeImgTag,
  addGroup,
  removeGroup,
  addSingleImg,
  batchAddImg,
  checkSingleImage,
  checkImgAll,
  moveImg
} = useColorGroup()

const groupList = computed({
  get() {
    return props.tabId === '1'
      ? groupAppearanceList.value
      : groupDeitalList.value
  },
  set(val) {
    if (props.tabId === '1') {
      groupAppearanceList.value = val
    } else {
      groupDeitalList.value = val
    }
  }
})
const moveSelectUtil = new MoveSelectUtil({
  item: '.container-item',
  container: '.container' + props.tabId,
  update(data) {
    data.forEach((item) => {
      checkSingleImage(item.selected, selectIndex.value, item.index)
    })
  }
})
const handleMove = (e: MouseEvent, container: string, index: number) => {
  selectIndex.value = index
  moveSelectUtil.setContainer('.' + container)
  moveSelectUtil.startSelect(e)
}

const { proxy } = getCurrentInstance() as any
const imgMove = ref(false)
const moveIndex = ref<any>()
const pictureBatchUploadVisible = ref<boolean>(false)

const chooseColorDialog = ref<any>()
const groupDialog = ref<boolean>(false)
const selectIndex = ref<number>(0)
const popuImgPool = ref<any>()
let _uploads: any[] = []

const pickedGroup = ref<IGroupList>({
  id: 0,
  colorName: '',
  index: 0,
  imgList: []
}) // 选中的组别

const changeGroup = (item?: any, index = -1) => {
  pickedGroup.value = { ...item, index } || {
    id: 0,
    colorName: '',
    imgList: [],
    index: -1
  }
  groupDialog.value = true
}

const chooseNewSendData = (data: any) => {
  pickedGroup.value.colorName =
    chooseColorDialog.value.selectColor?.name ||
    pickedGroup.value.colorName ||
    ''
  pickedGroup.value.color =
    chooseColorDialog.value.selectColor?.id || pickedGroup.value.color || ''
  updateState({ labels: data })
}

const chooseColor = () => {
  chooseColorDialog.value.init({
    title: '选择颜色',
    goodsId: imgState.value.goodsId,
    carId: imgState.value.carId,
    labels: imgState.value.labels || '',
    canChoose: true,
    selectRowId: pickedGroup.value?.color
  })
}
const sendDeleteColorId = (colorId: number) => {
  if (colorId === pickedGroup.value.color) {
    updateState({
      labels: imgState.value.labels.filter((item) => item.id !== colorId)
    })
    pickedGroup.value = {
      ...pickedGroup.value,
      color: -1,
      colorName: '无颜色'
    }
  }
}

const closeGroupDialog = () => {
  groupDialog.value = false
  pickedGroup.value = {} as any
}
// 新建或修改组别
const pickColor = () => {
  const group = groupList.value[pickedGroup.value.index]
  addGroup(group?.id, pickedGroup.value!.color)
  closeGroupDialog()
}

const selectGroup = (index: number) => {
  selectIndex.value = index
}

const httpRequest = (option: any) => {
  _uploads = _uploads || []
  _uploads.push({
    fn: proxy.$oss.ossUploadImage,
    option
  })
  proxy.$tools.debounce(call, 100)()
}
const call = async () => {
  for (const a of _uploads) {
    await a.fn(a.option)
  }
  _uploads = []
}
const onError = (res: any, file: any) => {
  res && proxy.$message.error(res)
  console.log(res)
}
const onSuccess = (
  res: { name: any; imgUrl: any },
  file: { name: any },
  fileList: any
) => {
  // 假性成功，直接return
  if (!res) return

  if (res.name) {
    addSingleImg(res.imgUrl, selectIndex.value)
  } else {
    proxy.$notify.error({
      title: '上传错误',
      message: file.name,
      duration: 0
    })
  }
}

const deleteGroup = (item: any, index: number) => {
  let fileIds: any[] = []
  let imageIds: any[] = []
  item.imgList.map((_: any) => {
    _.imgOrgUrl ? fileIds.push(_.imgOrgUrl) : null
    _.imgOrgUrl ? imageIds.push(_.imgId) : null
  })
  const tip =
    (imageIds.length
      ? `该组别下有${imageIds.length || 0}张图片，是否同步删除?`
      : '确定删除该分组') +
    `<p style="color: red">注：组别一旦删除，直接生效, 连同细节组别一起删除</p>`
  ElMessageBox.confirm(tip, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    dangerouslyUseHTMLString: true,
    type: 'warning'
  })
    .then(async (result) => {
      try {
        let tasks: any[] = []
        if (imageIds.length) {
          tasks.push(
            deleteImageV3({
              fileIds: fileIds.toString() || '',
              imageIds: imageIds.toString() || ''
            })
          )
        }
        tasks.push(removeGroup(item.id, index))
        const [num, info] = await Promise.all(tasks)
        if (
          (tasks.length === 2 && num.data.code === 0 && info.data.code === 0) ||
          (tasks.length === 1 && num.data.code === 0)
        ) {
          proxy.$message.success('删除成功')
        }
      } catch (error) {
        console.log(error)
      }
    })
    .catch((err) => {
      console.log(err)
    })
}

const openImgPool = () => {
  pictureBatchUploadVisible.value = true
}
const determineBatchUpload = (url: string) => {
  if (!url) {
    return proxy.$message.error('请输入url地址')
  }
  pictureBatchUploadVisible.value = false

  batchAddImg(url, selectIndex.value)
  popuImgPool.value.pictureBatchUploadUrl = ''
}

// 大图查看图片
const seeBigImg = (link: string | URL | undefined) => {
  if (link) {
    window.open(link)
  }
}

const moveGroup = (type: 'up' | 'down', index: number) => {
  if (type === 'up' && index !== 0) {
    const group = groupList.value[index]
    const group1 = groupList.value[index - 1]
    groupList.value[index] = group1
    groupList.value[index - 1] = group
  }
  if (type === 'down' && index !== groupList.value.length - 1) {
    const group = groupList.value[index]
    const group1 = groupList.value[index + 1]
    groupList.value[index] = group1
    groupList.value[index + 1] = group
  }
}

const showMoveImg = () => {
  if (hasCheckImg.value) {
    imgMove.value = true
  } else {
    ElMessage.warning('请选择图片')
  }
}

const cancleImgMove = () => {
  imgMove.value = false
  moveIndex.value = ''
}

const getLabelName = (item: IColorGroup) => {
  const emptyNum = item.imgList.filter((img: any) => !img.imgUrl).length
  let remain = props.tabId === '1' ? ` 空余：${emptyNum}` : ''
  return `${item.colorName}（id:${item.id}）${remain}`
}

const saveImgMove = () => {
  selectIndex.value = moveIndex.value
  moveImg(selectIndex.value)
  imgMove.value = false
}

const getColor = (data: any) => {
  const colors = data.image ? data.image.split(',') : []
  return colors.map((color: string) => {
    return { color, percentage: 100 / colors.length }
  })
}

const changeTag = ({ moved }: any) => {
  const { element, newIndex, oldIndex } = moved
  console.log('change', moved)
  changeImgTag(element.groupId)
}

defineExpose({
  showMoveImg
})
</script>

<style scoped lang="scss">
.group-content {
  border: 1px solid #ccc;
  padding: 10px;
  height: 100%;
}
.group-box {
  position: relative;
  border: 1px solid #ccc;
  display: flex;
  flex-direction: row;
  margin-bottom: 5px;

  .box-expand {
    position: absolute;
    right: 200px;
  }
  .box-left {
    width: 100px;
    min-height: 160px;
    border-right: 1px solid #ccc;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
  }
  .box-right {
    display: flex;
    align-items: center;
    position: relative;
    padding-right: 200px;

    &-left {
      @extend .box-left;
      width: 120px;
      border-right: none;
      padding-right: 10px;
    }
    &-right {
      display: flex;
      flex-direction: row;
      gap: 10px;
      max-width: 1000px;
      flex-wrap: wrap;
      position: relative;
    }
  }
  .tag-box {
    position: absolute;
    z-index: 1;
    display: flex;
    top: 83px;

    .tag-content {
      width: 100px;
      margin: 5px;
    }
    .tag {
      width: 60px;
      background-color: #e6a23c;
      color: black;
      font-weight: 600;
      text-align: center;
    }
  }
  .image-wrap {
    position: relative;
    display: inline-block;
    vertical-align: top;
    width: 100px;
    margin: 5px;
    text-align: center;
    .img-box {
      position: relative;
      width: 100%;
      height: 100%;
      .tag {
        position: absolute;
        bottom: 5px;
        left: 0;
        width: 40px;
        background-color: #e6a23c;
      }
    }
    .image {
      width: 100px;
      height: 100px;
      border: 1px solid #ccc;
    }
    .check-box {
      position: absolute;
      left: 0;
      top: -1px;
    }
    .delete {
      position: absolute;
      top: -5px;
      right: -5px;
      transform: scale(0.7);
    }

    .pic-tip {
      width: 100px;
      display: block;
      font-size: 12px;
      text-align: left;
      margin-bottom: 4px;
    }
    .right-icon {
      position: absolute;
      right: 0;
      top: 0;
      padding: 5px;
      background-color: #fff;
    }
  }
}
</style>
