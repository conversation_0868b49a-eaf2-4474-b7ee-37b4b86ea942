<template>
  <el-dialog width="40%" v-bind="$attrs" :before-close="close">
    <span>
      支持输入url地址，示例格式：
      http://imgs.58moto.com/carport/20200818/20200818142305_li49.jpg!square?_1242_1242,
      http://imgs.58moto.com/carport/20200818/20200818142305_li49.jpg!square?_1242_1242</span
    >
    <el-input
      :rows="10"
      v-model="pictureBatchUploadUrl"
      type="textarea"
      placeholder="请输入url地址"
      style="margin-top: 20px"
      clearablefh
    >
    </el-input>
    <template v-slot:footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="determineBatchUpload"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'

const emits = defineEmits([
  'update:modelValue',
  'determineBatchUpload',
  'close'
])

const pictureBatchUploadUrl = ref('')

const close = () => {
  emits('update:modelValue', false)
  emits('close')
}
const determineBatchUpload = () => {
  emits('determineBatchUpload', pictureBatchUploadUrl.value)
  pictureBatchUploadUrl.value = ''
}
defineExpose({
  pictureBatchUploadUrl
})
</script>

<style lang="scss" scoped></style>
