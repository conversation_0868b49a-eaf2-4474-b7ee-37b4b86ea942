<template>
  <div class="wd-mb-10px wd-flex wd-items-center">
    <span class="mr5">{{ props.isWg ? '拍摄于' : '来源于' }}</span>
    <SerchantMerchant
      class="mr5"
      @on-update="
        (shop) => {
          shopIdByName = shop.shopId
          shopName = shop.name
        }
      "
      @on-clear="
        () => {
          shopIdByName = ''
          shopName = ''
        }
      "
    />
    <span class="mr5">商家别名</span>
    <el-input
      v-model="shopAliasName"
      style="width: 200px"
      class="mr5"
    ></el-input>
    <el-radio v-model="displayArea" label="0">本市</el-radio>
    <el-radio v-model="displayArea" label="1">全国</el-radio>
    <span class="mr5">投放区域</span>
    <el-input
      v-model="location"
      placeholder="请选择投放区域"
      style="width: 450px"
      @click="placementArea()"
    />
    <el-button @click="syncSource">写入</el-button>
  </div>
  <MultiArea ref="multiArea" @backData="backData" />
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'

import MultiArea from '@/components/area/MultiArea.vue'
import SerchantMerchant from '@/components/search-merchant/index.vue'

const props = defineProps(['isWg', 'modelValue', 'shopName', 'shopAliasName'])
const emits = defineEmits([
  'syncSource',
  'update:modelValue',
  'update:shopName',
  'update:shopAliasName'
])
const shopIdByName = computed({
  get: () => props.modelValue,
  set: (val) => emits('update:modelValue', val)
})
const shopAliasName = computed({
  get: () => props.shopAliasName,
  set: (val) => emits('update:shopAliasName', val)
})
const shopName = computed({
  get: () => props.shopName,
  set: (val) => emits('update:shopName', val)
})
const displayArea = ref('0')
const location = ref('')
const multiArea = ref()

let provinceName = ''
let cityName = ''
let globalCountry!: number
const backData = (data: any) => {
  provinceName = data.provinceAddress
  cityName = data.cityAddress
  globalCountry = data.checkAll ? 1 : 0
  location.value = provinceName ? provinceName + '，' + cityName : cityName
}
const placementArea = () => {
  const obj = {
    checkAll: globalCountry === 1, // 是否选中全国 Bool
    provinceAddress: provinceName, // 选中的省 String
    cityAddress: cityName // 选中的市 String
  }
  multiArea.value.init(obj)
}

// 写入来源
const syncSource = () => {
  if (shopAliasName.value && !shopIdByName.value) {
    return ElMessage.error('商家别名必须要对应图片来源，请选择图片来源')
  }
  if ((provinceName || cityName) && !shopIdByName.value) {
    return ElMessage.error('城市必须要对应图片来源，请选择图片来源')
  }
  emits('syncSource', {
    shopIdByName: shopIdByName.value,
    shopName: shopAliasName.value || shopName.value,
    provinceName,
    cityName,
    displayArea: displayArea.value
  })
}
</script>

<style scoped></style>
