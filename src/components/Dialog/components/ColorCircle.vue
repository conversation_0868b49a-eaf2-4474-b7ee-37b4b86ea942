<template>
  <div class="color-circle" v-if="segments.length" :style="circleStyle"></div>
</template>

<script>
export default {
  name: 'ColorCircle',
  props: {
    segments: {
      type: Array,
      required: true,
      validator(segments) {
        return segments.every(
          (segment) => 'color' in segment && 'percentage' in segment
        )
      }
    }
  },
  computed: {
    circleStyle() {
      let cumulativePercentage = 0
      const gradientStops = this.segments
        .map((segment) => {
          const start = cumulativePercentage
          const end = cumulativePercentage + segment.percentage
          cumulativePercentage = end

          return `${segment.color} ${start}% ${end}%`
        })
        .join(', ')

      return {
        position: 'relative',
        width: '20px',
        height: '20px',
        borderRadius: '50%',
        background: `conic-gradient(from 0.5turn, ${gradientStops})`
      }
    }
  }
}
</script>

<style scoped>
.color-circle {
  border: 2px solid #e6ecf2;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.segment {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
}
</style>
