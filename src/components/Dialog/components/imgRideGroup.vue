<template>
  <div class="group-content">
    <draggable
      :disabled="false"
      v-model="rideGroupList"
      item-key="id"
      handle=".handle"
      sort="false"
      @change="moveGroup"
    >
      <template #item="{ element, index }">
        <div class="group-box" @click="selectGroup(index)">
          <div class="box-left handle !wd-justify-center">
            <div class="wd-flex wd-items-center">
              <p class="wd-mr-10px wd-w-60px">模特身高:</p>
              <el-input
                style="width: 150px; margin-right: 10px"
                v-model="element.modelHeight"
                placeholder="模特身高"
              >
                <template v-slot:append>cm</template>
              </el-input>
            </div>
            <div class="wd-flex wd-items-center">
              <p class="wd-w-50px wd-mr-10px wd-text-right">座高:</p>
              <el-input
                style="width: 150px"
                :value="props.goodSaddleHigh"
                placeholder="座高"
                disabled
              >
                <template v-slot:append>mm</template>
              </el-input>
            </div>
            <div class="wd-mt-20px">
              <el-button link type="primary" @click="updateGroup(element)"
                >保存</el-button
              >
              <el-button
                link
                type="primary"
                @click="deleteGroup(element, index)"
                >删除</el-button
              >
            </div>
          </div>
          <div
            class="box-right"
            :class="`container${index}${props.tabId}`"
            @mousedown="
              (e) => handleMove(e, `container${index}${props.tabId}`, index)
            "
            @mousemove="moveSelectUtil.onMouseMove"
          >
            <div class="box-right-left">
              <el-button type="" @click="openImgPool">图片池添加</el-button>
              <el-upload
                :show-file-list="false"
                :http-request="httpRequest"
                :on-success="onSuccess"
                :on-error="onError"
                name="upfile"
                style="display: inline-block"
                class="avatar-uploader"
                multiple
                action
              >
                <el-button class="button" type="primary" link>
                  <el-button>添加图片</el-button>
                </el-button>
              </el-upload>
              <el-button type="" @click="checkImgAll(index)">全选</el-button>
            </div>
            <div class="box-right-right">
              <draggable
                item-key="id"
                v-model="element.imgList"
                direction="horizontal"
              >
                <template #item="{ element: img, index: inx }">
                  <span
                    @mousedown.stop
                    v-show="inx <= 8 || (inx > 8 && element.isExpand)"
                    class="image-wrap container-item"
                  >
                    <div class="img-box">
                      <img
                        :src="img.imgUrl"
                        class="image"
                        @click="seeBigImg(img.imgOrgUrl)"
                      />
                    </div>
                    <el-checkbox
                      v-model="img.checked"
                      class="check-box"
                      @change="(value: any) => checkSingleImage(value, index, inx)"
                    />

                    <span
                      v-if="img.shopName && img.shopName !== 'null'"
                      class="pic-tip"
                      >商家：{{ img.shopName }}</span
                    >
                  </span>
                </template>
              </draggable>
            </div>
          </div>

          <div
            @click="element.isExpand = !element.isExpand"
            v-if="element.imgList.length > 9"
            :class="[
              'box-expand',
              'wd-cursor-pointer',
              element.isExpand ? 'wd-bottom-20px' : 'wd-top-40px',
              'point-auto'
            ]"
          >
            <span>{{ element.isExpand ? '收起' : '展开' }}</span>
            <el-icon
              ><CaretBottom v-if="!element.isExpand" /> <CaretTop v-else />
            </el-icon>
          </div>
        </div>
      </template>
    </draggable>

    <PopuImgPool
      ref="popuImgPool"
      v-model="pictureBatchUploadVisible"
      @determineBatchUpload="determineBatchUpload"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance } from 'vue'
import { CaretBottom, CaretTop } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import PopuImgPool from './popuImgPool.vue'
import { ElMessageBox } from 'element-plus'
import { deleteImageV3 } from '@/api/garage'
import { MoveSelectUtil } from '../hooks/moveSelect'

import { useRideStore } from '../stores/useRideStore'
import { storeToRefs } from 'pinia'
import { useDebounceFn } from '@vueuse/core'

const props = defineProps<{
  tabId: any
  carId: any
  goodSaddleHigh: any
}>()
const { groupList: rideGroupList } = storeToRefs(useRideStore())
const {
  checkImgAll,
  checkSingleImage,
  addSingleImg,
  batchAddImg,
  removeGroup,
  updateRideGroup,
  moveRideGroup
} = useRideStore()

const moveSelectUtil = new MoveSelectUtil({
  item: '.container-item',
  container: '.container' + props.tabId,
  update(data) {
    data.forEach((item) => {
      checkSingleImage(item.selected, selectIndex.value, item.index)
    })
  }
})
const handleMove = (e: MouseEvent, container: string, index: number) => {
  selectIndex.value = index
  moveSelectUtil.setContainer('.' + container)
  moveSelectUtil.startSelect(e)
}

const { proxy } = getCurrentInstance() as any

const pictureBatchUploadVisible = ref<boolean>(false)

const selectIndex = ref<number>(0)
const popuImgPool = ref<any>()
let _uploads: any[] = []

const selectGroup = (index: number) => {
  selectIndex.value = index
}

const httpRequest = (option: any) => {
  _uploads = _uploads || []
  _uploads.push({
    fn: proxy.$oss.ossUploadImage,
    option
  })
  proxy.$tools.debounce(call, 100)()
}
const call = async () => {
  for (const a of _uploads) {
    await a.fn(a.option)
  }
  _uploads = []
}
const onError = (res: any) => {
  res && proxy.$message.error(res)
  console.log(res)
}
const onSuccess = (res: { name: any; imgUrl: any }, file: { name: any }) => {
  if (!res) return

  if (res.name) {
    addSingleImg(res.imgUrl, selectIndex.value)
  } else {
    proxy.$notify.error({
      title: '上传错误',
      message: file.name,
      duration: 0
    })
  }
}

const deleteGroup = (item: any, index: number) => {
  let fileIds: any[] = []
  let imageIds: any[] = []
  item.imgList.map((_: any) => {
    _.imgOrgUrl ? fileIds.push(_.imgOrgUrl) : null
    _.imgOrgUrl ? imageIds.push(_.imgId) : null
  })
  const tip = imageIds.length
    ? `该组别下有${imageIds.length || 0}张图片，是否同步删除?`
    : '确定删除该分组'
  ElMessageBox.confirm(tip, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    dangerouslyUseHTMLString: true,
    type: 'warning'
  })
    .then(async () => {
      try {
        let tasks: any[] = []
        if (imageIds.length) {
          tasks.push(
            deleteImageV3({
              fileIds: fileIds.toString() || '',
              imageIds: imageIds.toString() || ''
            })
          )
        }
        tasks.push(removeGroup(item.id, index))
        const [num, info] = await Promise.all(tasks)
        if (
          (tasks.length === 2 && num.data.code === 0 && info.data.code === 0) ||
          (tasks.length === 1 && num.data.code === 0)
        ) {
          proxy.$message.success('删除成功')
        }
      } catch (error) {
        console.log(error)
      }
    })
    .catch((err) => {
      console.log(err)
    })
}

const openImgPool = () => {
  pictureBatchUploadVisible.value = true
}
const determineBatchUpload = (url: string) => {
  if (!url) {
    return proxy.$message.error('请输入url地址')
  }
  pictureBatchUploadVisible.value = false

  batchAddImg(url, selectIndex.value)
  popuImgPool.value.pictureBatchUploadUrl = ''
}

// 大图查看图片
const seeBigImg = (link: string | URL | undefined) => {
  if (link) {
    window.open(link)
  }
}
const moveGroup = () => {
  moveRideGroup()
}
const updateGroup = useDebounceFn((group) => {
  updateRideGroup({ id: group.id, updateModelHeight: group.modelHeight })
}, 1000)
</script>

<style scoped lang="scss">
.group-content {
  border: 1px solid #ccc;
  padding: 10px;
  height: 100%;
}
.group-box {
  position: relative;
  border: 1px solid #ccc;
  display: flex;
  flex-direction: row;
  margin-bottom: 5px;

  .box-expand {
    position: absolute;
    right: 200px;
  }
  .box-left {
    width: 270px;
    min-height: 160px;
    border-right: 1px solid #ccc;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
  }
  .box-right {
    display: flex;
    align-items: center;
    position: relative;
    padding-right: 200px;

    &-left {
      @extend .box-left;
      width: 120px;
      border-right: none;
      padding-right: 10px;
    }
    &-right {
      display: flex;
      flex-direction: row;
      gap: 10px;
      max-width: 1000px;
      flex-wrap: wrap;
      position: relative;
    }
  }
  .tag-box {
    position: absolute;
    z-index: 1;
    display: flex;
    top: 83px;

    .tag-content {
      width: 100px;
      margin: 5px;
    }
    .tag {
      width: 60px;
      background-color: #e6a23c;
      color: black;
      font-weight: 600;
      text-align: center;
    }
  }
  .image-wrap {
    position: relative;
    display: inline-block;
    vertical-align: top;
    width: 100px;
    margin: 5px;
    text-align: center;
    .img-box {
      position: relative;
      width: 100%;
      height: 100%;
      .tag {
        position: absolute;
        bottom: 5px;
        left: 0;
        width: 40px;
        background-color: #e6a23c;
      }
    }
    .image {
      width: 100px;
      height: 100px;
      border: 1px solid #ccc;
    }
    .check-box {
      position: absolute;
      left: 0;
      top: -1px;
    }
    .delete {
      position: absolute;
      top: -5px;
      right: -5px;
      transform: scale(0.7);
    }

    .pic-tip {
      width: 100px;
      display: block;
      font-size: 12px;
      text-align: left;
      margin-bottom: 4px;
    }
    .right-icon {
      position: absolute;
      right: 0;
      top: 0;
      padding: 5px;
      background-color: #fff;
    }
  }
}
</style>
