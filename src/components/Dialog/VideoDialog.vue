<template>
  <div class="wrap">
    <el-dialog
      v-loading="loading"
      v-model="dialogVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="video-dialog"
      width="1400px"
      append-to-body
    >
      <div class="video-head">
        <el-button
          type="primary"
          class="input-content"
          :class="{ 'point-none': eCarId }"
        >
          <input
            type="file"
            name="file"
            accept="video/*"
            class="imageFile"
            @input="addVideo"
          />
          <span class="add-video-tip">添加视频</span>
        </el-button>
        <el-button :class="{ 'point-none': eCarId }" @click="save()"
          >保存</el-button
        >
        &ensp;&ensp;&ensp;&ensp; 单个视频时长控制在5min内
        mp4格式，每次添加视频耗时约30s，请耐心等候
      </div>
      <div class="video-tab-content">
        <el-tabs
          v-model="currentAllTabId"
          type="card"
          @tab-click="handleClickOne"
        >
          <el-tab-pane
            v-for="(TabsControl, index) in allTabsControl"
            :key="index"
            :label="`${TabsControl.param}(${
              (TabsControl.tabList && TabsControl.tabList.length) || '0'
            })`"
            :name="`${TabsControl.id}`"
          >
            <el-tabs
              v-if="showTabs"
              v-model="currentTabId"
              type="card"
              class="tabs"
              @tab-click="handleClick"
            >
              <!-- name 切换对应 currentTabId 值  ${tab.id}-->
              <el-tab-pane
                v-for="(tab, index) in tabsControl"
                :key="index"
                :label="`${tab.param}(${
                  (tabs[tab.id] && tabs[tab.id].length) || '0'
                })`"
                :name="`${tab.id}`"
              >
                <draggable
                  v-if="tabs[currentTabId] && tabs[currentTabId].length"
                  v-model="tabs[currentTabId]"
                  @end="refreshImageList"
                  item-key="imgOrgUrl"
                >
                  <template #item="{ element, index }">
                    <div class="video-show-content">
                      <img
                        v-if="element.imgOrgUrl"
                        :src="element.imgOrgUrl"
                        class="video-img"
                      />
                      <img
                        v-else
                        src="@/assets/image/video-default.jpg"
                        alt
                        class="video-img"
                      />
                      <el-icon
                        class="right-icon"
                        size="20px"
                        :class="{ 'point-none': eCarId }"
                        @click="
                          () => {
                            operationDialogStatus = true
                            updataVideo = element
                          }
                        "
                        ><IconMore
                      /></el-icon>
                      <img
                        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAMAAADVRocKAAAAG1BMVEVHcEz///////////////////////////////8W/R0OAAAACXRSTlMAI67h/8p3TojBokCiAAAAqElEQVR42u3WyY0DMQwFUS7VZOcf8QTgi2iYBw3+S6BEQJuJiIiIiMg6j+TMU/aFYuBxm2pGHpvKwbIKqPkAacdqPkLBa8ccWA3YLwK+Hajs5QCELwegtgNkLwcgfDkA73aA7OUAhC8HSL96guyrd1H41Se5rr5Nsy9/0UyBj0CM/kVpMw60nXogbCiAqCNvAm5DnkyUzQXHsu0bXu+RahMRERERkf/gD1RhDECV8vT4AAAAAElFTkSuQmCC"
                        class="icon play-icon"
                        @click="seeVideoPlay(element)"
                      />
                      <input
                        v-model="element.imgDesc"
                        :maxlength="'10'"
                        type="text"
                        class="input-content"
                        @change="updataStatus = true"
                      />
                      <p>视频封面图：</p>
                      <div
                        v-if="element.videoUrl"
                        @click="videoImgIndex = index"
                      >
                        <el-upload
                          :show-file-list="false"
                          :http-request="httpRequest"
                          :on-success="onSuccessTitleimage"
                          name="titlefile"
                          style="display: inline-block"
                          class="avatar-uploader"
                          action
                        >
                          <img
                            v-if="element.imgOrgUrl"
                            :src="element.imgOrgUrl"
                            alt
                            class="video-img"
                          />
                          <el-button v-else type="primary" link
                            >选择视频封面图</el-button
                          >
                        </el-upload>
                      </div>
                    </div>
                  </template>
                </draggable>
                <div v-else>暂无视频</div>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
        </el-tabs>
      </div>
      <el-dialog v-model="operationDialogStatus" width="600px" append-to-body>
        <p>您想针对视频：{{ updataVideo.imgDesc }}做什么操作？</p>
        <el-radio v-model="updataVideo.type" label="1">删除</el-radio>
        <el-radio
          v-if="updataVideo.imgOrgUrl"
          v-model="updataVideo.type"
          label="2"
          >移动</el-radio
        >
        <el-select
          v-if="updataVideo.imgOrgUrl && updataVideo.imgOrgUrl"
          v-model="updataVideo.tabsId"
          placeholder="请选择"
        >
          <el-option
            v-for="item in secondTabs"
            :key="item.id"
            :label="item.param"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <div class="dialog-button">
          <el-button @click="cancalOperation(updataVideo)">取消</el-button>
          <el-button type="primary" @click="confirmOperation(updataVideo)"
            >保存</el-button
          >
        </div>
      </el-dialog>
      <choose-video ref="ChooseVideo" />
    </el-dialog>
  </div>
</template>

<script>
import { More as IconMore } from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import draggable from 'vuedraggable'
import ChooseVideo from './ChooseVideo.vue'
import { getVideoPageSign } from '@/api/commonModule'
import {
  getVideosList,
  deleteImage,
  deleteImageV3,
  postCarVideoInfo
} from '@/api/garage'
import { ElMessageBox } from 'element-plus'
import {
  createUploader,
  getVideoMes,
  getVideoTaskStatus
  // creatingVideoTranscoding
} from '@/api/video'
import { deepCopy } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  components: {
    draggable,
    ChooseVideo,
    IconMore
  },
  name: 'VideoDialog',
  props: {},
  data() {
    return {
      allTabsControl: [], // 视频一级标签分类
      allTabs: {},
      currentAllTabId: null, // 当前选中的一级视频标签
      tabsControl: [], // 视频二级标签分类
      postVideoList: [], // 发送视频
      tabs: {},
      videoContent: {
        id: '', // 视频id
        imgDesc: '', // 视频描述
        imgOrgUrl: '', // 视频占位图
        imgCategory: '', // 视频标签
        duration: '', // 时长
        videoUrl: '', // 视频播放地址
        vodTaskId: '', // 视频转码id
        vodTaskStatus: '' // 视频转码状态（已有时为undefined，未有SUCCESS，PENDING）
      }, // video传参必选项
      updataVideo: {}, // 更新视频
      goodsId: 0,
      carId: '',
      currentTabId: null, // 视频标签
      count: 0,
      dialogVisible: false, // 蒙层
      showTabs: true, // 展示列表
      loading: false, // loading
      getVideoAllStatus: false, // 是否获取状态
      operationDialogStatus: false, // 移动或删除
      setVideoInterval: null, // 轮询查事件
      updataStatus: false, // 是否更新
      secondTabs: [], // 所有的二级筛选
      videoImgIndex: 0, // 视频封面图位置
      changeStatus: false, // 视频是否发生改变
      eCarId: '' //
    }
  },
  computed: {
    ...mapGetters(['uid'])
  },
  watch: {},
  methods: {
    // 上传标题图片
    async httpRequest(option) {
      option.imageType = 'nowater' // 无水印
      option.quality = 1
      this.$oss.ossUploadImage(option)
    },
    // 活动封面
    onSuccessTitleimage(res) {
      if (!res) return
      this.tabs[this.currentTabId][this.videoImgIndex].imgOrgUrl = res.imgOrgUrl
      this.$forceUpdate()
    },
    handleClickOne(tab, event) {
      // 一级分类下所有二级分类数组
      console.log(tab)
      const me = this
      me.currentAllTabId = String(tab.props.name)
      me.allTabsControl.map(function (value, index) {
        if (me.currentAllTabId === String(value.id)) {
          me.tabsControl = value.subList
        }
      })
      me.currentTabId = String(me.tabsControl[0].id)
    },
    // 开始
    init(e) {
      ;(this.dialogVisible = true),
        (this.tabsControl = e.tabsControl || this.tabsControl),
        (this.tabs = e.tabs || {}),
        (this.currentTabId = String(
          this.tabsControl.length && this.tabsControl[0].id
        )),
        (this.goodsId = e.goodsId),
        (this.carId = e.carId || '')
      this.changeStatus = false
      this.eCarId = this.$route.query.eCarId || ''
      this.gettabsControl()
    },
    // 获取视频标签
    gettabsControl() {
      var me = this
      getVideoPageSign()
        .then(function (response) {
          if (
            response.data.data &&
            response.data.data.length &&
            response.data.code === 0
          ) {
            // const arr = response.data.data.map(function(v) {
            //   return { name: v.videoTab, id: v.id }
            // })
            // me.tabsControl = arr
            // me.currentTabId = me.tabsControl[0].id
            me.allTabsControl = response.data.data
            me.allTabsControl.map(function (value, index) {
              value.subList.map(function (v) {
                me.secondTabs.push(v)
              })
            })
            me.tabsControl = me.allTabsControl[0].subList
            me.currentAllTabId = String(me.allTabsControl[0].id)
            me.currentTabId = String(me.tabsControl[0].id)
          }
        })
        .finally(function () {
          console.log(me.tabs)
          me.tabs.length ? me.getVideo() : me.getVideosList()
        })
    },
    // 获取所有视频
    getVideosList() {
      var me = this
      if (!me.carId) {
        // 新增没有carId,不请求接口
        return
      }
      getVideosList({
        goodsId: me.goodsId,
        carId: me.carId
      })
        .then(function (response) {
          if (response.data.data === null && response.data.code === 0) {
            return me.$message.error('暂无视频信息')
          }
          if (response.data.code === 0) {
            const data = response.data.data
            Object.keys(data).map(function (t) {
              me.tabs[t] = data[t]
            })
            me.setTabList()
            me.refreshImageList()
          }
        })
        .catch(function (o) {
          me.$message.error(o.message)
        })
        .finally(function () {
          me.loading = false
        })
    },
    // 设置tabList数据，目的为了显示一级分类下的数据数量
    setTabList() {
      const me = this
      me.allTabsControl.map(function (value) {
        value.tabList = []
        value.subList.map(function (v) {
          if (me.tabs[v.id]) {
            value.tabList = value.tabList.concat(me.tabs[v.id])
          }
        })
      })
    },
    // 数据排序（已有视频时）
    getVideo() {
      const me = this
      const arr = []
      me.tabs.map(function (value) {
        arr[value.imgCategory]
          ? arr[value.imgCategory].unshift(value)
          : (arr[value.imgCategory] = [value])
      })
      me.tabs = arr
      me.setTabList()
      me.tabsControl = me.allTabsControl[0].subList
      me.currentAllTabId = String(me.allTabsControl[0].id)
      me.currentTabId = String(me.tabsControl[0].id)
      me.updataStatus = true
    },
    // 保存
    save() {
      const me = this
      const errVideo = []
      this.postVideoList.map(function (e) {
        if (e.vodTaskStatus !== undefined && e.vodTaskStatus !== 'SUCCESS') {
          errVideo.push(e)
        }
      })
      if (errVideo.length) {
        // 未传递的视频个数
        let name = ''
        errVideo.map(function (e) {
          name = name ? name + ',' + e.imgDesc : e.imgDesc
        })
        return this.$message.error('还有' + name + '视频正在上传')
      }
      let tabs = deepCopy(this.tabs)
      Object.keys(tabs).map(function (val) {
        // 传递的数据，需要针对数组倒序
        tabs[val].reverse()
      })
      tabs = this.updataStatus ? tabs : [] // 未做操作时，传空
      $emit(this, 'sendData', {
        tabs: tabs
      })
      let arr = []
      Object.values(tabs)?.map((_) => {
        arr = arr.concat(_)
      })
      const postVideoList = arr.length ? JSON.stringify(arr) : ''
      postCarVideoInfo({
        goodsId: this.goodsId,
        carId: this.carId,
        content: postVideoList
      })
        .then(function (response) {
          if (response.data.code === 0) {
            me.dialogVisible = false
            me.$message.success('保存成功')
          }
        })
        .catch(function (o) {
          console.log(o)
        })
    },
    // 添加视频
    addVideo(e) {
      const file = e.target.files
      const me = this
      if (
        file[0].name.indexOf('.mp4') === -1 &&
        file[0].name.indexOf('.mov') === -1 &&
        file[0].name.indexOf('.ts') === -1
      ) {
        return me.setToast('视频格式不是mp4、mov、ts')
      }
      const url = URL.createObjectURL(file[0])
      const video = new Audio(url)
      video.addEventListener('loadedmetadata', function (a) {
        if (a.duration > 300) {
          return me.$message.error('请控制时间在5分钟内')
        }
        me.postVideo(file, me.currentTabId),
          setTimeout(function () {
            e.target.value = ''
          }, 200)
      })
    },
    getVideoFirstFrameAndUpload(videoSrc, name) {
      const me = this
      return new Promise((resolve, reject) => {
        const video = document.createElement('video')
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        // 静音视频，防止播放时发出声音
        video.muted = true
        video.setAttribute('crossOrigin', 'anonymous') //处理跨域
        video.setAttribute('src', videoSrc)
        video.setAttribute('autoplay', 'autoplay')
        video.addEventListener('canplaythrough', () => {
          canvas.width = video.videoWidth
          canvas.height = video.videoHeight
          context.drawImage(video, 0, 0, canvas.width, canvas.height)
          let dataurl = canvas.toDataURL('image/png')
          let arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n)
          while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
          }
          let file = new File([u8arr], name, { type: mime })
          const option = {
            imageType: 'nowater',
            quality: 1,
            file
          }
          me.$oss
            .ossUploadImage(option)
            .then((res) => {
              const imgUrl = res.imgOrgUrl
              resolve(imgUrl)
            })
            .catch((err) => {
              reject(err)
            })
        })
      })
    },
    // 发送视频
    postVideo(file, currentTabId) {
      var me = this
      this.$message.warning('视频正在上传')

      const videoFile = file[0]
      const videoContent = deepCopy(me.videoContent)

      me.loading = true
      return createUploader
        .call(me, videoFile)
        .then((result = {}) => {
          me.getVideoFirstFrameAndUpload(result.videoUrl, videoFile.name).then(
            (res) => {
              me.loading = false
              videoContent.id = result.videoId
              videoContent.videoId = result.videoId
              videoContent.videoUrl = result.videoUrl
              videoContent.duration = result.duration
              videoContent.imgCategory = currentTabId
              videoContent.imgOrgUrl = res

              videoContent.imgDesc = videoFile.name
                ? videoFile.name.split('.mp4')[0].substring(0, 10)
                : '' // 名称
              videoContent.vodTaskId = '' // 转码id
              videoContent.vodTaskStatus = 'SUCCESS' // 转码状态
              me.postVideoList = !me.postVideoList.length
                ? [videoContent]
                : me.postVideoList.concat(videoContent) // 发送数据中增加视频，以便监听
              me.tabs[currentTabId]
                ? me.tabs[currentTabId].unshift(videoContent)
                : (me.tabs[currentTabId] = [videoContent]) // 当前页签下增加视频
              me.setTabList()
              me.refreshImageList() // 更新dom节点
            }
          )
          // const task = function () {
          //   creatingVideoTranscoding(result.videoId)
          //     .then(function (res) {
          //       // 创建视频转码任务
          //       if (res.data.code === 0) {
          //         if (res.data.data === -1) {
          //           // 后端延时2s，会出现返回值未-1情况，需再执行一次
          //           return setTimeout(function () {
          //             task()
          //           }, 3000)
          //         }
          //         videoContent.imgDesc = videoFile.name
          //           ? videoFile.name.split('.mp4')[0].substring(0, 10)
          //           : '' // 名称
          //         videoContent.vodTaskId = res.data.data // 转码id
          //         videoContent.vodTaskStatus = 'WAITING' // 转码状态
          //         me.postVideoList = !me.postVideoList.length
          //           ? [videoContent]
          //           : me.postVideoList.concat(videoContent) // 发送数据中增加视频，以便监听
          //         me.tabs[currentTabId]
          //           ? me.tabs[currentTabId].unshift(videoContent)
          //           : (me.tabs[currentTabId] = [videoContent]) // 当前页签下增加视频
          //         me.setTabList()
          //         me.refreshImageList() // 更新dom节点
          //         me.getVideoAll() // 执行轮询
          //       } else {
          //         me.$message.error(
          //           videoFile.name + '视频上传失败，请删除后重新上传'
          //         )
          //       }
          //     })
          //     .catch(function (o) {
          //       me.$message.error(
          //         videoFile.name + '该视频上传失败，请删除后重新上传'
          //       )
          //     })
          // }
          // task()
        })
        .catch((_) => {
          me.loading = false
          me.setMessageBoxNoCancel(_)
        })
    },
    // 轮训获取视频
    getVideoAll() {
      const me = this
      // 有视频则开始开始执行轮询查视频
      me.postVideoList &&
        me.postVideoList.length &&
        (me.getVideoAllStatus ||
          ((me.getVideoAllStatus = true),
          (me.setVideoInterval = setInterval(function () {
            var index = 0
            me.postVideoList.map(function (val) {
              if (val.vodTaskStatus === 'CompleteAllSucc') {
                index++
                me.getVideoContent(val)
              }
              if (
                val.vodTaskStatus !== 'SUCCESS' &&
                val.vodTaskStatus !== undefined
              ) {
                index++
                me.getVideoStatus(val)
              }
            })
            if (!index) {
              // 所以视频都为空，关闭轮询
              clearInterval(me.setVideoInterval)
              me.postVideoList = []
              me.setVideoInterval = null
              me.getVideoAllStatus = false
            }
          }, 2000))))
    },
    // 获取转码后视频
    getVideoContent(video) {
      var me = this
      getVideoMes(video.id)
        .then(function (res) {
          if (res.data.code === 0 && res.data.data !== null) {
            video.duration = res.data.data.duration // 视频时长
            video.videoUrl = res.data.data.sourceVideoUrl // 视频播放地址
            video.imgOrgUrl = res.data.data.coverUrl // 视频封面
            video.videoSize = res.data.data.mSize // 视频大小
            video.videoPixel = res.data.data.height + 'x' + res.data.data.width // 视频宽高
            video.vodTaskStatus = 'SUCCESS' // 视频状态
            me.delVideo(me.postVideoList, video)
            me.updataStatus = true
          }
        })
        .catch(function (res) {
          console.log(res),
            me.$message.error(res.message || '视频获取失败，请删除后重新上传')
        })
    },
    // 获取转码状态
    getVideoStatus(video) {
      var me = this
      video.vodTaskId &&
        getVideoTaskStatus(video.vodTaskId)
          .then(function (res) {
            video.vodTaskStatus = res.data.data
            if (res.data.data === 'CompleteAllSucc') {
              // 转码成功，获取视频转码成功后信息
              me.getVideoContent(video)
            }
          })
          .catch(function (res) {
            console.log(res),
              me.$message.error(res.message || '视频转码失败，请删除后重新上传')
          })
    },
    // 删除视频（列表）
    delVideo(allData, data) {
      const index = allData.findIndex(function (e) {
        return data.vodTaskStatus !== undefined &&
          data.vodTaskStatus !== 'SUCCESS'
          ? e.vodTaskId === data.vodTaskId
          : e.videoUrl === data.videoUrl
      })
      allData.splice(index, 1)
      this.changeStatus = true
      this.refreshImageList()
    },
    // 删除视频
    deleteVideo(data) {
      const me = this
      me.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(function () {
          delVideo(data)
        })
        .catch(function (res) {
          me.$message({
            type: 'info',
            message: (res && res.message) || '已取消删除'
          })
        })
      function delVideo(video) {
        // if (
        //   video.vodTaskStatus !== undefined &&
        //   video.vodTaskStatus !== 'SUCCESS'
        // ) {
        //   // 还未转码成功视频，直接删除（tab列表，发送列表）
        //   me.delVideo(me.postVideoList, video)
        //   return me.delVideo(me.tabs[me.currentTabId], video)
        // }
        // 还未转码成功视频，直接删除（tab列表，发送列表）
        me.delVideo(me.postVideoList, video)
        return me.delVideo(me.tabs[me.currentTabId], video)
        // deleteImageV3({
        //   // 已成功视频，需要接口删除
        //   fileIds: video.imgOrgUrl || video.videoUrl,
        //   imageIds: video.imgId
        //   // goodId: me.goodsId,
        //   // carId: me.carId
        // })
        //   .then(function (res) {
        //     res.data.code === 0
        //       ? (me.delVideo(me.tabs[me.currentTabId], video),
        //         me.$message.success('删除成功'))
        //       : me.$message.error(res.data.msg)
        //     me.setTabList()
        //   })
        //   .catch(function (e) {
        //     me.$message.error(e.message)
        //   })
      }
    },
    // 查看视频
    seeVideoPlay(data) {
      if (!data.videoUrl) {
        return this.$message.error('暂无视频播放地址')
      }
      this.$refs.ChooseVideo.init(data.videoUrl)
    },
    // 确认操作
    confirmOperation(data) {
      if (!data.type) {
        return (this.operationDialogStatus = false)
      }
      if (data.type === '1') {
        // 删除
        this.deleteVideo(data)
      } else if (
        data.type === '2' &&
        data.tabsId &&
        data.tabsId !== data.imgCategory
      ) {
        // 移动，且tabid不是当前
        const id = data.tabsId
        const videoData = deepCopy(data)
        videoData.imgCategory = videoData.tabsId // 切换视频标签
        delete videoData.tabsId
        delete videoData.type // 将tbasId（移动id）, type(操作类型)，删除以便下次使用
        this.delVideo(this.tabs[this.currentTabId], data)
        this.tabs[id]
          ? this.tabs[id].unshift(videoData)
          : (this.tabs[id] = [videoData])
        this.setTabList()
        this.updataStatus = true
      }
      this.operationDialogStatus = false
      this.refreshImageList()
    },
    // 取消操作
    cancalOperation(data) {
      delete data.type
      delete data.tabsId
      this.operationDialogStatus = false
    },
    // 排序
    handleClick(tab, event) {
      console.log(this.tabs[this.currentTabId]), this.refreshImageList()
    },
    // 排序
    refreshImageList() {
      var me = this
      ;(me.showTabs = false),
        clearTimeout(me.time),
        (me.time = setTimeout(function () {
          const arr = me.tabs
          ;(me.tabs = {}),
            (me.tabs = arr),
            (me.showTabs = true),
            (me.updataStatus = true)
        }, 150))
    },
    // 退出
    handleClose() {
      const me = this
      if (this.changeStatus) {
        ElMessageBox({
          title: '提示',
          message: '当前已删除的视频未保存!',
          type: 'warning',
          showCancelButton: true,
          confirmButtonText: '继续保存',
          cancelButtonText: '取消并退出',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              this.save()
              me.tabs = []
              clearInterval(me.setVideoInterval)
              me.postVideoList = []
              me.setVideoInterval = null
              me.dialogVisible = false
              done()
            } else {
              this.dialogVisible = false
              done()
            }
          }
        })
        this.isDel = false
      } else {
        me.tabs = []
        clearInterval(me.setVideoInterval)
        me.postVideoList = []
        me.setVideoInterval = null
        me.dialogVisible = false
        this.dialogVisible = false
      }
    },
    setMessageBoxNoCancel(content) {
      this.$confirm(content, '提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'warning',
        center: true
      })
    }
  },
  emits: ['sendData']
}
</script>

<style lang="scss">
.video-dialog {
  .video-head {
    margin-bottom: 10px;
    .input-content {
      position: relative;
      .imageFile {
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        display: inline-block;
        width: 100%;
        height: 100%;
        z-index: 2;
      }
    }
  }
  .video-show-content {
    display: inline-block;
    width: 200px;
    margin-right: 10px;
    position: relative;
    .video-img {
      display: inline-block;
      width: 200px;
      height: 150px;
      object-fit: cover;
    }
    .right-icon {
      position: absolute;
      right: 0;
      top: 0;
      padding: 5px;
      background-color: #fff;
    }
    .play-icon {
      position: absolute;
      left: 55px;
      top: 30px;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 5px;
    }
    .input-content {
      width: 100%;
      display: inline-block;
      height: 30px;
      border: 1px solid #e4e7ed;
      padding: 0 5px;
    }
  }
}
</style>
