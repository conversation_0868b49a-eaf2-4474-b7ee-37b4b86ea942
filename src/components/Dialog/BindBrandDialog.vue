<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      :close-on-click-modal="false"
      center
      class="choose-dialog"
      append-to-body
    >
      <el-select v-model="type">
        <el-option label="官方授权渠道" value="1"> </el-option>
        <el-option label="非官方授权渠道" value="2"> </el-option>
      </el-select>
      <el-select
        v-if="needInputBrand"
        v-model="superBrandName"
        :remote-method="searchBrandList"
        :loading="brandloading"
        placeholder="请输入上级品牌名称"
        filterable
        remote
        clearable
        style="width: 200px"
      >
        <el-option
          v-for="item in brandList"
          :key="item.brandId"
          :label="`${item.brandName}(${item.brandId})`"
          :value="item.brandName"
        />
      </el-select>
      <el-input
        v-else
        v-model="superBrandName"
        readonly
        style="width: 200px"
      ></el-input>
      <el-select
        v-show="type === '1'"
        v-model="superShopName"
        :remote-method="remoteMethodShop"
        :loading="shopLoading"
        placeholder="请输入上级经销商名称（非必填）"
        filterable
        remote
        clearable
        style="width: 260px"
      >
        <el-option
          v-for="item in shopList"
          :key="item.shopId"
          :label="`${item.shopName}(${item.shopId})`"
          :value="item.shopName"
        />
      </el-select>
      <div class="dialog-content center footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import agency from '@/api/agency'
import { debounce } from 'lodash-es'
import { searchBrand } from '@/api/articleModule'
import { GetShopApplyByName } from '@/api/garage'

export default {
  name: 'BindBrandDialog',
  props: {
    uid: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      title: '',
      type: '1',
      superShopName: '',
      superBrandName: '',
      shopId: '',
      dialogVisible: false,
      isAddBrand: false,
      brandList: [],
      brandloading: false,
      shopList: [],
      shopLoading: false,
      needInputBrand: true,
    }
  },
  methods: {
    // 重置状态
    init(params) {
      this.dialogVisible = true
      this.title = params.title
      this.shopId = params.shopId
      this.isAddBrand = params.isAddBrand
      this.superShopName = ''
      this.superBrandName = ''
      this.type = '1'
      this.brandList = []
      this.brandloading = false
      this.shopList = []
      this.shopLoading = false
      this.needInputBrand = !params.brandName
      if (!this.needInputBrand) {
        this.superBrandName = params.brandName
      }
    },
    searchBrandList(query) {
      this.brandloading = true
      this.getBrandList(query.trim())
    },
    remoteMethodShop(query) {
      this.shopLoading = true
      this.getShopList(query.trim())
    },
    // 获取品牌列表
    getBrandList: debounce(function (name) {
      searchBrand({
        name,
        page: 1,
        limit: 20,
      })
        .then((response) => {
          if (response.data.code === 0) {
            const result = response.data.data && response.data.data.listData
            this.brandList = [...result]
          }
        })
        .finally((_) => {
          this.brandloading = false
        })
    }, 600),
    // 查询经销商
    getShopList: debounce(function (name) {
      GetShopApplyByName({
        shopName: name, // 经销商名称
        limit: 20,
      })
        .then((response) => {
          if (response.data.code === 0) {
            const result = response.data.data
            this.shopList = result
          }
        })
        .finally((_) => {
          this.shopLoading = false
        })
    }, 600),
    // 关闭
    handleClose() {
      this.dialogVisible = false
    },
    // 确认
    confirm() {
      if (this.superBrandName === '') {
        return this.$message.error('请输入上级品牌名称')
      }
      if (this.isAddBrand) {
        agency
          .addShopBrandRel({
            shopId: this.shopId,
            superShopName: this.superShopName,
            superBrandName: this.superBrandName,
            type: this.type,
            flag:
              this.uid === 36 ||
              this.uid === 45 ||
              this.uid === 115 ||
              this.uid === 1
                ? 1
                : 0, // uid=36（伏星宇） uid=45（王长文）uid=115（刘敏）
          })
          .then(({ data }) => {
            if (data.data && data.data.length) {
              $emit(this, 'add-brand', data.data, this.superBrandName)
              this.dialogVisible = false
            }
          })
          .catch((err) => {
            console.log(err)
          })
      } else {
        $emit(this, 'save', {
          shopId: this.shopId,
          superShopName: this.superShopName,
          superBrandName: this.superBrandName,
          type: this.type,
        })
      }
    },
  },
  emits: ['add-brand', 'save'],
}
</script>

<style lang="scss">
.choose-dialog {
  .el-dialog--center .el-dialog__body {
    padding: 15px;
  }
  .el-dialog.el-dialog--center {
    margin-top: 10vh !important;
  }
  .el-table__header-wrapper .check-all .cell {
    .el-checkbox {
      display: none;
    }
    &::after {
      content: '选择';
    }
  }
}
</style>

<style lang="scss" scoped>
.choose-dialog {
  .dialog-content {
    padding: 30px 0 10px;
  }
}
</style>
