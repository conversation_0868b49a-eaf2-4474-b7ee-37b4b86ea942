<template>
  <div>
    <template v-if="dialogVisible">
      <viewer :images="[imgUrl]" :options="options" class="choose-show-item">
        <img
          :src="imgUrl"
          class="el-upload-list__item-thumbnail choose-show-img"
          alt=""
        />
      </viewer>
    </template>
  </div>
</template>

<script>
export default {
  name: 'ChooseShowImage',
  props: {},
  data() {
    return {
      dialogVisible: false,
      imgUrl: '',
      options: {
        navbar: false,
        transition: false, // 动画效果
        toolbar: {
          zoomIn: 1, // 放大
          zoomOut: 1, // 缩小
          prev: 0, // 上一张
          play: 0, // 播放（全屏幕）
          next: 0, // 下一张
          rotateLeft: 1, // 左旋转
          rotateRight: 1, // 右旋转
          oneToOne: 1, // 1:1 比例
          reset: 1, // 重置
          flipHorizontal: 0, // 上翻转
          flipVertical: 0 // 下翻转
        },
        zIndex: 9999
      }
    }
  },
  methods: {
    init(link) {
      const me = this
      me.dialogVisible = true
      me.imgUrl = link
      me.$nextTick(() => {
        const img = document.querySelector('.choose-show-img')
        img.click()
        me.executeClick()
      })
    },
    executeClick() {
      document.addEventListener('click', this.eventClick)
    },
    eventClick(e) {
      const name = e.target.className
      const flag =
        name.indexOf('viewer-canvas') > -1 || name.indexOf('viewer-close') > -1
      if (flag) {
        this.dialogVisible = false
        document.removeEventListener('click', this.eventClick)
      }
    },
    // 处理图片 获取图片宽高
    async editPhoto(arr) {
      const imgData = []
      const p = []
      arr.forEach((img) => {
        let p1 = new Promise((resolve) => {
          let imgUpload = new Image()
          imgUpload.src = img
          imgUpload.onload = () => {
            imgData.push({
              w: imgUpload.width || 0,
              h: imgUpload.height || 0,
              imageUrl: img
            })
            resolve(true)
          }
        })
        p.push(p1)
      })
      Promise.all(p).then(() => {
        this.canvasDrawImage(imgData)
      })
    },
    // 把多张图拼接成一张
    canvasDrawImage(imgData) {
      if (!imgData.length) return
      let width = 50
      let height = 50
      // 获取最大的宽度和所有的高度
      imgData.forEach((img) => {
        width = img.w > width ? img.w : width
        height = height + img.h
      })

      // 初始化canvas
      let canvas = document.createElement('canvas')
      let context = canvas.getContext('2d')
      canvas.width = width
      canvas.height = height

      // 绘制矩形添加白色背景色
      context.rect(0, 0, width, height)
      context.fillStyle = '#fff'
      context.fill()

      // 多图绘制，下一张的y应该是上一张的高度
      let beforeHeight = 0
      let p = []
      imgData.forEach((img) => {
        if (img.imageUrl) {
          // 因为图片加载会有延迟，因此使用promise 保证合并图片的正确性
          let p1 = new Promise((resolve) => {
            let imgUpload = new Image()
            // 防止跨域
            imgUpload.setAttribute('crossOrigin', 'anonymous')
            imgUpload.src =
              img.imageUrl.indexOf('?') > -1
                ? img.imageUrl + '&v=' + Math.random()
                : img.imageUrl + '?v=' + Math.random()
            imgUpload.onload = function () {
              context.drawImage(imgUpload, 0, beforeHeight, img.w, img.h)
              beforeHeight = beforeHeight + img.h
              resolve(true)
            }
          })
          p.push(p1)
        }
      })

      Promise.all(p).then(() => {
        try {
          let img = new Image()
          img.setAttribute('crossOrigin', 'anonymous')
          let src = canvas.toDataURL('image/png')
          this.init(src)
        } catch (err) {
          console.log(err)
        }
      })
    }
  }
}
</script>

<style lang="scss">
.choose-show-item {
  display: none;
}
.viewer-backdrop {
  .viewer-toolbar {
    ul {
      li {
        width: 30px;
        height: 30px;
      }
      li::before {
        margin: 4px 5px;
      }
      //   .viewer-rotate-left {
      //     position: relative;
      //     top: -3px;
      //     height: 36px;
      //     width: 36px;
      //   }
      //   .viewer-rotate-left::before {
      //     margin: 8px;
      //   }
      //   .viewer-rotate-right {
      //     position: relative;
      //     top: -3px;
      //     height: 36px;
      //     width: 36px;
      //   }
      //   .viewer-rotate-right::before {
      //     margin: 8px;
      //   }
    }
  }
}
</style>
