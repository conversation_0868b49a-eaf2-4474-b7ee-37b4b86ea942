<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="预览区域"
      center
      class="choose-iframe"
      append-to-body
    >
      <div class="flex">
        <div class="iframe-content">
          <p style="text-align: center">
            <el-button type="danger" @click="handleClose()">关闭预览</el-button>
            <el-button type="primary" @click="openWindow()"
              >新窗口打开</el-button
            >
            <el-button type="primary" @click="seeQrCode()"
              >查看二维码</el-button
            >
            <span v-if="showSwitch"
              >&ensp;&ensp;车辆测评置顶&ensp;
              <el-switch v-model="isSwitchStatus" @change="changeSwitch()" />
            </span>
          </p>
          <iframe
            allowfullscreen
            v-show="url"
            :src="url"
            frameborder="0"
            class="iframe"
          />
        </div>
        <slot name="right"></slot>
        <choose-qr-code ref="qrCode" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ChooseQrCode from '@/components/Dialog/ChooseQrCode.vue'
import { evaluationStick } from '@/api/article'
export default {
  name: 'DialogChooseIframe',
  components: {
    ChooseQrCode,
  },
  data() {
    return {
      dialogVisible: false,
      showSwitch: false, // 是否展示按钮
      isSwitchStatus: false, // 按钮状态
      id: '',
      url: '',
    }
  },
  methods: {
    init(url, switchData = {}) {
      this.dialogVisible = true
      this.showSwitch = switchData.showSwitch || false
      this.isSwitchStatus = switchData.switchStatus || false
      this.id = switchData.id || ''
      this.url = url
    },
    // 新窗口打开
    openWindow() {
      window.open(this.url)
    },
    // 查看二维码
    seeQrCode() {
      this.$refs.qrCode.init(this.url)
    },
    // 变更置顶
    changeSwitch() {
      const me = this
      evaluationStick({
        essayId: me.id,
        stickStatus: me.isSwitchStatus ? '1' : '0',
      }).then((response) => {
        if (response.data.code === 0) {
          me.$message.success('置顶成功，请重新筛选列表')
        } else {
          me.$message.error(response.data.msg)
        }
      })
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
      this.url = ''
    },
  },
}
</script>

<style lang="scss" scoped>
.choose-iframe {
  .flex {
    justify-content: center;
  }
  .iframe-content {
    width: 400px !important;
    height: auto !important;
    overflow-y: hidden;
  }
  .iframe {
    width: 380px;
    height: 600px;
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 5px;
    margin: 0 auto;
    display: block;
  }
}
</style>
