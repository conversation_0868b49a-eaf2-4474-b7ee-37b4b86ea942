<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="视频播放地址"
      center
      class="choose-video"
      append-to-body
    >
      <video :src="videoUrl" controls="controls" class="video"></video>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ChooseVideo',
  components: {},
  data() {
    return {
      dialogVisible: false,
      videoUrl: '',
    }
  },
  methods: {
    init(url) {
      this.dialogVisible = true
      this.videoUrl = url
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
      this.videoUrl = ''
    },
  },
}
</script>

<style lang="scss">
.choose-video {
  .video {
    width: 400px;
    height: 600px;
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 5px;
    margin: 0 auto;
    display: block;
  }
}
</style>
