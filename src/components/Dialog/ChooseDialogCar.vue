<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="beforreHandleClose"
      title="选择车辆"
      center
      class="choose-dialog"
      width="800px"
      append-to-body
    >
      <div class="choose-labels">
        <p v-for="(label, index) in labels" :key="index" class="box">
          {{ label.goodName }}
          <span @click="checkAction(label)">删除</span>
        </p>
      </div>

      <el-form inline style="margin-top: 20px">
        <el-form-item label="车型ID" prop="goodsId">
          <el-input
            v-model="ruleForm.goodsId"
            placeholder="请输入车型Id"
            clearable
            style="width: 130px"
          />
        </el-form-item>
        <el-form-item label="款型ID" prop="carId">
          <el-input
            v-model="ruleForm.carId"
            placeholder="请输入款型Id"
            clearable
            style="width: 130px"
          />
        </el-form-item>
        <el-form-item label="车型名称" prop="name">
          <el-input
            v-model="ruleForm.name"
            placeholder="请输入车型名称"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="车型品牌" prop="brand">
          <el-select
            v-model="ruleForm.brand"
            :remote-method="getBrands"
            :loading="false"
            placeholder="请输入车型品牌"
            filterable
            remote
            clearable
            @change="addLabel"
          >
            <el-option
              v-for="item in brandList"
              :key="item.labelId"
              :label="item.labelName"
              :value="item.labelName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属车系" prop="series">
          <el-select
            v-model="ruleForm.seriesName"
            placeholder="请选择所属车系"
            clearable
            readonly
            @change="addSeriesLabel"
          >
            <el-option
              v-for="item in seriesList"
              :key="item.seriesId"
              :label="item.seriesName"
              :value="item.seriesName"
            />
          </el-select>
        </el-form-item>
        <el-button type="primary" @click="currentChange(1)">查询</el-button>
      </el-form>

      <div class="list">
        <el-table
          ref="multipleTable"
          :data="dataList"
          row-key="multipleTable"
          border
          style="width: 100%; overflow-y: auto; height: 55vh"
          @row-click="checkAction"
          @select="handleSelect"
        >
          <el-table-column
            v-if="!isAlone"
            type="selection"
            label="选择"
            width="70"
            class-name="check-all"
          />
          <el-table-column prop="goodId" label="车辆ID" align="center" />
          <el-table-column prop="brandName" label="品牌名称" align="center" />
          <el-table-column prop="goodName" label="车辆名称" align="center" />
          <el-table-column label="价格" align="center">
            <template v-slot="scope">
              <span>￥{{ scope.row.minPrice.replace('.00', '') }}</span>
              <span v-if="scope.row.minPrice !== scope.row.maxPrice"
                >-{{ scope.row.maxPrice.replace('.00', '') }}</span
              >
              <!-- {{ scope.row.minPrice }} - {{ scope.row.maxPrice }} -->
            </template>
          </el-table-column>
          <el-table-column prop="saleStatus" label="状态" align="center">
            <template v-slot="scope">
              <span>{{ onSaleEnumAll[scope.row.saleStatus] }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" align="center" label="创建时间">
            <template v-slot="scope">{{
              $filters.timeFullS(scope.row.createTime)
            }}</template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        v-model:current-page="page"
        :page-size="20"
        :page-sizes="[10, 20, 40, 60]"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        style="text-align: center; margin-top: 10px"
        @size-change="currentChange"
        @current-change="currentChange"
      />
      <div class="dialog-content center footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { $emit } from '../../utils/gogocodeTransfer'
import { searchCarList, editShopGoods } from '@/api/garage'
import { onSaleEnum, onSaleEnumAll } from '@/utils/enum'
import { convertKeyValueEnum } from '@/utils/convert'
import { searchBrand, getSeriesList } from '@/api/articleModule'

export default {
  name: 'ChooseDialogCar',
  props: {
    isAlone: {
      typeOf: Boolean,
      default: false
    }
  },
  data() {
    return {
      isOnSaleList: onSaleEnum,
      onSaleEnumAll: convertKeyValueEnum(onSaleEnumAll),
      total: 0,
      page: 1,
      selectLimit: 9999, // 限制最大选择数
      brandId: 0, // 品牌id
      dataList: [], // 显示数据
      searchValue: '',
      dialogVisible: false,
      needEditShop: false, // 是否需要编辑店铺
      labels: [], // 已选中标签
      ruleForm: {
        goodsId: '',
        carId: '',
        name: '',
        brandId: '', // 车型品牌
        seriesName: '', // 车系名称
        seriesId: '' // 车系Id
      },
      brandList: [], // 搜索车型品牌列表
      seriesList: [
        {
          seriesId: 0,
          seriesName: '无'
        }
      ] // 车系列表
    }
  },
  methods: {
    // 重置状态
    init(params = {}) {
      this.searchValue = ''
      Object.keys(this.ruleForm).map((key) => {
        this.ruleForm[key] = ''
      })
      if (params.labels && params.labels.length) {
        this.labels = [...params.labels]
      } else {
        this.labels = []
      }
      this.page = 1
      this.brandId = params.brandId
      this.needEditShop = !!params.needEditShop
      this.selectLimit = params.selectLimit || this.selectLimit
      this.seriesList = [
        {
          seriesId: 0,
          seriesName: '无'
        }
      ]
      this.dialogVisible = true
      this.getList()
      this.getBrands()
    },
    // 获取列表
    getList() {
      const me = this
      searchCarList({
        ...this.ruleForm,
        isOnStatus: 1,
        page: me.page,
        limit: 20
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.dataList = response.data.data.list
            me.total = response.data.data.total
            me.initChecks('upDataLable')
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch(() => {})
        .finally(() => {})
    },
    // 更新页码
    currentChange(page) {
      this.page = page
      this.getList()
    },
    checkLabel(target) {
      const me = this

      let list = []
      const tempArr = me.labels.filter(
        (_) => (_.goodsId || _.goodId) === (target.goodId || target.goodsId)
      )
      const exist = tempArr.length > 0
      if (exist) {
        // 已存在，删除
        list = me.labels.filter(
          (_) => (_.goodsId || _.goodId) !== (target.goodId || target.goodsId)
        )
      } else {
        // 不存在，添加
        list = me.labels
        me.labels.length && me.isAlone ? null : list.splice(0, 0, target)
      }
      me.labels = list
    },
    // 选中或删除
    checkAction(item) {
      const me = this
      // 是否未选择标签
      const unCheck =
        me.labels.filter(
          (_) => (_.goodsId || _.goodId) === (item.goodId || item.goodsId)
        ).length === 0
      let index = 0
      me.dataList.map((value, dIndex) => {
        if (value.goodId === (item.goodId || item.goodsId)) {
          index = dIndex
        }
      })
      me.$refs.multipleTable.toggleRowSelection(me.dataList[index], unCheck)
      me.checkLabel(item)
    },
    // 单选
    handleSelect(list, target) {
      this.checkLabel(target)
    },
    initChecks(type) {
      const me = this
      let tempArr = []
      me.$nextTick(() => {
        me.dataList.map((value) => {
          // 是否已选择标签
          const check = me.labels.some(
            (_) => (_.goodsId || _.goodId) === value.goodId
          )
          tempArr = check ? tempArr.concat(value) : tempArr
          me.$refs.multipleTable.toggleRowSelection(value, check)
        })
      })
    },
    beforreHandleClose(done) {
      done()
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
      // console.log(this.labels)
      // if (this.labels.length === 0) {
      //   this.dialogVisible = false
      //   return false
      // }
      // this.$confirm('确认关闭？')
      //   .then(_ => {
      //     this.dialogVisible = false
      //   })
      //   .catch(_ => { })
    },
    // 确认
    confirm() {
      const me = this

      if (!me.needEditShop) {
        const result = [...me.labels]

        // 限制返回的长度
        result.length =
          result.length > me.selectLimit ? me.selectLimit : result.length

        console.log(result)
        $emit(me, 'sendCarData', result)
        me.dialogVisible = false
        return
      }

      const goodsId = []
      const goodsName = []
      me.labels.map(function (value) {
        goodsId.push(value.goodId)
        goodsName.push(value.goodName)
      })

      // 不建议放组件处理，历史代码，仅做兼容处理
      editShopGoods({
        brandId: me.brandId,
        goodsIds: goodsId.join(),
        shopId: me.$route.query.id
      })
        .then((response) => {
          if (response.data.code !== 0) {
            return me.$message.error(response.data.msg || '添加品牌车辆失败')
          }
          $emit(me, 'sendCarData', {
            goodsIds: goodsId.join(),
            goodsName: goodsName.join(),
            brandId: me.brandId,
            number: goodsId.length
          })
          me.dialogVisible = false
          console.log(this.labels)
        })
        .catch((err) => {
          me.$message.error(err.message || '添加品牌车辆失败')
        })
    },
    // 选择品牌
    addLabel(brandName) {
      const me = this
      const brand = me.brandList.filter((_) => _.labelName === brandName)
      me.ruleForm.brandId = (brand[0] && brand[0].labelId) || ''
      me.ruleForm.brand = brandName

      // 切换品牌后重置车系
      me.ruleForm.seriesName = ''
      me.ruleForm.seriesId = ''
      me.seriesList = [
        {
          seriesId: 0,
          seriesName: '无'
        }
      ] // 车系列表

      me.getSeriesList()
    },
    getBrands(query) {
      this.$tools.debounce(() => this.getBrandList(query), 300)()
    },
    // 获取品牌列表
    getBrandList(query = '') {
      const me = this
      searchBrand({
        name: query || me.searchValue,
        page: 1,
        limit: 10
      }).then((response) => {
        if (response.data.code === 0) {
          const brandList = []
          const result = response.data.data && response.data.data.listData
          result.map(function (value) {
            const newObj = {
              labelName: value.brandName,
              labelId: value.brandId
            }
            brandList.push(newObj)
            me.brandList = brandList
          })
        }
      })
    },
    // 获取品牌的车系列表
    getSeriesList() {
      const me = this
      getSeriesList({
        brandId: me.ruleForm.brandId
      }).then((response) => {
        if (response.data.code === 0) {
          me.seriesList = [
            {
              seriesId: 0,
              seriesName: '无'
            },
            ...response.data.data.list
          ]
        }
      })
    },
    addSeriesLabel(seriesName) {
      const me = this
      const series = me.seriesList.filter((_) => _.seriesName === seriesName)
      me.ruleForm.seriesId = series[0] && series[0].seriesId
      me.ruleForm.seriesName = seriesName
    }
  },
  emits: ['sendCarData']
}
</script>

<style lang="scss">
.choose-dialog {
  .el-dialog--center .el-dialog__body {
    padding: 15px;
  }
  .el-dialog.el-dialog--center {
    margin-top: 10vh !important;
  }
  .el-table__header-wrapper .check-all .cell {
    .el-checkbox {
      display: none;
    }
    &::after {
      content: '选择';
    }
  }
}
</style>

<style lang="scss" scoped>
.choose-dialog {
  .choose-labels {
    border-bottom: 1px solid #ddd;
    max-height: 100px;
    min-height: 35px;
    overflow-y: auto;
    .box {
      margin: 0 10px 10px 0;
      line-height: 24px;
      height: 24px;
      border: 1px solid #ddd;
      padding-left: 5px;
      display: inline-block;
      color: #ffffff;
      background: #9a7b49;
      span {
        text-decoration: underline;
        cursor: pointer;
        border-left: 1px solid #ddd;
        padding: 0 3px;
        display: inline-block;
        height: 100%;
        background: #f56c6c;
        float: right;
        margin-left: 5px;
      }
    }
  }
  .list {
    max-height: 70vh;
    .block-color {
      width: 100px;
      height: 50px;
      border-radius: 5px;
      margin: 0 auto;
    }
  }
  .dialog-content {
    padding: 10px 0;
  }
}
</style>
