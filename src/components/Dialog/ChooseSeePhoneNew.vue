<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :title="`查看具体${typeList[ruleForm.type]}号码`"
      center
      class="choose-dialog"
      width="600px"
      append-to-body
    >
      <el-form
        ref="phoneForm"
        :model="phoneData"
        label-width="100px"
        class="detail-entry"
      >
        <el-form-item :label="`${typeList[ruleForm.type]}号码`" required>
          <el-input v-focus v-model="phoneData.phone" type="text" readonly />
        </el-form-item>
      </el-form>
      <div class="dialog-content center footer">
        <el-button type="primary" @click="handleClose">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { commonDecryptLog } from '@/api/user'
export default {
  name: 'ChooseSeePhoneNew',
  props: {},
  data() {
    return {
      dialogVisible: false,
      phoneData: {
        phone: '',
      },
      ruleForm: {
        decryptContent: '', // 需要解密的号码
        source: '', // 来源
        type: 1, // 1手机号解密，2微信号解密，3身份证解密
        logModule: '', // 16 电话记录,17询价记录,18一口价记录,19,经销商资料审核详情,20资质审核详情,21商家入驻审核详情,22经销商详情,23试驾记录
        shopStatus: '', // 是否是经销商解密   1为用户，商家可以传空
      },
      typeList: {
        1: '手机',
        2: '微信',
        3: '身份证',
      },
    }
  },
  computed: {},
  methods: {
    init(data) {
      const me = this
      me.ruleForm = {
        ...me.ruleForm,
        ...data,
      }
      me.dialogVisible = true
      me.getNumber()
    },
    getNumber() {
      const me = this
      commonDecryptLog(me.ruleForm)
        .then((response) => {
          if (response.data.code === 0) {
            me.phoneData.phone = response.data.data
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
        })
    },
    // 关闭
    handleClose() {
      this.phoneData.phone = ''
      this.dialogVisible = false
    },
  },
}
</script>
