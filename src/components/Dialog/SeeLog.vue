<template>
  <el-dialog title="日志" v-model="dialogVisible" width="600px">
    <div v-loading="loading">
      <el-table :data="auditLogData" border>
        <el-table-column
          label="操作时间"
          prop="operateDate"
          align="center"
          width="200"
        />
        <el-table-column label="操作人" prop="userName" align="center" />
        <el-table-column label="操作类型" prop="operateType" align="center" />
        <el-table-column label="操作结果" prop="remark" align="center" />
      </el-table>
      <el-pagination
        v-model:current-page="page"
        :page-size="20"
        :page-sizes="[10, 20, 40, 60]"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        @size-change="currentChange"
        @current-change="currentChange"
      />
    </div>
  </el-dialog>
</template>

<script>
import { getQueryEassyOperateLog } from '@/api/articleModule'
export default {
  name: 'ChooseSeeLog',
  props: {},
  data() {
    return {
      dialogVisible: false,
      loading: false,
      auditLogData: [],
      type: 0, //0文章 、1评论
      page: 1,
      total: 0,
    }
  },
  methods: {
    init(id, type) {
      const me = this
      me.page = 1
      me.total = 0
      me.id = id
      me.auditLogData = []
      me.dialogVisible = true
      me.type = type || 0
      me.getData()
    },
    getData() {
      const me = this
      me.loading = true
      getQueryEassyOperateLog({
        essayId: me.id,
        pageSize: 20,
        pageNum: me.page,
        type: me.type,
      }).then((res) => {
        if (res.data.code === 0) {
          this.auditLogData = res.data.data.listData || []
          this.total = res.data.data.total || 0
        } else {
          this.$message.error('获取日志信息失败')
        }
        this.loading = false
      })
    },
    currentChange(page) {
      this.page = page
      this.getData()
    },
  },
}
</script>
