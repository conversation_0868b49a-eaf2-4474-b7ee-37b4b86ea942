import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import { v4 as uuidv4 } from 'uuid'
import {
  addModeGroup,
  delModeGroup,
  getModeGroup,
  updateModelGroup,
  sortModelGroup
} from '@/api/garage'
import { useGroupStore } from '.'
import { IColorGroup, IColorImage } from '../types/imageDialog'
import { useImageTool } from '../hooks/useImage'

export const useRideStore = defineStore('image', () => {
  const rootStore = useGroupStore()

  const groupList = ref<IColorGroup[]>([])

  // 图片相关操作
  const {
    checkImgAll,
    checkSingleImage,
    addSingleImg,
    batchAddImg,
    batchDelImg,
    syncImgInfo,
    hasCheckImg,
    checkAllGroupsImg,
    imgList
  } = useImageTool(groupList, rootStore)

  const getGroupList = async (carId: string, cache = false) => {
    try {
      const res = await getModeGroup({ carId })
      if (res.data.code === 0) {
        let data: IColorGroup[] = res.data.data
        data = data.map((item) => {
          item.isExpand = false
          item.imgList = []
          const sameGroup = groupList.value.find(
            (group) => group.id === item.id
          )
          return cache ? sameGroup || item : item
        })
        groupList.value = data
      }
    } catch (error) {
      console.log(error)
    }
  }

  const addRideGroup = async (goodsId: string, carId: string) => {
    try {
      const res = await addModeGroup({
        goodsId,
        carId,
        modelHeight: ''
      })
      if (res.data.code === 0) {
        getGroupList(carId, true)
      }
    } catch (error) {
      console.log(error)
    }
  }
  const removeGroup = async (id: number, index: number) => {
    try {
      const res = await delModeGroup({ id })
      if (res.data.code === 0) {
        groupList.value.splice(index, 1)
      }
      return res
    } catch (error) {
      console.log(error)
    }
  }
  const updateRideGroupImgList = (data: IColorImage[]) => {
    if (groupList.value.length) {
      groupList.value.forEach((item) => {
        item.imgList = []
      })
      data.forEach((item) => {
        groupList.value.forEach((group) => {
          if (group.id === item.groupId) {
            item.id = item.id || uuidv4()
            group.imgList.push(item)
          }
        })
      })
    }
  }
  const updateRideGroup = async (params: any) => {
    try {
      const res = await updateModelGroup(params)
      if (res.data.code === 0) {
        groupList.value.forEach((group) => {
          if (group.id === params.id) {
            group.modelHeight = params.updateModelHeight
            ElMessage.success('操作成功')
          }
        })
      }
    } catch (error) {
      console.log(error)
    }
  }
  const moveRideGroup = async () => {
    const ids = groupList.value.map((item) => item.id)
    try {
      const res = await sortModelGroup({
        sortJson: ids.join(',')
      })
    } catch (error) {
      console.log(error)
    }
  }

  return {
    getGroupList,
    groupList,
    addRideGroup,
    removeGroup,
    moveRideGroup,
    updateRideGroupImgList,
    updateRideGroup,
    checkImgAll,
    checkSingleImage,
    addSingleImg,
    batchAddImg,
    batchDelImg,
    hasCheckImg,
    checkAllGroupsImg,
    syncImgInfo,
    rideImgList: imgList
  }
})
