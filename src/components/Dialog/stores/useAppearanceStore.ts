import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import { v4 as uuidv4 } from 'uuid'
import {
  getGroupList as getColorGroupList,
  postUpdateGroup,
  postAddGroup,
  postDeleteGroup
} from '@/api/garage'
import { EnumTabTyps, IColorGroup, IColorImage } from '../types/imageDialog'
import { generateImageListByDirections } from '../hooks/util'
import { useGroupStore } from '.'
import { WritableComputedRef } from 'vue'
import { useImageTool } from '../hooks/useImage'

export const useColorGroup = defineStore('color', () => {
  const rootStore = useGroupStore()
  const groupAppearanceList = ref<IColorGroup[]>([])
  const groupDeitalList = ref<IColorGroup[]>([])
  const groupList: WritableComputedRef<IColorGroup[]> = computed({
    get: () => {
      return rootStore.imgState.currentTabId === EnumTabTyps.APPEARANCE
        ? groupAppearanceList.value
        : groupDeitalList.value
    },

    set: (val) => {
      if (rootStore.imgState.currentTabId === EnumTabTyps.APPEARANCE) {
        groupAppearanceList.value = val
      } else {
        groupDeitalList.value = val
      }
    }
  })

  // 因为外观顺序移动不是小保存的, 所以需要手动同步外观的顺序
  watch(
    groupAppearanceList,
    (newVal, oldVal) => {
      if (newVal.length !== oldVal.length) return
      groupDeitalList.value = groupDeitalList.value.sort((a, b) => {
        const indexA = newVal.findIndex((item) => item.id === a.id)
        const indexB = newVal.findIndex((item) => item.id === b.id)
        return indexA - indexB
      })
    },
    { deep: true }
  )

  // 图片相关操作
  const {
    checkImgAll,
    checkSingleImage,
    addSingleImg,
    batchAddImg,
    moveImg,
    batchDelImg,
    changeImgTag,
    checkAllGroupsImg,
    syncImgInfo,
    hasCheckImg
  } = useImageTool(groupList, rootStore)
  const getGroupList = async (cache = false) => {
    try {
      const res = await getColorGroupList({ carId: rootStore.imgState.carId })
      if (res.data.code === 0) {
        let data: IColorGroup[] = res.data.data
        const detailData = JSON.parse(JSON.stringify(data))
        const ids = rootStore.imgState.labels.map((_) => _.id)
        data = data.map((item, index) => {
          if (!ids.includes(item.color)) {
            item.color = -1
            item.colorName = '无颜色'
            item.image = ''
          }
          item.isExpand = false

          const detailGroup = updateDetialGroup(item)
          detailData[index] = detailGroup

          const appearanceGroup = updateAppearanceGroup(item)

          return appearanceGroup
        })

        groupAppearanceList.value = data

        groupDeitalList.value = detailData
      }
    } catch (error) {
      console.log(error)
    }

    function updateAppearanceGroup(item: IColorGroup) {
      item.imgList = generateImageListByDirections([], (img) => {
        return {
          ...img,
          carId: rootStore.imgState.carId,
          imgCategory: EnumTabTyps.APPEARANCE,
          id: uuidv4()
        }
      })

      const sameGroup = groupAppearanceList.value.find(
        (group) => group.id === item.id
      )
      if (sameGroup && cache) {
        item.imgList = sameGroup.imgList
        item.imgList.forEach((img) => {
          img.color = item.color
          img.colorName = item.colorName
          img.checked = false // 就是要求每次打开都不选中
        })
      }
      return item
    }
    function updateDetialGroup(item: IColorGroup) {
      const sameDetailGroup = groupDeitalList.value.find(
        (group) => group.id === item.id
      )
      const detailItem = JSON.parse(JSON.stringify(item))
      detailItem.imgList = []
      if (sameDetailGroup && cache) {
        detailItem.imgList = sameDetailGroup.imgList
        detailItem.imgList.forEach((img: IColorImage) => {
          img.color = item.color
          img.colorName = item.colorName
        })
      }
      detailItem.imgCategory = EnumTabTyps.DETAIL
      return detailItem
    }
  }

  const addGroup = async (groupId: string, color: string) => {
    const url = groupId ? postUpdateGroup : postAddGroup
    if (!color) return ElMessage.error('请选择颜色')
    const params: any = {
      goodsId: rootStore.imgState?.goodsId,
      carId: rootStore.imgState?.carId,
      color: color
    }
    if (groupId) {
      params.id = groupId
    } else {
      params.sort = groupList.value.length + 1
    }
    try {
      const res = await url(params)
      if (res.data.code === 0) {
        getGroupList(true)
      }
    } catch (error) {
      console.log(error)
    }
  }
  const removeGroup = async (id: number, index: number) => {
    try {
      const res = await postDeleteGroup({ id })
      if (res.data.code === 0) {
        groupList.value.splice(index, 1)
        getGroupList(true)
      }
      return res
    } catch (error) {
      console.log(error)
    }
  }
  const updateColorGroupImgList = (data: IColorImage[]) => {
    if (groupList.value.length) {
      groupDeitalList.value.forEach((item) => {
        item.imgList = []
      })
      data.forEach((item) => {
        const iten = { ...item }
        groupAppearanceList.value.forEach((group) => {
          if (
            group.id === item.groupId &&
            item.imgCategory === EnumTabTyps.APPEARANCE
          ) {
            item.id = item.id || uuidv4()
            item.color = group.color
            item.colorName = group.colorName
            const sameDirectionImgIndex = group.imgList.findIndex(
              (img) => img.tag === item.tag
            )
            group.imgList[sameDirectionImgIndex] = {
              ...group.imgList[sameDirectionImgIndex],
              ...item
            }
          }
        })

        groupDeitalList.value.forEach((group) => {
          if (
            group.id === iten.groupId &&
            iten.imgCategory === EnumTabTyps.DETAIL
          ) {
            iten.id = iten.id || uuidv4()
            iten.color = group.color
            iten.colorName = group.colorName
            group.imgList.push(iten)
          }
        })
      })
    }
  }

  return {
    syncImgInfo,
    groupAppearanceList,
    groupDeitalList,
    getGroupList,
    groupList,
    addGroup,
    removeGroup,
    updateColorGroupImgList,
    checkImgAll,
    checkSingleImage,
    addSingleImg,
    moveImg,
    batchAddImg,
    batchDelImg,
    hasCheckImg,
    checkAllGroupsImg,
    changeImgTag
  }
})
