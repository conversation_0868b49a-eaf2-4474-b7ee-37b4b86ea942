import { useGroupStore } from '.'
import { useImageTool } from '../hooks/useImage'
import { IImage } from '../types/imageDialog'

interface IGroup {
  imgList: IImage[]
}
export const useCommonImgStore = () => {
  const rootStore = useGroupStore()

  const config = ref<any>({})
  const groupList = computed<any>({
    get: () => {
      return config.value[rootStore.imgState.currentTabId]
    },
    set: (val) => {
      config.value[rootStore.imgState.currentTabId] = val
    }
  })
}
