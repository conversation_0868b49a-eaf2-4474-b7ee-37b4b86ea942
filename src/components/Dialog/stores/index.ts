import { defineStore, storeToRefs } from 'pinia'
import { useColorGroup } from './useAppearanceStore'
import { EnumTabTyps, IColorImage } from '../types/imageDialog'

type IGroupState = {
  goodsId: string
  carId: string
  currentTabId: EnumTabTyps
  labels: ILabel[]
}
type ILabel = { id: number }
export const useGroupStore = defineStore('group', () => {
  const imgState = ref<IGroupState>({
    goodsId: '',
    carId: '',
    labels: [],
    currentTabId: EnumTabTyps.APPEARANCE
  })

  const updateState = (data: Partial<IGroupState>) => {
    imgState.value = { ...imgState.value, ...data }
  }
  const appearanceImgList = computed(() => {
    const colorStore = useColorGroup()
    return colorStore.groupAppearanceList.reduce((cur, next) => {
      return cur.concat(next.imgList.filter((item) => item.imgUrl))
    }, [] as IColorImage[])
  })
  const deitalImgList = computed(() => {
    const colorStore = useColorGroup()

    return colorStore.groupDeitalList.reduce((cur, next) => {
      return cur.concat(next.imgList.filter((item) => item.imgUrl))
    }, [] as IColorImage[])
  })
  const checkedGroupImgList = computed(() => {
    return appearanceImgList.value
      .concat(deitalImgList.value)
      .filter((item) => item.checked)
  })
  return {
    updateState,
    imgState,
    appearanceImgList,
    checkedGroupImgList,
    deitalImgList
  }
})
