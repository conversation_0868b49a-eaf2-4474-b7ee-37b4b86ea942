<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :title="title"
      center
      class="choose-dialog"
      width="650px"
      append-to-body
      top="10vh"
    >
      <div v-if="canChoose" class="choose-labels">
        <p v-for="(label, index) in labels" :key="index" class="box">
          {{ label.shopName }}
          <span @click="checkAction(label)">删除</span>
        </p>
      </div>
      <p class="search">
        <el-input
          v-focus
          v-model="searchValue"
          :placeholder="isId ? '请输入商家Id' : '请输入关键词，回车搜索'"
          prefix-icon="el-icon-search"
          type="text"
          style="width: 300px"
          clearable
          @change="getSearchList()"
        />
      </p>

      <div class="list">
        <el-table
          ref="multipleTable"
          :data="showList"
          row-key="multipleTable"
          border
          height="50vh"
          @row-click="checkAction"
          @select="handleSelect"
          @select-all="handleSelectAll"
        >
          <el-table-column
            v-if="canChoose"
            type="selection"
            label="选择"
            width="70"
            class-name="check-all"
            align="center"
          />
          <el-table-column
            v-if="isId"
            prop="shopId"
            label="商家ID"
            align="center"
          />
          <el-table-column prop="shopName" label="商家名称" align="center" />
          <el-table-column
            v-if="!isId"
            prop="address"
            label="商家地址"
            align="center"
          />
        </el-table>
      </div>
      <el-pagination
        v-model:current-page="page"
        :page-size="20"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        style="justify-content: center; margin-top: 10px"
        @current-change="currentChange"
      />
      <div v-if="showFooter" class="dialog-content center footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// <choose-dialog-shop ref="ChooseDialogShop" @sendData="getShopData" />
// this.$refs.ChooseDialogShop.init({
//   title, // 弹框标题，非必需
//   labels, // 已选中labels，非必需
//   getListUrl, // 请求接口地址，默认颜色，非必需
//   selectLimit, // 限制最大选择数
//   canChoose: true, // 是否支持选中，非必需
//   needSelfSearchShop: true // 是否使用手动搜索
// })
import { getlistShopsByKeywords, searchMerchantList } from '@/api/garage'
export default {
  name: 'ColorDialogShop',
  props: {
    isId: {
      default: false,
      type: Boolean
    },
    memberType: {
      default: '',
      type: String
    }
  },
  data() {
    return {
      page: 1, // 页码
      total: 0, // 总数
      showList: [], // 显示数据
      dataList: [], // 所有数据
      selectLimit: 9999, // 限制最大选择数
      searchValue: '',
      dialogVisible: false,
      currentColorId: 0,
      labels: [], // 已选中标签
      title: '', // 弹框标题
      getListUrl: '', // 查询接口地址
      canChoose: false, // 是否支持选中
      showFooter: true, // 是否显示footer
      needSelfSearchShop: false // 是否使用手动搜索
    }
  },
  methods: {
    // 重置状态
    init(params = {}) {
      this.dialogVisible = true
      this.searchValue = ''
      this.showList = []
      if (params.labels && params.labels.length) {
        this.labels = [...params.labels]
      } else {
        this.labels = []
      }
      this.title = params.title || this.title
      this.getListUrl = params.getListUrl
      this.canChoose = params.canChoose
      this.showFooter = params.showFooter !== false
      this.needSelfSearchShop = params.needSelfSearchShop || false
      this.selectLimit = params.selectLimit || this.selectLimit
      this.total = 0
      this.dialogVisible = true
      if (this.needSelfSearchShop) {
        // todo
        // 接口 keywords 需要改为非必需，目前只支持按关键词搜索
        // this.getSearchList()
      } else {
        this.getList()
      }
    },
    // 获取列表
    getList() {
      this.dataList = []
      this.getListUrl
        .then((response) => {
          if (response.data.code === 0) {
            this.dataList = response.data.data
            this.showList = this.dataList
            this.initChecks('upDataLable')
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .catch(() => {})
        .finally(() => {})
    },
    checkLabel(target) {
      const me = this
      console.log(me.labels)

      let list = []
      const tempArr = me.labels.filter((_) => _.shopName === target.shopName)
      const exist = tempArr.length > 0
      if (exist) {
        // 已存在，删除
        list = me.labels.filter((_) => _.shopName !== target.shopName)
      } else {
        // 不存在，添加
        list = me.labels
        list.splice(0, 0, target)
      }
      me.labels = list
    },
    // 选中或删除
    checkAction(item) {
      const me = this
      // 是否未选择标签
      const unCheck =
        me.labels.filter((_) => _.shopName === item.shopName).length === 0
      // todo: dataList 和 showList 可以使用一个
      if (me.dataList && me.dataList.length) {
        let index = 0
        me.dataList.map((value, dIndex) => {
          if (value.goodName === item.goodName) {
            index = dIndex
          }
        })
        me.$refs.multipleTable.toggleRowSelection(me.dataList[index], unCheck)
      } else {
        me.$refs.multipleTable.toggleRowSelection(item, unCheck)
      }
      me.checkLabel(item)
    },
    // 单选
    handleSelect(list, target) {
      this.checkLabel(target)
    },
    // 多选
    handleSelectAll(list) {
      // this.$refs.multipleTable.clearSelection()
      // return
      // console.log(list)
      // this.labels = list
      // console.log(list)
    },
    initChecks(type) {
      const me = this
      let tempArr = []
      me.$nextTick(() => {
        me.showList.map((value) => {
          // 是否已选择标签
          const check = me.labels.some((_) => _.shopName === value.shopName)
          tempArr = check ? tempArr.concat(value) : tempArr
          me.$refs.multipleTable.toggleRowSelection(value, check)
        })
      })
      if (type) {
        // 因为外层数据跟获取数据不一致，此步骤更新整个外层数据
        setTimeout(() => {
          me.labels = tempArr
        }, 500)
      }
    },
    // 搜索
    getSearchList() {
      const me = this
      if (me.needSelfSearchShop) {
        return me.getSearchShopList()
      }
      me.showList = []
      me.dataList.map(function (value) {
        if (value.shopName.indexOf(me.searchValue) > -1) {
          me.showList.push(value)
        }
      })
    },
    // 变更页码，上一页或下一页
    currentChange(page) {
      this.page = page
      this.getSearchShopList()
    },
    // 搜索所有店铺列表
    getSearchShopList() {
      const me = this
      me.showList = []
      if (!me.searchValue) {
        return this.$message.error('请先输入搜索关键词')
      }
      const url = me.isId ? searchMerchantList : getlistShopsByKeywords
      url({
        keywords: me.isId ? '' : me.searchValue,
        page: me.page,
        shopId: me.isId ? me.searchValue : '',
        limit: 20,
        memberType: me.memberType
      })
        .then((response) => {
          if (response.data.code === 0) {
            const result = response.data.data
            me.showList = result.listData || result.list
            me.total = result.total
          } else {
            this.$message.error(response.data.msg)
          }
        })
        .catch(() => {})
        .finally(() => {
          me.initChecks()
        })
    },
    // 关闭
    handleClose() {
      // console.log(this.labels)
      if (this.labels.length === 0) {
        this.dialogVisible = false
        return false
      }
      this.$confirm('确认关闭？')
        .then(() => {
          this.dialogVisible = false
        })
        .catch(() => {})
    },
    // 确认
    confirm() {
      const me = this
      me.dialogVisible = false
      console.log(me.labels)
      if (!me.labels || !me.labels.length) return

      // 限制返回的长度
      me.labels.length =
        me.labels.length > me.selectLimit ? me.selectLimit : me.labels.length

      me.$emit('sendData', me.labels)
    }
  }
}
</script>
<style lang="scss">
.choose-dialog {
  .el-dialog--center .el-dialog__body {
    padding: 15px;
  }
  .el-dialog.el-dialog--center {
    margin-top: 10vh !important;
  }
  .el-table__header-wrapper .check-all .cell {
    .el-checkbox {
      display: none;
    }
    &::after {
      content: '选择';
    }
  }
}
</style>
<style lang="scss" scoped>
.choose-dialog {
  .choose-labels {
    border-bottom: 1px solid #ddd;
    max-height: 100px;
    min-height: 35px;
    overflow-y: auto;
    .box {
      margin: 0 10px 10px 0;
      line-height: 24px;
      height: 24px;
      border: 1px solid #ddd;
      padding-left: 5px;
      display: inline-block;
      color: #ffffff;
      background: #9a7b49;
      span {
        text-decoration: underline;
        cursor: pointer;
        border-left: 1px solid #ddd;
        padding: 0 3px;
        display: inline-block;
        height: 100%;
        background: #f56c6c;
        margin-left: 5px;
      }
    }
  }
  .list {
    max-height: 70vh;
    .block-color {
      width: 100px;
      height: 50px;
      border-radius: 5px;
      margin: 0 auto;
    }
  }
  .dialog-content {
    margin-top: 20px;
  }
}
</style>
