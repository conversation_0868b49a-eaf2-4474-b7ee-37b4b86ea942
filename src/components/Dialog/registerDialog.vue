<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      width="440px"
      center
      :before-close="handleClose"
      title="注册校验"
      append-to-body
    >
      <div style="margin-bottom: 20px">
        <div>
          手机号：<el-input
            placeholder="请输入手机号"
            disabled
            v-model="phone"
            type="text"
            style="width: 200px"
          />
        </div>
        <div style="margin-top: 20px">
          验证码：<el-input
            placeholder="请输入验证码"
            v-model="code"
            type="text"
            style="width: 200px"
          />
          <el-button type="primary" @click="getQaCreateCode()"
            >获取验证码</el-button
          >
        </div>
      </div>
      <template v-slot:footer>
        <div>
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="saveCode">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { RequestCodeCode, checkRegister, LoginMobile } from '@/api/system'
import { AES } from '@/utils/aes'
import { v4 as uuidv4 } from 'uuid'
export default {
  name: 'SingUpDialog',
  props: {},
  data() {
    return {
      dialogVisible: false,
      phone: '',
      code: ''
    }
  },
  methods: {
    init(data) {
      this.phone = data.phone
      this.checkPhone()
    },
    // 校验是否注册
    checkPhone() {
      checkRegister({
        mobile: this.phone
      })
        .then((response) => {
          if (response.data.code === 0) {
            if (response.data.data === 1) {
              $emit(this, 'sucess')
            } else {
              this.dialogVisible = true
            }
          } else {
            this.dialogVisible = true
          }
        })
        .catch()
        .finally(() => {})
    },
    // 获取验证码
    getQaCreateCode() {
      RequestCodeCode({
        bundleId: 'com.jdd.motorfans',
        mobile: AES(this.phone.trim())
      }).then((response) => {})
    },
    // 注册新用户
    saveCode() {
      if (!this.code) {
        return this.$message.error('请输入验证码')
      }
      LoginMobile({
        mobile: AES(this.phone.trim()),
        code: this.code.trim(),
        bundleId: 'com.jdd.motorfans',
        deviceId: uuidv4().toUpperCase()
      }).then((response) => {
        if (response.data.code === 0) {
          $emit(this, 'sucess')
          this.dialogVisible = false
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
    }
  },
  emits: ['sucess']
}
</script>

<style lang="scss">
.qr-content {
  img {
    margin: 0 auto;
  }
}
</style>
