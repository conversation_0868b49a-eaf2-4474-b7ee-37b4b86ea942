<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="手机号码"
      center
      class="choose-dialog"
      width="600px"
      append-to-body
    >
      <el-form
        ref="phoneForm"
        :model="phoneData"
        :rules="rules"
        label-width="100px"
        class="detail-entry"
      >
        <el-form-item label="联系人" required>
          <el-input
            v-focus
            v-model="phoneData.name"
            type="text"
            maxlength="12"
          />
        </el-form-item>
        <el-form-item label="手机号码" required>
          <el-input
            v-focus
            v-model="phoneData.phone"
            type="number"
            maxlength="13"
          />
        </el-form-item>
        <el-form-item label="人员角色">
          <el-select v-model="phoneData.roleType">
            <el-option
              v-for="item in roleTypes"
              :key="item.name"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="接收线索">
          <el-switch v-model="phoneData.canReceiveClue" />
        </el-form-item>
      </el-form>
      <div class="dialog-content center footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
    <RegisterDialog @sucess="sucess" ref="RegisterDialog"></RegisterDialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../utils/gogocodeTransfer'
import { deepCopy } from '@/utils'
import { getMobile } from '@/api/user'
import RegisterDialog from './registerDialog.vue'
export default {
  name: 'ChoosePhone',
  components: {
    RegisterDialog
  },
  props: {
    source: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      updataStatus: false, // 变更状态
      allData: [], // 所有数据，最后判定重复使用
      oldPhoneData: {}, // 旧数据
      oldPhone: '', // 旧数据
      registerSign: false, // 是否需要检测注册信息
      roleTypes: [
        {
          name: '管理员',
          value: 1
        },
        {
          name: '店长',
          value: 2
        },
        {
          name: '店员',
          value: 3
        }
      ],
      phoneData: {
        name: '',
        phone: '',
        roleType: 1
      },
      rules: {} // 验证信息
    }
  },
  methods: {
    setData(data, allData, registerSign) {
      const me = this
      me.phoneData = {
        ...this.phoneData,
        ...data
      }
      me.registerSign = registerSign
      if (me.phoneData.phone && isNaN(me.phoneData.phone)) {
        me.getPhone()
      }
      me.updataStatus = !!data.phone
      if (data.phone) {
        me.oldPhoneData = deepCopy(me.phoneData)
      }
      me.allData = allData
      me.dialogVisible = true
      setTimeout(() => {
        me.$refs['phoneForm'].resetFields() // 重置验证
      }, 100)
    },
    // 查看手机
    getPhone() {
      //  mobile, source
      const me = this
      me.dialogVisible = true
      getMobile({
        mobile: me.phoneData.phone,
        source: me.source
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.phoneData.phone = response.data.data
            me.oldPhone = response.data.data
          } else {
            me.$message.error(response.data.msg)
          }
        })
        .catch((err) => {
          me.$message.error(err.data.msg)
        })
    },
    // 关闭
    handleClose() {
      if (!this.phoneData.name && !this.phoneData.phone) {
        this.dialogVisible = false
        return false
      }
      this.$confirm('确认关闭？')
        .then((_) => {
          this.dialogVisible = false
        })
        .catch((_) => {})
    },
    // 确认
    confirm() {
      const me = this
      if (!me.phoneData.name) {
        return me.$message.error('请输入联系人姓名')
      }
      if (!me.phoneData.phone) {
        return me.$message.error('请输入手机号码')
      }
      const tempArr =
        me.allData && me.allData.filter((_) => _.phone === me.phoneData.phone)
      if (
        (!me.updataStatus && tempArr.length) ||
        (me.updataStatus &&
          tempArr.length &&
          me.oldPhoneData &&
          me.oldPhoneData.phone !== me.phoneData.phone)
      ) {
        // 新增时、编辑时
        me.$message.error('手机号码有重复')
        return
      }
      // 取当前管理员的数量
      const curAdminLength = me.allData.filter(
        (item) => item.roleType === 1
      ).length
      // 如果是编辑状态
      if (me.updataStatus) {
        if (
          curAdminLength <= 1 &&
          me.oldPhoneData.roleType === 1 &&
          me.phoneData.roleType !== 1
        ) {
          return me.$message.error('必须存在一个管理员')
        }
      }
      // 需要检测是否注册信息逻辑
      if (!me.registerSign || me.phoneData.phone === me.oldPhone) {
        me.emitData()
      } else {
        this.$refs.RegisterDialog.init({
          phone: me.phoneData.phone
        })
      }
    },
    // 发送数据
    emitData() {
      const me = this
      const phoneData = deepCopy(me.phoneData)
      $emit(me, 'sendPhoneData', phoneData, me.updataStatus)
      me.updataStatus = false
      me.oldPhoneData = {}
      me.dialogVisible = false
    },
    // 注册成功
    sucess() {
      this.emitData()
    }
  },
  emits: ['sendPhoneData']
}
</script>
