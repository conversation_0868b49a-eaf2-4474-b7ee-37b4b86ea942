<template>
  <div>
    <el-dialog
      :title="forget ? '忘记密码' : '修改密码'"
      v-model="changePasswordDialog"
      width="400px"
      center
      append-to-body
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <div class="text-center" style="margin-bottom: 10px">
        <el-radio-group v-if="!forget" v-model="modifyType">
          <el-radio :label="1">旧密码验证</el-radio>
          <el-radio :label="2">验证码验证</el-radio>
        </el-radio-group>
      </div>
      <el-form :model="form" label-width="55px">
        <el-form-item v-if="forget" label="用户名">
          <el-input v-model="form.name" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item v-if="modifyType === 1 && !forget" label="旧密码">
          <el-input
            v-model="form.oldPassword"
            placeholder="请输入旧密码"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item label="新密码">
          <el-input
            v-model="form.newPassword"
            placeholder="请输入新密码"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item v-if="modifyType === 2 || forget" label="验证码">
          <div class="flex">
            <el-input
              class="flex1"
              v-model="form.code"
              placeholder="请输入验证码"
              maxlength="6"
            ></el-input>
            <el-button
              style="margin-left: 10px; width: 110px"
              :type="latencyTime ? '' : 'primary'"
              plain
              @click="getCode"
              >{{ buttonTxt }}</el-button
            >
          </div>
        </el-form-item>
      </el-form>
      <div class="text-center">
        <el-button style="width: 50%" type="primary" @click="submit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  modifyPassword,
  sendEmailVerifyCode,
  getVerifyCodeByEmail,
  resetPassword
} from '@/api/user'
export default {
  name: 'changePassword',
  props: {
    isLogin: {
      type: Boolean,
      default: false
    },
    forget: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      changePasswordDialog: false,
      modifyType: 1,
      form: {
        oldPassword: '',
        newPassword: '',
        name: '',
        code: ''
      },
      buttonTxt: '获取验证码',
      latencyTime: 0,
      timeInterval: null
    }
  },
  computed: {
    ...mapGetters(['name', 'uid'])
  },
  methods: {
    init() {
      this.modifyType = 1
      this.form = {
        oldPassword: '',
        newPassword: '',
        code: '',
        name: ''
      }
      this.buttonTxt = '获取验证码'
      this.latencyTime = 0
      this.timeInterval = null
      this.changePasswordDialog = true
    },
    handleClose() {
      clearInterval(this.timeInterval)
      if (this.isLogin && !this.forget) {
        this.$emit('updateCode')
      }
      if (this.forget) {
        this.$emit('updateStatus')
      }
      this.changePasswordDialog = false
    },
    getCode() {
      if (this.latencyTime > 0) return
      this.latencyTime = 60
      this.buttonTxt = '60s'
      this.timeInterval = setInterval(() => {
        this.latencyTime = --this.latencyTime
        if (this.latencyTime <= 0) {
          clearInterval(this.timeInterval)
          this.latencyTime = 0
          return (this.buttonTxt = '获取验证码')
        }
        this.buttonTxt = `${this.latencyTime}s`
      }, 1000)
      const sessionStorageUser = JSON.parse(
        sessionStorage.getItem('user') || '{}'
      )
      const fn = this.forget
        ? () => getVerifyCodeByEmail(this.form.name)
        : () =>
            sendEmailVerifyCode({ uid: this.uid || sessionStorageUser.userid })
      fn()
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('验证码已发送至企业邮箱')
          } else {
            this.latencyTime = 0
            this.buttonTxt = '获取验证码'
            this.$message.error('验证码获取失败')
          }
        })
        .catch(() => {
          this.latencyTime = 0
          this.buttonTxt = '获取验证码'
        })
    },
    submit() {
      if (!this.form.name && this.forget) {
        return this.$message.error('请输入用户名')
      }
      if (this.modifyType === 1 && !this.forget && !this.form.oldPassword) {
        return this.$message.error('请输入旧密码')
      }
      if (!this.form.newPassword) {
        return this.$message.error('请输入新密码')
      }
      if (this.form.newPassword.length < 8) {
        return this.$message.error('密码长度必须大于等于8')
      }
      const checkPassword =
        // eslint-disable-next-line no-useless-escape
        /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{8,64}$/
      if (!checkPassword.test(this.form.newPassword)) {
        return this.$message.error('新密码必须包含数字+字母大小写+特殊字符')
      }
      if ((this.modifyType === 2 || this.forget) && !this.form.code) {
        return this.$message.error('请输入验证码')
      }
      this.forget ? this.resetPassword() : this.confirm()
    },
    async resetPassword() {
      try {
        const res = await resetPassword({
          newPassword: this.form.newPassword,
          verifyCode: this.form.code,
          userCode: this.form.name
        })
        if (res.data.code === 0) {
          this.$message.success('重置成功')
          this.handleClose()
        }
      } catch (error) {
        console.log(error)
        // const errData = error.response.data || {}
        // this.$message.error(errData.msg || '重置失败')
      }
    },
    confirm() {
      const sessionStorageUser = JSON.parse(
        sessionStorage.getItem('user') || '{}'
      )
      let parameter = {
        uid: this.uid || sessionStorageUser.userid,
        oldPassword: this.form.oldPassword,
        newPassword: this.form.newPassword,
        verifyCode: this.form.code,
        modifyType: this.modifyType
      }
      if (this.modifyType === 1) {
        delete parameter.verifyCode
      }
      if (this.modifyType === 2) {
        delete parameter.oldPassword
      }
      const me = this
      modifyPassword(parameter)
        .then((res) => {
          if (res.data.code === 0) {
            me.$message.success('修改成功')
            setTimeout(() => {
              me.handleClose()
              me.logOut()
            }, 1000)
          } else {
            me.$message.error('修改失败')
          }
        })
        .catch((err) => {
          console.log('err', err)
        })
    },
    // 修改成功退出登录
    logOut() {
      this.$store.dispatch('LogOut').then(() => {
        location.reload()
      })
    }
  }
}
</script>
