<template>
  <el-form-item v-if="options.length" :label="title" :required="required">
    <el-select
      v-model="channel"
      placeholder="请选择售卖渠道"
      @change="handleChange"
      style="width: 150px"
    >
      <el-option
        v-for="item in options"
        :key="item.channel"
        :label="item.channelName"
        :value="item.channel"
      ></el-option>
    </el-select>
  </el-form-item>
</template>

<script setup>
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  updateShopBrandChannel,
  getBrandChannelCfg,
  updateChannelOnMidMax
} from '@/api/garage'
import { ref, computed, watchPostEffect } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '售卖渠道'
  },
  modelValue: {},
  item: {
    type: Object,
    default: () => {
      return {
        shopId: '',
        channel: '',
        brandId: ''
      }
    }
  },
  //立即更新
  immediate: {
    type: Boolean,
    default: false
  },
  defaultOptions: {
    type: Object,
    default: null
  },
  required: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])
const channel = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

const options = ref(props.defaultOptions?.channels || [])
const channelType = ref(props.defaultOptions?.channelType || '')
let oldChannel = props.modelValue

watchPostEffect(() => {
  if (props.item.brandId && !props.defaultOptions) {
    getChannel()
  }
})

const getChannel = async () => {
  const params = {
    brandId: props.item.brandId
  }
  try {
    const { data } = await getBrandChannelCfg(params)
    if (data.code == 0) {
      options.value = data.data[0]?.channels || []
      channelType.value = data.data[0]?.channelType || ''
    }
  } catch (error) {
    console.log(error)
  }
}

const handleChange = (value) => {
  if (props.immediate) {
    ElMessageBox.confirm('确认更改售卖渠道？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        let url = updateShopBrandChannel
        if (channelType.value == 2) {
          url = updateChannelOnMidMax
        }
        url({
          shopId: props.item.shopId,
          channel: props.item.channel,
          brandId: props.item.brandId,
          channelType: channelType.value
        })
          .then((res) => {
            if (res.data.code == 0) {
              ElMessage({
                type: 'success',
                message: '更新成功'
              })
              oldChannel = channel.value
              emit('change', channelType.value)
            }
          })
          .catch(() => {
            channel.value = oldChannel
          })
      })
      .catch(() => {
        channel.value = oldChannel
      })
  } else {
    emit('change', channelType.value)
  }
}

defineExpose({
  options
})
</script>

<style lang="scss" scoped></style>
